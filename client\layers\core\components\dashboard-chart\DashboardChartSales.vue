<script setup lang="ts">
import { ref } from "vue";

const months = ref(["Jan", "Feb", "Mar", "Apr", "May", "Jun"]);
const data = ref([
  {
    month: "Jan",
    values: [
      { name: "Products", value: 12000, color: "var(--color-primary-500)" },
      { name: "Services", value: 8000, color: "var(--color-info-500)" },
      { name: "Subscriptions", value: 5000, color: "var(--color-success-500)" },
    ],
  },
  {
    month: "Feb",
    values: [
      { name: "Products", value: 19000, color: "var(--color-primary-500)" },
      { name: "Services", value: 11000, color: "var(--color-info-500)" },
      { name: "Subscriptions", value: 7500, color: "var(--color-success-500)" },
    ],
  },
  {
    month: "Mar",
    values: [
      { name: "Products", value: 15000, color: "var(--color-primary-500)" },
      { name: "Services", value: 9500, color: "var(--color-info-500)" },
      { name: "Subscriptions", value: 6200, color: "var(--color-success-500)" },
    ],
  },
  {
    month: "Apr",
    values: [
      { name: "Products", value: 22000, color: "var(--color-primary-500)" },
      { name: "Services", value: 14000, color: "var(--color-info-500)" },
      { name: "Subscriptions", value: 9000, color: "var(--color-success-500)" },
    ],
  },
  {
    month: "May",
    values: [
      { name: "Products", value: 18000, color: "var(--color-primary-500)" },
      { name: "Services", value: 12500, color: "var(--color-info-500)" },
      { name: "Subscriptions", value: 8200, color: "var(--color-success-500)" },
    ],
  },
  {
    month: "Jun",
    values: [
      { name: "Products", value: 25000, color: "var(--color-primary-500)" },
      { name: "Services", value: 16000, color: "var(--color-info-500)" },
      {
        name: "Subscriptions",
        value: 10500,
        color: "var(--color-success-500)",
      },
    ],
  },
]);

// Find the maximum value for scaling
const maxValue = Math.max(
  ...data.value.flatMap((item) => item.values.map((v) => v.value))
);

// Calculate the height of each bar (as a percentage of the maximum)
data.value.forEach((item) => {
  item.values.forEach((value) => {
    value.height = (value.value / maxValue) * 90;
  });
});

// Bar width and spacing
const barWidth = 8;
const barSpacing = 2;
const groupWidth = (barWidth + barSpacing) * 3;
const groupSpacing = 10;
</script>

<template>
  <div class="h-40 w-full">
    <svg
      class="h-full w-full"
      viewBox="0 0 340 150"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Y-axis -->
      <line
        x1="40"
        y1="10"
        x2="40"
        y2="120"
        stroke="var(--color-muted-300)"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />

      <!-- X-axis -->
      <line
        x1="40"
        y1="120"
        x2="330"
        y2="120"
        stroke="var(--color-muted-300)"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />

      <!-- Horizontal grid lines -->
      <line
        x1="40"
        y1="90"
        x2="330"
        y2="90"
        stroke="var(--color-muted-200)"
        stroke-width="1"
        stroke-dasharray="4"
        class="dark:stroke-muted-800"
      />
      <line
        x1="40"
        y1="60"
        x2="330"
        y2="60"
        stroke="var(--color-muted-200)"
        stroke-width="1"
        stroke-dasharray="4"
        class="dark:stroke-muted-800"
      />
      <line
        x1="40"
        y1="30"
        x2="330"
        y2="30"
        stroke="var(--color-muted-200)"
        stroke-width="1"
        stroke-dasharray="4"
        class="dark:stroke-muted-800"
      />

      <!-- Bar groups -->
      <g v-for="(item, groupIndex) in data" :key="`group-${groupIndex}`">
        <!-- Group of bars -->
        <g
          v-for="(value, barIndex) in item.values"
          :key="`bar-${groupIndex}-${barIndex}`"
        >
          <rect
            :x="
              50 +
              groupIndex * (groupWidth + groupSpacing) +
              barIndex * (barWidth + barSpacing)
            "
            :y="120 - value.height"
            :width="barWidth"
            :height="value.height"
            :fill="value.color"
            rx="2"
          />
        </g>

        <!-- X-axis labels -->
        <text
          :x="50 + groupIndex * (groupWidth + groupSpacing) + groupWidth / 2"
          y="135"
          text-anchor="middle"
          fill="var(--color-muted-500)"
          font-size="9"
        >
          {{ item.month }}
        </text>
      </g>

      <!-- Y-axis labels -->
      <text
        x="35"
        y="120"
        text-anchor="end"
        fill="var(--color-muted-500)"
        font-size="9"
      >
        0
      </text>
      <text
        x="35"
        y="90"
        text-anchor="end"
        fill="var(--color-muted-500)"
        font-size="9"
      >
        10k
      </text>
      <text
        x="35"
        y="60"
        text-anchor="end"
        fill="var(--color-muted-500)"
        font-size="9"
      >
        20k
      </text>
      <text
        x="35"
        y="30"
        text-anchor="end"
        fill="var(--color-muted-500)"
        font-size="9"
      >
        30k
      </text>

      <!-- Legend -->
      <g transform="translate(120, 145)">
        <rect
          x="0"
          y="0"
          width="8"
          height="8"
          fill="var(--color-primary-500)"
          rx="1"
        />
        <text x="12" y="7" fill="var(--color-muted-500)" font-size="8">
          Products
        </text>

        <rect
          x="70"
          y="0"
          width="8"
          height="8"
          fill="var(--color-info-500)"
          rx="1"
        />
        <text x="82" y="7" fill="var(--color-muted-500)" font-size="8">
          Services
        </text>

        <rect
          x="140"
          y="0"
          width="8"
          height="8"
          fill="var(--color-success-500)"
          rx="1"
        />
        <text x="152" y="7" fill="var(--color-muted-500)" font-size="8">
          Subscriptions
        </text>
      </g>
    </svg>
  </div>
</template>
