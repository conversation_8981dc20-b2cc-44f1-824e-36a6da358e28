@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\cli\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\cli\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\cli\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\cli\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules\@nuxt\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@nuxt+cli@3.25.1_magicast@0.3.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\@nuxt\cli\bin\nuxi.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\@nuxt\cli\bin\nuxi.mjs" %*
)
