<template>
  <div class="relative mb-16">
    <!-- Cover Image -->
    <div
      class="h-40 md:h-60 w-full rounded-lg bg-muted-200 dark:bg-muted-800 overflow-hidden relative z-0"
    >
      <img
        v-if="company?.coverImage"
        :src="company.coverImage"
        alt="Company Cover"
        class="w-full h-full object-cover"
      />
      <div
        v-else
        class="w-full h-full flex items-center justify-center bg-gradient-to-r from-primary-500 to-primary-700"
      >
        <span class="text-white text-lg font-medium">{{
          company?.name || "Company"
        }}</span>
      </div>

      <div class="absolute top-1/2 transform -translate-y-1/2 left-20 z-10">
        <div class="relative">
          <div
            class="absolute inset-0 rounded-full bg-white dark:bg-muted-900 -m-1.5 z-10"
          ></div>
          <BaseAvatar
            v-if="company?.logo"
            :src="company.logo"
            size="3xl"
            class="relative z-20"
            style="object-fit: cover"
          />
          <BaseAvatar
            v-else
            :icon="'ph:building'"
            size="3xl"
            class="relative z-20 shadow-lg"
          />
        </div>
        <!-- Logo Upload Button -->
        <div class="absolute -bottom-1 -right-1 z-30">
          <BaseButton
            color="primary"
            shape="full"
            size="sm"
            class="shadow-lg"
            @click="openLogoUpload"
          >
            <Icon name="ph:camera-duotone" class="h-4 w-4" />
          </BaseButton>
          <input
            ref="logoInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleLogoUpload"
          />
        </div>
      </div>

      <!-- Cover Image Upload Button -->
      <div class="absolute bottom-4 right-4">
        <BaseButton
          color="white"
          shape="rounded"
          class="shadow-lg"
          @click="openCoverImageUpload"
        >
          <Icon name="ph:camera-duotone" class="h-4 w-4 mr-1" />
          Change Cover
        </BaseButton>
        <input
          ref="coverImageInput"
          type="file"
          accept="image/*"
          class="hidden"
          @change="handleCoverImageUpload"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useUserStore } from "../../../stores/useUserStore";
import { useApi } from "../../../app/composables/useApi";

const props = defineProps<{
  company?: {
    id?: string;
    name?: string;
    logo?: string;
    coverImage?: string;
    type?: string;
    status?: string;
  };
}>();

const emit = defineEmits<{
  (e: "logoUpdated" | "coverUpdated"): void;
}>();

// Refs
const logoInput = ref<HTMLInputElement | null>(null);
const coverImageInput = ref<HTMLInputElement | null>(null);

// Services
const userStore = useUserStore();
const toaster = useNuiToasts();
const api = useApi();

// Methods
const openLogoUpload = () => {
  if (logoInput.value) {
    logoInput.value.click();
  }
};

const handleLogoUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0 && props.company?.id) {
    const file = input.files[0];

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await api.post(
        `/uploads/company-logo/${props.company.id}`,
        formData
      );

      if (response?.data && response.data.url) {
        // Refresh user data to get updated company info
        await userStore.fetchUser();
        emit("logoUpdated");
        toaster.add({
          title: "Success",
          description: "Company logo updated successfully",
          icon: "ph:check-circle-duotone",
          progress: true,
        });
      }
    } catch (error) {
      console.error("Error uploading company logo:", error);
      toaster.add({
        title: "Error",
        description: "Failed to upload company logo",
        icon: "ph:x-circle-duotone",
        progress: true,
      });
    }
  }
};

const openCoverImageUpload = () => {
  if (coverImageInput.value) {
    coverImageInput.value.click();
  }
};

const handleCoverImageUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0 && props.company?.id) {
    const file = input.files[0];

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await api.post(
        `/uploads/company-cover/${props.company.id}`,
        formData
      );

      if (response?.data && response.data.url) {
        // Refresh user data to get updated company info
        await userStore.fetchUser();
        emit("coverUpdated");
        toaster.add({
          title: "Success",
          description: "Company cover image updated successfully",
          icon: "ph:check-circle-duotone",
          progress: true,
        });
      }
    } catch (error) {
      console.error("Error uploading company cover image:", error);
      toaster.add({
        title: "Error",
        description: "Failed to upload company cover image",
        icon: "ph:x-circle-duotone",
        progress: true,
      });
    }
  }
};
</script>
