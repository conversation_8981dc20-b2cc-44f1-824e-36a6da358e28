#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/sqlite-wasm/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/sqlite-wasm/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/sqlite-wasm/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/sqlite-wasm/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules/@sqlite.org/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@sqlite.org+sqlite-wasm@3.49.1-build2/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@sqlite.org/sqlite-wasm/bin/index.js" "$@"
else
  exec node  "$basedir/../@sqlite.org/sqlite-wasm/bin/index.js" "$@"
fi
