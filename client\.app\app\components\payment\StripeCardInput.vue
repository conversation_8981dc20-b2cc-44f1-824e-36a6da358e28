<template>
  <div>
    <BaseField
      :label="label"
      :state="error ? 'error' : 'idle'"
      :error="error"
      :required="required"
    >
      <div
        :id="elementId"
        class="border border-muted-200 dark:border-muted-700 rounded-lg p-3 bg-white dark:bg-muted-900 transition-colors duration-200"
        :class="{
          'border-red-500 dark:border-red-500': error,
          'border-primary-500 dark:border-primary-500': focused,
        }"
      >
        <!-- Stripe Elements will be mounted here -->
      </div>
      <div v-if="error" class="text-red-500 text-sm mt-1">
        {{ error }}
      </div>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";

// Props
interface Props {
  label?: string;
  required?: boolean;
  error?: string;
  elementId?: string;
  stripePublicKey?: string;
}

const props = withDefaults(defineProps<Props>(), {
  label: "Card Information",
  required: true,
  error: "",
  elementId: "stripe-card-element",
  stripePublicKey: "",
});

// Emits
const emit = defineEmits<{
  ready: [element: any];
  change: [event: any];
  focus: [];
  blur: [];
  error: [error: string];
}>();

// State
const stripe = ref(null);
const cardElement = ref(null);
const focused = ref(false);
const isLoading = ref(true);

// Stripe initialization
const initializeStripe = async () => {
  try {
    if (!window.Stripe) {
      throw new Error("Stripe.js not loaded");
    }

    if (!props.stripePublicKey) {
      throw new Error("Stripe public key not provided");
    }

    stripe.value = window.Stripe(props.stripePublicKey);
    const elements = stripe.value.elements();

    // Create card element
    cardElement.value = elements.create("card", {
      style: {
        base: {
          fontSize: "16px",
          color: "#424770",
          "::placeholder": {
            color: "#aab7c4",
          },
        },
        invalid: {
          color: "#9e2146",
        },
      },
      hidePostalCode: true, // We'll collect this separately
    });

    // Mount the element
    cardElement.value.mount(`#${props.elementId}`);

    // Set up event listeners
    cardElement.value.on("ready", () => {
      isLoading.value = false;
      emit("ready", cardElement.value);
    });

    cardElement.value.on("change", (event: any) => {
      if (event.error) {
        emit("error", event.error.message);
      } else {
        emit("error", "");
      }
      emit("change", event);
    });

    cardElement.value.on("focus", () => {
      focused.value = true;
      emit("focus");
    });

    cardElement.value.on("blur", () => {
      focused.value = false;
      emit("blur");
    });
  } catch (error) {
    console.error("Error initializing Stripe:", error);
    emit("error", error.message || "Failed to initialize payment form");
    isLoading.value = false;
  }
};

// Load Stripe script
const loadStripeScript = () => {
  return new Promise((resolve, reject) => {
    if (window.Stripe) {
      resolve(window.Stripe);
      return;
    }

    const script = document.createElement("script");
    script.src = "https://js.stripe.com/v3/";
    script.onload = () => resolve(window.Stripe);
    script.onerror = () => reject(new Error("Failed to load Stripe.js"));
    document.head.appendChild(script);
  });
};

// Public methods
const getElement = () => cardElement.value;
const getStripe = () => stripe.value;

// Expose methods to parent
defineExpose({
  getElement,
  getStripe,
});

// Lifecycle
onMounted(async () => {
  try {
    await loadStripeScript();
    await initializeStripe();
  } catch (error) {
    console.error("Error setting up Stripe:", error);
    emit("error", error.message || "Failed to load payment form");
  }
});

onUnmounted(() => {
  if (cardElement.value) {
    cardElement.value.destroy();
  }
});

// Watch for stripe key changes
watch(
  () => props.stripePublicKey,
  (newKey) => {
    if (newKey && !stripe.value) {
      initializeStripe();
    }
  }
);
</script>
