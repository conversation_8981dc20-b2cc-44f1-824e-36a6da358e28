// client/.app/stores/useAiStore.ts

import { defineStore } from "pinia";
import { useApi } from "../app/composables/useApi";

interface CommandContext {
  currentPath: string;
  currentModule: string;
  availableIntents: string[];
  currentLocation?: {
    path: string;
    module: string;
    breadcrumb: string[];
  };
  navigation?: {
    mainMenu: Record<string, unknown>[];
    moduleMenus: Record<string, Record<string, unknown>[]>;
  };
}

interface CommandResponse {
  action: string;
  parameters?: Record<string, unknown>;
  message: string;
}

export const useAiStore = defineStore("ai", {
  state: () => ({
    isProcessing: false,
    error: null as string | null,
    abortController: null as AbortController | null,
  }),

  actions: {
    cancelProcessing() {
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      this.isProcessing = false;
    },

    async processCommand(
      command: string,
      context: CommandContext
    ): Promise<CommandResponse> {
      // Cancel any ongoing processing
      if (this.isProcessing) {
        this.cancelProcessing();
      }

      // Create a new abort controller for this request
      this.abortController = new AbortController();

      this.isProcessing = true;
      this.error = null;

      try {
        console.log("AI Store: Processing command:", command);
        console.log("AI Store: Context:", context);

        // Get the user's preferred language from i18n
        const { locale } = useI18n();
        const userLanguage = locale.value || "en";
        console.log(`AI Store: User language for command: ${userLanguage}`);

        // Try to make the API call
        try {
          const api = useApi();
          const response = await api.post("/ai/process-command", {
            command,
            context,
            language: userLanguage, // Send the language preference to the API
          });
          return response as CommandResponse;
        } catch (apiError) {
          console.warn(
            "AI Store: API call failed, using mock response:",
            apiError
          );

          // Mock response for testing
          // Create a mock response in the appropriate language
          if (userLanguage === "et") {
            return {
              action: "explain",
              message: `Ma sain aru teie käsust: "${command}". Kuid ma olen praegu võrguühenduseta režiimis. Võrguühendusega režiimis aitaksin teil rakenduses navigeerida või teostada teie soovitud toiminguid.`,
              parameters: {},
            };
          } else {
            return {
              action: "explain",
              message: `I understood your command: "${command}". However, I'm currently in offline mode. In online mode, I would help you navigate the application or perform actions based on your request.`,
              parameters: {},
            };
          }
        }
      } catch (error: unknown) {
        const err = error as Error;
        this.error = err.message || "Failed to process command";
        console.error("AI Store: Error processing command:", err);

        // Return a fallback response in the appropriate language
        const { locale } = useI18n();
        const userLanguage = locale.value || "en";

        if (userLanguage === "et") {
          return {
            action: "explain",
            message:
              "Teie päringu töötlemisel tekkis viga. Palun proovige uuesti.",
            parameters: {},
          };
        } else {
          return {
            action: "explain",
            message:
              "I encountered an error processing your request. Please try again.",
            parameters: {},
          };
        }
      } finally {
        this.isProcessing = false;
      }
    },
  },
});
