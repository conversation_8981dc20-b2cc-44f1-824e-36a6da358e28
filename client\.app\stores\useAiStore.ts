// client/.app/stores/useAiStore.ts

import { defineStore } from "pinia";
import { useApi } from "../app/composables/useApi";

interface CommandContext {
  currentPath: string;
  currentModule: string;
  availableIntents: string[];
  currentLocation?: {
    path: string;
    module: string;
    breadcrumb: string[];
  };
  navigation?: {
    mainMenu: Record<string, unknown>[];
    moduleMenus: Record<string, Record<string, unknown>[]>;
  };
}

interface CommandResponse {
  action: string;
  parameters?: Record<string, unknown>;
  message: string;
}

export const useAiStore = defineStore("ai", {
  state: () => ({
    isProcessing: false,
    error: null as string | null,
    abortController: null as AbortController | null,
  }),

  actions: {
    async processCommand(
      command: string,
      context: CommandContext
    ): Promise<CommandResponse> {
      this.isProcessing = true;
      this.error = null;
      this.abortController = new AbortController();

      try {
        const api = useApi();

        const response = await api.post(
          "/ai/process-command",
          {
            command,
            context,
          },
          {
            signal: this.abortController.signal,
          }
        );

        return {
          action: response.action || "explain",
          message: response.message || "Command processed successfully",
          parameters: response.parameters || {},
        };
      } catch (error: any) {
        if (error.name === "AbortError") {
          throw new Error("Command processing was cancelled");
        }

        console.error("AI Store: Error processing command:", error);
        this.error = error.message || "Failed to process command";

        // Return a fallback response
        return {
          action: "explain",
          message: `I'm sorry, I couldn't process that command. Error: ${error.message}`,
        };
      } finally {
        this.isProcessing = false;
        this.abortController = null;
      }
    },

    async textToSpeech(text: string, language = "en"): Promise<ArrayBuffer> {
      try {
        const api = useApi();

        const response = await api.post("/ai/text-to-speech", {
          text,
          language,
        });

        // Handle different response formats
        if (response instanceof ArrayBuffer) {
          return response;
        }

        if (response && response.data) {
          if (response.data instanceof ArrayBuffer) {
            return response.data;
          }

          // Handle base64 encoded audio
          if (typeof response.data === "string") {
            const binaryString = atob(response.data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes.buffer;
          }
        }

        throw new Error("Invalid response format from TTS service");
      } catch (error) {
        console.error("AI Store: Error in text-to-speech:", error);
        throw error;
      }
    },

    cancelProcessing() {
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      this.isProcessing = false;
    },

    async processCommand(
      command: string,
      context: CommandContext
    ): Promise<CommandResponse> {
      // Cancel any ongoing processing
      if (this.isProcessing) {
        this.cancelProcessing();
      }

      // Create a new abort controller for this request
      this.abortController = new AbortController();

      this.isProcessing = true;
      this.error = null;

      try {
        console.log("AI Store: Processing command:", command);
        console.log("AI Store: Context:", context);

        // Get the user's preferred language
        const userLanguage = localStorage.getItem("preferredLanguage") || "en";
        console.log(`AI Store: User language for command: ${userLanguage}`);

        // Try to make the API call
        try {
          const { post } = useApi();
          const response = await post("/ai/process-command", {
            command,
            context,
            language: userLanguage, // Send the language preference to the API
          });
          return response as CommandResponse;
        } catch (apiError) {
          console.warn(
            "AI Store: API call failed, using mock response:",
            apiError
          );

          // Mock response for testing
          // Create a mock response in the appropriate language
          if (userLanguage === "et") {
            return {
              action: "explain",
              message: `Ma sain aru teie käsust: "${command}". Kuid ma olen praegu võrguühenduseta režiimis. Võrguühendusega režiimis aitaksin teil rakenduses navigeerida või teostada teie soovitud toiminguid.`,
              parameters: {},
            };
          } else {
            return {
              action: "explain",
              message: `I understood your command: "${command}". However, I'm currently in offline mode. In online mode, I would help you navigate the application or perform actions based on your request.`,
              parameters: {},
            };
          }
        }
      } catch (error: unknown) {
        const err = error as Error;
        this.error = err.message || "Failed to process command";
        console.error("AI Store: Error processing command:", err);

        // Return a fallback response in the appropriate language
        const userLanguage = localStorage.getItem("preferredLanguage") || "en";

        if (userLanguage === "et") {
          return {
            action: "explain",
            message:
              "Teie päringu töötlemisel tekkis viga. Palun proovige uuesti.",
            parameters: {},
          };
        } else {
          return {
            action: "explain",
            message:
              "I encountered an error processing your request. Please try again.",
            parameters: {},
          };
        }
      } finally {
        this.isProcessing = false;
      }
    },

    async textToSpeech(text: string, language?: string): Promise<ArrayBuffer> {
      try {
        console.log("AI Store: Converting text to speech:", text);

        // Get user's preferred language if not specified
        if (!language) {
          language = localStorage.getItem("preferredLanguage") || "en";
        }

        console.log(`AI Store: Using language: ${language} for text-to-speech`);

        // Try to make the API call
        try {
          const { post } = useApi();
          const response = await post("/ai/text-to-speech", {
            text,
            language, // Pass language to the API
          });
          return response as ArrayBuffer;
        } catch (apiError) {
          console.warn(
            "AI Store: Text-to-speech API call failed, using mock response:",
            apiError
          );

          // Create a simple beep sound as a mock response
          // This is just a placeholder - in a real implementation, you would use a proper TTS fallback
          const audioContext = new AudioContext();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.type = "sine";
          oscillator.frequency.value = 440; // A4 note
          gainNode.gain.value = 0.1; // Low volume

          oscillator.start();
          oscillator.stop(audioContext.currentTime + 0.2); // Short beep

          // Create a mock audio buffer
          const sampleRate = 44100;
          const buffer = audioContext.createBuffer(
            1,
            sampleRate * 0.5,
            sampleRate
          );
          const data = buffer.getChannelData(0);

          // Fill with a simple sine wave
          for (let i = 0; i < buffer.length; i++) {
            data[i] = Math.sin(i * 0.01) * 0.1;
          }

          // Convert AudioBuffer to ArrayBuffer
          const wavBuffer = new ArrayBuffer(buffer.length * 2);
          const view = new DataView(wavBuffer);
          for (let i = 0; i < buffer.length; i++) {
            const s = Math.max(-1, Math.min(1, data[i] || 0));
            view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7fff, true);
          }

          return wavBuffer;
        }
      } catch (error: unknown) {
        const err = error as Error;
        this.error = err.message || "Failed to convert text to speech";
        console.error("AI Store: Error in text-to-speech:", err);
        throw error;
      }
    },
  },
});
