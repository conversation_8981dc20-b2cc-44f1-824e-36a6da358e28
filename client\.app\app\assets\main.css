@import "tailwindcss" theme(static);
@import "@shuriken-ui/nuxt";
@import "#layers/@cssninja/tairo/theme.css";
@plugin "@tailwindcss/typography";

@source '../../examples';

@theme {
  --font-sans: "Inter", sans-serif;
  --default-mono-font-family: "Fira Code";
  --font-mono: var(--default-mono-font-family), ui-monospace, SFMono-Regular,
    Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --font-alt: "Karla", sans-serif;
  --font-heading: var(--font-sans);

  --animate-spin-slow: spin 3s linear infinite;
  --animate-spin-fast: spin 0.65s linear infinite;

  --color-chart-base: var(--color-primary-600);
  --color-chart-gradient: var(--color-white);
  --color-chart-title: var(--color-muted-600);
  --color-chart-subtitle: var(--color-muted-900);

  /* Custom background colors */
  --color-page-background-light: var(--color-muted-100);
  --color-page-background-dark: var(--color-black);

  /* Custom card colors */
  --color-card-background-light: var(--color-white);
  --color-card-background-dark: var(--color-muted-900);

  /* Override card background colors directly */
  --color-card-default-bg: var(--color-white);
  --color-card-muted-bg: var(--color-muted-50);

  --color-primary-50: var(--color-cyan-50);
  --color-primary-100: var(--color-cyan-100);
  --color-primary-200: var(--color-cyan-200);
  --color-primary-300: var(--color-cyan-300);
  --color-primary-400: var(--color-cyan-400);
  --color-primary-500: var(--color-cyan-500);
  --color-primary-600: var(--color-cyan-600);
  --color-primary-700: var(--color-cyan-700);
  --color-primary-800: var(--color-cyan-800);
  --color-primary-900: var(--color-cyan-900);

  --color-muted-50: var(--color-neutral-50);
  --color-muted-100: var(--color-neutral-100);
  --color-muted-200: var(--color-neutral-200);
  --color-muted-300: var(--color-neutral-300);
  --color-muted-400: var(--color-neutral-400);
  --color-muted-500: var(--color-neutral-500);
  --color-muted-600: var(--color-neutral-600);
  --color-muted-700: var(--color-neutral-700);
  --color-muted-800: var(--color-neutral-800);
  --color-muted-900: var(--color-neutral-900);

  /* Tag default variables */
  --color-tag-default-bg: var(--color-muted-400);
  --color-tag-default-border: var(--color-muted-700);
  --color-tag-default-text: var(--color-muted-700);

  /* Tag muted variables */
  --color-tag-muted-bg: var(--color-muted-400);
  --color-tag-muted-border: var(--color-muted-400);
  --color-tag-muted-text: var(--color-muted-400);

  /* Tag dark variables */
  --color-tag-dark-bg: var(--color-neutral-700);
  --color-tag-dark-border: var(--color-muted-900);
  --color-tag-dark-text: var(--color-muted-900);
}

@layer base {
  [lang^="ar"] {
    --font-sans: "Noto Naskh Arabic", sans-serif;
    --font-serif: "Noto Naskh Arabic", serif;

    /* Uncomment to improve Arabic font rendering */
    /*
    --text-xs: 1.25rem;
    --text-sm: 1.375rem;
    --text-base: 1.5rem;
    --text-lg: 1.625rem;
    --text-xl: 1.75rem;
    --text-2xl: 2rem;
    --text-3xl: 2.375rem;
    --text-4xl: 2.75rem;
    --text-5xl: 3.5rem;
    --text-6xl: 4.25rem;
    --text-7xl: 5rem;
    --text-8xl: 6.5rem;
    --text-9xl: 8.5rem;
    */
  }
  [lang^="ja"] {
    --font-sans: "Noto Sans JP", sans-serif;
    --font-serif: "Noto Sans JP", serif;

    /* Uncomment to improve Japanese font rendering */
    /*
    --text-xs: 1.25rem;
    --text-sm: 1.375rem;
    --text-base: 1.5rem;
    --text-lg: 1.625rem;
    --text-xl: 1.75rem;
    --text-2xl: 2rem;
    --text-3xl: 2.375rem;
    --text-4xl: 2.75rem;
    --text-5xl: 3.5rem;
    --text-6xl: 4.25rem;
    --text-7xl: 5rem;
    --text-8xl: 6.5rem;
    --text-9xl: 8.5rem;
    */
  }

  .dark {
    --color-chart-gradient: var(--color-muted-950);
    --color-chart-title: var(--color-muted-400);
    --color-chart-subtitle: var(--color-white);

    /* Apply custom background colors in dark mode */
    --color-bg-base: var(--color-page-background-dark);
    --color-card-default-bg: var(--color-card-background-dark);
  }

  /* Apply custom background colors in light mode */
  :root {
    --color-bg-base: var(--color-page-background-light);
    --color-card-default-bg: var(--color-card-background-light);
  }
}
