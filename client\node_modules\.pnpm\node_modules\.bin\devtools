#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6_4e61256f8ebebad5cf76052dbb58c995/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nuxt/devtools/cli.mjs" "$@"
else
  exec node  "$basedir/../@nuxt/devtools/cli.mjs" "$@"
fi
