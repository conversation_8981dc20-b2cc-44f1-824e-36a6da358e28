"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const core_controller_js_1 = require("../controllers/core/core.controller.js");
const health_controller_js_1 = require("../controllers/core/health.controller.js");
const authorizeRoles_js_1 = require("../middleware/authorizeRoles.js");
const accessRights_controller_js_1 = require("../controllers/core/accessRights.controller.js");
const router = express_1.default.Router();
// Business Rules routes
router.get("/business-rules", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.getBusinessRules));
router.post("/business-rules", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.saveBusinessRule));
router.put("/business-rules/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.saveBusinessRule));
// System Integration/Modules routes
router.get("/modules", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.getModuleStatus));
router.put("/modules/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.updateModuleStatus));
// AI Decisions routes
router.get("/ai-decisions", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(core_controller_js_1.getRecentDecisions));
// Health monitoring routes
router.get("/health", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(health_controller_js_1.getSystemHealth));
router.get("/health/history", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(health_controller_js_1.getHealthHistory));
// Access rights endpoints
router.get("/access-rights", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.getAllAccessRights));
router.get("/access-rights/role/:role", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.getAccessRightsByRole));
router.post("/access-rights", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.updateAccessRight));
router.post("/access-rights/bulk", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.bulkUpdateAccessRights));
router.get("/access-rights/me", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.getCurrentUserAccessRights));
router.post("/access-rights/initialize", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(accessRights_controller_js_1.initializeAccessRights));
exports.default = router;
//# sourceMappingURL=core.js.map