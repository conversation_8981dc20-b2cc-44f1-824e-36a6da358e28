#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules/nypm/dist/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules/nypm/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules/nypm/dist/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules/nypm/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nypm@0.6.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nypm/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../nypm/dist/cli.mjs" "$@"
fi
