import { Request, Response, NextFunction } from "express";
import { prisma } from "../../lib/prisma.js";

// Create a simple error function instead of using http-errors
function createError(statusCode: number, message: string) {
  const error: any = new Error(message);
  error.statusCode = statusCode;
  return error;
}

// Using centralized prisma instance from lib/prisma.js

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    roles: string[];
    email: string;
    companyId: string;
  };
}

interface CompanyPayload {
  name: string;
  type:
    | "CLIENT"
    | "SUPPLIER"
    | "PARTNER_AGENCY"
    | { label: string; value: string }
    | any;
  status: "ACTIVE" | "INACTIVE" | "SUSPENDED";
  industry?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  address2?: string;
  city?: string;
  postalCode?: string;
  state?: string;
  country?: string;
  notes?: string;
  registrationNumber?: string;
  vatNumber?: string;
  contractTerms?: Record<string, any>;
}

export const getAllCompanies = async (
  _req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const companies = await prisma.company.findMany({
      include: {
        contacts: true,
        workforceNeeds: true,
      },
    });
    res.json(companies);
  } catch (error) {
    next(createError(500, "Failed to fetch companies"));
  }
};

export const getCompanyById = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const company = await prisma.company.findUnique({
      where: { id: Number(id) },
      include: {
        contacts: true,
        workforceNeeds: true,
      },
    });

    if (!company) {
      return next(createError(404, "Company not found"));
    }

    res.json(company);
  } catch (error) {
    next(createError(500, "Failed to fetch company"));
  }
};

export const createCompany = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const payload = req.body as CompanyPayload;

    const company = await prisma.company.create({
      data: {
        ...payload,
        contractTerms: payload.contractTerms
          ? payload.contractTerms
          : undefined,
        // createdBy field might not exist in the schema
        // createdBy: req.user?.id,
      },
      include: {
        contacts: true,
        workforceNeeds: true,
      },
    });

    res.status(201).json(company);
  } catch (error) {
    next(createError(500, "Failed to create company"));
  }
};

export const updateCompany = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;
    const payload = req.body as Partial<CompanyPayload>;

    // Get existing contractTerms or initialize empty object
    let contractTerms = payload.contractTerms || {};

    // Handle legal entity type as part of contractTerms
    // Check if type is an object with value property (from frontend)
    if (
      payload.type &&
      typeof payload.type === "object" &&
      payload.type.value
    ) {
      contractTerms.legalEntityType = payload.type.value;
      // Remove type from payload to avoid conflict with CompanyType enum
      delete payload.type;
    }
    // Check if type is a string (direct API call)
    else if (
      payload.type &&
      typeof payload.type === "string" &&
      [
        "LLC",
        "CORPORATION",
        "PARTNERSHIP",
        "SOLE_PROPRIETORSHIP",
        "NON_PROFIT",
      ].includes(payload.type)
    ) {
      contractTerms.legalEntityType = payload.type;
      // Remove type from payload to avoid conflict with CompanyType enum
      delete payload.type;
    }

    // Convert string numbers to integers for database
    const processedPayload = {
      ...payload,
      foundedYear: payload.foundedYear
        ? Number(payload.foundedYear)
        : undefined,
      employeeCount: payload.employeeCount
        ? Number(payload.employeeCount)
        : undefined,
    };

    const company = await prisma.company.update({
      where: { id: Number(id) },
      data: {
        ...processedPayload,
        contractTerms:
          Object.keys(contractTerms).length > 0 ? contractTerms : undefined,
        updatedAt: new Date(),
      },
      include: {
        contacts: true,
        workforceNeeds: true,
      },
    });

    res.json(company);
  } catch (error: any) {
    console.error("Error updating company:", error);
    if (error.code === "P2025") {
      return next(createError(404, "Company not found"));
    }
    next(
      createError(
        500,
        `Failed to update company: ${error.message || "Unknown error"}`
      )
    );
  }
};

export const deleteCompany = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;

    await prisma.company.delete({
      where: { id: Number(id) },
    });

    res.status(204).send();
  } catch (error: any) {
    console.error("Error deleting company:", error);
    if (error.code === "P2025") {
      return next(createError(404, "Company not found"));
    }
    next(
      createError(
        500,
        `Failed to delete company: ${error.message || "Unknown error"}`
      )
    );
  }
};
