import type { PageCollectionItemBase, DataCollectionItemBase } from '@nuxt/content'

declare module '@nuxt/content' {
  interface DocsCollectionItem extends PageCollectionItemBase {
    components: string[];
    toc: boolean;
    icon: {
      src: string;
      srcDark?: string | undefined;
    };
  }

  interface PageCollections {
    docs: DocsCollectionItem
  }

  interface Collections {
    docs: DocsCollectionItem
  }
}
