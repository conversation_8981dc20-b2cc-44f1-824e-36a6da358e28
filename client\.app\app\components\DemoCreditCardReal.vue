<!-- client/.app/app/components/DemoCreditCardReal.vue -->

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    name?: string | null;
    number?: number | string;
    expiryYear?: number | string;
    expiryMonth?: number | string;
    cvc?: number | string;
    centered?: boolean;
    contrast?: "low" | "high";
    cardBrand?: string | null;
    paymentMethod?: any;
  }>(),
  {
    name: "•••••• ••••••",
    number: "•••• •••• •••• ••••",
    expiryYear: "••",
    expiryMonth: "••",
    cvc: "•••",
    centered: true,
    contrast: "low",
    cardBrand: null,
    paymentMethod: null,
  }
);

// Get card brand from Stripe payment method or fallback
const getCardBrand = () => {
  if (props.paymentMethod?.card?.brand) {
    return props.paymentMethod.card.brand;
  }
  if (props.cardBrand) {
    return props.cardBrand;
  }
  return null;
};

// Get card brand display name
const getCardBrandName = () => {
  const brand = getCardBrand();
  if (!brand) return "••••••••";

  const brandNames = {
    visa: "Visa",
    mastercard: "Mastercard",
    amex: "American Express",
    discover: "Discover",
    diners: "Diners Club",
    jcb: "JCB",
    unionpay: "UnionPay",
  };

  return brandNames[brand] || brand.charAt(0).toUpperCase() + brand.slice(1);
};

// Get card brand colors for the logo circles
const getCardBrandColors = () => {
  const brand = getCardBrand();

  const brandColors = {
    visa: { primary: "bg-blue-500/60", secondary: "bg-yellow-500/60" },
    mastercard: { primary: "bg-rose-500/60", secondary: "bg-yellow-500/60" },
    amex: { primary: "bg-blue-600/60", secondary: "bg-green-500/60" },
    discover: { primary: "bg-orange-500/60", secondary: "bg-gray-500/60" },
    diners: { primary: "bg-blue-700/60", secondary: "bg-silver-500/60" },
    jcb: { primary: "bg-red-500/60", secondary: "bg-blue-500/60" },
    unionpay: { primary: "bg-red-600/60", secondary: "bg-blue-600/60" },
  };

  return (
    brandColors[brand] || {
      primary: "bg-rose-500/60",
      secondary: "bg-yellow-500/60",
    }
  );
};

const cardColors = getCardBrandColors();
</script>

<template>
  <div
    class="border-muted-200 dark:border-muted-800 shadow-muted-400/10 dark:shadow-muted-800/10 relative h-[200px] w-full max-w-[315px] rounded-xl border p-6 shadow-xl"
    :class="[
      props.centered ? 'mx-auto' : '',
      props.contrast === 'high' &&
        'dark:bg-gradient-to-br dark:from-purple-900/20 dark:via-muted-950 dark:to-purple-800/10',
      props.contrast === 'low' &&
        'dark:bg-gradient-to-br dark:from-purple-900/10 dark:via-muted-900 dark:to-purple-800/5',
    ]"
    style="
      background: linear-gradient(
        135deg,
        rgba(250, 245, 255, 0.3) 0%,
        rgba(243, 232, 255, 0.3) 25%,
        rgba(233, 213, 255, 0.3) 50%,
        rgba(221, 214, 254, 0.3) 75%,
        rgba(196, 181, 253, 0.3) 100%
      );
    "
  >
    <!-- Card content -->
    <div class="flex h-full flex-col gap-3">
      <div class="flex items-center gap-2">
        <div class="bg-muted-200 dark:bg-muted-700 size-2 rounded-full" />
        <span class="text-muted-400 font-sans text-sm">{{
          getCardBrandName()
        }}</span>
      </div>
      <div class="mt-auto space-y-1">
        <img
          class="mb-3 w-11"
          src="/img/illustrations/card-chip.svg"
          alt="Card chip"
          width="44"
          height="31"
        />
        <div>
          <h5 class="font-heading text-muted-500 text-sm" x-text="cardholder">
            {{ props.name }}
          </h5>
        </div>
        <div>
          <p class="font-heading text-muted-400 text-xs">
            <span>{{ props.number }}</span>
          </p>
        </div>
        <div
          class="font-heading text-muted-400 flex w-full items-center gap-2 text-xs"
        >
          <div class="flex items-center gap-2">
            <span>{{ $t("credit_card.exp") }}</span>
            <span>{{ props.expiryMonth }}/{{ props.expiryYear }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span>{{ $t("credit_card.cvc") }}</span>
            <span>{{ props.cvc }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Card Brand Logo -->
    <div class="absolute end-5 top-4 flex">
      <div class="-me-2 size-9 rounded-full" :class="cardColors.primary" />
      <div
        class="relative z-10 -ms-2 size-9 rounded-full"
        :class="cardColors.secondary"
      />
    </div>

    <!-- Logo -->
    <div class="absolute bottom-7 end-5 flex">
      <Logo class="text-primary-500 size-10" />
    </div>
  </div>
</template>
