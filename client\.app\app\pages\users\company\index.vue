<!-- client/.app/app/pages/users/company/index.vue -->

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header with Cover Image and Logo -->
    <div class="relative mb-16 mt-8">
      <!-- Cover Image -->
      <div
        class="h-40 md:h-60 w-full rounded-lg bg-muted-200 dark:bg-muted-800 overflow-hidden relative z-0"
      >
        <img
          v-if="company?.coverImage"
          :src="company.coverImage"
          alt="Company Cover"
          class="w-full h-full object-cover z-50"
        />

        <div
          v-else
          class="w-full h-full flex items-center justify-center bg-gradient-to-r from-primary-500 to-primary-700"
        >
          <span class="text-white text-lg font-medium">{{
            company?.name
          }}</span>
        </div>

        <div class="absolute top-1/2 transform -translate-y-1/2 left-20 z-10">
          <div class="relative">
            <BaseAvatar
              v-if="company?.logo"
              :src="company.logo"
              size="3xl"
              class="relative z-20 ring-2 ring-cyan-200 dark:ring-cyan-700 bg-white dark:bg-black"
              style="object-fit: cover"
              ring
            />
            <BaseAvatar
              v-else
              :initials="companyInitials"
              size="3xl"
              class="relative z-20 shadow-lg ring-2 ring-cyan-200 dark:ring-cyan-700"
            />

            <div class="absolute -bottom-1 -right-1 z-30">
              <BaseButton
                variant="primary"
                color="primary"
                rounded="full"
                size="sm"
                class="h-8 w-8 !p-0"
                @click="openLogoUpload"
              >
                <Icon name="solar:camera-linear" class="h-4 w-4" />
              </BaseButton>
              <input
                ref="logoInput"
                type="file"
                accept="image/*"
                class="hidden"
                @change="handleLogoUpload"
              />
            </div>
          </div>
        </div>

        <div class="absolute bottom-4 right-4">
          <BaseButton
            variant="primary"
            color="primary"
            size="sm"
            @click="openCoverImageUpload"
          >
            <Icon name="solar:camera-linear" class="h-4 w-4 mr-1" />
            {{ $t("company.change_cover") }}
          </BaseButton>
          <input
            ref="coverImageInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleCoverImageUpload"
          />
        </div>
      </div>
    </div>

    <!-- Company Content -->
    <div class="mt-6 grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-3">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center gap-2">
            <h4
              class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
            >
              {{ $t("company.company_navigation") }}
            </h4>
          </div>
          <ul class="space-y-1 font-sans text-sm">
            <li>
              <button
                @click="activeTab = 'details'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'details'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:buildings-linear" class="size-5" />
                <span>{{ $t("company.company_details") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'team'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'team'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:users-group-rounded-linear" class="size-5" />
                <span>{{ $t("company.team_members") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'documents'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'documents'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:document-text-linear" class="size-5" />
                <span>{{ $t("company.company_documents") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'settings'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'settings'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:settings-linear" class="size-5" />
                <span>{{ $t("company.company_settings_tab") }}</span>
              </button>
            </li>
          </ul>
        </BaseCard>
      </div>
      <div class="col-span-12 lg:col-span-9">
        <!-- Company Details Tab -->
        <div v-show="activeTab === 'details'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("company.company_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("company.company_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("company.about_company") }}
                </BaseHeading>
                <div
                  class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
                >
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.company_name") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ company?.name }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.website") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.website || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.email") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.email || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.phone") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.phone || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.registration_number") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.registrationNumber ||
                            $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.vat_number") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.vatNumber || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.industry") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.industry || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>

                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.description") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.description || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.founded_year") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.foundedYear || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <div
                      class="font-heading text-muted-600 dark:text-muted-400 flex items-center gap-4 p-4 text-sm"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.employee_count") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.employeeCount || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                    </div>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <div
                      class="font-heading text-muted-600 dark:text-muted-400 flex items-center gap-4 p-4 text-sm"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.annual_revenue") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.annualRevenue
                              ? `€${company.annualRevenue.toLocaleString()}`
                              : $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                    </div>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <div
                      class="font-heading text-muted-600 dark:text-muted-400 flex items-center gap-4 p-4 text-sm"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.account_created") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ formatDisplayDate(company?.createdAt) }}</BaseText
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("company.address_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("company.address_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <BaseHeading
                    as="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    {{ $t("company.current_address") }}
                  </BaseHeading>
                  <div
                    v-if="hasAddress"
                    class="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <div v-if="company?.address">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.address_line_1") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.address }}
                      </BaseText>
                    </div>
                    <div v-if="company?.address2">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.address_line_2") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.address2 }}
                      </BaseText>
                    </div>
                    <div v-if="company?.city">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.city") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.city }}
                      </BaseText>
                    </div>
                    <div v-if="company?.postalCode">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.postal_code") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.postalCode }}
                      </BaseText>
                    </div>
                    <div v-if="company?.state">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.state_province") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.state }}
                      </BaseText>
                    </div>
                    <div v-if="company?.country">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.country") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.country }}
                      </BaseText>
                    </div>
                  </div>
                  <div v-else class="text-center py-8">
                    <Icon
                      name="solar:map-point-linear"
                      class="h-12 w-12 mx-auto text-muted-400 mb-4"
                    />
                    <BaseHeading
                      as="h3"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100 mb-2"
                    >
                      {{ $t("company.no_address") }}
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-500 dark:text-muted-400 mb-4"
                    >
                      {{ $t("company.no_address_desc") }}
                    </BaseParagraph>
                    <BaseButton
                      variant="primary"
                      color="primary"
                      size="sm"
                      @click="$router.push('/users/company/edit')"
                    >
                      <Icon name="solar:pen-2-linear" class="h-4 w-4 mr-1" />
                      {{ $t("company.add_address") }}
                    </BaseButton>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Identification Codes Section -->
        <div v-show="activeTab === 'details'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="mb-6">
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-1"
              >
                {{ $t("company.identification_codes") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400"
              >
                {{ $t("company.identification_codes_desc") }}
              </BaseParagraph>
            </div>

            <div v-if="company?.barcode || company?.qrCode" class="space-y-6">
              <div class="flex items-center justify-between">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ $t("company.scannable_codes") }}
                </BaseHeading>
                <BaseButton
                  v-if="company?.barcode || company?.qrCode"
                  variant="muted"
                  size="sm"
                  @click="printCodes"
                >
                  <Icon name="solar:printer-linear" class="size-4 mr-1" />
                  {{ $t("company.print_codes") }}
                </BaseButton>
              </div>

              <div
                v-if="company?.barcode || company?.qrCode"
                class="grid grid-cols-1 md:grid-cols-2 gap-6"
              >
                <div v-if="company?.barcode" class="flex flex-col">
                  <BaseHeading
                    as="h4"
                    size="xs"
                    weight="medium"
                    class="text-muted-400 mb-2"
                  >
                    {{ $t("company.barcode") }}
                  </BaseHeading>
                  <div
                    class="flex-1 flex flex-col justify-center items-center p-6 bg-white rounded-lg border border-muted-200 dark:border-muted-700 min-h-[200px]"
                  >
                    <!-- Generate barcode using JsBarcode -->
                    <div
                      class="flex-1 flex items-center justify-center w-full overflow-hidden"
                    >
                      <canvas
                        :id="`company-barcode-${company.id}`"
                        class="max-w-full h-auto"
                      ></canvas>
                    </div>
                    <BaseText
                      size="xs"
                      class="text-muted-600 font-mono mt-2 break-all"
                    >
                      {{ company.barcode }}
                    </BaseText>
                  </div>
                </div>
                <div v-if="company?.qrCode" class="flex flex-col">
                  <BaseHeading
                    as="h4"
                    size="xs"
                    weight="medium"
                    class="text-muted-400 mb-2"
                  >
                    {{ $t("company.qr_code") }}
                  </BaseHeading>
                  <div
                    class="flex-1 flex flex-col justify-center items-center p-6 bg-white rounded-lg border border-muted-200 dark:border-muted-700 min-h-[200px]"
                  >
                    <div class="flex-1 flex items-center justify-center">
                      <img
                        :src="company.qrCode"
                        alt="QR Code"
                        class="w-32 h-32 object-contain"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- No Codes State -->
            <div v-else class="text-center py-8">
              <div
                class="w-20 h-20 mx-auto mb-4 bg-muted-100 dark:bg-muted-800 rounded-2xl flex items-center justify-center"
              >
                <Icon
                  name="solar:qr-code-linear"
                  class="size-10 text-muted-400"
                />
              </div>
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.no_identification_codes") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400"
              >
                {{ $t("company.no_codes_desc") }}
              </BaseParagraph>
            </div>
          </BaseCard>
        </div>

        <!-- Team Members Tab -->
        <div v-show="activeTab === 'team'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="mb-6">
              <div
                class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
              >
                <div>
                  <BaseHeading
                    as="h3"
                    size="lg"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("company.team_members") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="sm"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("company.team_members_desc") }}
                  </BaseParagraph>
                </div>
                <div class="flex items-center gap-3">
                  <TairoInput
                    v-model="searchQuery"
                    icon="lucide:search"
                    :placeholder="$t('company.search_members')"
                    class="w-full sm:w-64"
                  />
                  <BaseButton
                    v-if="canManageTeam"
                    variant="primary"
                    size="sm"
                    @click="openInviteModal"
                  >
                    <Icon name="lucide:plus" class="size-4" />
                    <span>{{ $t("company.invite_member") }}</span>
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- Loading State -->
            <div v-if="isLoadingTeam" class="flex justify-center py-12">
              <BaseSpinner size="lg" />
            </div>

            <!-- Error State -->
            <div v-else-if="teamError" class="text-center py-12">
              <Icon
                name="lucide:alert-circle"
                class="h-16 w-16 mx-auto text-red-400 mb-4"
              />
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                Error Loading Team Members
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ teamError }}
              </BaseParagraph>
              <BaseButton variant="muted" @click="fetchTeamMembers">
                Try Again
              </BaseButton>
            </div>

            <!-- Empty State -->
            <div
              v-else-if="filteredTeamMembers.length === 0 && !searchQuery"
              class="text-center py-12"
            >
              <Icon
                name="solar:users-group-rounded-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.no_team_members") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.no_team_members_desc") }}
              </BaseParagraph>
              <BaseButton
                v-if="canManageTeam"
                variant="primary"
                @click="openInviteModal"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                {{ $t("company.invite_member") }}
              </BaseButton>
            </div>

            <!-- No Search Results -->
            <div
              v-else-if="filteredTeamMembers.length === 0 && searchQuery"
              class="text-center py-12"
            >
              <Icon
                name="lucide:search"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                No matching results
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400"
              >
                No team members found matching "{{ searchQuery }}"
              </BaseParagraph>
            </div>

            <!-- Team Members List -->
            <div v-else class="space-y-3">
              <TransitionGroup
                enter-active-class="transform-gpu"
                enter-from-class="opacity-0 -translate-x-full"
                enter-to-class="opacity-100 translate-x-0"
                leave-active-class="absolute transform-gpu"
                leave-from-class="opacity-100 translate-x-0"
                leave-to-class="opacity-0 -translate-x-full"
              >
                <BaseCard
                  v-for="member in filteredTeamMembers"
                  :key="member.id"
                  rounded="lg"
                  class="flex flex-col p-4 sm:flex-row sm:items-center"
                >
                  <div
                    class="flex flex-col items-center justify-center gap-3 text-center sm:flex-row sm:justify-start sm:text-start"
                  >
                    <BaseAvatar
                      size="sm"
                      :src="
                        member.user?.avatar || '/img/avatars/placeholder.svg'
                      "
                      :text="
                        getInitials(
                          member.user?.firstName,
                          member.user?.lastName
                        )
                      "
                    />
                    <div>
                      <BaseHeading
                        tag="h3"
                        size="sm"
                        weight="medium"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ member.user?.firstName || "Unknown" }}
                        {{ member.user?.lastName || "" }}
                      </BaseHeading>
                      <BaseParagraph
                        size="xs"
                        lead="none"
                        class="text-muted-600 dark:text-muted-400 flex items-end text-sm"
                      >
                        <span>{{ member.user?.email || "No email" }}</span>
                      </BaseParagraph>
                    </div>
                  </div>
                  <div
                    class="flex flex-col gap-4 pt-4 sm:ms-auto sm:flex-row sm:items-center sm:justify-end sm:pt-0"
                  >
                    <div
                      class="flex w-full items-center justify-center sm:w-[120px] sm:justify-end"
                    >
                      <BaseTag
                        size="sm"
                        :variant="getRoleVariant(member.role)"
                        rounded="full"
                      >
                        {{ formatRole(member.role) }}
                      </BaseTag>
                    </div>
                    <div
                      class="flex w-full items-center justify-center sm:w-[100px] sm:justify-end"
                    >
                      <BaseTag
                        size="sm"
                        :variant="getStatusVariant(member.status)"
                        rounded="full"
                      >
                        {{ member.status || "Active" }}
                      </BaseTag>
                    </div>
                    <div
                      class="flex w-full items-center justify-center sm:w-[120px] sm:justify-end"
                    >
                      <BaseParagraph
                        size="xs"
                        class="text-muted-600 dark:text-muted-400"
                      >
                        {{ formatDate(member.createdAt) }}
                      </BaseParagraph>
                    </div>
                    <div class="flex items-center gap-2 sm:ms-6">
                      <BaseButton
                        v-if="canEditMember(member)"
                        size="sm"
                        variant="muted"
                        @click="openEditRoleModal(member)"
                      >
                        <Icon name="lucide:edit" class="size-4" />
                      </BaseButton>
                      <BaseButton
                        v-if="canRemoveMember(member)"
                        size="sm"
                        variant="danger"
                        @click="confirmRemoveMember(member)"
                      >
                        <Icon name="lucide:trash-2" class="size-4" />
                      </BaseButton>
                    </div>
                  </div>
                </BaseCard>
              </TransitionGroup>
            </div>
          </BaseCard>
        </div>

        <!-- Documents Tab -->
        <div v-show="activeTab === 'documents'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:document-text-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.company_documents") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.documents_desc") }}
              </BaseParagraph>
              <BaseButton
                variant="primary"
                color="primary"
                @click="$router.push('/users/company/edit?tab=documents')"
              >
                <Icon name="solar:document-add-linear" class="h-4 w-4 mr-2" />
                {{ $t("company.manage_documents") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Settings Tab -->
        <div v-show="activeTab === 'settings'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:settings-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.settings") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.settings_desc") }}
              </BaseParagraph>
              <BaseButton
                variant="primary"
                color="primary"
                @click="$router.push('/users/company/edit?tab=settings')"
              >
                <Icon name="solar:settings-linear" class="h-4 w-4 mr-2" />
                {{ $t("company.manage_settings") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useUserStore } from "../../../../stores/useUserStore";
import { apiClient } from "../../../utils/api/client";
import { format } from "date-fns";

// Add JsBarcode library
useHead({
  script: [
    {
      src: "https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js",
      defer: true,
    },
  ],
});

// Declare global JsBarcode
declare global {
  interface Window {
    JsBarcode: any;
  }
}

// Initialize user store and toaster
const userStore = useUserStore();
const toaster = useNuiToasts();
const { t } = useI18n();
const api = useApi();

// State
const activeTab = ref("details");
const logoInput = ref<HTMLInputElement>();
const coverImageInput = ref<HTMLInputElement>();

// Team members state
const teamMembers = ref<TeamMember[]>([]);
const isLoadingTeam = ref(false);
const teamError = ref("");
const searchQuery = ref("");

// Team member interfaces
interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

interface TeamMember {
  id: number;
  userId: number;
  companyId: string;
  role: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: User;
}

// Computed properties
const company = computed(() => userStore.primaryCompany?.company);
const companyInitials = computed(() => {
  if (!company.value?.name) return "CO";
  return company.value.name
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);
});

const hasAddress = computed(() => {
  return !!(
    company.value?.address ||
    company.value?.address2 ||
    company.value?.city ||
    company.value?.postalCode ||
    company.value?.state ||
    company.value?.country
  );
});

// Methods
const formatDisplayDate = (dateString: string | null | undefined) => {
  if (!dateString) return "Not provided";
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    return "Invalid date";
  }
};

const openLogoUpload = () => {
  logoInput.value?.click();
};

const openCoverImageUpload = () => {
  coverImageInput.value?.click();
};

const handleLogoUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file && company.value?.id) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      await apiClient.postFormData(
        `/uploads/company-logo/${company.value.id}`,
        formData
      );

      toaster.add({
        title: t("common.success"),
        description: t("company.company_logo_updated_success"),
        icon: "solar:check-circle-linear",
        progress: true,
      });

      // Refresh user data to get updated logo
      await userStore.fetchUser();
    } catch (error) {
      toaster.add({
        title: t("common.error"),
        description: t("company.company_logo_update_failed"),
        icon: "solar:close-circle-linear",
        progress: true,
      });
    }
  }
};

const handleCoverImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file && company.value?.id) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      await apiClient.postFormData(
        `/uploads/company-cover/${company.value.id}`,
        formData
      );

      toaster.add({
        title: t("common.success"),
        description: t("company.company_cover_updated_success"),
        icon: "solar:check-circle-linear",
        progress: true,
      });

      // Refresh user data to get updated cover image
      await userStore.fetchUser();
    } catch (error) {
      toaster.add({
        title: t("common.error"),
        description: t("company.company_cover_update_failed"),
        icon: "solar:close-circle-linear",
        progress: true,
      });
    }
  }
};

// Team members computed properties
const filteredTeamMembers = computed(() => {
  if (!searchQuery.value) return teamMembers.value;

  const query = searchQuery.value.toLowerCase();
  return teamMembers.value.filter((member: TeamMember) => {
    const fullName = `${member.user?.firstName || ""} ${
      member.user?.lastName || ""
    }`.toLowerCase();
    const email = (member.user?.email || "").toLowerCase();
    const role = (member.role || "").toLowerCase();

    return (
      fullName.includes(query) || email.includes(query) || role.includes(query)
    );
  });
});

const canManageTeam = computed(() => {
  return (
    userStore.user?.roles?.includes("SUPERADMIN") ||
    teamMembers.value.some(
      (member: TeamMember) =>
        member.userId === userStore.user?.id &&
        ["OWNER", "ADMIN"].includes(member.role)
    )
  );
});

// Team members methods
const fetchTeamMembers = async () => {
  if (!company.value?.id) return;

  isLoadingTeam.value = true;
  teamError.value = "";

  try {
    const response = await api.get(`/companies/${company.value.id}/members`);
    teamMembers.value = response as TeamMember[];
    console.log("Team members fetched:", teamMembers.value);
  } catch (err) {
    console.error("Error fetching team members:", err);
    teamError.value = "Failed to load team members. Please try again.";
  } finally {
    isLoadingTeam.value = false;
  }
};

const getInitials = (firstName?: string, lastName?: string): string => {
  if (!firstName && !lastName) return "U";
  return `${firstName?.charAt(0) || ""}${
    lastName?.charAt(0) || ""
  }`.toUpperCase();
};

const formatRole = (role: string): string => {
  switch (role) {
    case "ADMIN":
      return "Admin";
    case "PROJECTLEADER":
      return "Project Leader";
    case "SALESMAN":
      return "Salesman";
    case "WORKER":
      return "Worker";
    case "CLIENT":
      return "Client";
    case "OWNER":
      return "Owner";
    default:
      return role || "Unknown";
  }
};

const getRoleVariant = (role: string): string => {
  switch (role) {
    case "ADMIN":
      return "info";
    case "PROJECTLEADER":
      return "warning";
    case "SALESMAN":
      return "primary";
    case "WORKER":
      return "success";
    case "CLIENT":
      return "muted";
    case "OWNER":
      return "primary";
    default:
      return "muted";
  }
};

const getStatusVariant = (status: string): string => {
  switch (status) {
    case "ACTIVE":
      return "success";
    case "PENDING":
      return "warning";
    case "INACTIVE":
      return "danger";
    default:
      return "muted";
  }
};

const formatDate = (dateString: string): string => {
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch {
    return dateString || "Unknown";
  }
};

const canEditMember = (member: TeamMember): boolean => {
  return (
    canManageTeam.value &&
    member.userId !== userStore.user?.id &&
    member.role !== "OWNER"
  );
};

const canRemoveMember = (member: TeamMember): boolean => {
  return (
    canManageTeam.value &&
    member.userId !== userStore.user?.id &&
    member.role !== "OWNER"
  );
};

// Placeholder functions for team management (to be implemented)
const openInviteModal = () => {
  console.log("Invite modal - to be implemented");
};

const openEditRoleModal = (member: TeamMember) => {
  console.log("Edit role modal - to be implemented", member);
};

const confirmRemoveMember = (member: TeamMember) => {
  console.log("Confirm remove modal - to be implemented", member);
};

// Helper function to print barcode/QR code
const printCodes = async () => {
  const printWindow = window.open("", "_blank");
  if (!printWindow) return;

  // Ensure barcode is generated first
  if (company.value?.barcode && company.value?.id) {
    generateBarcode();
    // Wait a bit for the barcode to render
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  // Get the barcode canvas as data URL
  let barcodeDataUrl = "";
  if (company.value?.barcode && company.value?.id) {
    const canvas = document.getElementById(
      `company-barcode-${company.value.id}`
    ) as HTMLCanvasElement;
    if (canvas) {
      try {
        barcodeDataUrl = canvas.toDataURL("image/png");
        console.log(
          "Barcode canvas captured:",
          barcodeDataUrl.substring(0, 50) + "..."
        );
      } catch (error) {
        console.error("Error capturing barcode canvas:", error);
      }
    } else {
      console.warn("Barcode canvas not found");
    }
  }

  // Debug QR code
  if (company.value?.qrCode) {
    console.log("QR Code URL:", company.value.qrCode.substring(0, 50) + "...");
  }

  const content = `
    <html>
      <head>
        <title>Company Identification Codes</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
          }
          .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
          }
          .header p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
          }
          .code-section {
            margin: 30px 0;
            text-align: center;
            page-break-inside: avoid;
          }
          .code-section h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
          }
          .code-section img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
          }
          .code-section p {
            margin: 10px 0;
            font-size: 12px;
            color: #666;
          }
          @media print {
            body { margin: 0; }
            .code-section { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${company.value?.name || "Company"} - Identification Codes</h1>
          <p><strong>Company:</strong> ${company.value?.name || "N/A"}</p>
          <p><strong>Email:</strong> ${company.value?.email || "N/A"}</p>
          <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
        </div>

        ${
          company.value?.barcode
            ? `
          <div class="code-section">
            <h3>Barcode</h3>
            ${
              barcodeDataUrl
                ? `<img src="${barcodeDataUrl}" alt="Barcode" style="max-height: 100px;" />`
                : `<p style="color: red;">Barcode could not be generated</p>`
            }
            <p>Code: ${company.value.barcode}</p>
          </div>
        `
            : ""
        }

        ${
          company.value?.qrCode
            ? `
          <div class="code-section">
            <h3>QR Code</h3>
            <img src="${company.value.qrCode}" alt="QR Code" style="max-height: 200px;" />
            <p>Scan this QR code for quick access</p>
          </div>
        `
            : ""
        }
      </body>
    </html>
  `;

  printWindow.document.write(content);
  printWindow.document.close();

  // Wait for images to load before printing
  setTimeout(() => {
    printWindow.focus();
    printWindow.print();
  }, 2000);
};

// Generate barcode when company data is available
const generateBarcode = () => {
  if (company.value?.barcode && company.value?.id) {
    nextTick(() => {
      const canvas = document.getElementById(
        `company-barcode-${company.value.id}`
      );
      if (canvas && window.JsBarcode) {
        try {
          // Clear any existing content
          const ctx = (canvas as HTMLCanvasElement).getContext("2d");
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          }

          // Get container width for responsive sizing
          const container = canvas.parentElement;
          const containerWidth = container ? container.clientWidth - 48 : 200; // 48px for padding
          const barcodeWidth = Math.min(containerWidth, 250);

          window.JsBarcode(canvas, company.value.barcode, {
            format: "CODE128",
            width: Math.max(2, Math.floor(barcodeWidth / 80)), // Better width calculation
            height: 60, // Slightly taller for better visibility
            displayValue: false,
            background: "#ffffff", // White background for better contrast
            lineColor: "#000000",
            margin: 5,
          });

          console.log(
            "Barcode generated successfully for:",
            company.value.barcode
          );
        } catch (error) {
          console.error("Error generating barcode:", error);
        }
      } else {
        console.warn("Canvas or JsBarcode not available");
      }
    });
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser();
  }

  // Redirect if user doesn't have a company
  if (!userStore.primaryCompany?.company) {
    toaster.add({
      title: t("common.error"),
      description: t("company.not_associated_with_company"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  }

  // Fetch team members
  await fetchTeamMembers();

  // Add resize listener for responsive barcode
  window.addEventListener("resize", generateBarcode);
});

// Watch for company changes to regenerate barcode
watch(
  company,
  () => {
    if (company.value?.barcode) {
      generateBarcode();
    }
  },
  { immediate: true }
);

// Cleanup on unmount
onUnmounted(() => {
  window.removeEventListener("resize", generateBarcode);
});
</script>
