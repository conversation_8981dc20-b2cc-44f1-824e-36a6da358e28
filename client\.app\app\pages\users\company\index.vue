<!-- client/.app/app/pages/users/company/index.vue -->

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header with Cover Image and Logo -->
    <div class="relative mb-16 mt-8">
      <!-- Cover Image -->
      <div
        class="h-40 md:h-60 w-full rounded-lg bg-muted-200 dark:bg-muted-800 overflow-hidden relative z-0"
      >
        <img
          v-if="company?.coverImage"
          :src="company.coverImage"
          alt="Company Cover"
          class="w-full h-full object-cover z-50"
        />

        <div
          v-else
          class="w-full h-full flex items-center justify-center bg-gradient-to-r from-primary-500 to-primary-700"
        >
          <span class="text-white text-lg font-medium">{{
            company?.name
          }}</span>
        </div>

        <div class="absolute top-1/2 transform -translate-y-1/2 left-20 z-10">
          <div class="relative">
            <BaseAvatar
              v-if="company?.logo"
              :src="company.logo"
              size="3xl"
              class="relative z-20 ring-2 ring-cyan-200 dark:ring-cyan-700"
              style="object-fit: cover"
              ring
            />
            <BaseAvatar
              v-else
              :initials="companyInitials"
              size="3xl"
              class="relative z-20 shadow-lg ring-2 ring-cyan-200 dark:ring-cyan-700"
            />

            <div class="absolute -bottom-1 -right-1 z-30">
              <BaseButton
                variant="primary"
                color="primary"
                rounded="full"
                size="sm"
                class="h-8 w-8 !p-0"
                @click="openLogoUpload"
              >
                <Icon name="solar:camera-linear" class="h-4 w-4" />
              </BaseButton>
              <input
                ref="logoInput"
                type="file"
                accept="image/*"
                class="hidden"
                @change="handleLogoUpload"
              />
            </div>
          </div>
        </div>

        <div class="absolute bottom-4 right-4">
          <BaseButton
            variant="primary"
            color="primary"
            size="sm"
            @click="openCoverImageUpload"
          >
            <Icon name="solar:camera-linear" class="h-4 w-4 mr-1" />
            {{ $t("company.change_cover") }}
          </BaseButton>
          <input
            ref="coverImageInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleCoverImageUpload"
          />
        </div>
      </div>
    </div>

    <!-- Company Content -->
    <div class="mt-6 grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-3">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center gap-2">
            <h4
              class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
            >
              {{ $t("company.company_navigation") }}
            </h4>
          </div>
          <ul class="space-y-1 font-sans text-sm">
            <li>
              <button
                @click="activeTab = 'details'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'details'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:buildings-linear" class="size-5" />
                <span>{{ $t("company.company_details") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'team'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'team'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:users-group-rounded-linear" class="size-5" />
                <span>{{ $t("company.team_members") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'documents'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'documents'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:document-text-linear" class="size-5" />
                <span>{{ $t("company.company_documents") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'settings'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'settings'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:settings-linear" class="size-5" />
                <span>{{ $t("company.company_settings_tab") }}</span>
              </button>
            </li>
          </ul>
        </BaseCard>
      </div>
      <div class="col-span-12 lg:col-span-9">
        <!-- Company Details Tab -->
        <div v-show="activeTab === 'details'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("company.company_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("company.company_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("company.about_company") }}
                </BaseHeading>
                <div
                  class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
                >
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.company_name") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ company?.name }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.registration_number") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.registrationNumber ||
                            $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.vat_number") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.vatNumber || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/company/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.industry") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{
                            company?.industry || $t("company.not_provided")
                          }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("company.edit_company_button") }}
                      </BaseText>
                    </NuxtLink>
                  </div>

                  <!-- Item -->
                  <div class="group">
                    <div
                      class="font-heading text-muted-600 dark:text-muted-400 flex items-center gap-4 p-4 text-sm"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("company.founded") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ formatDisplayDate(company?.createdAt) }}</BaseText
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("company.address_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("company.address_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <BaseHeading
                    as="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    {{ $t("company.current_address") }}
                  </BaseHeading>
                  <div
                    v-if="hasAddress"
                    class="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <div v-if="company?.address">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.address_line_1") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.address }}
                      </BaseText>
                    </div>
                    <div v-if="company?.address2">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.address_line_2") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.address2 }}
                      </BaseText>
                    </div>
                    <div v-if="company?.city">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.city") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.city }}
                      </BaseText>
                    </div>
                    <div v-if="company?.postalCode">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.postal_code") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.postalCode }}
                      </BaseText>
                    </div>
                    <div v-if="company?.state">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.state_province") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.state }}
                      </BaseText>
                    </div>
                    <div v-if="company?.country">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("company.country") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ company.country }}
                      </BaseText>
                    </div>
                  </div>
                  <div v-else class="text-center py-8">
                    <Icon
                      name="solar:map-point-linear"
                      class="h-12 w-12 mx-auto text-muted-400 mb-4"
                    />
                    <BaseHeading
                      as="h3"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100 mb-2"
                    >
                      {{ $t("company.no_address") }}
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-500 dark:text-muted-400 mb-4"
                    >
                      {{ $t("company.no_address_desc") }}
                    </BaseParagraph>
                    <BaseButton
                      variant="primary"
                      color="primary"
                      size="sm"
                      @click="$router.push('/users/company/edit')"
                    >
                      <Icon name="solar:pen-2-linear" class="h-4 w-4 mr-1" />
                      {{ $t("company.add_address") }}
                    </BaseButton>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Team Members Tab -->
        <div v-show="activeTab === 'team'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:users-group-rounded-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.team_members") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.team_members_desc") }}
              </BaseParagraph>
              <BaseButton
                variant="primary"
                color="primary"
                @click="$router.push('/users/company/edit?tab=team')"
              >
                <Icon name="solar:user-plus-linear" class="h-4 w-4 mr-2" />
                {{ $t("company.manage_team") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Documents Tab -->
        <div v-show="activeTab === 'documents'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:document-text-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.company_documents") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.documents_desc") }}
              </BaseParagraph>
              <BaseButton
                variant="primary"
                color="primary"
                @click="$router.push('/users/company/edit?tab=documents')"
              >
                <Icon name="solar:document-add-linear" class="h-4 w-4 mr-2" />
                {{ $t("company.manage_documents") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Settings Tab -->
        <div v-show="activeTab === 'settings'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:settings-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.settings") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.settings_desc") }}
              </BaseParagraph>
              <BaseButton
                variant="primary"
                color="primary"
                @click="$router.push('/users/company/edit?tab=settings')"
              >
                <Icon name="solar:settings-linear" class="h-4 w-4 mr-2" />
                {{ $t("company.manage_settings") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "../../../../stores/useUserStore";
import { apiClient } from "../../../utils/api/client";

// Initialize user store and toaster
const userStore = useUserStore();
const toaster = useNuiToasts();

// State
const activeTab = ref("details");
const logoInput = ref<HTMLInputElement>();
const coverImageInput = ref<HTMLInputElement>();

// Computed properties
const company = computed(() => userStore.primaryCompany?.company);
const companyInitials = computed(() => {
  if (!company.value?.name) return "CO";
  return company.value.name
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);
});

const hasAddress = computed(() => {
  return !!(
    company.value?.address ||
    company.value?.address2 ||
    company.value?.city ||
    company.value?.postalCode ||
    company.value?.state ||
    company.value?.country
  );
});

// Methods
const formatDisplayDate = (dateString: string | null | undefined) => {
  if (!dateString) return "Not provided";
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    return "Invalid date";
  }
};

const openLogoUpload = () => {
  logoInput.value?.click();
};

const openCoverImageUpload = () => {
  coverImageInput.value?.click();
};

const handleLogoUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file && company.value?.id) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      await apiClient.postFormData(
        `/uploads/company-logo/${company.value.id}`,
        formData
      );

      toaster.add({
        title: "Success",
        description: "Company logo updated successfully",
        icon: "solar:check-circle-linear",
        progress: true,
      });

      // Refresh user data to get updated logo
      await userStore.fetchUser();
    } catch (error) {
      toaster.add({
        title: "Error",
        description: "Failed to update company logo",
        icon: "solar:close-circle-linear",
        progress: true,
      });
    }
  }
};

const handleCoverImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file && company.value?.id) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      await apiClient.postFormData(
        `/uploads/company-cover/${company.value.id}`,
        formData
      );

      toaster.add({
        title: "Success",
        description: "Company cover image updated successfully",
        icon: "solar:check-circle-linear",
        progress: true,
      });

      // Refresh user data to get updated cover image
      await userStore.fetchUser();
    } catch (error) {
      toaster.add({
        title: "Error",
        description: "Failed to update company cover image",
        icon: "solar:close-circle-linear",
        progress: true,
      });
    }
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser();
  }

  // Redirect if user doesn't have a company
  if (!userStore.primaryCompany?.company) {
    toaster.add({
      title: "Error",
      description: "You are not associated with any company",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  }
});
</script>
