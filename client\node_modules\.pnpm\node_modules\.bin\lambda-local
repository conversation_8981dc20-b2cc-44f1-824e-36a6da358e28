#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules/lambda-local/build/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules/lambda-local/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules/lambda-local/build/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules/lambda-local/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/lambda-local@2.2.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../lambda-local/build/cli.js" "$@"
else
  exec node  "$basedir/../lambda-local/build/cli.js" "$@"
fi
