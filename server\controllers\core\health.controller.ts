import { Request, Response } from "express";
import { prisma } from "../../lib/prisma.js";
import os from "os";
import { performance } from "perf_hooks";

/**
 * Get comprehensive system health metrics
 */
export const getSystemHealth = async (req: Request, res: Response) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: "Company ID is required" });
    }

    // Start performance measurement
    const startTime = performance.now();

    // Get database metrics
    const dbMetrics = await getDatabaseMetrics();

    // Get system metrics
    const systemMetrics = getSystemMetrics();

    // Get application metrics
    const appMetrics = await getApplicationMetrics(companyId);

    // Get API performance metrics
    const apiMetrics = await getApiMetrics();

    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    res.json({
      timestamp: new Date().toISOString(),
      responseTime,
      system: systemMetrics,
      database: dbMetrics,
      application: appMetrics,
      api: apiMetrics,
      overallHealth: calculateOverallHealth(
        systemMetrics,
        dbMetrics,
        appMetrics
      ),
    });
  } catch (error) {
    console.error("Error getting system health:", error);
    res.status(500).json({ error: "Failed to get system health" });
  }
};

/**
 * Get database performance metrics
 */
const getDatabaseMetrics = async () => {
  try {
    // Get connection stats
    const connectionStats = (await prisma.$queryRaw`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `) as any[];

    // Get database size
    const sizeResult = (await prisma.$queryRaw`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `) as any[];

    // Get query performance
    const queryStats = (await prisma.$queryRaw`
      SELECT 
        round(avg(mean_exec_time)::numeric, 2) as avg_query_time,
        sum(calls) as total_queries,
        count(*) FILTER (WHERE mean_exec_time > 1000) as slow_queries
      FROM pg_stat_statements 
      WHERE dbid = (SELECT oid FROM pg_database WHERE datname = current_database())
      LIMIT 1
    `) as any[];

    // Get cache hit ratio
    const cacheStats = (await prisma.$queryRaw`
      SELECT 
        round(
          sum(blks_hit) * 100.0 / nullif(sum(blks_hit) + sum(blks_read), 0), 2
        ) as cache_hit_ratio
      FROM pg_stat_database 
      WHERE datname = current_database()
    `) as any[];

    const connections = connectionStats[0] || {};
    const size = sizeResult[0]?.size || "0 bytes";
    const queries = queryStats[0] || {};
    const cache = cacheStats[0] || {};

    return {
      connections: {
        total: parseInt(connections.total_connections) || 0,
        active: parseInt(connections.active_connections) || 0,
        idle: parseInt(connections.idle_connections) || 0,
      },
      size,
      performance: {
        avgQueryTime: parseFloat(queries.avg_query_time) || 0,
        totalQueries: parseInt(queries.total_queries) || 0,
        slowQueries: parseInt(queries.slow_queries) || 0,
        cacheHitRatio: parseFloat(cache.cache_hit_ratio) || 0,
      },
    };
  } catch (error) {
    console.error("Error getting database metrics:", error);
    return {
      connections: { total: 0, active: 0, idle: 0 },
      size: "Unknown",
      performance: {
        avgQueryTime: 0,
        totalQueries: 0,
        slowQueries: 0,
        cacheHitRatio: 0,
      },
    };
  }
};

/**
 * Get system resource metrics
 */
const getSystemMetrics = () => {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = (usedMemory / totalMemory) * 100;

  const cpuUsage = process.cpuUsage();
  const uptime = process.uptime();

  return {
    memory: {
      total: totalMemory,
      used: usedMemory,
      free: freeMemory,
      usage: Math.round(memoryUsage * 100) / 100,
    },
    cpu: {
      usage: Math.round((cpuUsage.user + cpuUsage.system) / 1000000), // Convert to seconds
      cores: os.cpus().length,
      loadAverage: os.loadavg(),
    },
    uptime: Math.round(uptime),
    platform: os.platform(),
    arch: os.arch(),
  };
};

/**
 * Get application-specific metrics
 */
const getApplicationMetrics = async (companyId: number) => {
  try {
    // Get user activity
    const activeUsers = await prisma.user.count({
      where: {
        lastLoginAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });

    // Get recent projects
    const activeProjects = await prisma.project.count({
      where: {
        companyId,
        status: "ACTIVE" as any,
      },
    });

    // Get recent tasks
    const pendingTasks = await prisma.task.count({
      where: {
        project: { companyId },
        status: { in: ["TODO", "IN_PROGRESS"] as any },
      },
    });

    // Get error logs (if you have an error logging table)
    const recentErrors = 0; // Placeholder - implement based on your error logging

    return {
      users: {
        active: activeUsers,
        total: await prisma.user.count(),
      },
      projects: {
        active: activeProjects,
        total: await prisma.project.count({ where: { companyId } }),
      },
      tasks: {
        pending: pendingTasks,
        total: await prisma.task.count({ where: { project: { companyId } } }),
      },
      errors: {
        recent: recentErrors,
      },
    };
  } catch (error) {
    console.error("Error getting application metrics:", error);
    return {
      users: { active: 0, total: 0 },
      projects: { active: 0, total: 0 },
      tasks: { pending: 0, total: 0 },
      errors: { recent: 0 },
    };
  }
};

/**
 * Get API performance metrics
 */
const getApiMetrics = async () => {
  // This would typically come from your API monitoring/logging system
  // For now, return mock data that could be replaced with real metrics
  return {
    requestsPerMinute: Math.floor(Math.random() * 100) + 50,
    averageResponseTime: Math.floor(Math.random() * 200) + 100,
    errorRate: Math.random() * 5, // 0-5% error rate
    uptime: 99.9,
  };
};

/**
 * Calculate overall health score
 */
const calculateOverallHealth = (
  system: any,
  database: any,
  application: any
) => {
  let score = 100;

  // Deduct points for high memory usage
  if (system.memory.usage > 90) score -= 20;
  else if (system.memory.usage > 80) score -= 10;
  else if (system.memory.usage > 70) score -= 5;

  // Deduct points for slow database queries
  if (database.performance.avgQueryTime > 1000) score -= 15;
  else if (database.performance.avgQueryTime > 500) score -= 10;
  else if (database.performance.avgQueryTime > 200) score -= 5;

  // Deduct points for low cache hit ratio
  if (database.performance.cacheHitRatio < 80) score -= 15;
  else if (database.performance.cacheHitRatio < 90) score -= 10;
  else if (database.performance.cacheHitRatio < 95) score -= 5;

  // Deduct points for many slow queries
  if (database.performance.slowQueries > 10) score -= 10;
  else if (database.performance.slowQueries > 5) score -= 5;

  return Math.max(0, Math.min(100, score));
};

/**
 * Get health metrics history (for charts)
 */
export const getHealthHistory = async (req: Request, res: Response) => {
  try {
    const { period = "24h" } = req.query;

    // Generate mock historical data
    // In a real implementation, you'd store these metrics in a time-series database
    const hours = period === "7d" ? 168 : 24;
    const interval = period === "7d" ? 60 : 60; // minutes

    const history = [];
    const now = new Date();

    for (let i = hours; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * interval * 60 * 1000);

      history.push({
        timestamp: timestamp.toISOString(),
        systemHealth: Math.floor(Math.random() * 20) + 80, // 80-100%
        memoryUsage: Math.floor(Math.random() * 30) + 50, // 50-80%
        cpuUsage: Math.floor(Math.random() * 40) + 20, // 20-60%
        dbConnections: Math.floor(Math.random() * 20) + 10, // 10-30
        responseTime: Math.floor(Math.random() * 100) + 100, // 100-200ms
        apiRequests: Math.floor(Math.random() * 50) + 25, // 25-75 req/min
      });
    }

    res.json({ history });
  } catch (error) {
    console.error("Error getting health history:", error);
    res.status(500).json({ error: "Failed to get health history" });
  }
};
