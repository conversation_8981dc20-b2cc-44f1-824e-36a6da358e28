
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'CodeGroup': typeof import("../app/components/content/CodeGroup.vue")['default']
    'CodeTimeline': typeof import("../app/components/content/CodeTimeline.vue")['default']
    'CodeTimelineItem': typeof import("../app/components/content/CodeTimelineItem.vue")['default']
    'DocButton': typeof import("../app/components/content/DocButton.vue")['default']
    'DocChecklist': typeof import("../app/components/content/DocChecklist.vue")['default']
    'DocComponentDemo': typeof import("../app/components/content/DocComponentDemo.vue")['default']
    'DocComponentList': typeof import("../app/components/content/DocComponentList.vue")['default']
    'DocComponentMeta': typeof import("../app/components/content/DocComponentMeta.vue")['default']
    'DocCustomizerButton': typeof import("../app/components/content/DocCustomizerButton.vue")['default']
    'DocGrid': typeof import("../app/components/content/DocGrid.vue")['default']
    'DocGridIcon': typeof import("../app/components/content/DocGridIcon.vue")['default']
    'DocImage': typeof import("../app/components/content/DocImage.vue")['default']
    'DocInfo': typeof import("../app/components/content/DocInfo.vue")['default']
    'DocLinker': typeof import("../app/components/content/DocLinker.vue")['default']
    'DocMessage': typeof import("../app/components/content/DocMessage.vue")['default']
    'DocOverview': typeof import("../app/components/content/DocOverview.vue")['default']
    'DocOverviewLayers': typeof import("../app/components/content/DocOverviewLayers.vue")['default']
    'DocStacks': typeof import("../app/components/content/DocStacks.vue")['default']
    'DocTag': typeof import("../app/components/content/DocTag.vue")['default']
    'AddonApexcharts': typeof import("../app/components/AddonApexcharts.vue")['default']
    'AddonCollapseTransition': typeof import("../app/components/AddonCollapseTransition.vue")['default']
    'AddonDatepicker': typeof import("../app/components/AddonDatepicker.vue")['default']
    'AddonInputPassword': typeof import("../app/components/AddonInputPassword.vue")['default']
    'AddonInputPhone': typeof import("../app/components/AddonInputPhone.vue")['default']
    'AddonLightweightCharts': typeof import("../app/components/AddonLightweightCharts.vue")['default']
    'AddonMapboxLocationPicker': typeof import("../app/components/AddonMapboxLocationPicker.vue")['default']
    'CodeGroupHeader': typeof import("../app/components/CodeGroupHeader.vue")['default']
    'ComponentMetaCode': typeof import("../app/components/ComponentMetaCode.vue")['default']
    'DemoAccountMenu': typeof import("../app/components/DemoAccountMenu.vue")['default']
    'DemoActionText': typeof import("../app/components/DemoActionText.vue")['default']
    'DemoActivityTable': typeof import("../app/components/DemoActivityTable.vue")['default']
    'DemoAppLayoutSwitcher': typeof import("../app/components/DemoAppLayoutSwitcher.vue")['default']
    'DemoAppSearch': typeof import("../app/components/DemoAppSearch.vue")['default']
    'DemoAppSearchResult': typeof import("../app/components/DemoAppSearchResult.vue")['default']
    'DemoAuthorsListCompact': typeof import("../app/components/DemoAuthorsListCompact.vue")['default']
    'DemoCalendarEvent': typeof import("../app/components/DemoCalendarEvent.vue")['default']
    'DemoCalendarEventPending': typeof import("../app/components/DemoCalendarEventPending.vue")['default']
    'DemoCardFilters': typeof import("../app/components/DemoCardFilters.vue")['default']
    'DemoCommentListCompact': typeof import("../app/components/DemoCommentListCompact.vue")['default']
    'DemoCompanyOverview': typeof import("../app/components/DemoCompanyOverview.vue")['default']
    'DemoCreditCard': typeof import("../app/components/DemoCreditCard.vue")['default']
    'DemoCreditCardReal': typeof import("../app/components/DemoCreditCardReal.vue")['default']
    'DemoCreditCardSmall': typeof import("../app/components/DemoCreditCardSmall.vue")['default']
    'DemoDatepicker': typeof import("../app/components/DemoDatepicker.vue")['default']
    'DemoDaysSquare': typeof import("../app/components/DemoDaysSquare.vue")['default']
    'DemoFileListTabbed': typeof import("../app/components/DemoFileListTabbed.vue")['default']
    'DemoFlexTableCell': typeof import("../app/components/DemoFlexTableCell.vue")['default']
    'DemoFlexTableRow': typeof import("../app/components/DemoFlexTableRow.vue")['default']
    'DemoFlexTableStart': typeof import("../app/components/DemoFlexTableStart.vue")['default']
    'DemoFlexTableWrapper': typeof import("../app/components/DemoFlexTableWrapper.vue")['default']
    'DemoFollowersCompact': typeof import("../app/components/DemoFollowersCompact.vue")['default']
    'DemoIconLinks': typeof import("../app/components/DemoIconLinks.vue")['default']
    'DemoIconText': typeof import("../app/components/DemoIconText.vue")['default']
    'DemoIconsSquare': typeof import("../app/components/DemoIconsSquare.vue")['default']
    'DemoImageLinks': typeof import("../app/components/DemoImageLinks.vue")['default']
    'DemoInboxMessage': typeof import("../app/components/DemoInboxMessage.vue")['default']
    'DemoInfoBadges': typeof import("../app/components/DemoInfoBadges.vue")['default']
    'DemoInfoImage': typeof import("../app/components/DemoInfoImage.vue")['default']
    'DemoLeagueListCompact': typeof import("../app/components/DemoLeagueListCompact.vue")['default']
    'DemoLinkArrow': typeof import("../app/components/DemoLinkArrow.vue")['default']
    'DemoMapMarker': typeof import("../app/components/DemoMapMarker.vue")['default']
    'DemoMenuIconList': typeof import("../app/components/DemoMenuIconList.vue")['default']
    'DemoNavigationTop': typeof import("../app/components/DemoNavigationTop.vue")['default']
    'DemoNotificationsCompact': typeof import("../app/components/DemoNotificationsCompact.vue")['default']
    'DemoOfferCollapse': typeof import("../app/components/DemoOfferCollapse.vue")['default']
    'DemoPanelAccount': typeof import("../app/components/DemoPanelAccount.vue")['default']
    'DemoPanelActivity': typeof import("../app/components/DemoPanelActivity.vue")['default']
    'DemoPanelCard': typeof import("../app/components/DemoPanelCard.vue")['default']
    'DemoPanelInvest': typeof import("../app/components/DemoPanelInvest.vue")['default']
    'DemoPanelLanguage': typeof import("../app/components/DemoPanelLanguage.vue")['default']
    'DemoPanelSearch': typeof import("../app/components/DemoPanelSearch.vue")['default']
    'DemoPanelTask': typeof import("../app/components/DemoPanelTask.vue")['default']
    'DemoPendingTickets': typeof import("../app/components/DemoPendingTickets.vue")['default']
    'DemoPicture': typeof import("../app/components/DemoPicture.vue")['default']
    'DemoPlaceholderCompact': typeof import("../app/components/DemoPlaceholderCompact.vue")['default']
    'DemoPlaceholderMinimal': typeof import("../app/components/DemoPlaceholderMinimal.vue")['default']
    'DemoPopularCryptos': typeof import("../app/components/DemoPopularCryptos.vue")['default']
    'DemoProductCompact': typeof import("../app/components/DemoProductCompact.vue")['default']
    'DemoProgressCircle': typeof import("../app/components/DemoProgressCircle.vue")['default']
    'DemoProjectListCompact': typeof import("../app/components/DemoProjectListCompact.vue")['default']
    'DemoSearchCompact': typeof import("../app/components/DemoSearchCompact.vue")['default']
    'DemoShoppingCartCompact': typeof import("../app/components/DemoShoppingCartCompact.vue")['default']
    'DemoSocialLinks': typeof import("../app/components/DemoSocialLinks.vue")['default']
    'DemoStarterSwitcher': typeof import("../app/components/DemoStarterSwitcher.vue")['default']
    'DemoSubsidebarMessaging': typeof import("../app/components/DemoSubsidebarMessaging.vue")['default']
    'DemoTabbedContent': typeof import("../app/components/DemoTabbedContent.vue")['default']
    'DemoTagListCompact': typeof import("../app/components/DemoTagListCompact.vue")['default']
    'DemoTeamListCompact': typeof import("../app/components/DemoTeamListCompact.vue")['default']
    'DemoTeamSearchCompact': typeof import("../app/components/DemoTeamSearchCompact.vue")['default']
    'DemoTimelineCompact': typeof import("../app/components/DemoTimelineCompact.vue")['default']
    'DemoTodoListCompact': typeof import("../app/components/DemoTodoListCompact.vue")['default']
    'DemoTodoListTabbed': typeof import("../app/components/DemoTodoListTabbed.vue")['default']
    'DemoToolbar': typeof import("../app/components/DemoToolbar.vue")['default']
    'DemoToolbarTopnav': typeof import("../app/components/DemoToolbarTopnav.vue")['default']
    'DemoTopicListCompact': typeof import("../app/components/DemoTopicListCompact.vue")['default']
    'DemoTransactionsFilters': typeof import("../app/components/DemoTransactionsFilters.vue")['default']
    'DemoTransactionsListPlaceload': typeof import("../app/components/DemoTransactionsListPlaceload.vue")['default']
    'DemoTrendingSkills': typeof import("../app/components/DemoTrendingSkills.vue")['default']
    'DemoUserList': typeof import("../app/components/DemoUserList.vue")['default']
    'DemoVcardRight': typeof import("../app/components/DemoVcardRight.vue")['default']
    'DemoVideoCompact': typeof import("../app/components/DemoVideoCompact.vue")['default']
    'DemoWidgetAccountBalance': typeof import("../app/components/DemoWidgetAccountBalance.vue")['default']
    'DemoWidgetFeatures': typeof import("../app/components/DemoWidgetFeatures.vue")['default']
    'DemoWidgetInvest': typeof import("../app/components/DemoWidgetInvest.vue")['default']
    'DemoWidgetMoneyIn': typeof import("../app/components/DemoWidgetMoneyIn.vue")['default']
    'DemoWidgetMoneyOut': typeof import("../app/components/DemoWidgetMoneyOut.vue")['default']
    'DemoWidgetTransactionCompact': typeof import("../app/components/DemoWidgetTransactionCompact.vue")['default']
    'DemoWidgetTransactionSummary': typeof import("../app/components/DemoWidgetTransactionSummary.vue")['default']
    'DemoWidgetWelcome': typeof import("../app/components/DemoWidgetWelcome.vue")['default']
    'DemoWizardButtons': typeof import("../app/components/DemoWizardButtons.vue")['default']
    'DemoWizardNavigation': typeof import("../app/components/DemoWizardNavigation.vue")['default']
    'DemoWizardStepTitle': typeof import("../app/components/DemoWizardStepTitle.vue")['default']
    'DemoWorkspaceDropdown': typeof import("../app/components/DemoWorkspaceDropdown.vue")['default']
    'DocLayoutSection': typeof import("../app/components/DocLayoutSection.vue")['default']
    'DocSurround': typeof import("../app/components/DocSurround.vue")['default']
    'DocToc': typeof import("../app/components/DocToc.vue")['default']
    'TairoLogo': typeof import("../app/components/TairoLogo.vue")['default']
    'TairoLogoText': typeof import("../app/components/TairoLogoText.vue")['default']
    'VectorChartStockOne': typeof import("../app/components/VectorChartStockOne.vue")['default']
    'VectorChartStockThree': typeof import("../app/components/VectorChartStockThree.vue")['default']
    'VectorChartStockTwo': typeof import("../app/components/VectorChartStockTwo.vue")['default']
    'VectorIllustrationBankFront': typeof import("../app/components/VectorIllustrationBankFront.vue")['default']
    'VectorIllustrationCalendar': typeof import("../app/components/VectorIllustrationCalendar.vue")['default']
    'VectorIllustrationCreditCard': typeof import("../app/components/VectorIllustrationCreditCard.vue")['default']
    'VectorIllustrationManWondering': typeof import("../app/components/VectorIllustrationManWondering.vue")['default']
    'VectorIllustrationTransaction': typeof import("../app/components/VectorIllustrationTransaction.vue")['default']
    'VectorLogoVisa': typeof import("../app/components/VectorLogoVisa.vue")['default']
    'DemoChartArea': typeof import("../app/components/demo-chart/DemoChartArea.vue")['default']
    'DemoChartAreaBalance': typeof import("../app/components/demo-chart/DemoChartAreaBalance.vue")['default']
    'DemoChartAreaBtcPrice': typeof import("../app/components/demo-chart/DemoChartAreaBtcPrice.vue")['default']
    'DemoChartAreaCondition': typeof import("../app/components/demo-chart/DemoChartAreaCondition.vue")['default']
    'DemoChartAreaCustomers': typeof import("../app/components/demo-chart/DemoChartAreaCustomers.vue")['default']
    'DemoChartAreaExpenses': typeof import("../app/components/demo-chart/DemoChartAreaExpenses.vue")['default']
    'DemoChartAreaIncomeHistory': typeof import("../app/components/demo-chart/DemoChartAreaIncomeHistory.vue")['default']
    'DemoChartAreaInterviews': typeof import("../app/components/demo-chart/DemoChartAreaInterviews.vue")['default']
    'DemoChartAreaMulti': typeof import("../app/components/demo-chart/DemoChartAreaMulti.vue")['default']
    'DemoChartAreaProgress': typeof import("../app/components/demo-chart/DemoChartAreaProgress.vue")['default']
    'DemoChartAreaSparkSalesFour': typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesFour.vue")['default']
    'DemoChartAreaSparkSalesOne': typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesOne.vue")['default']
    'DemoChartAreaSparkSalesThree': typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesThree.vue")['default']
    'DemoChartAreaSparkSalesTwo': typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesTwo.vue")['default']
    'DemoChartAreaStats': typeof import("../app/components/demo-chart/DemoChartAreaStats.vue")['default']
    'DemoChartAreaStockPrice': typeof import("../app/components/demo-chart/DemoChartAreaStockPrice.vue")['default']
    'DemoChartAreaTaskCompletion': typeof import("../app/components/demo-chart/DemoChartAreaTaskCompletion.vue")['default']
    'DemoChartBar': typeof import("../app/components/demo-chart/DemoChartBar.vue")['default']
    'DemoChartBarHorizontal': typeof import("../app/components/demo-chart/DemoChartBarHorizontal.vue")['default']
    'DemoChartBarHorizontalMulti': typeof import("../app/components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']
    'DemoChartBarMulti': typeof import("../app/components/demo-chart/DemoChartBarMulti.vue")['default']
    'DemoChartBarMultiIncome': typeof import("../app/components/demo-chart/DemoChartBarMultiIncome.vue")['default']
    'DemoChartBarOrders': typeof import("../app/components/demo-chart/DemoChartBarOrders.vue")['default']
    'DemoChartBarOxygen': typeof import("../app/components/demo-chart/DemoChartBarOxygen.vue")['default']
    'DemoChartBarProfit': typeof import("../app/components/demo-chart/DemoChartBarProfit.vue")['default']
    'DemoChartBarRange': typeof import("../app/components/demo-chart/DemoChartBarRange.vue")['default']
    'DemoChartBarSalesProfit': typeof import("../app/components/demo-chart/DemoChartBarSalesProfit.vue")['default']
    'DemoChartBarSocialChannels': typeof import("../app/components/demo-chart/DemoChartBarSocialChannels.vue")['default']
    'DemoChartBarStacked': typeof import("../app/components/demo-chart/DemoChartBarStacked.vue")['default']
    'DemoChartBarTeamEfficiency': typeof import("../app/components/demo-chart/DemoChartBarTeamEfficiency.vue")['default']
    'DemoChartBubble': typeof import("../app/components/demo-chart/DemoChartBubble.vue")['default']
    'DemoChartDonut': typeof import("../app/components/demo-chart/DemoChartDonut.vue")['default']
    'DemoChartDonutExpenses': typeof import("../app/components/demo-chart/DemoChartDonutExpenses.vue")['default']
    'DemoChartLine': typeof import("../app/components/demo-chart/DemoChartLine.vue")['default']
    'DemoChartLineMulti': typeof import("../app/components/demo-chart/DemoChartLineMulti.vue")['default']
    'DemoChartLineMultiAlt': typeof import("../app/components/demo-chart/DemoChartLineMultiAlt.vue")['default']
    'DemoChartLineRevenue': typeof import("../app/components/demo-chart/DemoChartLineRevenue.vue")['default']
    'DemoChartLineSparkFour': typeof import("../app/components/demo-chart/DemoChartLineSparkFour.vue")['default']
    'DemoChartLineSparkOne': typeof import("../app/components/demo-chart/DemoChartLineSparkOne.vue")['default']
    'DemoChartLineSparkThree': typeof import("../app/components/demo-chart/DemoChartLineSparkThree.vue")['default']
    'DemoChartLineSparkTwo': typeof import("../app/components/demo-chart/DemoChartLineSparkTwo.vue")['default']
    'DemoChartLineStep': typeof import("../app/components/demo-chart/DemoChartLineStep.vue")['default']
    'DemoChartPie': typeof import("../app/components/demo-chart/DemoChartPie.vue")['default']
    'DemoChartRadar': typeof import("../app/components/demo-chart/DemoChartRadar.vue")['default']
    'DemoChartRadial': typeof import("../app/components/demo-chart/DemoChartRadial.vue")['default']
    'DemoChartRadialEvolution': typeof import("../app/components/demo-chart/DemoChartRadialEvolution.vue")['default']
    'DemoChartRadialGauge': typeof import("../app/components/demo-chart/DemoChartRadialGauge.vue")['default']
    'DemoChartRadialGaugeAlt': typeof import("../app/components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']
    'DemoChartRadialGoal': typeof import("../app/components/demo-chart/DemoChartRadialGoal.vue")['default']
    'DemoChartRadialGrowth': typeof import("../app/components/demo-chart/DemoChartRadialGrowth.vue")['default']
    'DemoChartRadialMulti': typeof import("../app/components/demo-chart/DemoChartRadialMulti.vue")['default']
    'DemoChartRadialPopularity': typeof import("../app/components/demo-chart/DemoChartRadialPopularity.vue")['default']
    'DemoChartRadialSalesRevenue': typeof import("../app/components/demo-chart/DemoChartRadialSalesRevenue.vue")['default']
    'DemoChartRadialSmallOne': typeof import("../app/components/demo-chart/DemoChartRadialSmallOne.vue")['default']
    'DemoChartRadialSmallThree': typeof import("../app/components/demo-chart/DemoChartRadialSmallThree.vue")['default']
    'DemoChartRadialSmallTwo': typeof import("../app/components/demo-chart/DemoChartRadialSmallTwo.vue")['default']
    'DemoChartScatter': typeof import("../app/components/demo-chart/DemoChartScatter.vue")['default']
    'DemoChartScatterEnergy': typeof import("../app/components/demo-chart/DemoChartScatterEnergy.vue")['default']
    'DemoChartTimeline': typeof import("../app/components/demo-chart/DemoChartTimeline.vue")['default']
    'LandingBenefits': typeof import("../app/components/landing/LandingBenefits.vue")['default']
    'LandingContent': typeof import("../app/components/landing/LandingContent.vue")['default']
    'LandingCta': typeof import("../app/components/landing/LandingCta.vue")['default']
    'LandingCustomizer': typeof import("../app/components/landing/LandingCustomizer.vue")['default']
    'LandingDemoLink': typeof import("../app/components/landing/LandingDemoLink.vue")['default']
    'LandingDemos': typeof import("../app/components/landing/LandingDemos.vue")['default']
    'LandingFeatures': typeof import("../app/components/landing/LandingFeatures.vue")['default']
    'LandingFeaturesTile': typeof import("../app/components/landing/LandingFeaturesTile.vue")['default']
    'LandingFooter': typeof import("../app/components/landing/LandingFooter.vue")['default']
    'LandingHero': typeof import("../app/components/landing/LandingHero.vue")['default']
    'LandingHeroMockup': typeof import("../app/components/landing/LandingHeroMockup.vue")['default']
    'LandingLayers': typeof import("../app/components/landing/LandingLayers.vue")['default']
    'LandingLayersBox': typeof import("../app/components/landing/LandingLayersBox.vue")['default']
    'LandingLayout': typeof import("../app/components/landing/LandingLayout.vue")['default']
    'LandingLayouts': typeof import("../app/components/landing/LandingLayouts.vue")['default']
    'LandingMobileNav': typeof import("../app/components/landing/LandingMobileNav.vue")['default']
    'LandingNavbar': typeof import("../app/components/landing/LandingNavbar.vue")['default']
    'ProseA': typeof import("../app/components/prose/ProseA.vue")['default']
    'ProseBlockquote': typeof import("../app/components/prose/ProseBlockquote.vue")['default']
    'ProseCode': typeof import("../app/components/prose/ProseCode.vue")['default']
    'ProseCodeInline': typeof import("../app/components/prose/ProseCodeInline.vue")['default']
    'ProseEm': typeof import("../app/components/prose/ProseEm.vue")['default']
    'ProseH1': typeof import("../app/components/prose/ProseH1.vue")['default']
    'ProseH2': typeof import("../app/components/prose/ProseH2.vue")['default']
    'ProseH3': typeof import("../app/components/prose/ProseH3.vue")['default']
    'ProseH5': typeof import("../app/components/prose/ProseH5.vue")['default']
    'ProseH6': typeof import("../app/components/prose/ProseH6.vue")['default']
    'ProseHr': typeof import("../app/components/prose/ProseHr.vue")['default']
    'ProseImg': typeof import("../app/components/prose/ProseImg.vue")['default']
    'ProseLi': typeof import("../app/components/prose/ProseLi.vue")['default']
    'ProseOl': typeof import("../app/components/prose/ProseOl.vue")['default']
    'ProseP': typeof import("../app/components/prose/ProseP.vue")['default']
    'ProsePre': typeof import("../app/components/prose/ProsePre.vue")['default']
    'ProseScript': typeof import("../app/components/prose/ProseScript.vue")['default']
    'ProseStrong': typeof import("../app/components/prose/ProseStrong.vue")['default']
    'ProseTable': typeof import("../app/components/prose/ProseTable.vue")['default']
    'ProseTbody': typeof import("../app/components/prose/ProseTbody.vue")['default']
    'ProseTd': typeof import("../app/components/prose/ProseTd.vue")['default']
    'ProseTh': typeof import("../app/components/prose/ProseTh.vue")['default']
    'ProseThead': typeof import("../app/components/prose/ProseThead.vue")['default']
    'ProseTr': typeof import("../app/components/prose/ProseTr.vue")['default']
    'ProseUl': typeof import("../app/components/prose/ProseUl.vue")['default']
    'ProseH4': typeof import("../app/components/prose/proseH4.vue")['default']
    'TairoCheckAnimated': typeof import("../../layers/tairo/components/TairoCheckAnimated.vue")['default']
    'TairoCheckboxAnimated': typeof import("../../layers/tairo/components/TairoCheckboxAnimated.vue")['default']
    'TairoCheckboxCardIcon': typeof import("../../layers/tairo/components/TairoCheckboxCardIcon.vue")['default']
    'TairoContentWrapper': typeof import("../../layers/tairo/components/TairoContentWrapper.vue")['default']
    'TairoContentWrapperTabbed': typeof import("../../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']
    'TairoError': typeof import("../../layers/tairo/components/TairoError.vue")['default']
    'TairoFlexTable': typeof import("../../layers/tairo/components/TairoFlexTable.vue")['default']
    'TairoFlexTableCell': typeof import("../../layers/tairo/components/TairoFlexTableCell.vue")['default']
    'TairoFlexTableHeading': typeof import("../../layers/tairo/components/TairoFlexTableHeading.vue")['default']
    'TairoFlexTableRow': typeof import("../../layers/tairo/components/TairoFlexTableRow.vue")['default']
    'TairoFormGroup': typeof import("../../layers/tairo/components/TairoFormGroup.vue")['default']
    'TairoFormSave': typeof import("../../layers/tairo/components/TairoFormSave.vue")['default']
    'TairoFullscreenDropfile': typeof import("../../layers/tairo/components/TairoFullscreenDropfile.vue")['default']
    'TairoImageZoom': typeof import("../../layers/tairo/components/TairoImageZoom.vue")['default']
    'TairoInput': typeof import("../../layers/tairo/components/TairoInput.vue")['default']
    'TairoInputFileHeadless': typeof import("../../layers/tairo/components/TairoInputFileHeadless.vue")['default']
    'TairoMobileDrawer': typeof import("../../layers/tairo/components/TairoMobileDrawer.vue")['default']
    'TairoPanels': typeof import("../../layers/tairo/components/TairoPanels.vue")['default']
    'TairoRadioCard': typeof import("../../layers/tairo/components/TairoRadioCard.vue")['default']
    'TairoSelect': typeof import("../../layers/tairo/components/TairoSelect.vue")['default']
    'TairoSelectItem': typeof import("../../layers/tairo/components/TairoSelectItem.vue")['default']
    'TairoTable': typeof import("../../layers/tairo/components/TairoTable.vue")['default']
    'TairoTableCell': typeof import("../../layers/tairo/components/TairoTableCell.vue")['default']
    'TairoTableHeading': typeof import("../../layers/tairo/components/TairoTableHeading.vue")['default']
    'TairoTableRow': typeof import("../../layers/tairo/components/TairoTableRow.vue")['default']
    'TairoWelcome': typeof import("../../layers/tairo/components/TairoWelcome.vue")['default']
    'TairoCollapseBackdrop': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseBackdrop.vue")['default']
    'TairoCollapseCollapsible': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsible.vue")['default']
    'TairoCollapseCollapsibleLink': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']
    'TairoCollapseCollapsibleTrigger': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']
    'TairoCollapseContent': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseContent.vue")['default']
    'TairoCollapseLayout': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseLayout.vue")['default']
    'TairoCollapseSidebar': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebar.vue")['default']
    'TairoCollapseSidebarClose': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']
    'TairoCollapseSidebarHeader': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']
    'TairoCollapseSidebarLink': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']
    'TairoCollapseSidebarLinks': typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']
    'TairoMenu': typeof import("../../layers/tairo/components/tairo-menu/TairoMenu.vue")['default']
    'TairoMenuContent': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuContent.vue")['default']
    'TairoMenuIndicator': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuIndicator.vue")['default']
    'TairoMenuItem': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuItem.vue")['default']
    'TairoMenuLink': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLink.vue")['default']
    'TairoMenuLinkTab': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLinkTab.vue")['default']
    'TairoMenuList': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuList.vue")['default']
    'TairoMenuListItems': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuListItems.vue")['default']
    'TairoMenuTrigger': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuTrigger.vue")['default']
    'TairoMenuViewport': typeof import("../../layers/tairo/components/tairo-menu/TairoMenuViewport.vue")['default']
    'TairoSidebar': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebar.vue")['default']
    'TairoSidebarBackdrop': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']
    'TairoSidebarContent': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarContent.vue")['default']
    'TairoSidebarLayout': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLayout.vue")['default']
    'TairoSidebarLink': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLink.vue")['default']
    'TairoSidebarLinks': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLinks.vue")['default']
    'TairoSidebarNav': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarNav.vue")['default']
    'TairoSidebarSubsidebar': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']
    'TairoSidebarSubsidebarCollapsible': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']
    'TairoSidebarSubsidebarCollapsibleLink': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']
    'TairoSidebarSubsidebarCollapsibleTrigger': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']
    'TairoSidebarSubsidebarContent': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']
    'TairoSidebarSubsidebarHeader': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']
    'TairoSidebarSubsidebarLink': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']
    'TairoSidebarTrigger': typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarTrigger.vue")['default']
    'TairoSidenavBackdrop': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']
    'TairoSidenavCollapsible': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']
    'TairoSidenavCollapsibleLink': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']
    'TairoSidenavCollapsibleTrigger': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']
    'TairoSidenavContent': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavContent.vue")['default']
    'TairoSidenavLayout': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavLayout.vue")['default']
    'TairoSidenavSidebar': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebar.vue")['default']
    'TairoSidenavSidebarDivider': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']
    'TairoSidenavSidebarHeader': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']
    'TairoSidenavSidebarLink': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']
    'TairoSidenavSidebarLinks': typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']
    'TairoTopnavContent': typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavContent.vue")['default']
    'TairoTopnavHeader': typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavHeader.vue")['default']
    'TairoTopnavLayout': typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavLayout.vue")['default']
    'TairoTopnavNavbar': typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavNavbar.vue")['default']
    'ExamplesAddonsDatepicker': typeof import("../examples/addons/datepicker.vue")['default']
    'ExamplesAddonsMapbox': typeof import("../examples/addons/mapbox.vue")['default']
    'ExamplesApexchartsBase': typeof import("../examples/apexcharts/base.vue")['default']
    'ExamplesFlexTableCurved': typeof import("../examples/flex-table/curved.vue")['default']
    'ExamplesFlexTableRounded': typeof import("../examples/flex-table/rounded.vue")['default']
    'ExamplesFlexTableSmooth': typeof import("../examples/flex-table/smooth.vue")['default']
    'ExamplesFlexTableStraight': typeof import("../examples/flex-table/straight.vue")['default']
    'ExamplesInputPasswordBase': typeof import("../examples/input-password/base.vue")['default']
    'ExamplesInputPasswordDisabled': typeof import("../examples/input-password/disabled.vue")['default']
    'ExamplesInputPasswordLocale': typeof import("../examples/input-password/locale.vue")['default']
    'ExamplesInputPasswordUserInput': typeof import("../examples/input-password/user-input.vue")['default']
    'ExamplesInputPasswordValidation': typeof import("../examples/input-password/validation.vue")['default']
    'ExamplesInputPhoneBase': typeof import("../examples/input-phone/base.vue")['default']
    'ExamplesInputPhoneCountry': typeof import("../examples/input-phone/country.vue")['default']
    'ExamplesInputPhoneDisabled': typeof import("../examples/input-phone/disabled.vue")['default']
    'ExamplesInputPhoneFormat': typeof import("../examples/input-phone/format.vue")['default']
    'ExamplesInputPhoneShape': typeof import("../examples/input-phone/shape.vue")['default']
    'ExamplesInputPhoneSize': typeof import("../examples/input-phone/size.vue")['default']
    'ExamplesInputPhoneValidation': typeof import("../examples/input-phone/validation.vue")['default']
    'ExamplesLightweightChartsBase': typeof import("../examples/lightweight-charts/base.vue")['default']
    'ExamplesPanelActivity': typeof import("../examples/panel/activity.vue")['default']
    'ExamplesPanelLanguage': typeof import("../examples/panel/language.vue")['default']
    'ExamplesPanelSearch': typeof import("../examples/panel/search.vue")['default']
    'ExamplesPanelTask': typeof import("../examples/panel/task.vue")['default']
    'ExamplesTableCurved': typeof import("../examples/table/curved.vue")['default']
    'ExamplesTableMediaCurved': typeof import("../examples/table/media-curved.vue")['default']
    'ExamplesTableMediaRounded': typeof import("../examples/table/media-rounded.vue")['default']
    'ExamplesTableMediaSmooth': typeof import("../examples/table/media-smooth.vue")['default']
    'ExamplesTableMediaStraight': typeof import("../examples/table/media-straight.vue")['default']
    'ExamplesTableRounded': typeof import("../examples/table/rounded.vue")['default']
    'ExamplesTableSmooth': typeof import("../examples/table/smooth.vue")['default']
    'ExamplesTableStraight': typeof import("../examples/table/straight.vue")['default']
    'ExamplesTairoCheckAnimated': typeof import("../examples/tairo/check-animated.vue")['default']
    'ExamplesTairoCheckboxAnimated': typeof import("../examples/tairo/checkbox-animated.vue")['default']
    'ExamplesTairoCheckboxCardIcon': typeof import("../examples/tairo/checkbox-card-icon.vue")['default']
    'ExamplesTairoCircularMenu': typeof import("../examples/tairo/circular-menu.vue")['default']
    'ExamplesTairoError': typeof import("../examples/tairo/error.vue")['default']
    'ExamplesTairoFormGroup': typeof import("../examples/tairo/form-group.vue")['default']
    'ExamplesTairoFormSave': typeof import("../examples/tairo/form-save.vue")['default']
    'ExamplesTairoInput': typeof import("../examples/tairo/input.vue")['default']
    'ExamplesTairoLogo': typeof import("../examples/tairo/logo.vue")['default']
    'ExamplesTairoLogotext': typeof import("../examples/tairo/logotext.vue")['default']
    'ExamplesTairoMenuComplete': typeof import("../examples/tairo/menu-complete.vue")['default']
    'ExamplesTairoMenu': typeof import("../examples/tairo/menu.vue")['default']
    'ExamplesTairoMobileDrawer': typeof import("../examples/tairo/mobile-drawer.vue")['default']
    'ExamplesTairoRadioCard': typeof import("../examples/tairo/radio-card.vue")['default']
    'ExamplesTairoSelect': typeof import("../examples/tairo/select.vue")['default']
    'ExamplesTairoValidation': typeof import("../examples/tairo/validation.vue")['default']
    'BaseAccordion': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']
    'BaseAccordionItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']
    'BaseAutocomplete': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']
    'BaseAutocompleteGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']
    'BaseAutocompleteItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']
    'BaseAutocompleteLabel': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']
    'BaseAutocompleteSeparator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']
    'BaseAvatar': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']
    'BaseAvatarGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']
    'BaseBreadcrumb': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']
    'BaseButton': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']
    'BaseCard': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']
    'BaseCheckbox': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']
    'BaseCheckboxGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']
    'BaseChip': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']
    'BaseDropdown': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']
    'BaseDropdownArrow': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']
    'BaseDropdownCheckbox': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']
    'BaseDropdownItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']
    'BaseDropdownLabel': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']
    'BaseDropdownRadioGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']
    'BaseDropdownRadioItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']
    'BaseDropdownSeparator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']
    'BaseDropdownSub': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']
    'BaseField': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']
    'BaseHeading': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']
    'BaseIconBox': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']
    'BaseInput': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']
    'BaseInputFile': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']
    'BaseInputNumber': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']
    'BaseKbd': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']
    'BaseLink': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']
    'BaseList': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']
    'BaseListItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']
    'BaseMessage': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']
    'BasePagination': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']
    'BasePaginationButtonFirst': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']
    'BasePaginationButtonLast': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']
    'BasePaginationButtonNext': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']
    'BasePaginationButtonPrev': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']
    'BasePaginationItems': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']
    'BaseParagraph': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']
    'BasePlaceholderPage': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']
    'BasePlaceload': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']
    'BasePopover': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']
    'BasePrimitiveField': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']
    'BasePrimitiveFieldController': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']
    'BasePrimitiveFieldDescription': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']
    'BasePrimitiveFieldError': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']
    'BasePrimitiveFieldErrorIndicator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']
    'BasePrimitiveFieldLabel': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']
    'BasePrimitiveFieldLoadingIndicator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']
    'BasePrimitiveFieldRequiredIndicator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']
    'BasePrimitiveFieldSuccessIndicator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']
    'BaseProgress': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']
    'BaseProgressCircle': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']
    'BaseProse': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']
    'BaseProviders': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']
    'BaseRadio': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']
    'BaseRadioGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']
    'BaseSelect': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']
    'BaseSelectGroup': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']
    'BaseSelectItem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']
    'BaseSelectLabel': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']
    'BaseSelectSeparator': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']
    'BaseSlider': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']
    'BaseSnack': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']
    'BaseSwitchBall': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']
    'BaseSwitchThin': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']
    'BaseTabs': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']
    'BaseTabsContent': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']
    'BaseTabsTrigger': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']
    'BaseTag': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']
    'BaseText': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']
    'BaseTextarea': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']
    'BaseThemeSwitch': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']
    'BaseThemeSystem': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']
    'BaseThemeToggle': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']
    'BaseToast': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']
    'BaseToastProvider': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']
    'BaseTooltip': typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']
    'NuxtWelcome': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtRouteAnnouncer': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
    'NuxtPicture': typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
    'AccordionContent': typeof import("reka-ui")['AccordionContent']
    'AccordionHeader': typeof import("reka-ui")['AccordionHeader']
    'AccordionItem': typeof import("reka-ui")['AccordionItem']
    'AccordionRoot': typeof import("reka-ui")['AccordionRoot']
    'AccordionTrigger': typeof import("reka-ui")['AccordionTrigger']
    'AlertDialogRoot': typeof import("reka-ui")['AlertDialogRoot']
    'AlertDialogTrigger': typeof import("reka-ui")['AlertDialogTrigger']
    'AlertDialogPortal': typeof import("reka-ui")['AlertDialogPortal']
    'AlertDialogContent': typeof import("reka-ui")['AlertDialogContent']
    'AlertDialogOverlay': typeof import("reka-ui")['AlertDialogOverlay']
    'AlertDialogCancel': typeof import("reka-ui")['AlertDialogCancel']
    'AlertDialogTitle': typeof import("reka-ui")['AlertDialogTitle']
    'AlertDialogDescription': typeof import("reka-ui")['AlertDialogDescription']
    'AlertDialogAction': typeof import("reka-ui")['AlertDialogAction']
    'AspectRatio': typeof import("reka-ui")['AspectRatio']
    'AvatarRoot': typeof import("reka-ui")['AvatarRoot']
    'AvatarFallback': typeof import("reka-ui")['AvatarFallback']
    'AvatarImage': typeof import("reka-ui")['AvatarImage']
    'CalendarRoot': typeof import("reka-ui")['CalendarRoot']
    'CalendarHeader': typeof import("reka-ui")['CalendarHeader']
    'CalendarHeading': typeof import("reka-ui")['CalendarHeading']
    'CalendarGrid': typeof import("reka-ui")['CalendarGrid']
    'CalendarCell': typeof import("reka-ui")['CalendarCell']
    'CalendarHeadCell': typeof import("reka-ui")['CalendarHeadCell']
    'CalendarNext': typeof import("reka-ui")['CalendarNext']
    'CalendarPrev': typeof import("reka-ui")['CalendarPrev']
    'CalendarGridHead': typeof import("reka-ui")['CalendarGridHead']
    'CalendarGridBody': typeof import("reka-ui")['CalendarGridBody']
    'CalendarGridRow': typeof import("reka-ui")['CalendarGridRow']
    'CalendarCellTrigger': typeof import("reka-ui")['CalendarCellTrigger']
    'CheckboxGroupRoot': typeof import("reka-ui")['CheckboxGroupRoot']
    'CheckboxRoot': typeof import("reka-ui")['CheckboxRoot']
    'CheckboxIndicator': typeof import("reka-ui")['CheckboxIndicator']
    'CollapsibleRoot': typeof import("reka-ui")['CollapsibleRoot']
    'CollapsibleTrigger': typeof import("reka-ui")['CollapsibleTrigger']
    'CollapsibleContent': typeof import("reka-ui")['CollapsibleContent']
    'ComboboxRoot': typeof import("reka-ui")['ComboboxRoot']
    'ComboboxInput': typeof import("reka-ui")['ComboboxInput']
    'ComboboxAnchor': typeof import("reka-ui")['ComboboxAnchor']
    'ComboboxEmpty': typeof import("reka-ui")['ComboboxEmpty']
    'ComboboxTrigger': typeof import("reka-ui")['ComboboxTrigger']
    'ComboboxCancel': typeof import("reka-ui")['ComboboxCancel']
    'ComboboxGroup': typeof import("reka-ui")['ComboboxGroup']
    'ComboboxLabel': typeof import("reka-ui")['ComboboxLabel']
    'ComboboxContent': typeof import("reka-ui")['ComboboxContent']
    'ComboboxViewport': typeof import("reka-ui")['ComboboxViewport']
    'ComboboxVirtualizer': typeof import("reka-ui")['ComboboxVirtualizer']
    'ComboboxItem': typeof import("reka-ui")['ComboboxItem']
    'ComboboxItemIndicator': typeof import("reka-ui")['ComboboxItemIndicator']
    'ComboboxSeparator': typeof import("reka-ui")['ComboboxSeparator']
    'ComboboxArrow': typeof import("reka-ui")['ComboboxArrow']
    'ComboboxPortal': typeof import("reka-ui")['ComboboxPortal']
    'ContextMenuRoot': typeof import("reka-ui")['ContextMenuRoot']
    'ContextMenuTrigger': typeof import("reka-ui")['ContextMenuTrigger']
    'ContextMenuPortal': typeof import("reka-ui")['ContextMenuPortal']
    'ContextMenuContent': typeof import("reka-ui")['ContextMenuContent']
    'ContextMenuArrow': typeof import("reka-ui")['ContextMenuArrow']
    'ContextMenuItem': typeof import("reka-ui")['ContextMenuItem']
    'ContextMenuGroup': typeof import("reka-ui")['ContextMenuGroup']
    'ContextMenuSeparator': typeof import("reka-ui")['ContextMenuSeparator']
    'ContextMenuCheckboxItem': typeof import("reka-ui")['ContextMenuCheckboxItem']
    'ContextMenuItemIndicator': typeof import("reka-ui")['ContextMenuItemIndicator']
    'ContextMenuLabel': typeof import("reka-ui")['ContextMenuLabel']
    'ContextMenuRadioGroup': typeof import("reka-ui")['ContextMenuRadioGroup']
    'ContextMenuRadioItem': typeof import("reka-ui")['ContextMenuRadioItem']
    'ContextMenuSub': typeof import("reka-ui")['ContextMenuSub']
    'ContextMenuSubContent': typeof import("reka-ui")['ContextMenuSubContent']
    'ContextMenuSubTrigger': typeof import("reka-ui")['ContextMenuSubTrigger']
    'DateFieldRoot': typeof import("reka-ui")['DateFieldRoot']
    'DateFieldInput': typeof import("reka-ui")['DateFieldInput']
    'DatePickerRoot': typeof import("reka-ui")['DatePickerRoot']
    'DatePickerHeader': typeof import("reka-ui")['DatePickerHeader']
    'DatePickerHeading': typeof import("reka-ui")['DatePickerHeading']
    'DatePickerGrid': typeof import("reka-ui")['DatePickerGrid']
    'DatePickerCell': typeof import("reka-ui")['DatePickerCell']
    'DatePickerHeadCell': typeof import("reka-ui")['DatePickerHeadCell']
    'DatePickerNext': typeof import("reka-ui")['DatePickerNext']
    'DatePickerPrev': typeof import("reka-ui")['DatePickerPrev']
    'DatePickerGridHead': typeof import("reka-ui")['DatePickerGridHead']
    'DatePickerGridBody': typeof import("reka-ui")['DatePickerGridBody']
    'DatePickerGridRow': typeof import("reka-ui")['DatePickerGridRow']
    'DatePickerCellTrigger': typeof import("reka-ui")['DatePickerCellTrigger']
    'DatePickerInput': typeof import("reka-ui")['DatePickerInput']
    'DatePickerCalendar': typeof import("reka-ui")['DatePickerCalendar']
    'DatePickerField': typeof import("reka-ui")['DatePickerField']
    'DatePickerAnchor': typeof import("reka-ui")['DatePickerAnchor']
    'DatePickerArrow': typeof import("reka-ui")['DatePickerArrow']
    'DatePickerClose': typeof import("reka-ui")['DatePickerClose']
    'DatePickerTrigger': typeof import("reka-ui")['DatePickerTrigger']
    'DatePickerContent': typeof import("reka-ui")['DatePickerContent']
    'DateRangePickerRoot': typeof import("reka-ui")['DateRangePickerRoot']
    'DateRangePickerHeader': typeof import("reka-ui")['DateRangePickerHeader']
    'DateRangePickerHeading': typeof import("reka-ui")['DateRangePickerHeading']
    'DateRangePickerGrid': typeof import("reka-ui")['DateRangePickerGrid']
    'DateRangePickerCell': typeof import("reka-ui")['DateRangePickerCell']
    'DateRangePickerHeadCell': typeof import("reka-ui")['DateRangePickerHeadCell']
    'DateRangePickerNext': typeof import("reka-ui")['DateRangePickerNext']
    'DateRangePickerPrev': typeof import("reka-ui")['DateRangePickerPrev']
    'DateRangePickerGridHead': typeof import("reka-ui")['DateRangePickerGridHead']
    'DateRangePickerGridBody': typeof import("reka-ui")['DateRangePickerGridBody']
    'DateRangePickerGridRow': typeof import("reka-ui")['DateRangePickerGridRow']
    'DateRangePickerCellTrigger': typeof import("reka-ui")['DateRangePickerCellTrigger']
    'DateRangePickerInput': typeof import("reka-ui")['DateRangePickerInput']
    'DateRangePickerCalendar': typeof import("reka-ui")['DateRangePickerCalendar']
    'DateRangePickerField': typeof import("reka-ui")['DateRangePickerField']
    'DateRangePickerAnchor': typeof import("reka-ui")['DateRangePickerAnchor']
    'DateRangePickerArrow': typeof import("reka-ui")['DateRangePickerArrow']
    'DateRangePickerClose': typeof import("reka-ui")['DateRangePickerClose']
    'DateRangePickerTrigger': typeof import("reka-ui")['DateRangePickerTrigger']
    'DateRangePickerContent': typeof import("reka-ui")['DateRangePickerContent']
    'DateRangeFieldRoot': typeof import("reka-ui")['DateRangeFieldRoot']
    'DateRangeFieldInput': typeof import("reka-ui")['DateRangeFieldInput']
    'DialogRoot': typeof import("reka-ui")['DialogRoot']
    'DialogTrigger': typeof import("reka-ui")['DialogTrigger']
    'DialogPortal': typeof import("reka-ui")['DialogPortal']
    'DialogContent': typeof import("reka-ui")['DialogContent']
    'DialogOverlay': typeof import("reka-ui")['DialogOverlay']
    'DialogClose': typeof import("reka-ui")['DialogClose']
    'DialogTitle': typeof import("reka-ui")['DialogTitle']
    'DialogDescription': typeof import("reka-ui")['DialogDescription']
    'DropdownMenuRoot': typeof import("reka-ui")['DropdownMenuRoot']
    'DropdownMenuTrigger': typeof import("reka-ui")['DropdownMenuTrigger']
    'DropdownMenuPortal': typeof import("reka-ui")['DropdownMenuPortal']
    'DropdownMenuContent': typeof import("reka-ui")['DropdownMenuContent']
    'DropdownMenuArrow': typeof import("reka-ui")['DropdownMenuArrow']
    'DropdownMenuItem': typeof import("reka-ui")['DropdownMenuItem']
    'DropdownMenuGroup': typeof import("reka-ui")['DropdownMenuGroup']
    'DropdownMenuSeparator': typeof import("reka-ui")['DropdownMenuSeparator']
    'DropdownMenuCheckboxItem': typeof import("reka-ui")['DropdownMenuCheckboxItem']
    'DropdownMenuItemIndicator': typeof import("reka-ui")['DropdownMenuItemIndicator']
    'DropdownMenuLabel': typeof import("reka-ui")['DropdownMenuLabel']
    'DropdownMenuRadioGroup': typeof import("reka-ui")['DropdownMenuRadioGroup']
    'DropdownMenuRadioItem': typeof import("reka-ui")['DropdownMenuRadioItem']
    'DropdownMenuSub': typeof import("reka-ui")['DropdownMenuSub']
    'DropdownMenuSubContent': typeof import("reka-ui")['DropdownMenuSubContent']
    'DropdownMenuSubTrigger': typeof import("reka-ui")['DropdownMenuSubTrigger']
    'EditableRoot': typeof import("reka-ui")['EditableRoot']
    'EditableArea': typeof import("reka-ui")['EditableArea']
    'EditableInput': typeof import("reka-ui")['EditableInput']
    'EditablePreview': typeof import("reka-ui")['EditablePreview']
    'EditableSubmitTrigger': typeof import("reka-ui")['EditableSubmitTrigger']
    'EditableCancelTrigger': typeof import("reka-ui")['EditableCancelTrigger']
    'EditableEditTrigger': typeof import("reka-ui")['EditableEditTrigger']
    'HoverCardRoot': typeof import("reka-ui")['HoverCardRoot']
    'HoverCardTrigger': typeof import("reka-ui")['HoverCardTrigger']
    'HoverCardPortal': typeof import("reka-ui")['HoverCardPortal']
    'HoverCardContent': typeof import("reka-ui")['HoverCardContent']
    'HoverCardArrow': typeof import("reka-ui")['HoverCardArrow']
    'Label': typeof import("reka-ui")['Label']
    'ListboxRoot': typeof import("reka-ui")['ListboxRoot']
    'ListboxContent': typeof import("reka-ui")['ListboxContent']
    'ListboxFilter': typeof import("reka-ui")['ListboxFilter']
    'ListboxItem': typeof import("reka-ui")['ListboxItem']
    'ListboxItemIndicator': typeof import("reka-ui")['ListboxItemIndicator']
    'ListboxVirtualizer': typeof import("reka-ui")['ListboxVirtualizer']
    'ListboxGroup': typeof import("reka-ui")['ListboxGroup']
    'ListboxGroupLabel': typeof import("reka-ui")['ListboxGroupLabel']
    'MenubarRoot': typeof import("reka-ui")['MenubarRoot']
    'MenubarTrigger': typeof import("reka-ui")['MenubarTrigger']
    'MenubarPortal': typeof import("reka-ui")['MenubarPortal']
    'MenubarContent': typeof import("reka-ui")['MenubarContent']
    'MenubarArrow': typeof import("reka-ui")['MenubarArrow']
    'MenubarItem': typeof import("reka-ui")['MenubarItem']
    'MenubarGroup': typeof import("reka-ui")['MenubarGroup']
    'MenubarSeparator': typeof import("reka-ui")['MenubarSeparator']
    'MenubarCheckboxItem': typeof import("reka-ui")['MenubarCheckboxItem']
    'MenubarItemIndicator': typeof import("reka-ui")['MenubarItemIndicator']
    'MenubarLabel': typeof import("reka-ui")['MenubarLabel']
    'MenubarRadioGroup': typeof import("reka-ui")['MenubarRadioGroup']
    'MenubarRadioItem': typeof import("reka-ui")['MenubarRadioItem']
    'MenubarSub': typeof import("reka-ui")['MenubarSub']
    'MenubarSubContent': typeof import("reka-ui")['MenubarSubContent']
    'MenubarSubTrigger': typeof import("reka-ui")['MenubarSubTrigger']
    'MenubarMenu': typeof import("reka-ui")['MenubarMenu']
    'NavigationMenuRoot': typeof import("reka-ui")['NavigationMenuRoot']
    'NavigationMenuContent': typeof import("reka-ui")['NavigationMenuContent']
    'NavigationMenuIndicator': typeof import("reka-ui")['NavigationMenuIndicator']
    'NavigationMenuItem': typeof import("reka-ui")['NavigationMenuItem']
    'NavigationMenuLink': typeof import("reka-ui")['NavigationMenuLink']
    'NavigationMenuList': typeof import("reka-ui")['NavigationMenuList']
    'NavigationMenuSub': typeof import("reka-ui")['NavigationMenuSub']
    'NavigationMenuTrigger': typeof import("reka-ui")['NavigationMenuTrigger']
    'NavigationMenuViewport': typeof import("reka-ui")['NavigationMenuViewport']
    'NumberFieldRoot': typeof import("reka-ui")['NumberFieldRoot']
    'NumberFieldInput': typeof import("reka-ui")['NumberFieldInput']
    'NumberFieldIncrement': typeof import("reka-ui")['NumberFieldIncrement']
    'NumberFieldDecrement': typeof import("reka-ui")['NumberFieldDecrement']
    'PaginationRoot': typeof import("reka-ui")['PaginationRoot']
    'PaginationEllipsis': typeof import("reka-ui")['PaginationEllipsis']
    'PaginationFirst': typeof import("reka-ui")['PaginationFirst']
    'PaginationLast': typeof import("reka-ui")['PaginationLast']
    'PaginationList': typeof import("reka-ui")['PaginationList']
    'PaginationListItem': typeof import("reka-ui")['PaginationListItem']
    'PaginationNext': typeof import("reka-ui")['PaginationNext']
    'PaginationPrev': typeof import("reka-ui")['PaginationPrev']
    'PinInputRoot': typeof import("reka-ui")['PinInputRoot']
    'PinInputInput': typeof import("reka-ui")['PinInputInput']
    'PopoverRoot': typeof import("reka-ui")['PopoverRoot']
    'PopoverTrigger': typeof import("reka-ui")['PopoverTrigger']
    'PopoverPortal': typeof import("reka-ui")['PopoverPortal']
    'PopoverContent': typeof import("reka-ui")['PopoverContent']
    'PopoverArrow': typeof import("reka-ui")['PopoverArrow']
    'PopoverClose': typeof import("reka-ui")['PopoverClose']
    'PopoverAnchor': typeof import("reka-ui")['PopoverAnchor']
    'ProgressRoot': typeof import("reka-ui")['ProgressRoot']
    'ProgressIndicator': typeof import("reka-ui")['ProgressIndicator']
    'RadioGroupRoot': typeof import("reka-ui")['RadioGroupRoot']
    'RadioGroupItem': typeof import("reka-ui")['RadioGroupItem']
    'RadioGroupIndicator': typeof import("reka-ui")['RadioGroupIndicator']
    'RangeCalendarRoot': typeof import("reka-ui")['RangeCalendarRoot']
    'RangeCalendarHeader': typeof import("reka-ui")['RangeCalendarHeader']
    'RangeCalendarHeading': typeof import("reka-ui")['RangeCalendarHeading']
    'RangeCalendarGrid': typeof import("reka-ui")['RangeCalendarGrid']
    'RangeCalendarCell': typeof import("reka-ui")['RangeCalendarCell']
    'RangeCalendarHeadCell': typeof import("reka-ui")['RangeCalendarHeadCell']
    'RangeCalendarNext': typeof import("reka-ui")['RangeCalendarNext']
    'RangeCalendarPrev': typeof import("reka-ui")['RangeCalendarPrev']
    'RangeCalendarGridHead': typeof import("reka-ui")['RangeCalendarGridHead']
    'RangeCalendarGridBody': typeof import("reka-ui")['RangeCalendarGridBody']
    'RangeCalendarGridRow': typeof import("reka-ui")['RangeCalendarGridRow']
    'RangeCalendarCellTrigger': typeof import("reka-ui")['RangeCalendarCellTrigger']
    'ScrollAreaRoot': typeof import("reka-ui")['ScrollAreaRoot']
    'ScrollAreaViewport': typeof import("reka-ui")['ScrollAreaViewport']
    'ScrollAreaScrollbar': typeof import("reka-ui")['ScrollAreaScrollbar']
    'ScrollAreaThumb': typeof import("reka-ui")['ScrollAreaThumb']
    'ScrollAreaCorner': typeof import("reka-ui")['ScrollAreaCorner']
    'SelectRoot': typeof import("reka-ui")['SelectRoot']
    'SelectTrigger': typeof import("reka-ui")['SelectTrigger']
    'SelectPortal': typeof import("reka-ui")['SelectPortal']
    'SelectContent': typeof import("reka-ui")['SelectContent']
    'SelectArrow': typeof import("reka-ui")['SelectArrow']
    'SelectSeparator': typeof import("reka-ui")['SelectSeparator']
    'SelectItemIndicator': typeof import("reka-ui")['SelectItemIndicator']
    'SelectLabel': typeof import("reka-ui")['SelectLabel']
    'SelectGroup': typeof import("reka-ui")['SelectGroup']
    'SelectItem': typeof import("reka-ui")['SelectItem']
    'SelectItemText': typeof import("reka-ui")['SelectItemText']
    'SelectViewport': typeof import("reka-ui")['SelectViewport']
    'SelectScrollUpButton': typeof import("reka-ui")['SelectScrollUpButton']
    'SelectScrollDownButton': typeof import("reka-ui")['SelectScrollDownButton']
    'SelectValue': typeof import("reka-ui")['SelectValue']
    'SelectIcon': typeof import("reka-ui")['SelectIcon']
    'Separator': typeof import("reka-ui")['Separator']
    'SliderRoot': typeof import("reka-ui")['SliderRoot']
    'SliderThumb': typeof import("reka-ui")['SliderThumb']
    'SliderTrack': typeof import("reka-ui")['SliderTrack']
    'SliderRange': typeof import("reka-ui")['SliderRange']
    'SplitterGroup': typeof import("reka-ui")['SplitterGroup']
    'SplitterPanel': typeof import("reka-ui")['SplitterPanel']
    'SplitterResizeHandle': typeof import("reka-ui")['SplitterResizeHandle']
    'StepperRoot': typeof import("reka-ui")['StepperRoot']
    'StepperItem': typeof import("reka-ui")['StepperItem']
    'StepperTrigger': typeof import("reka-ui")['StepperTrigger']
    'StepperDescription': typeof import("reka-ui")['StepperDescription']
    'StepperTitle': typeof import("reka-ui")['StepperTitle']
    'StepperIndicator': typeof import("reka-ui")['StepperIndicator']
    'StepperSeparator': typeof import("reka-ui")['StepperSeparator']
    'SwitchRoot': typeof import("reka-ui")['SwitchRoot']
    'SwitchThumb': typeof import("reka-ui")['SwitchThumb']
    'TabsRoot': typeof import("reka-ui")['TabsRoot']
    'TabsList': typeof import("reka-ui")['TabsList']
    'TabsContent': typeof import("reka-ui")['TabsContent']
    'TabsTrigger': typeof import("reka-ui")['TabsTrigger']
    'TabsIndicator': typeof import("reka-ui")['TabsIndicator']
    'TagsInputRoot': typeof import("reka-ui")['TagsInputRoot']
    'TagsInputInput': typeof import("reka-ui")['TagsInputInput']
    'TagsInputItem': typeof import("reka-ui")['TagsInputItem']
    'TagsInputItemText': typeof import("reka-ui")['TagsInputItemText']
    'TagsInputItemDelete': typeof import("reka-ui")['TagsInputItemDelete']
    'TagsInputClear': typeof import("reka-ui")['TagsInputClear']
    'TimeFieldInput': typeof import("reka-ui")['TimeFieldInput']
    'TimeFieldRoot': typeof import("reka-ui")['TimeFieldRoot']
    'ToastProvider': typeof import("reka-ui")['ToastProvider']
    'ToastRoot': typeof import("reka-ui")['ToastRoot']
    'ToastPortal': typeof import("reka-ui")['ToastPortal']
    'ToastAction': typeof import("reka-ui")['ToastAction']
    'ToastClose': typeof import("reka-ui")['ToastClose']
    'ToastViewport': typeof import("reka-ui")['ToastViewport']
    'ToastTitle': typeof import("reka-ui")['ToastTitle']
    'ToastDescription': typeof import("reka-ui")['ToastDescription']
    'Toggle': typeof import("reka-ui")['Toggle']
    'ToggleGroupRoot': typeof import("reka-ui")['ToggleGroupRoot']
    'ToggleGroupItem': typeof import("reka-ui")['ToggleGroupItem']
    'ToolbarRoot': typeof import("reka-ui")['ToolbarRoot']
    'ToolbarButton': typeof import("reka-ui")['ToolbarButton']
    'ToolbarLink': typeof import("reka-ui")['ToolbarLink']
    'ToolbarToggleGroup': typeof import("reka-ui")['ToolbarToggleGroup']
    'ToolbarToggleItem': typeof import("reka-ui")['ToolbarToggleItem']
    'ToolbarSeparator': typeof import("reka-ui")['ToolbarSeparator']
    'TooltipRoot': typeof import("reka-ui")['TooltipRoot']
    'TooltipTrigger': typeof import("reka-ui")['TooltipTrigger']
    'TooltipContent': typeof import("reka-ui")['TooltipContent']
    'TooltipArrow': typeof import("reka-ui")['TooltipArrow']
    'TooltipPortal': typeof import("reka-ui")['TooltipPortal']
    'TooltipProvider': typeof import("reka-ui")['TooltipProvider']
    'TreeRoot': typeof import("reka-ui")['TreeRoot']
    'TreeItem': typeof import("reka-ui")['TreeItem']
    'TreeVirtualizer': typeof import("reka-ui")['TreeVirtualizer']
    'Viewport': typeof import("reka-ui")['Viewport']
    'ConfigProvider': typeof import("reka-ui")['ConfigProvider']
    'FocusScope': typeof import("reka-ui")['FocusScope']
    'RovingFocusGroup': typeof import("reka-ui")['RovingFocusGroup']
    'RovingFocusItem': typeof import("reka-ui")['RovingFocusItem']
    'Presence': typeof import("reka-ui")['Presence']
    'Primitive': typeof import("reka-ui")['Primitive']
    'Slot': typeof import("reka-ui")['Slot']
    'VisuallyHidden': typeof import("reka-ui")['VisuallyHidden']
    'NuxtLinkLocale': typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'SwitchLocalePathLink': typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'ContentRenderer': typeof import("../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue")['default']
    'MDC': typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue")['default']
    'MDCRenderer': typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue")['default']
    'MDCSlot': typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue")['default']
    'ColorScheme': typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'Icon': typeof import("../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtPage': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyCodeGroup': LazyComponent<typeof import("../app/components/content/CodeGroup.vue")['default']>
    'LazyCodeTimeline': LazyComponent<typeof import("../app/components/content/CodeTimeline.vue")['default']>
    'LazyCodeTimelineItem': LazyComponent<typeof import("../app/components/content/CodeTimelineItem.vue")['default']>
    'LazyDocButton': LazyComponent<typeof import("../app/components/content/DocButton.vue")['default']>
    'LazyDocChecklist': LazyComponent<typeof import("../app/components/content/DocChecklist.vue")['default']>
    'LazyDocComponentDemo': LazyComponent<typeof import("../app/components/content/DocComponentDemo.vue")['default']>
    'LazyDocComponentList': LazyComponent<typeof import("../app/components/content/DocComponentList.vue")['default']>
    'LazyDocComponentMeta': LazyComponent<typeof import("../app/components/content/DocComponentMeta.vue")['default']>
    'LazyDocCustomizerButton': LazyComponent<typeof import("../app/components/content/DocCustomizerButton.vue")['default']>
    'LazyDocGrid': LazyComponent<typeof import("../app/components/content/DocGrid.vue")['default']>
    'LazyDocGridIcon': LazyComponent<typeof import("../app/components/content/DocGridIcon.vue")['default']>
    'LazyDocImage': LazyComponent<typeof import("../app/components/content/DocImage.vue")['default']>
    'LazyDocInfo': LazyComponent<typeof import("../app/components/content/DocInfo.vue")['default']>
    'LazyDocLinker': LazyComponent<typeof import("../app/components/content/DocLinker.vue")['default']>
    'LazyDocMessage': LazyComponent<typeof import("../app/components/content/DocMessage.vue")['default']>
    'LazyDocOverview': LazyComponent<typeof import("../app/components/content/DocOverview.vue")['default']>
    'LazyDocOverviewLayers': LazyComponent<typeof import("../app/components/content/DocOverviewLayers.vue")['default']>
    'LazyDocStacks': LazyComponent<typeof import("../app/components/content/DocStacks.vue")['default']>
    'LazyDocTag': LazyComponent<typeof import("../app/components/content/DocTag.vue")['default']>
    'LazyAddonApexcharts': LazyComponent<typeof import("../app/components/AddonApexcharts.vue")['default']>
    'LazyAddonCollapseTransition': LazyComponent<typeof import("../app/components/AddonCollapseTransition.vue")['default']>
    'LazyAddonDatepicker': LazyComponent<typeof import("../app/components/AddonDatepicker.vue")['default']>
    'LazyAddonInputPassword': LazyComponent<typeof import("../app/components/AddonInputPassword.vue")['default']>
    'LazyAddonInputPhone': LazyComponent<typeof import("../app/components/AddonInputPhone.vue")['default']>
    'LazyAddonLightweightCharts': LazyComponent<typeof import("../app/components/AddonLightweightCharts.vue")['default']>
    'LazyAddonMapboxLocationPicker': LazyComponent<typeof import("../app/components/AddonMapboxLocationPicker.vue")['default']>
    'LazyCodeGroupHeader': LazyComponent<typeof import("../app/components/CodeGroupHeader.vue")['default']>
    'LazyComponentMetaCode': LazyComponent<typeof import("../app/components/ComponentMetaCode.vue")['default']>
    'LazyDemoAccountMenu': LazyComponent<typeof import("../app/components/DemoAccountMenu.vue")['default']>
    'LazyDemoActionText': LazyComponent<typeof import("../app/components/DemoActionText.vue")['default']>
    'LazyDemoActivityTable': LazyComponent<typeof import("../app/components/DemoActivityTable.vue")['default']>
    'LazyDemoAppLayoutSwitcher': LazyComponent<typeof import("../app/components/DemoAppLayoutSwitcher.vue")['default']>
    'LazyDemoAppSearch': LazyComponent<typeof import("../app/components/DemoAppSearch.vue")['default']>
    'LazyDemoAppSearchResult': LazyComponent<typeof import("../app/components/DemoAppSearchResult.vue")['default']>
    'LazyDemoAuthorsListCompact': LazyComponent<typeof import("../app/components/DemoAuthorsListCompact.vue")['default']>
    'LazyDemoCalendarEvent': LazyComponent<typeof import("../app/components/DemoCalendarEvent.vue")['default']>
    'LazyDemoCalendarEventPending': LazyComponent<typeof import("../app/components/DemoCalendarEventPending.vue")['default']>
    'LazyDemoCardFilters': LazyComponent<typeof import("../app/components/DemoCardFilters.vue")['default']>
    'LazyDemoCommentListCompact': LazyComponent<typeof import("../app/components/DemoCommentListCompact.vue")['default']>
    'LazyDemoCompanyOverview': LazyComponent<typeof import("../app/components/DemoCompanyOverview.vue")['default']>
    'LazyDemoCreditCard': LazyComponent<typeof import("../app/components/DemoCreditCard.vue")['default']>
    'LazyDemoCreditCardReal': LazyComponent<typeof import("../app/components/DemoCreditCardReal.vue")['default']>
    'LazyDemoCreditCardSmall': LazyComponent<typeof import("../app/components/DemoCreditCardSmall.vue")['default']>
    'LazyDemoDatepicker': LazyComponent<typeof import("../app/components/DemoDatepicker.vue")['default']>
    'LazyDemoDaysSquare': LazyComponent<typeof import("../app/components/DemoDaysSquare.vue")['default']>
    'LazyDemoFileListTabbed': LazyComponent<typeof import("../app/components/DemoFileListTabbed.vue")['default']>
    'LazyDemoFlexTableCell': LazyComponent<typeof import("../app/components/DemoFlexTableCell.vue")['default']>
    'LazyDemoFlexTableRow': LazyComponent<typeof import("../app/components/DemoFlexTableRow.vue")['default']>
    'LazyDemoFlexTableStart': LazyComponent<typeof import("../app/components/DemoFlexTableStart.vue")['default']>
    'LazyDemoFlexTableWrapper': LazyComponent<typeof import("../app/components/DemoFlexTableWrapper.vue")['default']>
    'LazyDemoFollowersCompact': LazyComponent<typeof import("../app/components/DemoFollowersCompact.vue")['default']>
    'LazyDemoIconLinks': LazyComponent<typeof import("../app/components/DemoIconLinks.vue")['default']>
    'LazyDemoIconText': LazyComponent<typeof import("../app/components/DemoIconText.vue")['default']>
    'LazyDemoIconsSquare': LazyComponent<typeof import("../app/components/DemoIconsSquare.vue")['default']>
    'LazyDemoImageLinks': LazyComponent<typeof import("../app/components/DemoImageLinks.vue")['default']>
    'LazyDemoInboxMessage': LazyComponent<typeof import("../app/components/DemoInboxMessage.vue")['default']>
    'LazyDemoInfoBadges': LazyComponent<typeof import("../app/components/DemoInfoBadges.vue")['default']>
    'LazyDemoInfoImage': LazyComponent<typeof import("../app/components/DemoInfoImage.vue")['default']>
    'LazyDemoLeagueListCompact': LazyComponent<typeof import("../app/components/DemoLeagueListCompact.vue")['default']>
    'LazyDemoLinkArrow': LazyComponent<typeof import("../app/components/DemoLinkArrow.vue")['default']>
    'LazyDemoMapMarker': LazyComponent<typeof import("../app/components/DemoMapMarker.vue")['default']>
    'LazyDemoMenuIconList': LazyComponent<typeof import("../app/components/DemoMenuIconList.vue")['default']>
    'LazyDemoNavigationTop': LazyComponent<typeof import("../app/components/DemoNavigationTop.vue")['default']>
    'LazyDemoNotificationsCompact': LazyComponent<typeof import("../app/components/DemoNotificationsCompact.vue")['default']>
    'LazyDemoOfferCollapse': LazyComponent<typeof import("../app/components/DemoOfferCollapse.vue")['default']>
    'LazyDemoPanelAccount': LazyComponent<typeof import("../app/components/DemoPanelAccount.vue")['default']>
    'LazyDemoPanelActivity': LazyComponent<typeof import("../app/components/DemoPanelActivity.vue")['default']>
    'LazyDemoPanelCard': LazyComponent<typeof import("../app/components/DemoPanelCard.vue")['default']>
    'LazyDemoPanelInvest': LazyComponent<typeof import("../app/components/DemoPanelInvest.vue")['default']>
    'LazyDemoPanelLanguage': LazyComponent<typeof import("../app/components/DemoPanelLanguage.vue")['default']>
    'LazyDemoPanelSearch': LazyComponent<typeof import("../app/components/DemoPanelSearch.vue")['default']>
    'LazyDemoPanelTask': LazyComponent<typeof import("../app/components/DemoPanelTask.vue")['default']>
    'LazyDemoPendingTickets': LazyComponent<typeof import("../app/components/DemoPendingTickets.vue")['default']>
    'LazyDemoPicture': LazyComponent<typeof import("../app/components/DemoPicture.vue")['default']>
    'LazyDemoPlaceholderCompact': LazyComponent<typeof import("../app/components/DemoPlaceholderCompact.vue")['default']>
    'LazyDemoPlaceholderMinimal': LazyComponent<typeof import("../app/components/DemoPlaceholderMinimal.vue")['default']>
    'LazyDemoPopularCryptos': LazyComponent<typeof import("../app/components/DemoPopularCryptos.vue")['default']>
    'LazyDemoProductCompact': LazyComponent<typeof import("../app/components/DemoProductCompact.vue")['default']>
    'LazyDemoProgressCircle': LazyComponent<typeof import("../app/components/DemoProgressCircle.vue")['default']>
    'LazyDemoProjectListCompact': LazyComponent<typeof import("../app/components/DemoProjectListCompact.vue")['default']>
    'LazyDemoSearchCompact': LazyComponent<typeof import("../app/components/DemoSearchCompact.vue")['default']>
    'LazyDemoShoppingCartCompact': LazyComponent<typeof import("../app/components/DemoShoppingCartCompact.vue")['default']>
    'LazyDemoSocialLinks': LazyComponent<typeof import("../app/components/DemoSocialLinks.vue")['default']>
    'LazyDemoStarterSwitcher': LazyComponent<typeof import("../app/components/DemoStarterSwitcher.vue")['default']>
    'LazyDemoSubsidebarMessaging': LazyComponent<typeof import("../app/components/DemoSubsidebarMessaging.vue")['default']>
    'LazyDemoTabbedContent': LazyComponent<typeof import("../app/components/DemoTabbedContent.vue")['default']>
    'LazyDemoTagListCompact': LazyComponent<typeof import("../app/components/DemoTagListCompact.vue")['default']>
    'LazyDemoTeamListCompact': LazyComponent<typeof import("../app/components/DemoTeamListCompact.vue")['default']>
    'LazyDemoTeamSearchCompact': LazyComponent<typeof import("../app/components/DemoTeamSearchCompact.vue")['default']>
    'LazyDemoTimelineCompact': LazyComponent<typeof import("../app/components/DemoTimelineCompact.vue")['default']>
    'LazyDemoTodoListCompact': LazyComponent<typeof import("../app/components/DemoTodoListCompact.vue")['default']>
    'LazyDemoTodoListTabbed': LazyComponent<typeof import("../app/components/DemoTodoListTabbed.vue")['default']>
    'LazyDemoToolbar': LazyComponent<typeof import("../app/components/DemoToolbar.vue")['default']>
    'LazyDemoToolbarTopnav': LazyComponent<typeof import("../app/components/DemoToolbarTopnav.vue")['default']>
    'LazyDemoTopicListCompact': LazyComponent<typeof import("../app/components/DemoTopicListCompact.vue")['default']>
    'LazyDemoTransactionsFilters': LazyComponent<typeof import("../app/components/DemoTransactionsFilters.vue")['default']>
    'LazyDemoTransactionsListPlaceload': LazyComponent<typeof import("../app/components/DemoTransactionsListPlaceload.vue")['default']>
    'LazyDemoTrendingSkills': LazyComponent<typeof import("../app/components/DemoTrendingSkills.vue")['default']>
    'LazyDemoUserList': LazyComponent<typeof import("../app/components/DemoUserList.vue")['default']>
    'LazyDemoVcardRight': LazyComponent<typeof import("../app/components/DemoVcardRight.vue")['default']>
    'LazyDemoVideoCompact': LazyComponent<typeof import("../app/components/DemoVideoCompact.vue")['default']>
    'LazyDemoWidgetAccountBalance': LazyComponent<typeof import("../app/components/DemoWidgetAccountBalance.vue")['default']>
    'LazyDemoWidgetFeatures': LazyComponent<typeof import("../app/components/DemoWidgetFeatures.vue")['default']>
    'LazyDemoWidgetInvest': LazyComponent<typeof import("../app/components/DemoWidgetInvest.vue")['default']>
    'LazyDemoWidgetMoneyIn': LazyComponent<typeof import("../app/components/DemoWidgetMoneyIn.vue")['default']>
    'LazyDemoWidgetMoneyOut': LazyComponent<typeof import("../app/components/DemoWidgetMoneyOut.vue")['default']>
    'LazyDemoWidgetTransactionCompact': LazyComponent<typeof import("../app/components/DemoWidgetTransactionCompact.vue")['default']>
    'LazyDemoWidgetTransactionSummary': LazyComponent<typeof import("../app/components/DemoWidgetTransactionSummary.vue")['default']>
    'LazyDemoWidgetWelcome': LazyComponent<typeof import("../app/components/DemoWidgetWelcome.vue")['default']>
    'LazyDemoWizardButtons': LazyComponent<typeof import("../app/components/DemoWizardButtons.vue")['default']>
    'LazyDemoWizardNavigation': LazyComponent<typeof import("../app/components/DemoWizardNavigation.vue")['default']>
    'LazyDemoWizardStepTitle': LazyComponent<typeof import("../app/components/DemoWizardStepTitle.vue")['default']>
    'LazyDemoWorkspaceDropdown': LazyComponent<typeof import("../app/components/DemoWorkspaceDropdown.vue")['default']>
    'LazyDocLayoutSection': LazyComponent<typeof import("../app/components/DocLayoutSection.vue")['default']>
    'LazyDocSurround': LazyComponent<typeof import("../app/components/DocSurround.vue")['default']>
    'LazyDocToc': LazyComponent<typeof import("../app/components/DocToc.vue")['default']>
    'LazyTairoLogo': LazyComponent<typeof import("../app/components/TairoLogo.vue")['default']>
    'LazyTairoLogoText': LazyComponent<typeof import("../app/components/TairoLogoText.vue")['default']>
    'LazyVectorChartStockOne': LazyComponent<typeof import("../app/components/VectorChartStockOne.vue")['default']>
    'LazyVectorChartStockThree': LazyComponent<typeof import("../app/components/VectorChartStockThree.vue")['default']>
    'LazyVectorChartStockTwo': LazyComponent<typeof import("../app/components/VectorChartStockTwo.vue")['default']>
    'LazyVectorIllustrationBankFront': LazyComponent<typeof import("../app/components/VectorIllustrationBankFront.vue")['default']>
    'LazyVectorIllustrationCalendar': LazyComponent<typeof import("../app/components/VectorIllustrationCalendar.vue")['default']>
    'LazyVectorIllustrationCreditCard': LazyComponent<typeof import("../app/components/VectorIllustrationCreditCard.vue")['default']>
    'LazyVectorIllustrationManWondering': LazyComponent<typeof import("../app/components/VectorIllustrationManWondering.vue")['default']>
    'LazyVectorIllustrationTransaction': LazyComponent<typeof import("../app/components/VectorIllustrationTransaction.vue")['default']>
    'LazyVectorLogoVisa': LazyComponent<typeof import("../app/components/VectorLogoVisa.vue")['default']>
    'LazyDemoChartArea': LazyComponent<typeof import("../app/components/demo-chart/DemoChartArea.vue")['default']>
    'LazyDemoChartAreaBalance': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaBalance.vue")['default']>
    'LazyDemoChartAreaBtcPrice': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaBtcPrice.vue")['default']>
    'LazyDemoChartAreaCondition': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaCondition.vue")['default']>
    'LazyDemoChartAreaCustomers': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaCustomers.vue")['default']>
    'LazyDemoChartAreaExpenses': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaExpenses.vue")['default']>
    'LazyDemoChartAreaIncomeHistory': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaIncomeHistory.vue")['default']>
    'LazyDemoChartAreaInterviews': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaInterviews.vue")['default']>
    'LazyDemoChartAreaMulti': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaMulti.vue")['default']>
    'LazyDemoChartAreaProgress': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaProgress.vue")['default']>
    'LazyDemoChartAreaSparkSalesFour': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesFour.vue")['default']>
    'LazyDemoChartAreaSparkSalesOne': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesOne.vue")['default']>
    'LazyDemoChartAreaSparkSalesThree': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesThree.vue")['default']>
    'LazyDemoChartAreaSparkSalesTwo': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesTwo.vue")['default']>
    'LazyDemoChartAreaStats': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaStats.vue")['default']>
    'LazyDemoChartAreaStockPrice': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaStockPrice.vue")['default']>
    'LazyDemoChartAreaTaskCompletion': LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaTaskCompletion.vue")['default']>
    'LazyDemoChartBar': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBar.vue")['default']>
    'LazyDemoChartBarHorizontal': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarHorizontal.vue")['default']>
    'LazyDemoChartBarHorizontalMulti': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']>
    'LazyDemoChartBarMulti': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarMulti.vue")['default']>
    'LazyDemoChartBarMultiIncome': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarMultiIncome.vue")['default']>
    'LazyDemoChartBarOrders': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarOrders.vue")['default']>
    'LazyDemoChartBarOxygen': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarOxygen.vue")['default']>
    'LazyDemoChartBarProfit': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarProfit.vue")['default']>
    'LazyDemoChartBarRange': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarRange.vue")['default']>
    'LazyDemoChartBarSalesProfit': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarSalesProfit.vue")['default']>
    'LazyDemoChartBarSocialChannels': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarSocialChannels.vue")['default']>
    'LazyDemoChartBarStacked': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarStacked.vue")['default']>
    'LazyDemoChartBarTeamEfficiency': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarTeamEfficiency.vue")['default']>
    'LazyDemoChartBubble': LazyComponent<typeof import("../app/components/demo-chart/DemoChartBubble.vue")['default']>
    'LazyDemoChartDonut': LazyComponent<typeof import("../app/components/demo-chart/DemoChartDonut.vue")['default']>
    'LazyDemoChartDonutExpenses': LazyComponent<typeof import("../app/components/demo-chart/DemoChartDonutExpenses.vue")['default']>
    'LazyDemoChartLine': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLine.vue")['default']>
    'LazyDemoChartLineMulti': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineMulti.vue")['default']>
    'LazyDemoChartLineMultiAlt': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineMultiAlt.vue")['default']>
    'LazyDemoChartLineRevenue': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineRevenue.vue")['default']>
    'LazyDemoChartLineSparkFour': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkFour.vue")['default']>
    'LazyDemoChartLineSparkOne': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkOne.vue")['default']>
    'LazyDemoChartLineSparkThree': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkThree.vue")['default']>
    'LazyDemoChartLineSparkTwo': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkTwo.vue")['default']>
    'LazyDemoChartLineStep': LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineStep.vue")['default']>
    'LazyDemoChartPie': LazyComponent<typeof import("../app/components/demo-chart/DemoChartPie.vue")['default']>
    'LazyDemoChartRadar': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadar.vue")['default']>
    'LazyDemoChartRadial': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadial.vue")['default']>
    'LazyDemoChartRadialEvolution': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialEvolution.vue")['default']>
    'LazyDemoChartRadialGauge': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGauge.vue")['default']>
    'LazyDemoChartRadialGaugeAlt': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']>
    'LazyDemoChartRadialGoal': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGoal.vue")['default']>
    'LazyDemoChartRadialGrowth': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGrowth.vue")['default']>
    'LazyDemoChartRadialMulti': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialMulti.vue")['default']>
    'LazyDemoChartRadialPopularity': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialPopularity.vue")['default']>
    'LazyDemoChartRadialSalesRevenue': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSalesRevenue.vue")['default']>
    'LazyDemoChartRadialSmallOne': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallOne.vue")['default']>
    'LazyDemoChartRadialSmallThree': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallThree.vue")['default']>
    'LazyDemoChartRadialSmallTwo': LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallTwo.vue")['default']>
    'LazyDemoChartScatter': LazyComponent<typeof import("../app/components/demo-chart/DemoChartScatter.vue")['default']>
    'LazyDemoChartScatterEnergy': LazyComponent<typeof import("../app/components/demo-chart/DemoChartScatterEnergy.vue")['default']>
    'LazyDemoChartTimeline': LazyComponent<typeof import("../app/components/demo-chart/DemoChartTimeline.vue")['default']>
    'LazyLandingBenefits': LazyComponent<typeof import("../app/components/landing/LandingBenefits.vue")['default']>
    'LazyLandingContent': LazyComponent<typeof import("../app/components/landing/LandingContent.vue")['default']>
    'LazyLandingCta': LazyComponent<typeof import("../app/components/landing/LandingCta.vue")['default']>
    'LazyLandingCustomizer': LazyComponent<typeof import("../app/components/landing/LandingCustomizer.vue")['default']>
    'LazyLandingDemoLink': LazyComponent<typeof import("../app/components/landing/LandingDemoLink.vue")['default']>
    'LazyLandingDemos': LazyComponent<typeof import("../app/components/landing/LandingDemos.vue")['default']>
    'LazyLandingFeatures': LazyComponent<typeof import("../app/components/landing/LandingFeatures.vue")['default']>
    'LazyLandingFeaturesTile': LazyComponent<typeof import("../app/components/landing/LandingFeaturesTile.vue")['default']>
    'LazyLandingFooter': LazyComponent<typeof import("../app/components/landing/LandingFooter.vue")['default']>
    'LazyLandingHero': LazyComponent<typeof import("../app/components/landing/LandingHero.vue")['default']>
    'LazyLandingHeroMockup': LazyComponent<typeof import("../app/components/landing/LandingHeroMockup.vue")['default']>
    'LazyLandingLayers': LazyComponent<typeof import("../app/components/landing/LandingLayers.vue")['default']>
    'LazyLandingLayersBox': LazyComponent<typeof import("../app/components/landing/LandingLayersBox.vue")['default']>
    'LazyLandingLayout': LazyComponent<typeof import("../app/components/landing/LandingLayout.vue")['default']>
    'LazyLandingLayouts': LazyComponent<typeof import("../app/components/landing/LandingLayouts.vue")['default']>
    'LazyLandingMobileNav': LazyComponent<typeof import("../app/components/landing/LandingMobileNav.vue")['default']>
    'LazyLandingNavbar': LazyComponent<typeof import("../app/components/landing/LandingNavbar.vue")['default']>
    'LazyProseA': LazyComponent<typeof import("../app/components/prose/ProseA.vue")['default']>
    'LazyProseBlockquote': LazyComponent<typeof import("../app/components/prose/ProseBlockquote.vue")['default']>
    'LazyProseCode': LazyComponent<typeof import("../app/components/prose/ProseCode.vue")['default']>
    'LazyProseCodeInline': LazyComponent<typeof import("../app/components/prose/ProseCodeInline.vue")['default']>
    'LazyProseEm': LazyComponent<typeof import("../app/components/prose/ProseEm.vue")['default']>
    'LazyProseH1': LazyComponent<typeof import("../app/components/prose/ProseH1.vue")['default']>
    'LazyProseH2': LazyComponent<typeof import("../app/components/prose/ProseH2.vue")['default']>
    'LazyProseH3': LazyComponent<typeof import("../app/components/prose/ProseH3.vue")['default']>
    'LazyProseH5': LazyComponent<typeof import("../app/components/prose/ProseH5.vue")['default']>
    'LazyProseH6': LazyComponent<typeof import("../app/components/prose/ProseH6.vue")['default']>
    'LazyProseHr': LazyComponent<typeof import("../app/components/prose/ProseHr.vue")['default']>
    'LazyProseImg': LazyComponent<typeof import("../app/components/prose/ProseImg.vue")['default']>
    'LazyProseLi': LazyComponent<typeof import("../app/components/prose/ProseLi.vue")['default']>
    'LazyProseOl': LazyComponent<typeof import("../app/components/prose/ProseOl.vue")['default']>
    'LazyProseP': LazyComponent<typeof import("../app/components/prose/ProseP.vue")['default']>
    'LazyProsePre': LazyComponent<typeof import("../app/components/prose/ProsePre.vue")['default']>
    'LazyProseScript': LazyComponent<typeof import("../app/components/prose/ProseScript.vue")['default']>
    'LazyProseStrong': LazyComponent<typeof import("../app/components/prose/ProseStrong.vue")['default']>
    'LazyProseTable': LazyComponent<typeof import("../app/components/prose/ProseTable.vue")['default']>
    'LazyProseTbody': LazyComponent<typeof import("../app/components/prose/ProseTbody.vue")['default']>
    'LazyProseTd': LazyComponent<typeof import("../app/components/prose/ProseTd.vue")['default']>
    'LazyProseTh': LazyComponent<typeof import("../app/components/prose/ProseTh.vue")['default']>
    'LazyProseThead': LazyComponent<typeof import("../app/components/prose/ProseThead.vue")['default']>
    'LazyProseTr': LazyComponent<typeof import("../app/components/prose/ProseTr.vue")['default']>
    'LazyProseUl': LazyComponent<typeof import("../app/components/prose/ProseUl.vue")['default']>
    'LazyProseH4': LazyComponent<typeof import("../app/components/prose/proseH4.vue")['default']>
    'LazyTairoCheckAnimated': LazyComponent<typeof import("../../layers/tairo/components/TairoCheckAnimated.vue")['default']>
    'LazyTairoCheckboxAnimated': LazyComponent<typeof import("../../layers/tairo/components/TairoCheckboxAnimated.vue")['default']>
    'LazyTairoCheckboxCardIcon': LazyComponent<typeof import("../../layers/tairo/components/TairoCheckboxCardIcon.vue")['default']>
    'LazyTairoContentWrapper': LazyComponent<typeof import("../../layers/tairo/components/TairoContentWrapper.vue")['default']>
    'LazyTairoContentWrapperTabbed': LazyComponent<typeof import("../../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']>
    'LazyTairoError': LazyComponent<typeof import("../../layers/tairo/components/TairoError.vue")['default']>
    'LazyTairoFlexTable': LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTable.vue")['default']>
    'LazyTairoFlexTableCell': LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableCell.vue")['default']>
    'LazyTairoFlexTableHeading': LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableHeading.vue")['default']>
    'LazyTairoFlexTableRow': LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableRow.vue")['default']>
    'LazyTairoFormGroup': LazyComponent<typeof import("../../layers/tairo/components/TairoFormGroup.vue")['default']>
    'LazyTairoFormSave': LazyComponent<typeof import("../../layers/tairo/components/TairoFormSave.vue")['default']>
    'LazyTairoFullscreenDropfile': LazyComponent<typeof import("../../layers/tairo/components/TairoFullscreenDropfile.vue")['default']>
    'LazyTairoImageZoom': LazyComponent<typeof import("../../layers/tairo/components/TairoImageZoom.vue")['default']>
    'LazyTairoInput': LazyComponent<typeof import("../../layers/tairo/components/TairoInput.vue")['default']>
    'LazyTairoInputFileHeadless': LazyComponent<typeof import("../../layers/tairo/components/TairoInputFileHeadless.vue")['default']>
    'LazyTairoMobileDrawer': LazyComponent<typeof import("../../layers/tairo/components/TairoMobileDrawer.vue")['default']>
    'LazyTairoPanels': LazyComponent<typeof import("../../layers/tairo/components/TairoPanels.vue")['default']>
    'LazyTairoRadioCard': LazyComponent<typeof import("../../layers/tairo/components/TairoRadioCard.vue")['default']>
    'LazyTairoSelect': LazyComponent<typeof import("../../layers/tairo/components/TairoSelect.vue")['default']>
    'LazyTairoSelectItem': LazyComponent<typeof import("../../layers/tairo/components/TairoSelectItem.vue")['default']>
    'LazyTairoTable': LazyComponent<typeof import("../../layers/tairo/components/TairoTable.vue")['default']>
    'LazyTairoTableCell': LazyComponent<typeof import("../../layers/tairo/components/TairoTableCell.vue")['default']>
    'LazyTairoTableHeading': LazyComponent<typeof import("../../layers/tairo/components/TairoTableHeading.vue")['default']>
    'LazyTairoTableRow': LazyComponent<typeof import("../../layers/tairo/components/TairoTableRow.vue")['default']>
    'LazyTairoWelcome': LazyComponent<typeof import("../../layers/tairo/components/TairoWelcome.vue")['default']>
    'LazyTairoCollapseBackdrop': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseBackdrop.vue")['default']>
    'LazyTairoCollapseCollapsible': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsible.vue")['default']>
    'LazyTairoCollapseCollapsibleLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']>
    'LazyTairoCollapseCollapsibleTrigger': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']>
    'LazyTairoCollapseContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseContent.vue")['default']>
    'LazyTairoCollapseLayout': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseLayout.vue")['default']>
    'LazyTairoCollapseSidebar': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebar.vue")['default']>
    'LazyTairoCollapseSidebarClose': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']>
    'LazyTairoCollapseSidebarHeader': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']>
    'LazyTairoCollapseSidebarLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']>
    'LazyTairoCollapseSidebarLinks': LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']>
    'LazyTairoMenu': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenu.vue")['default']>
    'LazyTairoMenuContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuContent.vue")['default']>
    'LazyTairoMenuIndicator': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuIndicator.vue")['default']>
    'LazyTairoMenuItem': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuItem.vue")['default']>
    'LazyTairoMenuLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLink.vue")['default']>
    'LazyTairoMenuLinkTab': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLinkTab.vue")['default']>
    'LazyTairoMenuList': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuList.vue")['default']>
    'LazyTairoMenuListItems': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuListItems.vue")['default']>
    'LazyTairoMenuTrigger': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuTrigger.vue")['default']>
    'LazyTairoMenuViewport': LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuViewport.vue")['default']>
    'LazyTairoSidebar': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebar.vue")['default']>
    'LazyTairoSidebarBackdrop': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']>
    'LazyTairoSidebarContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarContent.vue")['default']>
    'LazyTairoSidebarLayout': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLayout.vue")['default']>
    'LazyTairoSidebarLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLink.vue")['default']>
    'LazyTairoSidebarLinks': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLinks.vue")['default']>
    'LazyTairoSidebarNav': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarNav.vue")['default']>
    'LazyTairoSidebarSubsidebar': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsible': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsibleLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsibleTrigger': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']>
    'LazyTairoSidebarSubsidebarContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']>
    'LazyTairoSidebarSubsidebarHeader': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']>
    'LazyTairoSidebarSubsidebarLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']>
    'LazyTairoSidebarTrigger': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarTrigger.vue")['default']>
    'LazyTairoSidenavBackdrop': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']>
    'LazyTairoSidenavCollapsible': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']>
    'LazyTairoSidenavCollapsibleLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']>
    'LazyTairoSidenavCollapsibleTrigger': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']>
    'LazyTairoSidenavContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavContent.vue")['default']>
    'LazyTairoSidenavLayout': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavLayout.vue")['default']>
    'LazyTairoSidenavSidebar': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebar.vue")['default']>
    'LazyTairoSidenavSidebarDivider': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']>
    'LazyTairoSidenavSidebarHeader': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']>
    'LazyTairoSidenavSidebarLink': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']>
    'LazyTairoSidenavSidebarLinks': LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']>
    'LazyTairoTopnavContent': LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavContent.vue")['default']>
    'LazyTairoTopnavHeader': LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavHeader.vue")['default']>
    'LazyTairoTopnavLayout': LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavLayout.vue")['default']>
    'LazyTairoTopnavNavbar': LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavNavbar.vue")['default']>
    'LazyExamplesAddonsDatepicker': LazyComponent<typeof import("../examples/addons/datepicker.vue")['default']>
    'LazyExamplesAddonsMapbox': LazyComponent<typeof import("../examples/addons/mapbox.vue")['default']>
    'LazyExamplesApexchartsBase': LazyComponent<typeof import("../examples/apexcharts/base.vue")['default']>
    'LazyExamplesFlexTableCurved': LazyComponent<typeof import("../examples/flex-table/curved.vue")['default']>
    'LazyExamplesFlexTableRounded': LazyComponent<typeof import("../examples/flex-table/rounded.vue")['default']>
    'LazyExamplesFlexTableSmooth': LazyComponent<typeof import("../examples/flex-table/smooth.vue")['default']>
    'LazyExamplesFlexTableStraight': LazyComponent<typeof import("../examples/flex-table/straight.vue")['default']>
    'LazyExamplesInputPasswordBase': LazyComponent<typeof import("../examples/input-password/base.vue")['default']>
    'LazyExamplesInputPasswordDisabled': LazyComponent<typeof import("../examples/input-password/disabled.vue")['default']>
    'LazyExamplesInputPasswordLocale': LazyComponent<typeof import("../examples/input-password/locale.vue")['default']>
    'LazyExamplesInputPasswordUserInput': LazyComponent<typeof import("../examples/input-password/user-input.vue")['default']>
    'LazyExamplesInputPasswordValidation': LazyComponent<typeof import("../examples/input-password/validation.vue")['default']>
    'LazyExamplesInputPhoneBase': LazyComponent<typeof import("../examples/input-phone/base.vue")['default']>
    'LazyExamplesInputPhoneCountry': LazyComponent<typeof import("../examples/input-phone/country.vue")['default']>
    'LazyExamplesInputPhoneDisabled': LazyComponent<typeof import("../examples/input-phone/disabled.vue")['default']>
    'LazyExamplesInputPhoneFormat': LazyComponent<typeof import("../examples/input-phone/format.vue")['default']>
    'LazyExamplesInputPhoneShape': LazyComponent<typeof import("../examples/input-phone/shape.vue")['default']>
    'LazyExamplesInputPhoneSize': LazyComponent<typeof import("../examples/input-phone/size.vue")['default']>
    'LazyExamplesInputPhoneValidation': LazyComponent<typeof import("../examples/input-phone/validation.vue")['default']>
    'LazyExamplesLightweightChartsBase': LazyComponent<typeof import("../examples/lightweight-charts/base.vue")['default']>
    'LazyExamplesPanelActivity': LazyComponent<typeof import("../examples/panel/activity.vue")['default']>
    'LazyExamplesPanelLanguage': LazyComponent<typeof import("../examples/panel/language.vue")['default']>
    'LazyExamplesPanelSearch': LazyComponent<typeof import("../examples/panel/search.vue")['default']>
    'LazyExamplesPanelTask': LazyComponent<typeof import("../examples/panel/task.vue")['default']>
    'LazyExamplesTableCurved': LazyComponent<typeof import("../examples/table/curved.vue")['default']>
    'LazyExamplesTableMediaCurved': LazyComponent<typeof import("../examples/table/media-curved.vue")['default']>
    'LazyExamplesTableMediaRounded': LazyComponent<typeof import("../examples/table/media-rounded.vue")['default']>
    'LazyExamplesTableMediaSmooth': LazyComponent<typeof import("../examples/table/media-smooth.vue")['default']>
    'LazyExamplesTableMediaStraight': LazyComponent<typeof import("../examples/table/media-straight.vue")['default']>
    'LazyExamplesTableRounded': LazyComponent<typeof import("../examples/table/rounded.vue")['default']>
    'LazyExamplesTableSmooth': LazyComponent<typeof import("../examples/table/smooth.vue")['default']>
    'LazyExamplesTableStraight': LazyComponent<typeof import("../examples/table/straight.vue")['default']>
    'LazyExamplesTairoCheckAnimated': LazyComponent<typeof import("../examples/tairo/check-animated.vue")['default']>
    'LazyExamplesTairoCheckboxAnimated': LazyComponent<typeof import("../examples/tairo/checkbox-animated.vue")['default']>
    'LazyExamplesTairoCheckboxCardIcon': LazyComponent<typeof import("../examples/tairo/checkbox-card-icon.vue")['default']>
    'LazyExamplesTairoCircularMenu': LazyComponent<typeof import("../examples/tairo/circular-menu.vue")['default']>
    'LazyExamplesTairoError': LazyComponent<typeof import("../examples/tairo/error.vue")['default']>
    'LazyExamplesTairoFormGroup': LazyComponent<typeof import("../examples/tairo/form-group.vue")['default']>
    'LazyExamplesTairoFormSave': LazyComponent<typeof import("../examples/tairo/form-save.vue")['default']>
    'LazyExamplesTairoInput': LazyComponent<typeof import("../examples/tairo/input.vue")['default']>
    'LazyExamplesTairoLogo': LazyComponent<typeof import("../examples/tairo/logo.vue")['default']>
    'LazyExamplesTairoLogotext': LazyComponent<typeof import("../examples/tairo/logotext.vue")['default']>
    'LazyExamplesTairoMenuComplete': LazyComponent<typeof import("../examples/tairo/menu-complete.vue")['default']>
    'LazyExamplesTairoMenu': LazyComponent<typeof import("../examples/tairo/menu.vue")['default']>
    'LazyExamplesTairoMobileDrawer': LazyComponent<typeof import("../examples/tairo/mobile-drawer.vue")['default']>
    'LazyExamplesTairoRadioCard': LazyComponent<typeof import("../examples/tairo/radio-card.vue")['default']>
    'LazyExamplesTairoSelect': LazyComponent<typeof import("../examples/tairo/select.vue")['default']>
    'LazyExamplesTairoValidation': LazyComponent<typeof import("../examples/tairo/validation.vue")['default']>
    'LazyBaseAccordion': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']>
    'LazyBaseAccordionItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']>
    'LazyBaseAutocomplete': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']>
    'LazyBaseAutocompleteGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']>
    'LazyBaseAutocompleteItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']>
    'LazyBaseAutocompleteLabel': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']>
    'LazyBaseAutocompleteSeparator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']>
    'LazyBaseAvatar': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']>
    'LazyBaseAvatarGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']>
    'LazyBaseBreadcrumb': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']>
    'LazyBaseButton': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']>
    'LazyBaseCard': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']>
    'LazyBaseCheckbox': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']>
    'LazyBaseCheckboxGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']>
    'LazyBaseChip': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']>
    'LazyBaseDropdown': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']>
    'LazyBaseDropdownArrow': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']>
    'LazyBaseDropdownCheckbox': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']>
    'LazyBaseDropdownItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']>
    'LazyBaseDropdownLabel': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']>
    'LazyBaseDropdownRadioGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']>
    'LazyBaseDropdownRadioItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']>
    'LazyBaseDropdownSeparator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']>
    'LazyBaseDropdownSub': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']>
    'LazyBaseField': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']>
    'LazyBaseHeading': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']>
    'LazyBaseIconBox': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']>
    'LazyBaseInput': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']>
    'LazyBaseInputFile': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']>
    'LazyBaseInputNumber': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']>
    'LazyBaseKbd': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']>
    'LazyBaseLink': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']>
    'LazyBaseList': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']>
    'LazyBaseListItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']>
    'LazyBaseMessage': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']>
    'LazyBasePagination': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']>
    'LazyBasePaginationButtonFirst': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']>
    'LazyBasePaginationButtonLast': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']>
    'LazyBasePaginationButtonNext': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']>
    'LazyBasePaginationButtonPrev': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']>
    'LazyBasePaginationItems': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']>
    'LazyBaseParagraph': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']>
    'LazyBasePlaceholderPage': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']>
    'LazyBasePlaceload': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']>
    'LazyBasePopover': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']>
    'LazyBasePrimitiveField': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']>
    'LazyBasePrimitiveFieldController': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']>
    'LazyBasePrimitiveFieldDescription': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']>
    'LazyBasePrimitiveFieldError': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']>
    'LazyBasePrimitiveFieldErrorIndicator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']>
    'LazyBasePrimitiveFieldLabel': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']>
    'LazyBasePrimitiveFieldLoadingIndicator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']>
    'LazyBasePrimitiveFieldRequiredIndicator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']>
    'LazyBasePrimitiveFieldSuccessIndicator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']>
    'LazyBaseProgress': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']>
    'LazyBaseProgressCircle': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']>
    'LazyBaseProse': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']>
    'LazyBaseProviders': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']>
    'LazyBaseRadio': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']>
    'LazyBaseRadioGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']>
    'LazyBaseSelect': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']>
    'LazyBaseSelectGroup': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']>
    'LazyBaseSelectItem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']>
    'LazyBaseSelectLabel': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']>
    'LazyBaseSelectSeparator': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']>
    'LazyBaseSlider': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']>
    'LazyBaseSnack': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']>
    'LazyBaseSwitchBall': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']>
    'LazyBaseSwitchThin': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']>
    'LazyBaseTabs': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']>
    'LazyBaseTabsContent': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']>
    'LazyBaseTabsTrigger': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']>
    'LazyBaseTag': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']>
    'LazyBaseText': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']>
    'LazyBaseTextarea': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']>
    'LazyBaseThemeSwitch': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']>
    'LazyBaseThemeSystem': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']>
    'LazyBaseThemeToggle': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']>
    'LazyBaseToast': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']>
    'LazyBaseToastProvider': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']>
    'LazyBaseTooltip': LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
    'LazyNuxtPicture': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
    'LazyAccordionContent': LazyComponent<typeof import("reka-ui")['AccordionContent']>
    'LazyAccordionHeader': LazyComponent<typeof import("reka-ui")['AccordionHeader']>
    'LazyAccordionItem': LazyComponent<typeof import("reka-ui")['AccordionItem']>
    'LazyAccordionRoot': LazyComponent<typeof import("reka-ui")['AccordionRoot']>
    'LazyAccordionTrigger': LazyComponent<typeof import("reka-ui")['AccordionTrigger']>
    'LazyAlertDialogRoot': LazyComponent<typeof import("reka-ui")['AlertDialogRoot']>
    'LazyAlertDialogTrigger': LazyComponent<typeof import("reka-ui")['AlertDialogTrigger']>
    'LazyAlertDialogPortal': LazyComponent<typeof import("reka-ui")['AlertDialogPortal']>
    'LazyAlertDialogContent': LazyComponent<typeof import("reka-ui")['AlertDialogContent']>
    'LazyAlertDialogOverlay': LazyComponent<typeof import("reka-ui")['AlertDialogOverlay']>
    'LazyAlertDialogCancel': LazyComponent<typeof import("reka-ui")['AlertDialogCancel']>
    'LazyAlertDialogTitle': LazyComponent<typeof import("reka-ui")['AlertDialogTitle']>
    'LazyAlertDialogDescription': LazyComponent<typeof import("reka-ui")['AlertDialogDescription']>
    'LazyAlertDialogAction': LazyComponent<typeof import("reka-ui")['AlertDialogAction']>
    'LazyAspectRatio': LazyComponent<typeof import("reka-ui")['AspectRatio']>
    'LazyAvatarRoot': LazyComponent<typeof import("reka-ui")['AvatarRoot']>
    'LazyAvatarFallback': LazyComponent<typeof import("reka-ui")['AvatarFallback']>
    'LazyAvatarImage': LazyComponent<typeof import("reka-ui")['AvatarImage']>
    'LazyCalendarRoot': LazyComponent<typeof import("reka-ui")['CalendarRoot']>
    'LazyCalendarHeader': LazyComponent<typeof import("reka-ui")['CalendarHeader']>
    'LazyCalendarHeading': LazyComponent<typeof import("reka-ui")['CalendarHeading']>
    'LazyCalendarGrid': LazyComponent<typeof import("reka-ui")['CalendarGrid']>
    'LazyCalendarCell': LazyComponent<typeof import("reka-ui")['CalendarCell']>
    'LazyCalendarHeadCell': LazyComponent<typeof import("reka-ui")['CalendarHeadCell']>
    'LazyCalendarNext': LazyComponent<typeof import("reka-ui")['CalendarNext']>
    'LazyCalendarPrev': LazyComponent<typeof import("reka-ui")['CalendarPrev']>
    'LazyCalendarGridHead': LazyComponent<typeof import("reka-ui")['CalendarGridHead']>
    'LazyCalendarGridBody': LazyComponent<typeof import("reka-ui")['CalendarGridBody']>
    'LazyCalendarGridRow': LazyComponent<typeof import("reka-ui")['CalendarGridRow']>
    'LazyCalendarCellTrigger': LazyComponent<typeof import("reka-ui")['CalendarCellTrigger']>
    'LazyCheckboxGroupRoot': LazyComponent<typeof import("reka-ui")['CheckboxGroupRoot']>
    'LazyCheckboxRoot': LazyComponent<typeof import("reka-ui")['CheckboxRoot']>
    'LazyCheckboxIndicator': LazyComponent<typeof import("reka-ui")['CheckboxIndicator']>
    'LazyCollapsibleRoot': LazyComponent<typeof import("reka-ui")['CollapsibleRoot']>
    'LazyCollapsibleTrigger': LazyComponent<typeof import("reka-ui")['CollapsibleTrigger']>
    'LazyCollapsibleContent': LazyComponent<typeof import("reka-ui")['CollapsibleContent']>
    'LazyComboboxRoot': LazyComponent<typeof import("reka-ui")['ComboboxRoot']>
    'LazyComboboxInput': LazyComponent<typeof import("reka-ui")['ComboboxInput']>
    'LazyComboboxAnchor': LazyComponent<typeof import("reka-ui")['ComboboxAnchor']>
    'LazyComboboxEmpty': LazyComponent<typeof import("reka-ui")['ComboboxEmpty']>
    'LazyComboboxTrigger': LazyComponent<typeof import("reka-ui")['ComboboxTrigger']>
    'LazyComboboxCancel': LazyComponent<typeof import("reka-ui")['ComboboxCancel']>
    'LazyComboboxGroup': LazyComponent<typeof import("reka-ui")['ComboboxGroup']>
    'LazyComboboxLabel': LazyComponent<typeof import("reka-ui")['ComboboxLabel']>
    'LazyComboboxContent': LazyComponent<typeof import("reka-ui")['ComboboxContent']>
    'LazyComboboxViewport': LazyComponent<typeof import("reka-ui")['ComboboxViewport']>
    'LazyComboboxVirtualizer': LazyComponent<typeof import("reka-ui")['ComboboxVirtualizer']>
    'LazyComboboxItem': LazyComponent<typeof import("reka-ui")['ComboboxItem']>
    'LazyComboboxItemIndicator': LazyComponent<typeof import("reka-ui")['ComboboxItemIndicator']>
    'LazyComboboxSeparator': LazyComponent<typeof import("reka-ui")['ComboboxSeparator']>
    'LazyComboboxArrow': LazyComponent<typeof import("reka-ui")['ComboboxArrow']>
    'LazyComboboxPortal': LazyComponent<typeof import("reka-ui")['ComboboxPortal']>
    'LazyContextMenuRoot': LazyComponent<typeof import("reka-ui")['ContextMenuRoot']>
    'LazyContextMenuTrigger': LazyComponent<typeof import("reka-ui")['ContextMenuTrigger']>
    'LazyContextMenuPortal': LazyComponent<typeof import("reka-ui")['ContextMenuPortal']>
    'LazyContextMenuContent': LazyComponent<typeof import("reka-ui")['ContextMenuContent']>
    'LazyContextMenuArrow': LazyComponent<typeof import("reka-ui")['ContextMenuArrow']>
    'LazyContextMenuItem': LazyComponent<typeof import("reka-ui")['ContextMenuItem']>
    'LazyContextMenuGroup': LazyComponent<typeof import("reka-ui")['ContextMenuGroup']>
    'LazyContextMenuSeparator': LazyComponent<typeof import("reka-ui")['ContextMenuSeparator']>
    'LazyContextMenuCheckboxItem': LazyComponent<typeof import("reka-ui")['ContextMenuCheckboxItem']>
    'LazyContextMenuItemIndicator': LazyComponent<typeof import("reka-ui")['ContextMenuItemIndicator']>
    'LazyContextMenuLabel': LazyComponent<typeof import("reka-ui")['ContextMenuLabel']>
    'LazyContextMenuRadioGroup': LazyComponent<typeof import("reka-ui")['ContextMenuRadioGroup']>
    'LazyContextMenuRadioItem': LazyComponent<typeof import("reka-ui")['ContextMenuRadioItem']>
    'LazyContextMenuSub': LazyComponent<typeof import("reka-ui")['ContextMenuSub']>
    'LazyContextMenuSubContent': LazyComponent<typeof import("reka-ui")['ContextMenuSubContent']>
    'LazyContextMenuSubTrigger': LazyComponent<typeof import("reka-ui")['ContextMenuSubTrigger']>
    'LazyDateFieldRoot': LazyComponent<typeof import("reka-ui")['DateFieldRoot']>
    'LazyDateFieldInput': LazyComponent<typeof import("reka-ui")['DateFieldInput']>
    'LazyDatePickerRoot': LazyComponent<typeof import("reka-ui")['DatePickerRoot']>
    'LazyDatePickerHeader': LazyComponent<typeof import("reka-ui")['DatePickerHeader']>
    'LazyDatePickerHeading': LazyComponent<typeof import("reka-ui")['DatePickerHeading']>
    'LazyDatePickerGrid': LazyComponent<typeof import("reka-ui")['DatePickerGrid']>
    'LazyDatePickerCell': LazyComponent<typeof import("reka-ui")['DatePickerCell']>
    'LazyDatePickerHeadCell': LazyComponent<typeof import("reka-ui")['DatePickerHeadCell']>
    'LazyDatePickerNext': LazyComponent<typeof import("reka-ui")['DatePickerNext']>
    'LazyDatePickerPrev': LazyComponent<typeof import("reka-ui")['DatePickerPrev']>
    'LazyDatePickerGridHead': LazyComponent<typeof import("reka-ui")['DatePickerGridHead']>
    'LazyDatePickerGridBody': LazyComponent<typeof import("reka-ui")['DatePickerGridBody']>
    'LazyDatePickerGridRow': LazyComponent<typeof import("reka-ui")['DatePickerGridRow']>
    'LazyDatePickerCellTrigger': LazyComponent<typeof import("reka-ui")['DatePickerCellTrigger']>
    'LazyDatePickerInput': LazyComponent<typeof import("reka-ui")['DatePickerInput']>
    'LazyDatePickerCalendar': LazyComponent<typeof import("reka-ui")['DatePickerCalendar']>
    'LazyDatePickerField': LazyComponent<typeof import("reka-ui")['DatePickerField']>
    'LazyDatePickerAnchor': LazyComponent<typeof import("reka-ui")['DatePickerAnchor']>
    'LazyDatePickerArrow': LazyComponent<typeof import("reka-ui")['DatePickerArrow']>
    'LazyDatePickerClose': LazyComponent<typeof import("reka-ui")['DatePickerClose']>
    'LazyDatePickerTrigger': LazyComponent<typeof import("reka-ui")['DatePickerTrigger']>
    'LazyDatePickerContent': LazyComponent<typeof import("reka-ui")['DatePickerContent']>
    'LazyDateRangePickerRoot': LazyComponent<typeof import("reka-ui")['DateRangePickerRoot']>
    'LazyDateRangePickerHeader': LazyComponent<typeof import("reka-ui")['DateRangePickerHeader']>
    'LazyDateRangePickerHeading': LazyComponent<typeof import("reka-ui")['DateRangePickerHeading']>
    'LazyDateRangePickerGrid': LazyComponent<typeof import("reka-ui")['DateRangePickerGrid']>
    'LazyDateRangePickerCell': LazyComponent<typeof import("reka-ui")['DateRangePickerCell']>
    'LazyDateRangePickerHeadCell': LazyComponent<typeof import("reka-ui")['DateRangePickerHeadCell']>
    'LazyDateRangePickerNext': LazyComponent<typeof import("reka-ui")['DateRangePickerNext']>
    'LazyDateRangePickerPrev': LazyComponent<typeof import("reka-ui")['DateRangePickerPrev']>
    'LazyDateRangePickerGridHead': LazyComponent<typeof import("reka-ui")['DateRangePickerGridHead']>
    'LazyDateRangePickerGridBody': LazyComponent<typeof import("reka-ui")['DateRangePickerGridBody']>
    'LazyDateRangePickerGridRow': LazyComponent<typeof import("reka-ui")['DateRangePickerGridRow']>
    'LazyDateRangePickerCellTrigger': LazyComponent<typeof import("reka-ui")['DateRangePickerCellTrigger']>
    'LazyDateRangePickerInput': LazyComponent<typeof import("reka-ui")['DateRangePickerInput']>
    'LazyDateRangePickerCalendar': LazyComponent<typeof import("reka-ui")['DateRangePickerCalendar']>
    'LazyDateRangePickerField': LazyComponent<typeof import("reka-ui")['DateRangePickerField']>
    'LazyDateRangePickerAnchor': LazyComponent<typeof import("reka-ui")['DateRangePickerAnchor']>
    'LazyDateRangePickerArrow': LazyComponent<typeof import("reka-ui")['DateRangePickerArrow']>
    'LazyDateRangePickerClose': LazyComponent<typeof import("reka-ui")['DateRangePickerClose']>
    'LazyDateRangePickerTrigger': LazyComponent<typeof import("reka-ui")['DateRangePickerTrigger']>
    'LazyDateRangePickerContent': LazyComponent<typeof import("reka-ui")['DateRangePickerContent']>
    'LazyDateRangeFieldRoot': LazyComponent<typeof import("reka-ui")['DateRangeFieldRoot']>
    'LazyDateRangeFieldInput': LazyComponent<typeof import("reka-ui")['DateRangeFieldInput']>
    'LazyDialogRoot': LazyComponent<typeof import("reka-ui")['DialogRoot']>
    'LazyDialogTrigger': LazyComponent<typeof import("reka-ui")['DialogTrigger']>
    'LazyDialogPortal': LazyComponent<typeof import("reka-ui")['DialogPortal']>
    'LazyDialogContent': LazyComponent<typeof import("reka-ui")['DialogContent']>
    'LazyDialogOverlay': LazyComponent<typeof import("reka-ui")['DialogOverlay']>
    'LazyDialogClose': LazyComponent<typeof import("reka-ui")['DialogClose']>
    'LazyDialogTitle': LazyComponent<typeof import("reka-ui")['DialogTitle']>
    'LazyDialogDescription': LazyComponent<typeof import("reka-ui")['DialogDescription']>
    'LazyDropdownMenuRoot': LazyComponent<typeof import("reka-ui")['DropdownMenuRoot']>
    'LazyDropdownMenuTrigger': LazyComponent<typeof import("reka-ui")['DropdownMenuTrigger']>
    'LazyDropdownMenuPortal': LazyComponent<typeof import("reka-ui")['DropdownMenuPortal']>
    'LazyDropdownMenuContent': LazyComponent<typeof import("reka-ui")['DropdownMenuContent']>
    'LazyDropdownMenuArrow': LazyComponent<typeof import("reka-ui")['DropdownMenuArrow']>
    'LazyDropdownMenuItem': LazyComponent<typeof import("reka-ui")['DropdownMenuItem']>
    'LazyDropdownMenuGroup': LazyComponent<typeof import("reka-ui")['DropdownMenuGroup']>
    'LazyDropdownMenuSeparator': LazyComponent<typeof import("reka-ui")['DropdownMenuSeparator']>
    'LazyDropdownMenuCheckboxItem': LazyComponent<typeof import("reka-ui")['DropdownMenuCheckboxItem']>
    'LazyDropdownMenuItemIndicator': LazyComponent<typeof import("reka-ui")['DropdownMenuItemIndicator']>
    'LazyDropdownMenuLabel': LazyComponent<typeof import("reka-ui")['DropdownMenuLabel']>
    'LazyDropdownMenuRadioGroup': LazyComponent<typeof import("reka-ui")['DropdownMenuRadioGroup']>
    'LazyDropdownMenuRadioItem': LazyComponent<typeof import("reka-ui")['DropdownMenuRadioItem']>
    'LazyDropdownMenuSub': LazyComponent<typeof import("reka-ui")['DropdownMenuSub']>
    'LazyDropdownMenuSubContent': LazyComponent<typeof import("reka-ui")['DropdownMenuSubContent']>
    'LazyDropdownMenuSubTrigger': LazyComponent<typeof import("reka-ui")['DropdownMenuSubTrigger']>
    'LazyEditableRoot': LazyComponent<typeof import("reka-ui")['EditableRoot']>
    'LazyEditableArea': LazyComponent<typeof import("reka-ui")['EditableArea']>
    'LazyEditableInput': LazyComponent<typeof import("reka-ui")['EditableInput']>
    'LazyEditablePreview': LazyComponent<typeof import("reka-ui")['EditablePreview']>
    'LazyEditableSubmitTrigger': LazyComponent<typeof import("reka-ui")['EditableSubmitTrigger']>
    'LazyEditableCancelTrigger': LazyComponent<typeof import("reka-ui")['EditableCancelTrigger']>
    'LazyEditableEditTrigger': LazyComponent<typeof import("reka-ui")['EditableEditTrigger']>
    'LazyHoverCardRoot': LazyComponent<typeof import("reka-ui")['HoverCardRoot']>
    'LazyHoverCardTrigger': LazyComponent<typeof import("reka-ui")['HoverCardTrigger']>
    'LazyHoverCardPortal': LazyComponent<typeof import("reka-ui")['HoverCardPortal']>
    'LazyHoverCardContent': LazyComponent<typeof import("reka-ui")['HoverCardContent']>
    'LazyHoverCardArrow': LazyComponent<typeof import("reka-ui")['HoverCardArrow']>
    'LazyLabel': LazyComponent<typeof import("reka-ui")['Label']>
    'LazyListboxRoot': LazyComponent<typeof import("reka-ui")['ListboxRoot']>
    'LazyListboxContent': LazyComponent<typeof import("reka-ui")['ListboxContent']>
    'LazyListboxFilter': LazyComponent<typeof import("reka-ui")['ListboxFilter']>
    'LazyListboxItem': LazyComponent<typeof import("reka-ui")['ListboxItem']>
    'LazyListboxItemIndicator': LazyComponent<typeof import("reka-ui")['ListboxItemIndicator']>
    'LazyListboxVirtualizer': LazyComponent<typeof import("reka-ui")['ListboxVirtualizer']>
    'LazyListboxGroup': LazyComponent<typeof import("reka-ui")['ListboxGroup']>
    'LazyListboxGroupLabel': LazyComponent<typeof import("reka-ui")['ListboxGroupLabel']>
    'LazyMenubarRoot': LazyComponent<typeof import("reka-ui")['MenubarRoot']>
    'LazyMenubarTrigger': LazyComponent<typeof import("reka-ui")['MenubarTrigger']>
    'LazyMenubarPortal': LazyComponent<typeof import("reka-ui")['MenubarPortal']>
    'LazyMenubarContent': LazyComponent<typeof import("reka-ui")['MenubarContent']>
    'LazyMenubarArrow': LazyComponent<typeof import("reka-ui")['MenubarArrow']>
    'LazyMenubarItem': LazyComponent<typeof import("reka-ui")['MenubarItem']>
    'LazyMenubarGroup': LazyComponent<typeof import("reka-ui")['MenubarGroup']>
    'LazyMenubarSeparator': LazyComponent<typeof import("reka-ui")['MenubarSeparator']>
    'LazyMenubarCheckboxItem': LazyComponent<typeof import("reka-ui")['MenubarCheckboxItem']>
    'LazyMenubarItemIndicator': LazyComponent<typeof import("reka-ui")['MenubarItemIndicator']>
    'LazyMenubarLabel': LazyComponent<typeof import("reka-ui")['MenubarLabel']>
    'LazyMenubarRadioGroup': LazyComponent<typeof import("reka-ui")['MenubarRadioGroup']>
    'LazyMenubarRadioItem': LazyComponent<typeof import("reka-ui")['MenubarRadioItem']>
    'LazyMenubarSub': LazyComponent<typeof import("reka-ui")['MenubarSub']>
    'LazyMenubarSubContent': LazyComponent<typeof import("reka-ui")['MenubarSubContent']>
    'LazyMenubarSubTrigger': LazyComponent<typeof import("reka-ui")['MenubarSubTrigger']>
    'LazyMenubarMenu': LazyComponent<typeof import("reka-ui")['MenubarMenu']>
    'LazyNavigationMenuRoot': LazyComponent<typeof import("reka-ui")['NavigationMenuRoot']>
    'LazyNavigationMenuContent': LazyComponent<typeof import("reka-ui")['NavigationMenuContent']>
    'LazyNavigationMenuIndicator': LazyComponent<typeof import("reka-ui")['NavigationMenuIndicator']>
    'LazyNavigationMenuItem': LazyComponent<typeof import("reka-ui")['NavigationMenuItem']>
    'LazyNavigationMenuLink': LazyComponent<typeof import("reka-ui")['NavigationMenuLink']>
    'LazyNavigationMenuList': LazyComponent<typeof import("reka-ui")['NavigationMenuList']>
    'LazyNavigationMenuSub': LazyComponent<typeof import("reka-ui")['NavigationMenuSub']>
    'LazyNavigationMenuTrigger': LazyComponent<typeof import("reka-ui")['NavigationMenuTrigger']>
    'LazyNavigationMenuViewport': LazyComponent<typeof import("reka-ui")['NavigationMenuViewport']>
    'LazyNumberFieldRoot': LazyComponent<typeof import("reka-ui")['NumberFieldRoot']>
    'LazyNumberFieldInput': LazyComponent<typeof import("reka-ui")['NumberFieldInput']>
    'LazyNumberFieldIncrement': LazyComponent<typeof import("reka-ui")['NumberFieldIncrement']>
    'LazyNumberFieldDecrement': LazyComponent<typeof import("reka-ui")['NumberFieldDecrement']>
    'LazyPaginationRoot': LazyComponent<typeof import("reka-ui")['PaginationRoot']>
    'LazyPaginationEllipsis': LazyComponent<typeof import("reka-ui")['PaginationEllipsis']>
    'LazyPaginationFirst': LazyComponent<typeof import("reka-ui")['PaginationFirst']>
    'LazyPaginationLast': LazyComponent<typeof import("reka-ui")['PaginationLast']>
    'LazyPaginationList': LazyComponent<typeof import("reka-ui")['PaginationList']>
    'LazyPaginationListItem': LazyComponent<typeof import("reka-ui")['PaginationListItem']>
    'LazyPaginationNext': LazyComponent<typeof import("reka-ui")['PaginationNext']>
    'LazyPaginationPrev': LazyComponent<typeof import("reka-ui")['PaginationPrev']>
    'LazyPinInputRoot': LazyComponent<typeof import("reka-ui")['PinInputRoot']>
    'LazyPinInputInput': LazyComponent<typeof import("reka-ui")['PinInputInput']>
    'LazyPopoverRoot': LazyComponent<typeof import("reka-ui")['PopoverRoot']>
    'LazyPopoverTrigger': LazyComponent<typeof import("reka-ui")['PopoverTrigger']>
    'LazyPopoverPortal': LazyComponent<typeof import("reka-ui")['PopoverPortal']>
    'LazyPopoverContent': LazyComponent<typeof import("reka-ui")['PopoverContent']>
    'LazyPopoverArrow': LazyComponent<typeof import("reka-ui")['PopoverArrow']>
    'LazyPopoverClose': LazyComponent<typeof import("reka-ui")['PopoverClose']>
    'LazyPopoverAnchor': LazyComponent<typeof import("reka-ui")['PopoverAnchor']>
    'LazyProgressRoot': LazyComponent<typeof import("reka-ui")['ProgressRoot']>
    'LazyProgressIndicator': LazyComponent<typeof import("reka-ui")['ProgressIndicator']>
    'LazyRadioGroupRoot': LazyComponent<typeof import("reka-ui")['RadioGroupRoot']>
    'LazyRadioGroupItem': LazyComponent<typeof import("reka-ui")['RadioGroupItem']>
    'LazyRadioGroupIndicator': LazyComponent<typeof import("reka-ui")['RadioGroupIndicator']>
    'LazyRangeCalendarRoot': LazyComponent<typeof import("reka-ui")['RangeCalendarRoot']>
    'LazyRangeCalendarHeader': LazyComponent<typeof import("reka-ui")['RangeCalendarHeader']>
    'LazyRangeCalendarHeading': LazyComponent<typeof import("reka-ui")['RangeCalendarHeading']>
    'LazyRangeCalendarGrid': LazyComponent<typeof import("reka-ui")['RangeCalendarGrid']>
    'LazyRangeCalendarCell': LazyComponent<typeof import("reka-ui")['RangeCalendarCell']>
    'LazyRangeCalendarHeadCell': LazyComponent<typeof import("reka-ui")['RangeCalendarHeadCell']>
    'LazyRangeCalendarNext': LazyComponent<typeof import("reka-ui")['RangeCalendarNext']>
    'LazyRangeCalendarPrev': LazyComponent<typeof import("reka-ui")['RangeCalendarPrev']>
    'LazyRangeCalendarGridHead': LazyComponent<typeof import("reka-ui")['RangeCalendarGridHead']>
    'LazyRangeCalendarGridBody': LazyComponent<typeof import("reka-ui")['RangeCalendarGridBody']>
    'LazyRangeCalendarGridRow': LazyComponent<typeof import("reka-ui")['RangeCalendarGridRow']>
    'LazyRangeCalendarCellTrigger': LazyComponent<typeof import("reka-ui")['RangeCalendarCellTrigger']>
    'LazyScrollAreaRoot': LazyComponent<typeof import("reka-ui")['ScrollAreaRoot']>
    'LazyScrollAreaViewport': LazyComponent<typeof import("reka-ui")['ScrollAreaViewport']>
    'LazyScrollAreaScrollbar': LazyComponent<typeof import("reka-ui")['ScrollAreaScrollbar']>
    'LazyScrollAreaThumb': LazyComponent<typeof import("reka-ui")['ScrollAreaThumb']>
    'LazyScrollAreaCorner': LazyComponent<typeof import("reka-ui")['ScrollAreaCorner']>
    'LazySelectRoot': LazyComponent<typeof import("reka-ui")['SelectRoot']>
    'LazySelectTrigger': LazyComponent<typeof import("reka-ui")['SelectTrigger']>
    'LazySelectPortal': LazyComponent<typeof import("reka-ui")['SelectPortal']>
    'LazySelectContent': LazyComponent<typeof import("reka-ui")['SelectContent']>
    'LazySelectArrow': LazyComponent<typeof import("reka-ui")['SelectArrow']>
    'LazySelectSeparator': LazyComponent<typeof import("reka-ui")['SelectSeparator']>
    'LazySelectItemIndicator': LazyComponent<typeof import("reka-ui")['SelectItemIndicator']>
    'LazySelectLabel': LazyComponent<typeof import("reka-ui")['SelectLabel']>
    'LazySelectGroup': LazyComponent<typeof import("reka-ui")['SelectGroup']>
    'LazySelectItem': LazyComponent<typeof import("reka-ui")['SelectItem']>
    'LazySelectItemText': LazyComponent<typeof import("reka-ui")['SelectItemText']>
    'LazySelectViewport': LazyComponent<typeof import("reka-ui")['SelectViewport']>
    'LazySelectScrollUpButton': LazyComponent<typeof import("reka-ui")['SelectScrollUpButton']>
    'LazySelectScrollDownButton': LazyComponent<typeof import("reka-ui")['SelectScrollDownButton']>
    'LazySelectValue': LazyComponent<typeof import("reka-ui")['SelectValue']>
    'LazySelectIcon': LazyComponent<typeof import("reka-ui")['SelectIcon']>
    'LazySeparator': LazyComponent<typeof import("reka-ui")['Separator']>
    'LazySliderRoot': LazyComponent<typeof import("reka-ui")['SliderRoot']>
    'LazySliderThumb': LazyComponent<typeof import("reka-ui")['SliderThumb']>
    'LazySliderTrack': LazyComponent<typeof import("reka-ui")['SliderTrack']>
    'LazySliderRange': LazyComponent<typeof import("reka-ui")['SliderRange']>
    'LazySplitterGroup': LazyComponent<typeof import("reka-ui")['SplitterGroup']>
    'LazySplitterPanel': LazyComponent<typeof import("reka-ui")['SplitterPanel']>
    'LazySplitterResizeHandle': LazyComponent<typeof import("reka-ui")['SplitterResizeHandle']>
    'LazyStepperRoot': LazyComponent<typeof import("reka-ui")['StepperRoot']>
    'LazyStepperItem': LazyComponent<typeof import("reka-ui")['StepperItem']>
    'LazyStepperTrigger': LazyComponent<typeof import("reka-ui")['StepperTrigger']>
    'LazyStepperDescription': LazyComponent<typeof import("reka-ui")['StepperDescription']>
    'LazyStepperTitle': LazyComponent<typeof import("reka-ui")['StepperTitle']>
    'LazyStepperIndicator': LazyComponent<typeof import("reka-ui")['StepperIndicator']>
    'LazyStepperSeparator': LazyComponent<typeof import("reka-ui")['StepperSeparator']>
    'LazySwitchRoot': LazyComponent<typeof import("reka-ui")['SwitchRoot']>
    'LazySwitchThumb': LazyComponent<typeof import("reka-ui")['SwitchThumb']>
    'LazyTabsRoot': LazyComponent<typeof import("reka-ui")['TabsRoot']>
    'LazyTabsList': LazyComponent<typeof import("reka-ui")['TabsList']>
    'LazyTabsContent': LazyComponent<typeof import("reka-ui")['TabsContent']>
    'LazyTabsTrigger': LazyComponent<typeof import("reka-ui")['TabsTrigger']>
    'LazyTabsIndicator': LazyComponent<typeof import("reka-ui")['TabsIndicator']>
    'LazyTagsInputRoot': LazyComponent<typeof import("reka-ui")['TagsInputRoot']>
    'LazyTagsInputInput': LazyComponent<typeof import("reka-ui")['TagsInputInput']>
    'LazyTagsInputItem': LazyComponent<typeof import("reka-ui")['TagsInputItem']>
    'LazyTagsInputItemText': LazyComponent<typeof import("reka-ui")['TagsInputItemText']>
    'LazyTagsInputItemDelete': LazyComponent<typeof import("reka-ui")['TagsInputItemDelete']>
    'LazyTagsInputClear': LazyComponent<typeof import("reka-ui")['TagsInputClear']>
    'LazyTimeFieldInput': LazyComponent<typeof import("reka-ui")['TimeFieldInput']>
    'LazyTimeFieldRoot': LazyComponent<typeof import("reka-ui")['TimeFieldRoot']>
    'LazyToastProvider': LazyComponent<typeof import("reka-ui")['ToastProvider']>
    'LazyToastRoot': LazyComponent<typeof import("reka-ui")['ToastRoot']>
    'LazyToastPortal': LazyComponent<typeof import("reka-ui")['ToastPortal']>
    'LazyToastAction': LazyComponent<typeof import("reka-ui")['ToastAction']>
    'LazyToastClose': LazyComponent<typeof import("reka-ui")['ToastClose']>
    'LazyToastViewport': LazyComponent<typeof import("reka-ui")['ToastViewport']>
    'LazyToastTitle': LazyComponent<typeof import("reka-ui")['ToastTitle']>
    'LazyToastDescription': LazyComponent<typeof import("reka-ui")['ToastDescription']>
    'LazyToggle': LazyComponent<typeof import("reka-ui")['Toggle']>
    'LazyToggleGroupRoot': LazyComponent<typeof import("reka-ui")['ToggleGroupRoot']>
    'LazyToggleGroupItem': LazyComponent<typeof import("reka-ui")['ToggleGroupItem']>
    'LazyToolbarRoot': LazyComponent<typeof import("reka-ui")['ToolbarRoot']>
    'LazyToolbarButton': LazyComponent<typeof import("reka-ui")['ToolbarButton']>
    'LazyToolbarLink': LazyComponent<typeof import("reka-ui")['ToolbarLink']>
    'LazyToolbarToggleGroup': LazyComponent<typeof import("reka-ui")['ToolbarToggleGroup']>
    'LazyToolbarToggleItem': LazyComponent<typeof import("reka-ui")['ToolbarToggleItem']>
    'LazyToolbarSeparator': LazyComponent<typeof import("reka-ui")['ToolbarSeparator']>
    'LazyTooltipRoot': LazyComponent<typeof import("reka-ui")['TooltipRoot']>
    'LazyTooltipTrigger': LazyComponent<typeof import("reka-ui")['TooltipTrigger']>
    'LazyTooltipContent': LazyComponent<typeof import("reka-ui")['TooltipContent']>
    'LazyTooltipArrow': LazyComponent<typeof import("reka-ui")['TooltipArrow']>
    'LazyTooltipPortal': LazyComponent<typeof import("reka-ui")['TooltipPortal']>
    'LazyTooltipProvider': LazyComponent<typeof import("reka-ui")['TooltipProvider']>
    'LazyTreeRoot': LazyComponent<typeof import("reka-ui")['TreeRoot']>
    'LazyTreeItem': LazyComponent<typeof import("reka-ui")['TreeItem']>
    'LazyTreeVirtualizer': LazyComponent<typeof import("reka-ui")['TreeVirtualizer']>
    'LazyViewport': LazyComponent<typeof import("reka-ui")['Viewport']>
    'LazyConfigProvider': LazyComponent<typeof import("reka-ui")['ConfigProvider']>
    'LazyFocusScope': LazyComponent<typeof import("reka-ui")['FocusScope']>
    'LazyRovingFocusGroup': LazyComponent<typeof import("reka-ui")['RovingFocusGroup']>
    'LazyRovingFocusItem': LazyComponent<typeof import("reka-ui")['RovingFocusItem']>
    'LazyPresence': LazyComponent<typeof import("reka-ui")['Presence']>
    'LazyPrimitive': LazyComponent<typeof import("reka-ui")['Primitive']>
    'LazySlot': LazyComponent<typeof import("reka-ui")['Slot']>
    'LazyVisuallyHidden': LazyComponent<typeof import("reka-ui")['VisuallyHidden']>
    'LazyNuxtLinkLocale': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
    'LazySwitchLocalePathLink': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
    'LazyContentRenderer': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue")['default']>
    'LazyMDC': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue")['default']>
    'LazyMDCRenderer': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue")['default']>
    'LazyMDCSlot': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue")['default']>
    'LazyColorScheme': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const CodeGroup: typeof import("../app/components/content/CodeGroup.vue")['default']
export const CodeTimeline: typeof import("../app/components/content/CodeTimeline.vue")['default']
export const CodeTimelineItem: typeof import("../app/components/content/CodeTimelineItem.vue")['default']
export const DocButton: typeof import("../app/components/content/DocButton.vue")['default']
export const DocChecklist: typeof import("../app/components/content/DocChecklist.vue")['default']
export const DocComponentDemo: typeof import("../app/components/content/DocComponentDemo.vue")['default']
export const DocComponentList: typeof import("../app/components/content/DocComponentList.vue")['default']
export const DocComponentMeta: typeof import("../app/components/content/DocComponentMeta.vue")['default']
export const DocCustomizerButton: typeof import("../app/components/content/DocCustomizerButton.vue")['default']
export const DocGrid: typeof import("../app/components/content/DocGrid.vue")['default']
export const DocGridIcon: typeof import("../app/components/content/DocGridIcon.vue")['default']
export const DocImage: typeof import("../app/components/content/DocImage.vue")['default']
export const DocInfo: typeof import("../app/components/content/DocInfo.vue")['default']
export const DocLinker: typeof import("../app/components/content/DocLinker.vue")['default']
export const DocMessage: typeof import("../app/components/content/DocMessage.vue")['default']
export const DocOverview: typeof import("../app/components/content/DocOverview.vue")['default']
export const DocOverviewLayers: typeof import("../app/components/content/DocOverviewLayers.vue")['default']
export const DocStacks: typeof import("../app/components/content/DocStacks.vue")['default']
export const DocTag: typeof import("../app/components/content/DocTag.vue")['default']
export const AddonApexcharts: typeof import("../app/components/AddonApexcharts.vue")['default']
export const AddonCollapseTransition: typeof import("../app/components/AddonCollapseTransition.vue")['default']
export const AddonDatepicker: typeof import("../app/components/AddonDatepicker.vue")['default']
export const AddonInputPassword: typeof import("../app/components/AddonInputPassword.vue")['default']
export const AddonInputPhone: typeof import("../app/components/AddonInputPhone.vue")['default']
export const AddonLightweightCharts: typeof import("../app/components/AddonLightweightCharts.vue")['default']
export const AddonMapboxLocationPicker: typeof import("../app/components/AddonMapboxLocationPicker.vue")['default']
export const CodeGroupHeader: typeof import("../app/components/CodeGroupHeader.vue")['default']
export const ComponentMetaCode: typeof import("../app/components/ComponentMetaCode.vue")['default']
export const DemoAccountMenu: typeof import("../app/components/DemoAccountMenu.vue")['default']
export const DemoActionText: typeof import("../app/components/DemoActionText.vue")['default']
export const DemoActivityTable: typeof import("../app/components/DemoActivityTable.vue")['default']
export const DemoAppLayoutSwitcher: typeof import("../app/components/DemoAppLayoutSwitcher.vue")['default']
export const DemoAppSearch: typeof import("../app/components/DemoAppSearch.vue")['default']
export const DemoAppSearchResult: typeof import("../app/components/DemoAppSearchResult.vue")['default']
export const DemoAuthorsListCompact: typeof import("../app/components/DemoAuthorsListCompact.vue")['default']
export const DemoCalendarEvent: typeof import("../app/components/DemoCalendarEvent.vue")['default']
export const DemoCalendarEventPending: typeof import("../app/components/DemoCalendarEventPending.vue")['default']
export const DemoCardFilters: typeof import("../app/components/DemoCardFilters.vue")['default']
export const DemoCommentListCompact: typeof import("../app/components/DemoCommentListCompact.vue")['default']
export const DemoCompanyOverview: typeof import("../app/components/DemoCompanyOverview.vue")['default']
export const DemoCreditCard: typeof import("../app/components/DemoCreditCard.vue")['default']
export const DemoCreditCardReal: typeof import("../app/components/DemoCreditCardReal.vue")['default']
export const DemoCreditCardSmall: typeof import("../app/components/DemoCreditCardSmall.vue")['default']
export const DemoDatepicker: typeof import("../app/components/DemoDatepicker.vue")['default']
export const DemoDaysSquare: typeof import("../app/components/DemoDaysSquare.vue")['default']
export const DemoFileListTabbed: typeof import("../app/components/DemoFileListTabbed.vue")['default']
export const DemoFlexTableCell: typeof import("../app/components/DemoFlexTableCell.vue")['default']
export const DemoFlexTableRow: typeof import("../app/components/DemoFlexTableRow.vue")['default']
export const DemoFlexTableStart: typeof import("../app/components/DemoFlexTableStart.vue")['default']
export const DemoFlexTableWrapper: typeof import("../app/components/DemoFlexTableWrapper.vue")['default']
export const DemoFollowersCompact: typeof import("../app/components/DemoFollowersCompact.vue")['default']
export const DemoIconLinks: typeof import("../app/components/DemoIconLinks.vue")['default']
export const DemoIconText: typeof import("../app/components/DemoIconText.vue")['default']
export const DemoIconsSquare: typeof import("../app/components/DemoIconsSquare.vue")['default']
export const DemoImageLinks: typeof import("../app/components/DemoImageLinks.vue")['default']
export const DemoInboxMessage: typeof import("../app/components/DemoInboxMessage.vue")['default']
export const DemoInfoBadges: typeof import("../app/components/DemoInfoBadges.vue")['default']
export const DemoInfoImage: typeof import("../app/components/DemoInfoImage.vue")['default']
export const DemoLeagueListCompact: typeof import("../app/components/DemoLeagueListCompact.vue")['default']
export const DemoLinkArrow: typeof import("../app/components/DemoLinkArrow.vue")['default']
export const DemoMapMarker: typeof import("../app/components/DemoMapMarker.vue")['default']
export const DemoMenuIconList: typeof import("../app/components/DemoMenuIconList.vue")['default']
export const DemoNavigationTop: typeof import("../app/components/DemoNavigationTop.vue")['default']
export const DemoNotificationsCompact: typeof import("../app/components/DemoNotificationsCompact.vue")['default']
export const DemoOfferCollapse: typeof import("../app/components/DemoOfferCollapse.vue")['default']
export const DemoPanelAccount: typeof import("../app/components/DemoPanelAccount.vue")['default']
export const DemoPanelActivity: typeof import("../app/components/DemoPanelActivity.vue")['default']
export const DemoPanelCard: typeof import("../app/components/DemoPanelCard.vue")['default']
export const DemoPanelInvest: typeof import("../app/components/DemoPanelInvest.vue")['default']
export const DemoPanelLanguage: typeof import("../app/components/DemoPanelLanguage.vue")['default']
export const DemoPanelSearch: typeof import("../app/components/DemoPanelSearch.vue")['default']
export const DemoPanelTask: typeof import("../app/components/DemoPanelTask.vue")['default']
export const DemoPendingTickets: typeof import("../app/components/DemoPendingTickets.vue")['default']
export const DemoPicture: typeof import("../app/components/DemoPicture.vue")['default']
export const DemoPlaceholderCompact: typeof import("../app/components/DemoPlaceholderCompact.vue")['default']
export const DemoPlaceholderMinimal: typeof import("../app/components/DemoPlaceholderMinimal.vue")['default']
export const DemoPopularCryptos: typeof import("../app/components/DemoPopularCryptos.vue")['default']
export const DemoProductCompact: typeof import("../app/components/DemoProductCompact.vue")['default']
export const DemoProgressCircle: typeof import("../app/components/DemoProgressCircle.vue")['default']
export const DemoProjectListCompact: typeof import("../app/components/DemoProjectListCompact.vue")['default']
export const DemoSearchCompact: typeof import("../app/components/DemoSearchCompact.vue")['default']
export const DemoShoppingCartCompact: typeof import("../app/components/DemoShoppingCartCompact.vue")['default']
export const DemoSocialLinks: typeof import("../app/components/DemoSocialLinks.vue")['default']
export const DemoStarterSwitcher: typeof import("../app/components/DemoStarterSwitcher.vue")['default']
export const DemoSubsidebarMessaging: typeof import("../app/components/DemoSubsidebarMessaging.vue")['default']
export const DemoTabbedContent: typeof import("../app/components/DemoTabbedContent.vue")['default']
export const DemoTagListCompact: typeof import("../app/components/DemoTagListCompact.vue")['default']
export const DemoTeamListCompact: typeof import("../app/components/DemoTeamListCompact.vue")['default']
export const DemoTeamSearchCompact: typeof import("../app/components/DemoTeamSearchCompact.vue")['default']
export const DemoTimelineCompact: typeof import("../app/components/DemoTimelineCompact.vue")['default']
export const DemoTodoListCompact: typeof import("../app/components/DemoTodoListCompact.vue")['default']
export const DemoTodoListTabbed: typeof import("../app/components/DemoTodoListTabbed.vue")['default']
export const DemoToolbar: typeof import("../app/components/DemoToolbar.vue")['default']
export const DemoToolbarTopnav: typeof import("../app/components/DemoToolbarTopnav.vue")['default']
export const DemoTopicListCompact: typeof import("../app/components/DemoTopicListCompact.vue")['default']
export const DemoTransactionsFilters: typeof import("../app/components/DemoTransactionsFilters.vue")['default']
export const DemoTransactionsListPlaceload: typeof import("../app/components/DemoTransactionsListPlaceload.vue")['default']
export const DemoTrendingSkills: typeof import("../app/components/DemoTrendingSkills.vue")['default']
export const DemoUserList: typeof import("../app/components/DemoUserList.vue")['default']
export const DemoVcardRight: typeof import("../app/components/DemoVcardRight.vue")['default']
export const DemoVideoCompact: typeof import("../app/components/DemoVideoCompact.vue")['default']
export const DemoWidgetAccountBalance: typeof import("../app/components/DemoWidgetAccountBalance.vue")['default']
export const DemoWidgetFeatures: typeof import("../app/components/DemoWidgetFeatures.vue")['default']
export const DemoWidgetInvest: typeof import("../app/components/DemoWidgetInvest.vue")['default']
export const DemoWidgetMoneyIn: typeof import("../app/components/DemoWidgetMoneyIn.vue")['default']
export const DemoWidgetMoneyOut: typeof import("../app/components/DemoWidgetMoneyOut.vue")['default']
export const DemoWidgetTransactionCompact: typeof import("../app/components/DemoWidgetTransactionCompact.vue")['default']
export const DemoWidgetTransactionSummary: typeof import("../app/components/DemoWidgetTransactionSummary.vue")['default']
export const DemoWidgetWelcome: typeof import("../app/components/DemoWidgetWelcome.vue")['default']
export const DemoWizardButtons: typeof import("../app/components/DemoWizardButtons.vue")['default']
export const DemoWizardNavigation: typeof import("../app/components/DemoWizardNavigation.vue")['default']
export const DemoWizardStepTitle: typeof import("../app/components/DemoWizardStepTitle.vue")['default']
export const DemoWorkspaceDropdown: typeof import("../app/components/DemoWorkspaceDropdown.vue")['default']
export const DocLayoutSection: typeof import("../app/components/DocLayoutSection.vue")['default']
export const DocSurround: typeof import("../app/components/DocSurround.vue")['default']
export const DocToc: typeof import("../app/components/DocToc.vue")['default']
export const TairoLogo: typeof import("../app/components/TairoLogo.vue")['default']
export const TairoLogoText: typeof import("../app/components/TairoLogoText.vue")['default']
export const VectorChartStockOne: typeof import("../app/components/VectorChartStockOne.vue")['default']
export const VectorChartStockThree: typeof import("../app/components/VectorChartStockThree.vue")['default']
export const VectorChartStockTwo: typeof import("../app/components/VectorChartStockTwo.vue")['default']
export const VectorIllustrationBankFront: typeof import("../app/components/VectorIllustrationBankFront.vue")['default']
export const VectorIllustrationCalendar: typeof import("../app/components/VectorIllustrationCalendar.vue")['default']
export const VectorIllustrationCreditCard: typeof import("../app/components/VectorIllustrationCreditCard.vue")['default']
export const VectorIllustrationManWondering: typeof import("../app/components/VectorIllustrationManWondering.vue")['default']
export const VectorIllustrationTransaction: typeof import("../app/components/VectorIllustrationTransaction.vue")['default']
export const VectorLogoVisa: typeof import("../app/components/VectorLogoVisa.vue")['default']
export const DemoChartArea: typeof import("../app/components/demo-chart/DemoChartArea.vue")['default']
export const DemoChartAreaBalance: typeof import("../app/components/demo-chart/DemoChartAreaBalance.vue")['default']
export const DemoChartAreaBtcPrice: typeof import("../app/components/demo-chart/DemoChartAreaBtcPrice.vue")['default']
export const DemoChartAreaCondition: typeof import("../app/components/demo-chart/DemoChartAreaCondition.vue")['default']
export const DemoChartAreaCustomers: typeof import("../app/components/demo-chart/DemoChartAreaCustomers.vue")['default']
export const DemoChartAreaExpenses: typeof import("../app/components/demo-chart/DemoChartAreaExpenses.vue")['default']
export const DemoChartAreaIncomeHistory: typeof import("../app/components/demo-chart/DemoChartAreaIncomeHistory.vue")['default']
export const DemoChartAreaInterviews: typeof import("../app/components/demo-chart/DemoChartAreaInterviews.vue")['default']
export const DemoChartAreaMulti: typeof import("../app/components/demo-chart/DemoChartAreaMulti.vue")['default']
export const DemoChartAreaProgress: typeof import("../app/components/demo-chart/DemoChartAreaProgress.vue")['default']
export const DemoChartAreaSparkSalesFour: typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesFour.vue")['default']
export const DemoChartAreaSparkSalesOne: typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesOne.vue")['default']
export const DemoChartAreaSparkSalesThree: typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesThree.vue")['default']
export const DemoChartAreaSparkSalesTwo: typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesTwo.vue")['default']
export const DemoChartAreaStats: typeof import("../app/components/demo-chart/DemoChartAreaStats.vue")['default']
export const DemoChartAreaStockPrice: typeof import("../app/components/demo-chart/DemoChartAreaStockPrice.vue")['default']
export const DemoChartAreaTaskCompletion: typeof import("../app/components/demo-chart/DemoChartAreaTaskCompletion.vue")['default']
export const DemoChartBar: typeof import("../app/components/demo-chart/DemoChartBar.vue")['default']
export const DemoChartBarHorizontal: typeof import("../app/components/demo-chart/DemoChartBarHorizontal.vue")['default']
export const DemoChartBarHorizontalMulti: typeof import("../app/components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']
export const DemoChartBarMulti: typeof import("../app/components/demo-chart/DemoChartBarMulti.vue")['default']
export const DemoChartBarMultiIncome: typeof import("../app/components/demo-chart/DemoChartBarMultiIncome.vue")['default']
export const DemoChartBarOrders: typeof import("../app/components/demo-chart/DemoChartBarOrders.vue")['default']
export const DemoChartBarOxygen: typeof import("../app/components/demo-chart/DemoChartBarOxygen.vue")['default']
export const DemoChartBarProfit: typeof import("../app/components/demo-chart/DemoChartBarProfit.vue")['default']
export const DemoChartBarRange: typeof import("../app/components/demo-chart/DemoChartBarRange.vue")['default']
export const DemoChartBarSalesProfit: typeof import("../app/components/demo-chart/DemoChartBarSalesProfit.vue")['default']
export const DemoChartBarSocialChannels: typeof import("../app/components/demo-chart/DemoChartBarSocialChannels.vue")['default']
export const DemoChartBarStacked: typeof import("../app/components/demo-chart/DemoChartBarStacked.vue")['default']
export const DemoChartBarTeamEfficiency: typeof import("../app/components/demo-chart/DemoChartBarTeamEfficiency.vue")['default']
export const DemoChartBubble: typeof import("../app/components/demo-chart/DemoChartBubble.vue")['default']
export const DemoChartDonut: typeof import("../app/components/demo-chart/DemoChartDonut.vue")['default']
export const DemoChartDonutExpenses: typeof import("../app/components/demo-chart/DemoChartDonutExpenses.vue")['default']
export const DemoChartLine: typeof import("../app/components/demo-chart/DemoChartLine.vue")['default']
export const DemoChartLineMulti: typeof import("../app/components/demo-chart/DemoChartLineMulti.vue")['default']
export const DemoChartLineMultiAlt: typeof import("../app/components/demo-chart/DemoChartLineMultiAlt.vue")['default']
export const DemoChartLineRevenue: typeof import("../app/components/demo-chart/DemoChartLineRevenue.vue")['default']
export const DemoChartLineSparkFour: typeof import("../app/components/demo-chart/DemoChartLineSparkFour.vue")['default']
export const DemoChartLineSparkOne: typeof import("../app/components/demo-chart/DemoChartLineSparkOne.vue")['default']
export const DemoChartLineSparkThree: typeof import("../app/components/demo-chart/DemoChartLineSparkThree.vue")['default']
export const DemoChartLineSparkTwo: typeof import("../app/components/demo-chart/DemoChartLineSparkTwo.vue")['default']
export const DemoChartLineStep: typeof import("../app/components/demo-chart/DemoChartLineStep.vue")['default']
export const DemoChartPie: typeof import("../app/components/demo-chart/DemoChartPie.vue")['default']
export const DemoChartRadar: typeof import("../app/components/demo-chart/DemoChartRadar.vue")['default']
export const DemoChartRadial: typeof import("../app/components/demo-chart/DemoChartRadial.vue")['default']
export const DemoChartRadialEvolution: typeof import("../app/components/demo-chart/DemoChartRadialEvolution.vue")['default']
export const DemoChartRadialGauge: typeof import("../app/components/demo-chart/DemoChartRadialGauge.vue")['default']
export const DemoChartRadialGaugeAlt: typeof import("../app/components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']
export const DemoChartRadialGoal: typeof import("../app/components/demo-chart/DemoChartRadialGoal.vue")['default']
export const DemoChartRadialGrowth: typeof import("../app/components/demo-chart/DemoChartRadialGrowth.vue")['default']
export const DemoChartRadialMulti: typeof import("../app/components/demo-chart/DemoChartRadialMulti.vue")['default']
export const DemoChartRadialPopularity: typeof import("../app/components/demo-chart/DemoChartRadialPopularity.vue")['default']
export const DemoChartRadialSalesRevenue: typeof import("../app/components/demo-chart/DemoChartRadialSalesRevenue.vue")['default']
export const DemoChartRadialSmallOne: typeof import("../app/components/demo-chart/DemoChartRadialSmallOne.vue")['default']
export const DemoChartRadialSmallThree: typeof import("../app/components/demo-chart/DemoChartRadialSmallThree.vue")['default']
export const DemoChartRadialSmallTwo: typeof import("../app/components/demo-chart/DemoChartRadialSmallTwo.vue")['default']
export const DemoChartScatter: typeof import("../app/components/demo-chart/DemoChartScatter.vue")['default']
export const DemoChartScatterEnergy: typeof import("../app/components/demo-chart/DemoChartScatterEnergy.vue")['default']
export const DemoChartTimeline: typeof import("../app/components/demo-chart/DemoChartTimeline.vue")['default']
export const LandingBenefits: typeof import("../app/components/landing/LandingBenefits.vue")['default']
export const LandingContent: typeof import("../app/components/landing/LandingContent.vue")['default']
export const LandingCta: typeof import("../app/components/landing/LandingCta.vue")['default']
export const LandingCustomizer: typeof import("../app/components/landing/LandingCustomizer.vue")['default']
export const LandingDemoLink: typeof import("../app/components/landing/LandingDemoLink.vue")['default']
export const LandingDemos: typeof import("../app/components/landing/LandingDemos.vue")['default']
export const LandingFeatures: typeof import("../app/components/landing/LandingFeatures.vue")['default']
export const LandingFeaturesTile: typeof import("../app/components/landing/LandingFeaturesTile.vue")['default']
export const LandingFooter: typeof import("../app/components/landing/LandingFooter.vue")['default']
export const LandingHero: typeof import("../app/components/landing/LandingHero.vue")['default']
export const LandingHeroMockup: typeof import("../app/components/landing/LandingHeroMockup.vue")['default']
export const LandingLayers: typeof import("../app/components/landing/LandingLayers.vue")['default']
export const LandingLayersBox: typeof import("../app/components/landing/LandingLayersBox.vue")['default']
export const LandingLayout: typeof import("../app/components/landing/LandingLayout.vue")['default']
export const LandingLayouts: typeof import("../app/components/landing/LandingLayouts.vue")['default']
export const LandingMobileNav: typeof import("../app/components/landing/LandingMobileNav.vue")['default']
export const LandingNavbar: typeof import("../app/components/landing/LandingNavbar.vue")['default']
export const ProseA: typeof import("../app/components/prose/ProseA.vue")['default']
export const ProseBlockquote: typeof import("../app/components/prose/ProseBlockquote.vue")['default']
export const ProseCode: typeof import("../app/components/prose/ProseCode.vue")['default']
export const ProseCodeInline: typeof import("../app/components/prose/ProseCodeInline.vue")['default']
export const ProseEm: typeof import("../app/components/prose/ProseEm.vue")['default']
export const ProseH1: typeof import("../app/components/prose/ProseH1.vue")['default']
export const ProseH2: typeof import("../app/components/prose/ProseH2.vue")['default']
export const ProseH3: typeof import("../app/components/prose/ProseH3.vue")['default']
export const ProseH5: typeof import("../app/components/prose/ProseH5.vue")['default']
export const ProseH6: typeof import("../app/components/prose/ProseH6.vue")['default']
export const ProseHr: typeof import("../app/components/prose/ProseHr.vue")['default']
export const ProseImg: typeof import("../app/components/prose/ProseImg.vue")['default']
export const ProseLi: typeof import("../app/components/prose/ProseLi.vue")['default']
export const ProseOl: typeof import("../app/components/prose/ProseOl.vue")['default']
export const ProseP: typeof import("../app/components/prose/ProseP.vue")['default']
export const ProsePre: typeof import("../app/components/prose/ProsePre.vue")['default']
export const ProseScript: typeof import("../app/components/prose/ProseScript.vue")['default']
export const ProseStrong: typeof import("../app/components/prose/ProseStrong.vue")['default']
export const ProseTable: typeof import("../app/components/prose/ProseTable.vue")['default']
export const ProseTbody: typeof import("../app/components/prose/ProseTbody.vue")['default']
export const ProseTd: typeof import("../app/components/prose/ProseTd.vue")['default']
export const ProseTh: typeof import("../app/components/prose/ProseTh.vue")['default']
export const ProseThead: typeof import("../app/components/prose/ProseThead.vue")['default']
export const ProseTr: typeof import("../app/components/prose/ProseTr.vue")['default']
export const ProseUl: typeof import("../app/components/prose/ProseUl.vue")['default']
export const ProseH4: typeof import("../app/components/prose/proseH4.vue")['default']
export const TairoCheckAnimated: typeof import("../../layers/tairo/components/TairoCheckAnimated.vue")['default']
export const TairoCheckboxAnimated: typeof import("../../layers/tairo/components/TairoCheckboxAnimated.vue")['default']
export const TairoCheckboxCardIcon: typeof import("../../layers/tairo/components/TairoCheckboxCardIcon.vue")['default']
export const TairoContentWrapper: typeof import("../../layers/tairo/components/TairoContentWrapper.vue")['default']
export const TairoContentWrapperTabbed: typeof import("../../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']
export const TairoError: typeof import("../../layers/tairo/components/TairoError.vue")['default']
export const TairoFlexTable: typeof import("../../layers/tairo/components/TairoFlexTable.vue")['default']
export const TairoFlexTableCell: typeof import("../../layers/tairo/components/TairoFlexTableCell.vue")['default']
export const TairoFlexTableHeading: typeof import("../../layers/tairo/components/TairoFlexTableHeading.vue")['default']
export const TairoFlexTableRow: typeof import("../../layers/tairo/components/TairoFlexTableRow.vue")['default']
export const TairoFormGroup: typeof import("../../layers/tairo/components/TairoFormGroup.vue")['default']
export const TairoFormSave: typeof import("../../layers/tairo/components/TairoFormSave.vue")['default']
export const TairoFullscreenDropfile: typeof import("../../layers/tairo/components/TairoFullscreenDropfile.vue")['default']
export const TairoImageZoom: typeof import("../../layers/tairo/components/TairoImageZoom.vue")['default']
export const TairoInput: typeof import("../../layers/tairo/components/TairoInput.vue")['default']
export const TairoInputFileHeadless: typeof import("../../layers/tairo/components/TairoInputFileHeadless.vue")['default']
export const TairoMobileDrawer: typeof import("../../layers/tairo/components/TairoMobileDrawer.vue")['default']
export const TairoPanels: typeof import("../../layers/tairo/components/TairoPanels.vue")['default']
export const TairoRadioCard: typeof import("../../layers/tairo/components/TairoRadioCard.vue")['default']
export const TairoSelect: typeof import("../../layers/tairo/components/TairoSelect.vue")['default']
export const TairoSelectItem: typeof import("../../layers/tairo/components/TairoSelectItem.vue")['default']
export const TairoTable: typeof import("../../layers/tairo/components/TairoTable.vue")['default']
export const TairoTableCell: typeof import("../../layers/tairo/components/TairoTableCell.vue")['default']
export const TairoTableHeading: typeof import("../../layers/tairo/components/TairoTableHeading.vue")['default']
export const TairoTableRow: typeof import("../../layers/tairo/components/TairoTableRow.vue")['default']
export const TairoWelcome: typeof import("../../layers/tairo/components/TairoWelcome.vue")['default']
export const TairoCollapseBackdrop: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseBackdrop.vue")['default']
export const TairoCollapseCollapsible: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsible.vue")['default']
export const TairoCollapseCollapsibleLink: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']
export const TairoCollapseCollapsibleTrigger: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']
export const TairoCollapseContent: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseContent.vue")['default']
export const TairoCollapseLayout: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseLayout.vue")['default']
export const TairoCollapseSidebar: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebar.vue")['default']
export const TairoCollapseSidebarClose: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']
export const TairoCollapseSidebarHeader: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']
export const TairoCollapseSidebarLink: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']
export const TairoCollapseSidebarLinks: typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']
export const TairoMenu: typeof import("../../layers/tairo/components/tairo-menu/TairoMenu.vue")['default']
export const TairoMenuContent: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuContent.vue")['default']
export const TairoMenuIndicator: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuIndicator.vue")['default']
export const TairoMenuItem: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuItem.vue")['default']
export const TairoMenuLink: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLink.vue")['default']
export const TairoMenuLinkTab: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLinkTab.vue")['default']
export const TairoMenuList: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuList.vue")['default']
export const TairoMenuListItems: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuListItems.vue")['default']
export const TairoMenuTrigger: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuTrigger.vue")['default']
export const TairoMenuViewport: typeof import("../../layers/tairo/components/tairo-menu/TairoMenuViewport.vue")['default']
export const TairoSidebar: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebar.vue")['default']
export const TairoSidebarBackdrop: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']
export const TairoSidebarContent: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarContent.vue")['default']
export const TairoSidebarLayout: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLayout.vue")['default']
export const TairoSidebarLink: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLink.vue")['default']
export const TairoSidebarLinks: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLinks.vue")['default']
export const TairoSidebarNav: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarNav.vue")['default']
export const TairoSidebarSubsidebar: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']
export const TairoSidebarSubsidebarCollapsible: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']
export const TairoSidebarSubsidebarCollapsibleLink: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']
export const TairoSidebarSubsidebarCollapsibleTrigger: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']
export const TairoSidebarSubsidebarContent: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']
export const TairoSidebarSubsidebarHeader: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']
export const TairoSidebarSubsidebarLink: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']
export const TairoSidebarTrigger: typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarTrigger.vue")['default']
export const TairoSidenavBackdrop: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']
export const TairoSidenavCollapsible: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']
export const TairoSidenavCollapsibleLink: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']
export const TairoSidenavCollapsibleTrigger: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']
export const TairoSidenavContent: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavContent.vue")['default']
export const TairoSidenavLayout: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavLayout.vue")['default']
export const TairoSidenavSidebar: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebar.vue")['default']
export const TairoSidenavSidebarDivider: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']
export const TairoSidenavSidebarHeader: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']
export const TairoSidenavSidebarLink: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']
export const TairoSidenavSidebarLinks: typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']
export const TairoTopnavContent: typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavContent.vue")['default']
export const TairoTopnavHeader: typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavHeader.vue")['default']
export const TairoTopnavLayout: typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavLayout.vue")['default']
export const TairoTopnavNavbar: typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavNavbar.vue")['default']
export const ExamplesAddonsDatepicker: typeof import("../examples/addons/datepicker.vue")['default']
export const ExamplesAddonsMapbox: typeof import("../examples/addons/mapbox.vue")['default']
export const ExamplesApexchartsBase: typeof import("../examples/apexcharts/base.vue")['default']
export const ExamplesFlexTableCurved: typeof import("../examples/flex-table/curved.vue")['default']
export const ExamplesFlexTableRounded: typeof import("../examples/flex-table/rounded.vue")['default']
export const ExamplesFlexTableSmooth: typeof import("../examples/flex-table/smooth.vue")['default']
export const ExamplesFlexTableStraight: typeof import("../examples/flex-table/straight.vue")['default']
export const ExamplesInputPasswordBase: typeof import("../examples/input-password/base.vue")['default']
export const ExamplesInputPasswordDisabled: typeof import("../examples/input-password/disabled.vue")['default']
export const ExamplesInputPasswordLocale: typeof import("../examples/input-password/locale.vue")['default']
export const ExamplesInputPasswordUserInput: typeof import("../examples/input-password/user-input.vue")['default']
export const ExamplesInputPasswordValidation: typeof import("../examples/input-password/validation.vue")['default']
export const ExamplesInputPhoneBase: typeof import("../examples/input-phone/base.vue")['default']
export const ExamplesInputPhoneCountry: typeof import("../examples/input-phone/country.vue")['default']
export const ExamplesInputPhoneDisabled: typeof import("../examples/input-phone/disabled.vue")['default']
export const ExamplesInputPhoneFormat: typeof import("../examples/input-phone/format.vue")['default']
export const ExamplesInputPhoneShape: typeof import("../examples/input-phone/shape.vue")['default']
export const ExamplesInputPhoneSize: typeof import("../examples/input-phone/size.vue")['default']
export const ExamplesInputPhoneValidation: typeof import("../examples/input-phone/validation.vue")['default']
export const ExamplesLightweightChartsBase: typeof import("../examples/lightweight-charts/base.vue")['default']
export const ExamplesPanelActivity: typeof import("../examples/panel/activity.vue")['default']
export const ExamplesPanelLanguage: typeof import("../examples/panel/language.vue")['default']
export const ExamplesPanelSearch: typeof import("../examples/panel/search.vue")['default']
export const ExamplesPanelTask: typeof import("../examples/panel/task.vue")['default']
export const ExamplesTableCurved: typeof import("../examples/table/curved.vue")['default']
export const ExamplesTableMediaCurved: typeof import("../examples/table/media-curved.vue")['default']
export const ExamplesTableMediaRounded: typeof import("../examples/table/media-rounded.vue")['default']
export const ExamplesTableMediaSmooth: typeof import("../examples/table/media-smooth.vue")['default']
export const ExamplesTableMediaStraight: typeof import("../examples/table/media-straight.vue")['default']
export const ExamplesTableRounded: typeof import("../examples/table/rounded.vue")['default']
export const ExamplesTableSmooth: typeof import("../examples/table/smooth.vue")['default']
export const ExamplesTableStraight: typeof import("../examples/table/straight.vue")['default']
export const ExamplesTairoCheckAnimated: typeof import("../examples/tairo/check-animated.vue")['default']
export const ExamplesTairoCheckboxAnimated: typeof import("../examples/tairo/checkbox-animated.vue")['default']
export const ExamplesTairoCheckboxCardIcon: typeof import("../examples/tairo/checkbox-card-icon.vue")['default']
export const ExamplesTairoCircularMenu: typeof import("../examples/tairo/circular-menu.vue")['default']
export const ExamplesTairoError: typeof import("../examples/tairo/error.vue")['default']
export const ExamplesTairoFormGroup: typeof import("../examples/tairo/form-group.vue")['default']
export const ExamplesTairoFormSave: typeof import("../examples/tairo/form-save.vue")['default']
export const ExamplesTairoInput: typeof import("../examples/tairo/input.vue")['default']
export const ExamplesTairoLogo: typeof import("../examples/tairo/logo.vue")['default']
export const ExamplesTairoLogotext: typeof import("../examples/tairo/logotext.vue")['default']
export const ExamplesTairoMenuComplete: typeof import("../examples/tairo/menu-complete.vue")['default']
export const ExamplesTairoMenu: typeof import("../examples/tairo/menu.vue")['default']
export const ExamplesTairoMobileDrawer: typeof import("../examples/tairo/mobile-drawer.vue")['default']
export const ExamplesTairoRadioCard: typeof import("../examples/tairo/radio-card.vue")['default']
export const ExamplesTairoSelect: typeof import("../examples/tairo/select.vue")['default']
export const ExamplesTairoValidation: typeof import("../examples/tairo/validation.vue")['default']
export const BaseAccordion: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']
export const BaseAccordionItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']
export const BaseAutocomplete: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']
export const BaseAutocompleteGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']
export const BaseAutocompleteItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']
export const BaseAutocompleteLabel: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']
export const BaseAutocompleteSeparator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']
export const BaseAvatar: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']
export const BaseAvatarGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']
export const BaseBreadcrumb: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']
export const BaseButton: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']
export const BaseCard: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']
export const BaseCheckbox: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']
export const BaseCheckboxGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']
export const BaseChip: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']
export const BaseDropdown: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']
export const BaseDropdownArrow: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']
export const BaseDropdownCheckbox: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']
export const BaseDropdownItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']
export const BaseDropdownLabel: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']
export const BaseDropdownRadioGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']
export const BaseDropdownRadioItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']
export const BaseDropdownSeparator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']
export const BaseDropdownSub: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']
export const BaseField: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']
export const BaseHeading: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']
export const BaseIconBox: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']
export const BaseInput: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']
export const BaseInputFile: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']
export const BaseInputNumber: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']
export const BaseKbd: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']
export const BaseLink: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']
export const BaseList: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']
export const BaseListItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']
export const BaseMessage: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']
export const BasePagination: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']
export const BasePaginationButtonFirst: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']
export const BasePaginationButtonLast: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']
export const BasePaginationButtonNext: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']
export const BasePaginationButtonPrev: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']
export const BasePaginationItems: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']
export const BaseParagraph: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']
export const BasePlaceholderPage: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']
export const BasePlaceload: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']
export const BasePopover: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']
export const BasePrimitiveField: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']
export const BasePrimitiveFieldController: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']
export const BasePrimitiveFieldDescription: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']
export const BasePrimitiveFieldError: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']
export const BasePrimitiveFieldErrorIndicator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']
export const BasePrimitiveFieldLabel: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']
export const BasePrimitiveFieldLoadingIndicator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']
export const BasePrimitiveFieldRequiredIndicator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']
export const BasePrimitiveFieldSuccessIndicator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']
export const BaseProgress: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']
export const BaseProgressCircle: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']
export const BaseProse: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']
export const BaseProviders: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']
export const BaseRadio: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']
export const BaseRadioGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']
export const BaseSelect: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']
export const BaseSelectGroup: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']
export const BaseSelectItem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']
export const BaseSelectLabel: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']
export const BaseSelectSeparator: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']
export const BaseSlider: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']
export const BaseSnack: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']
export const BaseSwitchBall: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']
export const BaseSwitchThin: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']
export const BaseTabs: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']
export const BaseTabsContent: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']
export const BaseTabsTrigger: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']
export const BaseTag: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']
export const BaseText: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']
export const BaseTextarea: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']
export const BaseThemeSwitch: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']
export const BaseThemeSystem: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']
export const BaseThemeToggle: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']
export const BaseToast: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']
export const BaseToastProvider: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']
export const BaseTooltip: typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']
export const NuxtWelcome: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtRouteAnnouncer: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']
export const NuxtPicture: typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']
export const AccordionContent: typeof import("reka-ui")['AccordionContent']
export const AccordionHeader: typeof import("reka-ui")['AccordionHeader']
export const AccordionItem: typeof import("reka-ui")['AccordionItem']
export const AccordionRoot: typeof import("reka-ui")['AccordionRoot']
export const AccordionTrigger: typeof import("reka-ui")['AccordionTrigger']
export const AlertDialogRoot: typeof import("reka-ui")['AlertDialogRoot']
export const AlertDialogTrigger: typeof import("reka-ui")['AlertDialogTrigger']
export const AlertDialogPortal: typeof import("reka-ui")['AlertDialogPortal']
export const AlertDialogContent: typeof import("reka-ui")['AlertDialogContent']
export const AlertDialogOverlay: typeof import("reka-ui")['AlertDialogOverlay']
export const AlertDialogCancel: typeof import("reka-ui")['AlertDialogCancel']
export const AlertDialogTitle: typeof import("reka-ui")['AlertDialogTitle']
export const AlertDialogDescription: typeof import("reka-ui")['AlertDialogDescription']
export const AlertDialogAction: typeof import("reka-ui")['AlertDialogAction']
export const AspectRatio: typeof import("reka-ui")['AspectRatio']
export const AvatarRoot: typeof import("reka-ui")['AvatarRoot']
export const AvatarFallback: typeof import("reka-ui")['AvatarFallback']
export const AvatarImage: typeof import("reka-ui")['AvatarImage']
export const CalendarRoot: typeof import("reka-ui")['CalendarRoot']
export const CalendarHeader: typeof import("reka-ui")['CalendarHeader']
export const CalendarHeading: typeof import("reka-ui")['CalendarHeading']
export const CalendarGrid: typeof import("reka-ui")['CalendarGrid']
export const CalendarCell: typeof import("reka-ui")['CalendarCell']
export const CalendarHeadCell: typeof import("reka-ui")['CalendarHeadCell']
export const CalendarNext: typeof import("reka-ui")['CalendarNext']
export const CalendarPrev: typeof import("reka-ui")['CalendarPrev']
export const CalendarGridHead: typeof import("reka-ui")['CalendarGridHead']
export const CalendarGridBody: typeof import("reka-ui")['CalendarGridBody']
export const CalendarGridRow: typeof import("reka-ui")['CalendarGridRow']
export const CalendarCellTrigger: typeof import("reka-ui")['CalendarCellTrigger']
export const CheckboxGroupRoot: typeof import("reka-ui")['CheckboxGroupRoot']
export const CheckboxRoot: typeof import("reka-ui")['CheckboxRoot']
export const CheckboxIndicator: typeof import("reka-ui")['CheckboxIndicator']
export const CollapsibleRoot: typeof import("reka-ui")['CollapsibleRoot']
export const CollapsibleTrigger: typeof import("reka-ui")['CollapsibleTrigger']
export const CollapsibleContent: typeof import("reka-ui")['CollapsibleContent']
export const ComboboxRoot: typeof import("reka-ui")['ComboboxRoot']
export const ComboboxInput: typeof import("reka-ui")['ComboboxInput']
export const ComboboxAnchor: typeof import("reka-ui")['ComboboxAnchor']
export const ComboboxEmpty: typeof import("reka-ui")['ComboboxEmpty']
export const ComboboxTrigger: typeof import("reka-ui")['ComboboxTrigger']
export const ComboboxCancel: typeof import("reka-ui")['ComboboxCancel']
export const ComboboxGroup: typeof import("reka-ui")['ComboboxGroup']
export const ComboboxLabel: typeof import("reka-ui")['ComboboxLabel']
export const ComboboxContent: typeof import("reka-ui")['ComboboxContent']
export const ComboboxViewport: typeof import("reka-ui")['ComboboxViewport']
export const ComboboxVirtualizer: typeof import("reka-ui")['ComboboxVirtualizer']
export const ComboboxItem: typeof import("reka-ui")['ComboboxItem']
export const ComboboxItemIndicator: typeof import("reka-ui")['ComboboxItemIndicator']
export const ComboboxSeparator: typeof import("reka-ui")['ComboboxSeparator']
export const ComboboxArrow: typeof import("reka-ui")['ComboboxArrow']
export const ComboboxPortal: typeof import("reka-ui")['ComboboxPortal']
export const ContextMenuRoot: typeof import("reka-ui")['ContextMenuRoot']
export const ContextMenuTrigger: typeof import("reka-ui")['ContextMenuTrigger']
export const ContextMenuPortal: typeof import("reka-ui")['ContextMenuPortal']
export const ContextMenuContent: typeof import("reka-ui")['ContextMenuContent']
export const ContextMenuArrow: typeof import("reka-ui")['ContextMenuArrow']
export const ContextMenuItem: typeof import("reka-ui")['ContextMenuItem']
export const ContextMenuGroup: typeof import("reka-ui")['ContextMenuGroup']
export const ContextMenuSeparator: typeof import("reka-ui")['ContextMenuSeparator']
export const ContextMenuCheckboxItem: typeof import("reka-ui")['ContextMenuCheckboxItem']
export const ContextMenuItemIndicator: typeof import("reka-ui")['ContextMenuItemIndicator']
export const ContextMenuLabel: typeof import("reka-ui")['ContextMenuLabel']
export const ContextMenuRadioGroup: typeof import("reka-ui")['ContextMenuRadioGroup']
export const ContextMenuRadioItem: typeof import("reka-ui")['ContextMenuRadioItem']
export const ContextMenuSub: typeof import("reka-ui")['ContextMenuSub']
export const ContextMenuSubContent: typeof import("reka-ui")['ContextMenuSubContent']
export const ContextMenuSubTrigger: typeof import("reka-ui")['ContextMenuSubTrigger']
export const DateFieldRoot: typeof import("reka-ui")['DateFieldRoot']
export const DateFieldInput: typeof import("reka-ui")['DateFieldInput']
export const DatePickerRoot: typeof import("reka-ui")['DatePickerRoot']
export const DatePickerHeader: typeof import("reka-ui")['DatePickerHeader']
export const DatePickerHeading: typeof import("reka-ui")['DatePickerHeading']
export const DatePickerGrid: typeof import("reka-ui")['DatePickerGrid']
export const DatePickerCell: typeof import("reka-ui")['DatePickerCell']
export const DatePickerHeadCell: typeof import("reka-ui")['DatePickerHeadCell']
export const DatePickerNext: typeof import("reka-ui")['DatePickerNext']
export const DatePickerPrev: typeof import("reka-ui")['DatePickerPrev']
export const DatePickerGridHead: typeof import("reka-ui")['DatePickerGridHead']
export const DatePickerGridBody: typeof import("reka-ui")['DatePickerGridBody']
export const DatePickerGridRow: typeof import("reka-ui")['DatePickerGridRow']
export const DatePickerCellTrigger: typeof import("reka-ui")['DatePickerCellTrigger']
export const DatePickerInput: typeof import("reka-ui")['DatePickerInput']
export const DatePickerCalendar: typeof import("reka-ui")['DatePickerCalendar']
export const DatePickerField: typeof import("reka-ui")['DatePickerField']
export const DatePickerAnchor: typeof import("reka-ui")['DatePickerAnchor']
export const DatePickerArrow: typeof import("reka-ui")['DatePickerArrow']
export const DatePickerClose: typeof import("reka-ui")['DatePickerClose']
export const DatePickerTrigger: typeof import("reka-ui")['DatePickerTrigger']
export const DatePickerContent: typeof import("reka-ui")['DatePickerContent']
export const DateRangePickerRoot: typeof import("reka-ui")['DateRangePickerRoot']
export const DateRangePickerHeader: typeof import("reka-ui")['DateRangePickerHeader']
export const DateRangePickerHeading: typeof import("reka-ui")['DateRangePickerHeading']
export const DateRangePickerGrid: typeof import("reka-ui")['DateRangePickerGrid']
export const DateRangePickerCell: typeof import("reka-ui")['DateRangePickerCell']
export const DateRangePickerHeadCell: typeof import("reka-ui")['DateRangePickerHeadCell']
export const DateRangePickerNext: typeof import("reka-ui")['DateRangePickerNext']
export const DateRangePickerPrev: typeof import("reka-ui")['DateRangePickerPrev']
export const DateRangePickerGridHead: typeof import("reka-ui")['DateRangePickerGridHead']
export const DateRangePickerGridBody: typeof import("reka-ui")['DateRangePickerGridBody']
export const DateRangePickerGridRow: typeof import("reka-ui")['DateRangePickerGridRow']
export const DateRangePickerCellTrigger: typeof import("reka-ui")['DateRangePickerCellTrigger']
export const DateRangePickerInput: typeof import("reka-ui")['DateRangePickerInput']
export const DateRangePickerCalendar: typeof import("reka-ui")['DateRangePickerCalendar']
export const DateRangePickerField: typeof import("reka-ui")['DateRangePickerField']
export const DateRangePickerAnchor: typeof import("reka-ui")['DateRangePickerAnchor']
export const DateRangePickerArrow: typeof import("reka-ui")['DateRangePickerArrow']
export const DateRangePickerClose: typeof import("reka-ui")['DateRangePickerClose']
export const DateRangePickerTrigger: typeof import("reka-ui")['DateRangePickerTrigger']
export const DateRangePickerContent: typeof import("reka-ui")['DateRangePickerContent']
export const DateRangeFieldRoot: typeof import("reka-ui")['DateRangeFieldRoot']
export const DateRangeFieldInput: typeof import("reka-ui")['DateRangeFieldInput']
export const DialogRoot: typeof import("reka-ui")['DialogRoot']
export const DialogTrigger: typeof import("reka-ui")['DialogTrigger']
export const DialogPortal: typeof import("reka-ui")['DialogPortal']
export const DialogContent: typeof import("reka-ui")['DialogContent']
export const DialogOverlay: typeof import("reka-ui")['DialogOverlay']
export const DialogClose: typeof import("reka-ui")['DialogClose']
export const DialogTitle: typeof import("reka-ui")['DialogTitle']
export const DialogDescription: typeof import("reka-ui")['DialogDescription']
export const DropdownMenuRoot: typeof import("reka-ui")['DropdownMenuRoot']
export const DropdownMenuTrigger: typeof import("reka-ui")['DropdownMenuTrigger']
export const DropdownMenuPortal: typeof import("reka-ui")['DropdownMenuPortal']
export const DropdownMenuContent: typeof import("reka-ui")['DropdownMenuContent']
export const DropdownMenuArrow: typeof import("reka-ui")['DropdownMenuArrow']
export const DropdownMenuItem: typeof import("reka-ui")['DropdownMenuItem']
export const DropdownMenuGroup: typeof import("reka-ui")['DropdownMenuGroup']
export const DropdownMenuSeparator: typeof import("reka-ui")['DropdownMenuSeparator']
export const DropdownMenuCheckboxItem: typeof import("reka-ui")['DropdownMenuCheckboxItem']
export const DropdownMenuItemIndicator: typeof import("reka-ui")['DropdownMenuItemIndicator']
export const DropdownMenuLabel: typeof import("reka-ui")['DropdownMenuLabel']
export const DropdownMenuRadioGroup: typeof import("reka-ui")['DropdownMenuRadioGroup']
export const DropdownMenuRadioItem: typeof import("reka-ui")['DropdownMenuRadioItem']
export const DropdownMenuSub: typeof import("reka-ui")['DropdownMenuSub']
export const DropdownMenuSubContent: typeof import("reka-ui")['DropdownMenuSubContent']
export const DropdownMenuSubTrigger: typeof import("reka-ui")['DropdownMenuSubTrigger']
export const EditableRoot: typeof import("reka-ui")['EditableRoot']
export const EditableArea: typeof import("reka-ui")['EditableArea']
export const EditableInput: typeof import("reka-ui")['EditableInput']
export const EditablePreview: typeof import("reka-ui")['EditablePreview']
export const EditableSubmitTrigger: typeof import("reka-ui")['EditableSubmitTrigger']
export const EditableCancelTrigger: typeof import("reka-ui")['EditableCancelTrigger']
export const EditableEditTrigger: typeof import("reka-ui")['EditableEditTrigger']
export const HoverCardRoot: typeof import("reka-ui")['HoverCardRoot']
export const HoverCardTrigger: typeof import("reka-ui")['HoverCardTrigger']
export const HoverCardPortal: typeof import("reka-ui")['HoverCardPortal']
export const HoverCardContent: typeof import("reka-ui")['HoverCardContent']
export const HoverCardArrow: typeof import("reka-ui")['HoverCardArrow']
export const Label: typeof import("reka-ui")['Label']
export const ListboxRoot: typeof import("reka-ui")['ListboxRoot']
export const ListboxContent: typeof import("reka-ui")['ListboxContent']
export const ListboxFilter: typeof import("reka-ui")['ListboxFilter']
export const ListboxItem: typeof import("reka-ui")['ListboxItem']
export const ListboxItemIndicator: typeof import("reka-ui")['ListboxItemIndicator']
export const ListboxVirtualizer: typeof import("reka-ui")['ListboxVirtualizer']
export const ListboxGroup: typeof import("reka-ui")['ListboxGroup']
export const ListboxGroupLabel: typeof import("reka-ui")['ListboxGroupLabel']
export const MenubarRoot: typeof import("reka-ui")['MenubarRoot']
export const MenubarTrigger: typeof import("reka-ui")['MenubarTrigger']
export const MenubarPortal: typeof import("reka-ui")['MenubarPortal']
export const MenubarContent: typeof import("reka-ui")['MenubarContent']
export const MenubarArrow: typeof import("reka-ui")['MenubarArrow']
export const MenubarItem: typeof import("reka-ui")['MenubarItem']
export const MenubarGroup: typeof import("reka-ui")['MenubarGroup']
export const MenubarSeparator: typeof import("reka-ui")['MenubarSeparator']
export const MenubarCheckboxItem: typeof import("reka-ui")['MenubarCheckboxItem']
export const MenubarItemIndicator: typeof import("reka-ui")['MenubarItemIndicator']
export const MenubarLabel: typeof import("reka-ui")['MenubarLabel']
export const MenubarRadioGroup: typeof import("reka-ui")['MenubarRadioGroup']
export const MenubarRadioItem: typeof import("reka-ui")['MenubarRadioItem']
export const MenubarSub: typeof import("reka-ui")['MenubarSub']
export const MenubarSubContent: typeof import("reka-ui")['MenubarSubContent']
export const MenubarSubTrigger: typeof import("reka-ui")['MenubarSubTrigger']
export const MenubarMenu: typeof import("reka-ui")['MenubarMenu']
export const NavigationMenuRoot: typeof import("reka-ui")['NavigationMenuRoot']
export const NavigationMenuContent: typeof import("reka-ui")['NavigationMenuContent']
export const NavigationMenuIndicator: typeof import("reka-ui")['NavigationMenuIndicator']
export const NavigationMenuItem: typeof import("reka-ui")['NavigationMenuItem']
export const NavigationMenuLink: typeof import("reka-ui")['NavigationMenuLink']
export const NavigationMenuList: typeof import("reka-ui")['NavigationMenuList']
export const NavigationMenuSub: typeof import("reka-ui")['NavigationMenuSub']
export const NavigationMenuTrigger: typeof import("reka-ui")['NavigationMenuTrigger']
export const NavigationMenuViewport: typeof import("reka-ui")['NavigationMenuViewport']
export const NumberFieldRoot: typeof import("reka-ui")['NumberFieldRoot']
export const NumberFieldInput: typeof import("reka-ui")['NumberFieldInput']
export const NumberFieldIncrement: typeof import("reka-ui")['NumberFieldIncrement']
export const NumberFieldDecrement: typeof import("reka-ui")['NumberFieldDecrement']
export const PaginationRoot: typeof import("reka-ui")['PaginationRoot']
export const PaginationEllipsis: typeof import("reka-ui")['PaginationEllipsis']
export const PaginationFirst: typeof import("reka-ui")['PaginationFirst']
export const PaginationLast: typeof import("reka-ui")['PaginationLast']
export const PaginationList: typeof import("reka-ui")['PaginationList']
export const PaginationListItem: typeof import("reka-ui")['PaginationListItem']
export const PaginationNext: typeof import("reka-ui")['PaginationNext']
export const PaginationPrev: typeof import("reka-ui")['PaginationPrev']
export const PinInputRoot: typeof import("reka-ui")['PinInputRoot']
export const PinInputInput: typeof import("reka-ui")['PinInputInput']
export const PopoverRoot: typeof import("reka-ui")['PopoverRoot']
export const PopoverTrigger: typeof import("reka-ui")['PopoverTrigger']
export const PopoverPortal: typeof import("reka-ui")['PopoverPortal']
export const PopoverContent: typeof import("reka-ui")['PopoverContent']
export const PopoverArrow: typeof import("reka-ui")['PopoverArrow']
export const PopoverClose: typeof import("reka-ui")['PopoverClose']
export const PopoverAnchor: typeof import("reka-ui")['PopoverAnchor']
export const ProgressRoot: typeof import("reka-ui")['ProgressRoot']
export const ProgressIndicator: typeof import("reka-ui")['ProgressIndicator']
export const RadioGroupRoot: typeof import("reka-ui")['RadioGroupRoot']
export const RadioGroupItem: typeof import("reka-ui")['RadioGroupItem']
export const RadioGroupIndicator: typeof import("reka-ui")['RadioGroupIndicator']
export const RangeCalendarRoot: typeof import("reka-ui")['RangeCalendarRoot']
export const RangeCalendarHeader: typeof import("reka-ui")['RangeCalendarHeader']
export const RangeCalendarHeading: typeof import("reka-ui")['RangeCalendarHeading']
export const RangeCalendarGrid: typeof import("reka-ui")['RangeCalendarGrid']
export const RangeCalendarCell: typeof import("reka-ui")['RangeCalendarCell']
export const RangeCalendarHeadCell: typeof import("reka-ui")['RangeCalendarHeadCell']
export const RangeCalendarNext: typeof import("reka-ui")['RangeCalendarNext']
export const RangeCalendarPrev: typeof import("reka-ui")['RangeCalendarPrev']
export const RangeCalendarGridHead: typeof import("reka-ui")['RangeCalendarGridHead']
export const RangeCalendarGridBody: typeof import("reka-ui")['RangeCalendarGridBody']
export const RangeCalendarGridRow: typeof import("reka-ui")['RangeCalendarGridRow']
export const RangeCalendarCellTrigger: typeof import("reka-ui")['RangeCalendarCellTrigger']
export const ScrollAreaRoot: typeof import("reka-ui")['ScrollAreaRoot']
export const ScrollAreaViewport: typeof import("reka-ui")['ScrollAreaViewport']
export const ScrollAreaScrollbar: typeof import("reka-ui")['ScrollAreaScrollbar']
export const ScrollAreaThumb: typeof import("reka-ui")['ScrollAreaThumb']
export const ScrollAreaCorner: typeof import("reka-ui")['ScrollAreaCorner']
export const SelectRoot: typeof import("reka-ui")['SelectRoot']
export const SelectTrigger: typeof import("reka-ui")['SelectTrigger']
export const SelectPortal: typeof import("reka-ui")['SelectPortal']
export const SelectContent: typeof import("reka-ui")['SelectContent']
export const SelectArrow: typeof import("reka-ui")['SelectArrow']
export const SelectSeparator: typeof import("reka-ui")['SelectSeparator']
export const SelectItemIndicator: typeof import("reka-ui")['SelectItemIndicator']
export const SelectLabel: typeof import("reka-ui")['SelectLabel']
export const SelectGroup: typeof import("reka-ui")['SelectGroup']
export const SelectItem: typeof import("reka-ui")['SelectItem']
export const SelectItemText: typeof import("reka-ui")['SelectItemText']
export const SelectViewport: typeof import("reka-ui")['SelectViewport']
export const SelectScrollUpButton: typeof import("reka-ui")['SelectScrollUpButton']
export const SelectScrollDownButton: typeof import("reka-ui")['SelectScrollDownButton']
export const SelectValue: typeof import("reka-ui")['SelectValue']
export const SelectIcon: typeof import("reka-ui")['SelectIcon']
export const Separator: typeof import("reka-ui")['Separator']
export const SliderRoot: typeof import("reka-ui")['SliderRoot']
export const SliderThumb: typeof import("reka-ui")['SliderThumb']
export const SliderTrack: typeof import("reka-ui")['SliderTrack']
export const SliderRange: typeof import("reka-ui")['SliderRange']
export const SplitterGroup: typeof import("reka-ui")['SplitterGroup']
export const SplitterPanel: typeof import("reka-ui")['SplitterPanel']
export const SplitterResizeHandle: typeof import("reka-ui")['SplitterResizeHandle']
export const StepperRoot: typeof import("reka-ui")['StepperRoot']
export const StepperItem: typeof import("reka-ui")['StepperItem']
export const StepperTrigger: typeof import("reka-ui")['StepperTrigger']
export const StepperDescription: typeof import("reka-ui")['StepperDescription']
export const StepperTitle: typeof import("reka-ui")['StepperTitle']
export const StepperIndicator: typeof import("reka-ui")['StepperIndicator']
export const StepperSeparator: typeof import("reka-ui")['StepperSeparator']
export const SwitchRoot: typeof import("reka-ui")['SwitchRoot']
export const SwitchThumb: typeof import("reka-ui")['SwitchThumb']
export const TabsRoot: typeof import("reka-ui")['TabsRoot']
export const TabsList: typeof import("reka-ui")['TabsList']
export const TabsContent: typeof import("reka-ui")['TabsContent']
export const TabsTrigger: typeof import("reka-ui")['TabsTrigger']
export const TabsIndicator: typeof import("reka-ui")['TabsIndicator']
export const TagsInputRoot: typeof import("reka-ui")['TagsInputRoot']
export const TagsInputInput: typeof import("reka-ui")['TagsInputInput']
export const TagsInputItem: typeof import("reka-ui")['TagsInputItem']
export const TagsInputItemText: typeof import("reka-ui")['TagsInputItemText']
export const TagsInputItemDelete: typeof import("reka-ui")['TagsInputItemDelete']
export const TagsInputClear: typeof import("reka-ui")['TagsInputClear']
export const TimeFieldInput: typeof import("reka-ui")['TimeFieldInput']
export const TimeFieldRoot: typeof import("reka-ui")['TimeFieldRoot']
export const ToastProvider: typeof import("reka-ui")['ToastProvider']
export const ToastRoot: typeof import("reka-ui")['ToastRoot']
export const ToastPortal: typeof import("reka-ui")['ToastPortal']
export const ToastAction: typeof import("reka-ui")['ToastAction']
export const ToastClose: typeof import("reka-ui")['ToastClose']
export const ToastViewport: typeof import("reka-ui")['ToastViewport']
export const ToastTitle: typeof import("reka-ui")['ToastTitle']
export const ToastDescription: typeof import("reka-ui")['ToastDescription']
export const Toggle: typeof import("reka-ui")['Toggle']
export const ToggleGroupRoot: typeof import("reka-ui")['ToggleGroupRoot']
export const ToggleGroupItem: typeof import("reka-ui")['ToggleGroupItem']
export const ToolbarRoot: typeof import("reka-ui")['ToolbarRoot']
export const ToolbarButton: typeof import("reka-ui")['ToolbarButton']
export const ToolbarLink: typeof import("reka-ui")['ToolbarLink']
export const ToolbarToggleGroup: typeof import("reka-ui")['ToolbarToggleGroup']
export const ToolbarToggleItem: typeof import("reka-ui")['ToolbarToggleItem']
export const ToolbarSeparator: typeof import("reka-ui")['ToolbarSeparator']
export const TooltipRoot: typeof import("reka-ui")['TooltipRoot']
export const TooltipTrigger: typeof import("reka-ui")['TooltipTrigger']
export const TooltipContent: typeof import("reka-ui")['TooltipContent']
export const TooltipArrow: typeof import("reka-ui")['TooltipArrow']
export const TooltipPortal: typeof import("reka-ui")['TooltipPortal']
export const TooltipProvider: typeof import("reka-ui")['TooltipProvider']
export const TreeRoot: typeof import("reka-ui")['TreeRoot']
export const TreeItem: typeof import("reka-ui")['TreeItem']
export const TreeVirtualizer: typeof import("reka-ui")['TreeVirtualizer']
export const Viewport: typeof import("reka-ui")['Viewport']
export const ConfigProvider: typeof import("reka-ui")['ConfigProvider']
export const FocusScope: typeof import("reka-ui")['FocusScope']
export const RovingFocusGroup: typeof import("reka-ui")['RovingFocusGroup']
export const RovingFocusItem: typeof import("reka-ui")['RovingFocusItem']
export const Presence: typeof import("reka-ui")['Presence']
export const Primitive: typeof import("reka-ui")['Primitive']
export const Slot: typeof import("reka-ui")['Slot']
export const VisuallyHidden: typeof import("reka-ui")['VisuallyHidden']
export const NuxtLinkLocale: typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const SwitchLocalePathLink: typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const ContentRenderer: typeof import("../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue")['default']
export const MDC: typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue")['default']
export const MDCRenderer: typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue")['default']
export const MDCSlot: typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue")['default']
export const ColorScheme: typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const Icon: typeof import("../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtPage: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyCodeGroup: LazyComponent<typeof import("../app/components/content/CodeGroup.vue")['default']>
export const LazyCodeTimeline: LazyComponent<typeof import("../app/components/content/CodeTimeline.vue")['default']>
export const LazyCodeTimelineItem: LazyComponent<typeof import("../app/components/content/CodeTimelineItem.vue")['default']>
export const LazyDocButton: LazyComponent<typeof import("../app/components/content/DocButton.vue")['default']>
export const LazyDocChecklist: LazyComponent<typeof import("../app/components/content/DocChecklist.vue")['default']>
export const LazyDocComponentDemo: LazyComponent<typeof import("../app/components/content/DocComponentDemo.vue")['default']>
export const LazyDocComponentList: LazyComponent<typeof import("../app/components/content/DocComponentList.vue")['default']>
export const LazyDocComponentMeta: LazyComponent<typeof import("../app/components/content/DocComponentMeta.vue")['default']>
export const LazyDocCustomizerButton: LazyComponent<typeof import("../app/components/content/DocCustomizerButton.vue")['default']>
export const LazyDocGrid: LazyComponent<typeof import("../app/components/content/DocGrid.vue")['default']>
export const LazyDocGridIcon: LazyComponent<typeof import("../app/components/content/DocGridIcon.vue")['default']>
export const LazyDocImage: LazyComponent<typeof import("../app/components/content/DocImage.vue")['default']>
export const LazyDocInfo: LazyComponent<typeof import("../app/components/content/DocInfo.vue")['default']>
export const LazyDocLinker: LazyComponent<typeof import("../app/components/content/DocLinker.vue")['default']>
export const LazyDocMessage: LazyComponent<typeof import("../app/components/content/DocMessage.vue")['default']>
export const LazyDocOverview: LazyComponent<typeof import("../app/components/content/DocOverview.vue")['default']>
export const LazyDocOverviewLayers: LazyComponent<typeof import("../app/components/content/DocOverviewLayers.vue")['default']>
export const LazyDocStacks: LazyComponent<typeof import("../app/components/content/DocStacks.vue")['default']>
export const LazyDocTag: LazyComponent<typeof import("../app/components/content/DocTag.vue")['default']>
export const LazyAddonApexcharts: LazyComponent<typeof import("../app/components/AddonApexcharts.vue")['default']>
export const LazyAddonCollapseTransition: LazyComponent<typeof import("../app/components/AddonCollapseTransition.vue")['default']>
export const LazyAddonDatepicker: LazyComponent<typeof import("../app/components/AddonDatepicker.vue")['default']>
export const LazyAddonInputPassword: LazyComponent<typeof import("../app/components/AddonInputPassword.vue")['default']>
export const LazyAddonInputPhone: LazyComponent<typeof import("../app/components/AddonInputPhone.vue")['default']>
export const LazyAddonLightweightCharts: LazyComponent<typeof import("../app/components/AddonLightweightCharts.vue")['default']>
export const LazyAddonMapboxLocationPicker: LazyComponent<typeof import("../app/components/AddonMapboxLocationPicker.vue")['default']>
export const LazyCodeGroupHeader: LazyComponent<typeof import("../app/components/CodeGroupHeader.vue")['default']>
export const LazyComponentMetaCode: LazyComponent<typeof import("../app/components/ComponentMetaCode.vue")['default']>
export const LazyDemoAccountMenu: LazyComponent<typeof import("../app/components/DemoAccountMenu.vue")['default']>
export const LazyDemoActionText: LazyComponent<typeof import("../app/components/DemoActionText.vue")['default']>
export const LazyDemoActivityTable: LazyComponent<typeof import("../app/components/DemoActivityTable.vue")['default']>
export const LazyDemoAppLayoutSwitcher: LazyComponent<typeof import("../app/components/DemoAppLayoutSwitcher.vue")['default']>
export const LazyDemoAppSearch: LazyComponent<typeof import("../app/components/DemoAppSearch.vue")['default']>
export const LazyDemoAppSearchResult: LazyComponent<typeof import("../app/components/DemoAppSearchResult.vue")['default']>
export const LazyDemoAuthorsListCompact: LazyComponent<typeof import("../app/components/DemoAuthorsListCompact.vue")['default']>
export const LazyDemoCalendarEvent: LazyComponent<typeof import("../app/components/DemoCalendarEvent.vue")['default']>
export const LazyDemoCalendarEventPending: LazyComponent<typeof import("../app/components/DemoCalendarEventPending.vue")['default']>
export const LazyDemoCardFilters: LazyComponent<typeof import("../app/components/DemoCardFilters.vue")['default']>
export const LazyDemoCommentListCompact: LazyComponent<typeof import("../app/components/DemoCommentListCompact.vue")['default']>
export const LazyDemoCompanyOverview: LazyComponent<typeof import("../app/components/DemoCompanyOverview.vue")['default']>
export const LazyDemoCreditCard: LazyComponent<typeof import("../app/components/DemoCreditCard.vue")['default']>
export const LazyDemoCreditCardReal: LazyComponent<typeof import("../app/components/DemoCreditCardReal.vue")['default']>
export const LazyDemoCreditCardSmall: LazyComponent<typeof import("../app/components/DemoCreditCardSmall.vue")['default']>
export const LazyDemoDatepicker: LazyComponent<typeof import("../app/components/DemoDatepicker.vue")['default']>
export const LazyDemoDaysSquare: LazyComponent<typeof import("../app/components/DemoDaysSquare.vue")['default']>
export const LazyDemoFileListTabbed: LazyComponent<typeof import("../app/components/DemoFileListTabbed.vue")['default']>
export const LazyDemoFlexTableCell: LazyComponent<typeof import("../app/components/DemoFlexTableCell.vue")['default']>
export const LazyDemoFlexTableRow: LazyComponent<typeof import("../app/components/DemoFlexTableRow.vue")['default']>
export const LazyDemoFlexTableStart: LazyComponent<typeof import("../app/components/DemoFlexTableStart.vue")['default']>
export const LazyDemoFlexTableWrapper: LazyComponent<typeof import("../app/components/DemoFlexTableWrapper.vue")['default']>
export const LazyDemoFollowersCompact: LazyComponent<typeof import("../app/components/DemoFollowersCompact.vue")['default']>
export const LazyDemoIconLinks: LazyComponent<typeof import("../app/components/DemoIconLinks.vue")['default']>
export const LazyDemoIconText: LazyComponent<typeof import("../app/components/DemoIconText.vue")['default']>
export const LazyDemoIconsSquare: LazyComponent<typeof import("../app/components/DemoIconsSquare.vue")['default']>
export const LazyDemoImageLinks: LazyComponent<typeof import("../app/components/DemoImageLinks.vue")['default']>
export const LazyDemoInboxMessage: LazyComponent<typeof import("../app/components/DemoInboxMessage.vue")['default']>
export const LazyDemoInfoBadges: LazyComponent<typeof import("../app/components/DemoInfoBadges.vue")['default']>
export const LazyDemoInfoImage: LazyComponent<typeof import("../app/components/DemoInfoImage.vue")['default']>
export const LazyDemoLeagueListCompact: LazyComponent<typeof import("../app/components/DemoLeagueListCompact.vue")['default']>
export const LazyDemoLinkArrow: LazyComponent<typeof import("../app/components/DemoLinkArrow.vue")['default']>
export const LazyDemoMapMarker: LazyComponent<typeof import("../app/components/DemoMapMarker.vue")['default']>
export const LazyDemoMenuIconList: LazyComponent<typeof import("../app/components/DemoMenuIconList.vue")['default']>
export const LazyDemoNavigationTop: LazyComponent<typeof import("../app/components/DemoNavigationTop.vue")['default']>
export const LazyDemoNotificationsCompact: LazyComponent<typeof import("../app/components/DemoNotificationsCompact.vue")['default']>
export const LazyDemoOfferCollapse: LazyComponent<typeof import("../app/components/DemoOfferCollapse.vue")['default']>
export const LazyDemoPanelAccount: LazyComponent<typeof import("../app/components/DemoPanelAccount.vue")['default']>
export const LazyDemoPanelActivity: LazyComponent<typeof import("../app/components/DemoPanelActivity.vue")['default']>
export const LazyDemoPanelCard: LazyComponent<typeof import("../app/components/DemoPanelCard.vue")['default']>
export const LazyDemoPanelInvest: LazyComponent<typeof import("../app/components/DemoPanelInvest.vue")['default']>
export const LazyDemoPanelLanguage: LazyComponent<typeof import("../app/components/DemoPanelLanguage.vue")['default']>
export const LazyDemoPanelSearch: LazyComponent<typeof import("../app/components/DemoPanelSearch.vue")['default']>
export const LazyDemoPanelTask: LazyComponent<typeof import("../app/components/DemoPanelTask.vue")['default']>
export const LazyDemoPendingTickets: LazyComponent<typeof import("../app/components/DemoPendingTickets.vue")['default']>
export const LazyDemoPicture: LazyComponent<typeof import("../app/components/DemoPicture.vue")['default']>
export const LazyDemoPlaceholderCompact: LazyComponent<typeof import("../app/components/DemoPlaceholderCompact.vue")['default']>
export const LazyDemoPlaceholderMinimal: LazyComponent<typeof import("../app/components/DemoPlaceholderMinimal.vue")['default']>
export const LazyDemoPopularCryptos: LazyComponent<typeof import("../app/components/DemoPopularCryptos.vue")['default']>
export const LazyDemoProductCompact: LazyComponent<typeof import("../app/components/DemoProductCompact.vue")['default']>
export const LazyDemoProgressCircle: LazyComponent<typeof import("../app/components/DemoProgressCircle.vue")['default']>
export const LazyDemoProjectListCompact: LazyComponent<typeof import("../app/components/DemoProjectListCompact.vue")['default']>
export const LazyDemoSearchCompact: LazyComponent<typeof import("../app/components/DemoSearchCompact.vue")['default']>
export const LazyDemoShoppingCartCompact: LazyComponent<typeof import("../app/components/DemoShoppingCartCompact.vue")['default']>
export const LazyDemoSocialLinks: LazyComponent<typeof import("../app/components/DemoSocialLinks.vue")['default']>
export const LazyDemoStarterSwitcher: LazyComponent<typeof import("../app/components/DemoStarterSwitcher.vue")['default']>
export const LazyDemoSubsidebarMessaging: LazyComponent<typeof import("../app/components/DemoSubsidebarMessaging.vue")['default']>
export const LazyDemoTabbedContent: LazyComponent<typeof import("../app/components/DemoTabbedContent.vue")['default']>
export const LazyDemoTagListCompact: LazyComponent<typeof import("../app/components/DemoTagListCompact.vue")['default']>
export const LazyDemoTeamListCompact: LazyComponent<typeof import("../app/components/DemoTeamListCompact.vue")['default']>
export const LazyDemoTeamSearchCompact: LazyComponent<typeof import("../app/components/DemoTeamSearchCompact.vue")['default']>
export const LazyDemoTimelineCompact: LazyComponent<typeof import("../app/components/DemoTimelineCompact.vue")['default']>
export const LazyDemoTodoListCompact: LazyComponent<typeof import("../app/components/DemoTodoListCompact.vue")['default']>
export const LazyDemoTodoListTabbed: LazyComponent<typeof import("../app/components/DemoTodoListTabbed.vue")['default']>
export const LazyDemoToolbar: LazyComponent<typeof import("../app/components/DemoToolbar.vue")['default']>
export const LazyDemoToolbarTopnav: LazyComponent<typeof import("../app/components/DemoToolbarTopnav.vue")['default']>
export const LazyDemoTopicListCompact: LazyComponent<typeof import("../app/components/DemoTopicListCompact.vue")['default']>
export const LazyDemoTransactionsFilters: LazyComponent<typeof import("../app/components/DemoTransactionsFilters.vue")['default']>
export const LazyDemoTransactionsListPlaceload: LazyComponent<typeof import("../app/components/DemoTransactionsListPlaceload.vue")['default']>
export const LazyDemoTrendingSkills: LazyComponent<typeof import("../app/components/DemoTrendingSkills.vue")['default']>
export const LazyDemoUserList: LazyComponent<typeof import("../app/components/DemoUserList.vue")['default']>
export const LazyDemoVcardRight: LazyComponent<typeof import("../app/components/DemoVcardRight.vue")['default']>
export const LazyDemoVideoCompact: LazyComponent<typeof import("../app/components/DemoVideoCompact.vue")['default']>
export const LazyDemoWidgetAccountBalance: LazyComponent<typeof import("../app/components/DemoWidgetAccountBalance.vue")['default']>
export const LazyDemoWidgetFeatures: LazyComponent<typeof import("../app/components/DemoWidgetFeatures.vue")['default']>
export const LazyDemoWidgetInvest: LazyComponent<typeof import("../app/components/DemoWidgetInvest.vue")['default']>
export const LazyDemoWidgetMoneyIn: LazyComponent<typeof import("../app/components/DemoWidgetMoneyIn.vue")['default']>
export const LazyDemoWidgetMoneyOut: LazyComponent<typeof import("../app/components/DemoWidgetMoneyOut.vue")['default']>
export const LazyDemoWidgetTransactionCompact: LazyComponent<typeof import("../app/components/DemoWidgetTransactionCompact.vue")['default']>
export const LazyDemoWidgetTransactionSummary: LazyComponent<typeof import("../app/components/DemoWidgetTransactionSummary.vue")['default']>
export const LazyDemoWidgetWelcome: LazyComponent<typeof import("../app/components/DemoWidgetWelcome.vue")['default']>
export const LazyDemoWizardButtons: LazyComponent<typeof import("../app/components/DemoWizardButtons.vue")['default']>
export const LazyDemoWizardNavigation: LazyComponent<typeof import("../app/components/DemoWizardNavigation.vue")['default']>
export const LazyDemoWizardStepTitle: LazyComponent<typeof import("../app/components/DemoWizardStepTitle.vue")['default']>
export const LazyDemoWorkspaceDropdown: LazyComponent<typeof import("../app/components/DemoWorkspaceDropdown.vue")['default']>
export const LazyDocLayoutSection: LazyComponent<typeof import("../app/components/DocLayoutSection.vue")['default']>
export const LazyDocSurround: LazyComponent<typeof import("../app/components/DocSurround.vue")['default']>
export const LazyDocToc: LazyComponent<typeof import("../app/components/DocToc.vue")['default']>
export const LazyTairoLogo: LazyComponent<typeof import("../app/components/TairoLogo.vue")['default']>
export const LazyTairoLogoText: LazyComponent<typeof import("../app/components/TairoLogoText.vue")['default']>
export const LazyVectorChartStockOne: LazyComponent<typeof import("../app/components/VectorChartStockOne.vue")['default']>
export const LazyVectorChartStockThree: LazyComponent<typeof import("../app/components/VectorChartStockThree.vue")['default']>
export const LazyVectorChartStockTwo: LazyComponent<typeof import("../app/components/VectorChartStockTwo.vue")['default']>
export const LazyVectorIllustrationBankFront: LazyComponent<typeof import("../app/components/VectorIllustrationBankFront.vue")['default']>
export const LazyVectorIllustrationCalendar: LazyComponent<typeof import("../app/components/VectorIllustrationCalendar.vue")['default']>
export const LazyVectorIllustrationCreditCard: LazyComponent<typeof import("../app/components/VectorIllustrationCreditCard.vue")['default']>
export const LazyVectorIllustrationManWondering: LazyComponent<typeof import("../app/components/VectorIllustrationManWondering.vue")['default']>
export const LazyVectorIllustrationTransaction: LazyComponent<typeof import("../app/components/VectorIllustrationTransaction.vue")['default']>
export const LazyVectorLogoVisa: LazyComponent<typeof import("../app/components/VectorLogoVisa.vue")['default']>
export const LazyDemoChartArea: LazyComponent<typeof import("../app/components/demo-chart/DemoChartArea.vue")['default']>
export const LazyDemoChartAreaBalance: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaBalance.vue")['default']>
export const LazyDemoChartAreaBtcPrice: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaBtcPrice.vue")['default']>
export const LazyDemoChartAreaCondition: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaCondition.vue")['default']>
export const LazyDemoChartAreaCustomers: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaCustomers.vue")['default']>
export const LazyDemoChartAreaExpenses: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaExpenses.vue")['default']>
export const LazyDemoChartAreaIncomeHistory: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaIncomeHistory.vue")['default']>
export const LazyDemoChartAreaInterviews: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaInterviews.vue")['default']>
export const LazyDemoChartAreaMulti: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaMulti.vue")['default']>
export const LazyDemoChartAreaProgress: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaProgress.vue")['default']>
export const LazyDemoChartAreaSparkSalesFour: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesFour.vue")['default']>
export const LazyDemoChartAreaSparkSalesOne: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesOne.vue")['default']>
export const LazyDemoChartAreaSparkSalesThree: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesThree.vue")['default']>
export const LazyDemoChartAreaSparkSalesTwo: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaSparkSalesTwo.vue")['default']>
export const LazyDemoChartAreaStats: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaStats.vue")['default']>
export const LazyDemoChartAreaStockPrice: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaStockPrice.vue")['default']>
export const LazyDemoChartAreaTaskCompletion: LazyComponent<typeof import("../app/components/demo-chart/DemoChartAreaTaskCompletion.vue")['default']>
export const LazyDemoChartBar: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBar.vue")['default']>
export const LazyDemoChartBarHorizontal: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarHorizontal.vue")['default']>
export const LazyDemoChartBarHorizontalMulti: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']>
export const LazyDemoChartBarMulti: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarMulti.vue")['default']>
export const LazyDemoChartBarMultiIncome: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarMultiIncome.vue")['default']>
export const LazyDemoChartBarOrders: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarOrders.vue")['default']>
export const LazyDemoChartBarOxygen: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarOxygen.vue")['default']>
export const LazyDemoChartBarProfit: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarProfit.vue")['default']>
export const LazyDemoChartBarRange: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarRange.vue")['default']>
export const LazyDemoChartBarSalesProfit: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarSalesProfit.vue")['default']>
export const LazyDemoChartBarSocialChannels: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarSocialChannels.vue")['default']>
export const LazyDemoChartBarStacked: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarStacked.vue")['default']>
export const LazyDemoChartBarTeamEfficiency: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBarTeamEfficiency.vue")['default']>
export const LazyDemoChartBubble: LazyComponent<typeof import("../app/components/demo-chart/DemoChartBubble.vue")['default']>
export const LazyDemoChartDonut: LazyComponent<typeof import("../app/components/demo-chart/DemoChartDonut.vue")['default']>
export const LazyDemoChartDonutExpenses: LazyComponent<typeof import("../app/components/demo-chart/DemoChartDonutExpenses.vue")['default']>
export const LazyDemoChartLine: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLine.vue")['default']>
export const LazyDemoChartLineMulti: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineMulti.vue")['default']>
export const LazyDemoChartLineMultiAlt: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineMultiAlt.vue")['default']>
export const LazyDemoChartLineRevenue: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineRevenue.vue")['default']>
export const LazyDemoChartLineSparkFour: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkFour.vue")['default']>
export const LazyDemoChartLineSparkOne: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkOne.vue")['default']>
export const LazyDemoChartLineSparkThree: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkThree.vue")['default']>
export const LazyDemoChartLineSparkTwo: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineSparkTwo.vue")['default']>
export const LazyDemoChartLineStep: LazyComponent<typeof import("../app/components/demo-chart/DemoChartLineStep.vue")['default']>
export const LazyDemoChartPie: LazyComponent<typeof import("../app/components/demo-chart/DemoChartPie.vue")['default']>
export const LazyDemoChartRadar: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadar.vue")['default']>
export const LazyDemoChartRadial: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadial.vue")['default']>
export const LazyDemoChartRadialEvolution: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialEvolution.vue")['default']>
export const LazyDemoChartRadialGauge: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGauge.vue")['default']>
export const LazyDemoChartRadialGaugeAlt: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']>
export const LazyDemoChartRadialGoal: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGoal.vue")['default']>
export const LazyDemoChartRadialGrowth: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialGrowth.vue")['default']>
export const LazyDemoChartRadialMulti: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialMulti.vue")['default']>
export const LazyDemoChartRadialPopularity: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialPopularity.vue")['default']>
export const LazyDemoChartRadialSalesRevenue: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSalesRevenue.vue")['default']>
export const LazyDemoChartRadialSmallOne: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallOne.vue")['default']>
export const LazyDemoChartRadialSmallThree: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallThree.vue")['default']>
export const LazyDemoChartRadialSmallTwo: LazyComponent<typeof import("../app/components/demo-chart/DemoChartRadialSmallTwo.vue")['default']>
export const LazyDemoChartScatter: LazyComponent<typeof import("../app/components/demo-chart/DemoChartScatter.vue")['default']>
export const LazyDemoChartScatterEnergy: LazyComponent<typeof import("../app/components/demo-chart/DemoChartScatterEnergy.vue")['default']>
export const LazyDemoChartTimeline: LazyComponent<typeof import("../app/components/demo-chart/DemoChartTimeline.vue")['default']>
export const LazyLandingBenefits: LazyComponent<typeof import("../app/components/landing/LandingBenefits.vue")['default']>
export const LazyLandingContent: LazyComponent<typeof import("../app/components/landing/LandingContent.vue")['default']>
export const LazyLandingCta: LazyComponent<typeof import("../app/components/landing/LandingCta.vue")['default']>
export const LazyLandingCustomizer: LazyComponent<typeof import("../app/components/landing/LandingCustomizer.vue")['default']>
export const LazyLandingDemoLink: LazyComponent<typeof import("../app/components/landing/LandingDemoLink.vue")['default']>
export const LazyLandingDemos: LazyComponent<typeof import("../app/components/landing/LandingDemos.vue")['default']>
export const LazyLandingFeatures: LazyComponent<typeof import("../app/components/landing/LandingFeatures.vue")['default']>
export const LazyLandingFeaturesTile: LazyComponent<typeof import("../app/components/landing/LandingFeaturesTile.vue")['default']>
export const LazyLandingFooter: LazyComponent<typeof import("../app/components/landing/LandingFooter.vue")['default']>
export const LazyLandingHero: LazyComponent<typeof import("../app/components/landing/LandingHero.vue")['default']>
export const LazyLandingHeroMockup: LazyComponent<typeof import("../app/components/landing/LandingHeroMockup.vue")['default']>
export const LazyLandingLayers: LazyComponent<typeof import("../app/components/landing/LandingLayers.vue")['default']>
export const LazyLandingLayersBox: LazyComponent<typeof import("../app/components/landing/LandingLayersBox.vue")['default']>
export const LazyLandingLayout: LazyComponent<typeof import("../app/components/landing/LandingLayout.vue")['default']>
export const LazyLandingLayouts: LazyComponent<typeof import("../app/components/landing/LandingLayouts.vue")['default']>
export const LazyLandingMobileNav: LazyComponent<typeof import("../app/components/landing/LandingMobileNav.vue")['default']>
export const LazyLandingNavbar: LazyComponent<typeof import("../app/components/landing/LandingNavbar.vue")['default']>
export const LazyProseA: LazyComponent<typeof import("../app/components/prose/ProseA.vue")['default']>
export const LazyProseBlockquote: LazyComponent<typeof import("../app/components/prose/ProseBlockquote.vue")['default']>
export const LazyProseCode: LazyComponent<typeof import("../app/components/prose/ProseCode.vue")['default']>
export const LazyProseCodeInline: LazyComponent<typeof import("../app/components/prose/ProseCodeInline.vue")['default']>
export const LazyProseEm: LazyComponent<typeof import("../app/components/prose/ProseEm.vue")['default']>
export const LazyProseH1: LazyComponent<typeof import("../app/components/prose/ProseH1.vue")['default']>
export const LazyProseH2: LazyComponent<typeof import("../app/components/prose/ProseH2.vue")['default']>
export const LazyProseH3: LazyComponent<typeof import("../app/components/prose/ProseH3.vue")['default']>
export const LazyProseH5: LazyComponent<typeof import("../app/components/prose/ProseH5.vue")['default']>
export const LazyProseH6: LazyComponent<typeof import("../app/components/prose/ProseH6.vue")['default']>
export const LazyProseHr: LazyComponent<typeof import("../app/components/prose/ProseHr.vue")['default']>
export const LazyProseImg: LazyComponent<typeof import("../app/components/prose/ProseImg.vue")['default']>
export const LazyProseLi: LazyComponent<typeof import("../app/components/prose/ProseLi.vue")['default']>
export const LazyProseOl: LazyComponent<typeof import("../app/components/prose/ProseOl.vue")['default']>
export const LazyProseP: LazyComponent<typeof import("../app/components/prose/ProseP.vue")['default']>
export const LazyProsePre: LazyComponent<typeof import("../app/components/prose/ProsePre.vue")['default']>
export const LazyProseScript: LazyComponent<typeof import("../app/components/prose/ProseScript.vue")['default']>
export const LazyProseStrong: LazyComponent<typeof import("../app/components/prose/ProseStrong.vue")['default']>
export const LazyProseTable: LazyComponent<typeof import("../app/components/prose/ProseTable.vue")['default']>
export const LazyProseTbody: LazyComponent<typeof import("../app/components/prose/ProseTbody.vue")['default']>
export const LazyProseTd: LazyComponent<typeof import("../app/components/prose/ProseTd.vue")['default']>
export const LazyProseTh: LazyComponent<typeof import("../app/components/prose/ProseTh.vue")['default']>
export const LazyProseThead: LazyComponent<typeof import("../app/components/prose/ProseThead.vue")['default']>
export const LazyProseTr: LazyComponent<typeof import("../app/components/prose/ProseTr.vue")['default']>
export const LazyProseUl: LazyComponent<typeof import("../app/components/prose/ProseUl.vue")['default']>
export const LazyProseH4: LazyComponent<typeof import("../app/components/prose/proseH4.vue")['default']>
export const LazyTairoCheckAnimated: LazyComponent<typeof import("../../layers/tairo/components/TairoCheckAnimated.vue")['default']>
export const LazyTairoCheckboxAnimated: LazyComponent<typeof import("../../layers/tairo/components/TairoCheckboxAnimated.vue")['default']>
export const LazyTairoCheckboxCardIcon: LazyComponent<typeof import("../../layers/tairo/components/TairoCheckboxCardIcon.vue")['default']>
export const LazyTairoContentWrapper: LazyComponent<typeof import("../../layers/tairo/components/TairoContentWrapper.vue")['default']>
export const LazyTairoContentWrapperTabbed: LazyComponent<typeof import("../../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']>
export const LazyTairoError: LazyComponent<typeof import("../../layers/tairo/components/TairoError.vue")['default']>
export const LazyTairoFlexTable: LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTable.vue")['default']>
export const LazyTairoFlexTableCell: LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableCell.vue")['default']>
export const LazyTairoFlexTableHeading: LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableHeading.vue")['default']>
export const LazyTairoFlexTableRow: LazyComponent<typeof import("../../layers/tairo/components/TairoFlexTableRow.vue")['default']>
export const LazyTairoFormGroup: LazyComponent<typeof import("../../layers/tairo/components/TairoFormGroup.vue")['default']>
export const LazyTairoFormSave: LazyComponent<typeof import("../../layers/tairo/components/TairoFormSave.vue")['default']>
export const LazyTairoFullscreenDropfile: LazyComponent<typeof import("../../layers/tairo/components/TairoFullscreenDropfile.vue")['default']>
export const LazyTairoImageZoom: LazyComponent<typeof import("../../layers/tairo/components/TairoImageZoom.vue")['default']>
export const LazyTairoInput: LazyComponent<typeof import("../../layers/tairo/components/TairoInput.vue")['default']>
export const LazyTairoInputFileHeadless: LazyComponent<typeof import("../../layers/tairo/components/TairoInputFileHeadless.vue")['default']>
export const LazyTairoMobileDrawer: LazyComponent<typeof import("../../layers/tairo/components/TairoMobileDrawer.vue")['default']>
export const LazyTairoPanels: LazyComponent<typeof import("../../layers/tairo/components/TairoPanels.vue")['default']>
export const LazyTairoRadioCard: LazyComponent<typeof import("../../layers/tairo/components/TairoRadioCard.vue")['default']>
export const LazyTairoSelect: LazyComponent<typeof import("../../layers/tairo/components/TairoSelect.vue")['default']>
export const LazyTairoSelectItem: LazyComponent<typeof import("../../layers/tairo/components/TairoSelectItem.vue")['default']>
export const LazyTairoTable: LazyComponent<typeof import("../../layers/tairo/components/TairoTable.vue")['default']>
export const LazyTairoTableCell: LazyComponent<typeof import("../../layers/tairo/components/TairoTableCell.vue")['default']>
export const LazyTairoTableHeading: LazyComponent<typeof import("../../layers/tairo/components/TairoTableHeading.vue")['default']>
export const LazyTairoTableRow: LazyComponent<typeof import("../../layers/tairo/components/TairoTableRow.vue")['default']>
export const LazyTairoWelcome: LazyComponent<typeof import("../../layers/tairo/components/TairoWelcome.vue")['default']>
export const LazyTairoCollapseBackdrop: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseBackdrop.vue")['default']>
export const LazyTairoCollapseCollapsible: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsible.vue")['default']>
export const LazyTairoCollapseCollapsibleLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']>
export const LazyTairoCollapseCollapsibleTrigger: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']>
export const LazyTairoCollapseContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseContent.vue")['default']>
export const LazyTairoCollapseLayout: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseLayout.vue")['default']>
export const LazyTairoCollapseSidebar: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebar.vue")['default']>
export const LazyTairoCollapseSidebarClose: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']>
export const LazyTairoCollapseSidebarHeader: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']>
export const LazyTairoCollapseSidebarLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']>
export const LazyTairoCollapseSidebarLinks: LazyComponent<typeof import("../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']>
export const LazyTairoMenu: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenu.vue")['default']>
export const LazyTairoMenuContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuContent.vue")['default']>
export const LazyTairoMenuIndicator: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuIndicator.vue")['default']>
export const LazyTairoMenuItem: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuItem.vue")['default']>
export const LazyTairoMenuLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLink.vue")['default']>
export const LazyTairoMenuLinkTab: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuLinkTab.vue")['default']>
export const LazyTairoMenuList: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuList.vue")['default']>
export const LazyTairoMenuListItems: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuListItems.vue")['default']>
export const LazyTairoMenuTrigger: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuTrigger.vue")['default']>
export const LazyTairoMenuViewport: LazyComponent<typeof import("../../layers/tairo/components/tairo-menu/TairoMenuViewport.vue")['default']>
export const LazyTairoSidebar: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebar.vue")['default']>
export const LazyTairoSidebarBackdrop: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']>
export const LazyTairoSidebarContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarContent.vue")['default']>
export const LazyTairoSidebarLayout: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLayout.vue")['default']>
export const LazyTairoSidebarLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLink.vue")['default']>
export const LazyTairoSidebarLinks: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarLinks.vue")['default']>
export const LazyTairoSidebarNav: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarNav.vue")['default']>
export const LazyTairoSidebarSubsidebar: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsible: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsibleLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsibleTrigger: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']>
export const LazyTairoSidebarSubsidebarContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']>
export const LazyTairoSidebarSubsidebarHeader: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']>
export const LazyTairoSidebarSubsidebarLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']>
export const LazyTairoSidebarTrigger: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidebar/TairoSidebarTrigger.vue")['default']>
export const LazyTairoSidenavBackdrop: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']>
export const LazyTairoSidenavCollapsible: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']>
export const LazyTairoSidenavCollapsibleLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']>
export const LazyTairoSidenavCollapsibleTrigger: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']>
export const LazyTairoSidenavContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavContent.vue")['default']>
export const LazyTairoSidenavLayout: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavLayout.vue")['default']>
export const LazyTairoSidenavSidebar: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebar.vue")['default']>
export const LazyTairoSidenavSidebarDivider: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']>
export const LazyTairoSidenavSidebarHeader: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']>
export const LazyTairoSidenavSidebarLink: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']>
export const LazyTairoSidenavSidebarLinks: LazyComponent<typeof import("../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']>
export const LazyTairoTopnavContent: LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavContent.vue")['default']>
export const LazyTairoTopnavHeader: LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavHeader.vue")['default']>
export const LazyTairoTopnavLayout: LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavLayout.vue")['default']>
export const LazyTairoTopnavNavbar: LazyComponent<typeof import("../../layers/tairo/components/tairo-topnav/TairoTopnavNavbar.vue")['default']>
export const LazyExamplesAddonsDatepicker: LazyComponent<typeof import("../examples/addons/datepicker.vue")['default']>
export const LazyExamplesAddonsMapbox: LazyComponent<typeof import("../examples/addons/mapbox.vue")['default']>
export const LazyExamplesApexchartsBase: LazyComponent<typeof import("../examples/apexcharts/base.vue")['default']>
export const LazyExamplesFlexTableCurved: LazyComponent<typeof import("../examples/flex-table/curved.vue")['default']>
export const LazyExamplesFlexTableRounded: LazyComponent<typeof import("../examples/flex-table/rounded.vue")['default']>
export const LazyExamplesFlexTableSmooth: LazyComponent<typeof import("../examples/flex-table/smooth.vue")['default']>
export const LazyExamplesFlexTableStraight: LazyComponent<typeof import("../examples/flex-table/straight.vue")['default']>
export const LazyExamplesInputPasswordBase: LazyComponent<typeof import("../examples/input-password/base.vue")['default']>
export const LazyExamplesInputPasswordDisabled: LazyComponent<typeof import("../examples/input-password/disabled.vue")['default']>
export const LazyExamplesInputPasswordLocale: LazyComponent<typeof import("../examples/input-password/locale.vue")['default']>
export const LazyExamplesInputPasswordUserInput: LazyComponent<typeof import("../examples/input-password/user-input.vue")['default']>
export const LazyExamplesInputPasswordValidation: LazyComponent<typeof import("../examples/input-password/validation.vue")['default']>
export const LazyExamplesInputPhoneBase: LazyComponent<typeof import("../examples/input-phone/base.vue")['default']>
export const LazyExamplesInputPhoneCountry: LazyComponent<typeof import("../examples/input-phone/country.vue")['default']>
export const LazyExamplesInputPhoneDisabled: LazyComponent<typeof import("../examples/input-phone/disabled.vue")['default']>
export const LazyExamplesInputPhoneFormat: LazyComponent<typeof import("../examples/input-phone/format.vue")['default']>
export const LazyExamplesInputPhoneShape: LazyComponent<typeof import("../examples/input-phone/shape.vue")['default']>
export const LazyExamplesInputPhoneSize: LazyComponent<typeof import("../examples/input-phone/size.vue")['default']>
export const LazyExamplesInputPhoneValidation: LazyComponent<typeof import("../examples/input-phone/validation.vue")['default']>
export const LazyExamplesLightweightChartsBase: LazyComponent<typeof import("../examples/lightweight-charts/base.vue")['default']>
export const LazyExamplesPanelActivity: LazyComponent<typeof import("../examples/panel/activity.vue")['default']>
export const LazyExamplesPanelLanguage: LazyComponent<typeof import("../examples/panel/language.vue")['default']>
export const LazyExamplesPanelSearch: LazyComponent<typeof import("../examples/panel/search.vue")['default']>
export const LazyExamplesPanelTask: LazyComponent<typeof import("../examples/panel/task.vue")['default']>
export const LazyExamplesTableCurved: LazyComponent<typeof import("../examples/table/curved.vue")['default']>
export const LazyExamplesTableMediaCurved: LazyComponent<typeof import("../examples/table/media-curved.vue")['default']>
export const LazyExamplesTableMediaRounded: LazyComponent<typeof import("../examples/table/media-rounded.vue")['default']>
export const LazyExamplesTableMediaSmooth: LazyComponent<typeof import("../examples/table/media-smooth.vue")['default']>
export const LazyExamplesTableMediaStraight: LazyComponent<typeof import("../examples/table/media-straight.vue")['default']>
export const LazyExamplesTableRounded: LazyComponent<typeof import("../examples/table/rounded.vue")['default']>
export const LazyExamplesTableSmooth: LazyComponent<typeof import("../examples/table/smooth.vue")['default']>
export const LazyExamplesTableStraight: LazyComponent<typeof import("../examples/table/straight.vue")['default']>
export const LazyExamplesTairoCheckAnimated: LazyComponent<typeof import("../examples/tairo/check-animated.vue")['default']>
export const LazyExamplesTairoCheckboxAnimated: LazyComponent<typeof import("../examples/tairo/checkbox-animated.vue")['default']>
export const LazyExamplesTairoCheckboxCardIcon: LazyComponent<typeof import("../examples/tairo/checkbox-card-icon.vue")['default']>
export const LazyExamplesTairoCircularMenu: LazyComponent<typeof import("../examples/tairo/circular-menu.vue")['default']>
export const LazyExamplesTairoError: LazyComponent<typeof import("../examples/tairo/error.vue")['default']>
export const LazyExamplesTairoFormGroup: LazyComponent<typeof import("../examples/tairo/form-group.vue")['default']>
export const LazyExamplesTairoFormSave: LazyComponent<typeof import("../examples/tairo/form-save.vue")['default']>
export const LazyExamplesTairoInput: LazyComponent<typeof import("../examples/tairo/input.vue")['default']>
export const LazyExamplesTairoLogo: LazyComponent<typeof import("../examples/tairo/logo.vue")['default']>
export const LazyExamplesTairoLogotext: LazyComponent<typeof import("../examples/tairo/logotext.vue")['default']>
export const LazyExamplesTairoMenuComplete: LazyComponent<typeof import("../examples/tairo/menu-complete.vue")['default']>
export const LazyExamplesTairoMenu: LazyComponent<typeof import("../examples/tairo/menu.vue")['default']>
export const LazyExamplesTairoMobileDrawer: LazyComponent<typeof import("../examples/tairo/mobile-drawer.vue")['default']>
export const LazyExamplesTairoRadioCard: LazyComponent<typeof import("../examples/tairo/radio-card.vue")['default']>
export const LazyExamplesTairoSelect: LazyComponent<typeof import("../examples/tairo/select.vue")['default']>
export const LazyExamplesTairoValidation: LazyComponent<typeof import("../examples/tairo/validation.vue")['default']>
export const LazyBaseAccordion: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']>
export const LazyBaseAccordionItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']>
export const LazyBaseAutocomplete: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']>
export const LazyBaseAutocompleteGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']>
export const LazyBaseAutocompleteItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']>
export const LazyBaseAutocompleteLabel: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']>
export const LazyBaseAutocompleteSeparator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']>
export const LazyBaseAvatar: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']>
export const LazyBaseAvatarGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']>
export const LazyBaseBreadcrumb: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']>
export const LazyBaseButton: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']>
export const LazyBaseCard: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']>
export const LazyBaseCheckbox: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']>
export const LazyBaseCheckboxGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']>
export const LazyBaseChip: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']>
export const LazyBaseDropdown: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']>
export const LazyBaseDropdownArrow: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']>
export const LazyBaseDropdownCheckbox: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']>
export const LazyBaseDropdownItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']>
export const LazyBaseDropdownLabel: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']>
export const LazyBaseDropdownRadioGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']>
export const LazyBaseDropdownRadioItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']>
export const LazyBaseDropdownSeparator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']>
export const LazyBaseDropdownSub: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']>
export const LazyBaseField: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']>
export const LazyBaseHeading: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']>
export const LazyBaseIconBox: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']>
export const LazyBaseInput: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']>
export const LazyBaseInputFile: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']>
export const LazyBaseInputNumber: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']>
export const LazyBaseKbd: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']>
export const LazyBaseLink: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']>
export const LazyBaseList: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']>
export const LazyBaseListItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']>
export const LazyBaseMessage: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']>
export const LazyBasePagination: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']>
export const LazyBasePaginationButtonFirst: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']>
export const LazyBasePaginationButtonLast: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']>
export const LazyBasePaginationButtonNext: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']>
export const LazyBasePaginationButtonPrev: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']>
export const LazyBasePaginationItems: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']>
export const LazyBaseParagraph: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']>
export const LazyBasePlaceholderPage: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']>
export const LazyBasePlaceload: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']>
export const LazyBasePopover: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']>
export const LazyBasePrimitiveField: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']>
export const LazyBasePrimitiveFieldController: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']>
export const LazyBasePrimitiveFieldDescription: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']>
export const LazyBasePrimitiveFieldError: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']>
export const LazyBasePrimitiveFieldErrorIndicator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']>
export const LazyBasePrimitiveFieldLabel: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']>
export const LazyBasePrimitiveFieldLoadingIndicator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']>
export const LazyBasePrimitiveFieldRequiredIndicator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']>
export const LazyBasePrimitiveFieldSuccessIndicator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']>
export const LazyBaseProgress: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']>
export const LazyBaseProgressCircle: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']>
export const LazyBaseProse: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']>
export const LazyBaseProviders: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']>
export const LazyBaseRadio: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']>
export const LazyBaseRadioGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']>
export const LazyBaseSelect: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']>
export const LazyBaseSelectGroup: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']>
export const LazyBaseSelectItem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']>
export const LazyBaseSelectLabel: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']>
export const LazyBaseSelectSeparator: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']>
export const LazyBaseSlider: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']>
export const LazyBaseSnack: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']>
export const LazyBaseSwitchBall: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']>
export const LazyBaseSwitchThin: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']>
export const LazyBaseTabs: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']>
export const LazyBaseTabsContent: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']>
export const LazyBaseTabsTrigger: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']>
export const LazyBaseTag: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']>
export const LazyBaseText: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']>
export const LazyBaseTextarea: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']>
export const LazyBaseThemeSwitch: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']>
export const LazyBaseThemeSystem: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']>
export const LazyBaseThemeToggle: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']>
export const LazyBaseToast: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']>
export const LazyBaseToastProvider: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']>
export const LazyBaseTooltip: LazyComponent<typeof import("../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue")['default']>
export const LazyNuxtPicture: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue")['default']>
export const LazyAccordionContent: LazyComponent<typeof import("reka-ui")['AccordionContent']>
export const LazyAccordionHeader: LazyComponent<typeof import("reka-ui")['AccordionHeader']>
export const LazyAccordionItem: LazyComponent<typeof import("reka-ui")['AccordionItem']>
export const LazyAccordionRoot: LazyComponent<typeof import("reka-ui")['AccordionRoot']>
export const LazyAccordionTrigger: LazyComponent<typeof import("reka-ui")['AccordionTrigger']>
export const LazyAlertDialogRoot: LazyComponent<typeof import("reka-ui")['AlertDialogRoot']>
export const LazyAlertDialogTrigger: LazyComponent<typeof import("reka-ui")['AlertDialogTrigger']>
export const LazyAlertDialogPortal: LazyComponent<typeof import("reka-ui")['AlertDialogPortal']>
export const LazyAlertDialogContent: LazyComponent<typeof import("reka-ui")['AlertDialogContent']>
export const LazyAlertDialogOverlay: LazyComponent<typeof import("reka-ui")['AlertDialogOverlay']>
export const LazyAlertDialogCancel: LazyComponent<typeof import("reka-ui")['AlertDialogCancel']>
export const LazyAlertDialogTitle: LazyComponent<typeof import("reka-ui")['AlertDialogTitle']>
export const LazyAlertDialogDescription: LazyComponent<typeof import("reka-ui")['AlertDialogDescription']>
export const LazyAlertDialogAction: LazyComponent<typeof import("reka-ui")['AlertDialogAction']>
export const LazyAspectRatio: LazyComponent<typeof import("reka-ui")['AspectRatio']>
export const LazyAvatarRoot: LazyComponent<typeof import("reka-ui")['AvatarRoot']>
export const LazyAvatarFallback: LazyComponent<typeof import("reka-ui")['AvatarFallback']>
export const LazyAvatarImage: LazyComponent<typeof import("reka-ui")['AvatarImage']>
export const LazyCalendarRoot: LazyComponent<typeof import("reka-ui")['CalendarRoot']>
export const LazyCalendarHeader: LazyComponent<typeof import("reka-ui")['CalendarHeader']>
export const LazyCalendarHeading: LazyComponent<typeof import("reka-ui")['CalendarHeading']>
export const LazyCalendarGrid: LazyComponent<typeof import("reka-ui")['CalendarGrid']>
export const LazyCalendarCell: LazyComponent<typeof import("reka-ui")['CalendarCell']>
export const LazyCalendarHeadCell: LazyComponent<typeof import("reka-ui")['CalendarHeadCell']>
export const LazyCalendarNext: LazyComponent<typeof import("reka-ui")['CalendarNext']>
export const LazyCalendarPrev: LazyComponent<typeof import("reka-ui")['CalendarPrev']>
export const LazyCalendarGridHead: LazyComponent<typeof import("reka-ui")['CalendarGridHead']>
export const LazyCalendarGridBody: LazyComponent<typeof import("reka-ui")['CalendarGridBody']>
export const LazyCalendarGridRow: LazyComponent<typeof import("reka-ui")['CalendarGridRow']>
export const LazyCalendarCellTrigger: LazyComponent<typeof import("reka-ui")['CalendarCellTrigger']>
export const LazyCheckboxGroupRoot: LazyComponent<typeof import("reka-ui")['CheckboxGroupRoot']>
export const LazyCheckboxRoot: LazyComponent<typeof import("reka-ui")['CheckboxRoot']>
export const LazyCheckboxIndicator: LazyComponent<typeof import("reka-ui")['CheckboxIndicator']>
export const LazyCollapsibleRoot: LazyComponent<typeof import("reka-ui")['CollapsibleRoot']>
export const LazyCollapsibleTrigger: LazyComponent<typeof import("reka-ui")['CollapsibleTrigger']>
export const LazyCollapsibleContent: LazyComponent<typeof import("reka-ui")['CollapsibleContent']>
export const LazyComboboxRoot: LazyComponent<typeof import("reka-ui")['ComboboxRoot']>
export const LazyComboboxInput: LazyComponent<typeof import("reka-ui")['ComboboxInput']>
export const LazyComboboxAnchor: LazyComponent<typeof import("reka-ui")['ComboboxAnchor']>
export const LazyComboboxEmpty: LazyComponent<typeof import("reka-ui")['ComboboxEmpty']>
export const LazyComboboxTrigger: LazyComponent<typeof import("reka-ui")['ComboboxTrigger']>
export const LazyComboboxCancel: LazyComponent<typeof import("reka-ui")['ComboboxCancel']>
export const LazyComboboxGroup: LazyComponent<typeof import("reka-ui")['ComboboxGroup']>
export const LazyComboboxLabel: LazyComponent<typeof import("reka-ui")['ComboboxLabel']>
export const LazyComboboxContent: LazyComponent<typeof import("reka-ui")['ComboboxContent']>
export const LazyComboboxViewport: LazyComponent<typeof import("reka-ui")['ComboboxViewport']>
export const LazyComboboxVirtualizer: LazyComponent<typeof import("reka-ui")['ComboboxVirtualizer']>
export const LazyComboboxItem: LazyComponent<typeof import("reka-ui")['ComboboxItem']>
export const LazyComboboxItemIndicator: LazyComponent<typeof import("reka-ui")['ComboboxItemIndicator']>
export const LazyComboboxSeparator: LazyComponent<typeof import("reka-ui")['ComboboxSeparator']>
export const LazyComboboxArrow: LazyComponent<typeof import("reka-ui")['ComboboxArrow']>
export const LazyComboboxPortal: LazyComponent<typeof import("reka-ui")['ComboboxPortal']>
export const LazyContextMenuRoot: LazyComponent<typeof import("reka-ui")['ContextMenuRoot']>
export const LazyContextMenuTrigger: LazyComponent<typeof import("reka-ui")['ContextMenuTrigger']>
export const LazyContextMenuPortal: LazyComponent<typeof import("reka-ui")['ContextMenuPortal']>
export const LazyContextMenuContent: LazyComponent<typeof import("reka-ui")['ContextMenuContent']>
export const LazyContextMenuArrow: LazyComponent<typeof import("reka-ui")['ContextMenuArrow']>
export const LazyContextMenuItem: LazyComponent<typeof import("reka-ui")['ContextMenuItem']>
export const LazyContextMenuGroup: LazyComponent<typeof import("reka-ui")['ContextMenuGroup']>
export const LazyContextMenuSeparator: LazyComponent<typeof import("reka-ui")['ContextMenuSeparator']>
export const LazyContextMenuCheckboxItem: LazyComponent<typeof import("reka-ui")['ContextMenuCheckboxItem']>
export const LazyContextMenuItemIndicator: LazyComponent<typeof import("reka-ui")['ContextMenuItemIndicator']>
export const LazyContextMenuLabel: LazyComponent<typeof import("reka-ui")['ContextMenuLabel']>
export const LazyContextMenuRadioGroup: LazyComponent<typeof import("reka-ui")['ContextMenuRadioGroup']>
export const LazyContextMenuRadioItem: LazyComponent<typeof import("reka-ui")['ContextMenuRadioItem']>
export const LazyContextMenuSub: LazyComponent<typeof import("reka-ui")['ContextMenuSub']>
export const LazyContextMenuSubContent: LazyComponent<typeof import("reka-ui")['ContextMenuSubContent']>
export const LazyContextMenuSubTrigger: LazyComponent<typeof import("reka-ui")['ContextMenuSubTrigger']>
export const LazyDateFieldRoot: LazyComponent<typeof import("reka-ui")['DateFieldRoot']>
export const LazyDateFieldInput: LazyComponent<typeof import("reka-ui")['DateFieldInput']>
export const LazyDatePickerRoot: LazyComponent<typeof import("reka-ui")['DatePickerRoot']>
export const LazyDatePickerHeader: LazyComponent<typeof import("reka-ui")['DatePickerHeader']>
export const LazyDatePickerHeading: LazyComponent<typeof import("reka-ui")['DatePickerHeading']>
export const LazyDatePickerGrid: LazyComponent<typeof import("reka-ui")['DatePickerGrid']>
export const LazyDatePickerCell: LazyComponent<typeof import("reka-ui")['DatePickerCell']>
export const LazyDatePickerHeadCell: LazyComponent<typeof import("reka-ui")['DatePickerHeadCell']>
export const LazyDatePickerNext: LazyComponent<typeof import("reka-ui")['DatePickerNext']>
export const LazyDatePickerPrev: LazyComponent<typeof import("reka-ui")['DatePickerPrev']>
export const LazyDatePickerGridHead: LazyComponent<typeof import("reka-ui")['DatePickerGridHead']>
export const LazyDatePickerGridBody: LazyComponent<typeof import("reka-ui")['DatePickerGridBody']>
export const LazyDatePickerGridRow: LazyComponent<typeof import("reka-ui")['DatePickerGridRow']>
export const LazyDatePickerCellTrigger: LazyComponent<typeof import("reka-ui")['DatePickerCellTrigger']>
export const LazyDatePickerInput: LazyComponent<typeof import("reka-ui")['DatePickerInput']>
export const LazyDatePickerCalendar: LazyComponent<typeof import("reka-ui")['DatePickerCalendar']>
export const LazyDatePickerField: LazyComponent<typeof import("reka-ui")['DatePickerField']>
export const LazyDatePickerAnchor: LazyComponent<typeof import("reka-ui")['DatePickerAnchor']>
export const LazyDatePickerArrow: LazyComponent<typeof import("reka-ui")['DatePickerArrow']>
export const LazyDatePickerClose: LazyComponent<typeof import("reka-ui")['DatePickerClose']>
export const LazyDatePickerTrigger: LazyComponent<typeof import("reka-ui")['DatePickerTrigger']>
export const LazyDatePickerContent: LazyComponent<typeof import("reka-ui")['DatePickerContent']>
export const LazyDateRangePickerRoot: LazyComponent<typeof import("reka-ui")['DateRangePickerRoot']>
export const LazyDateRangePickerHeader: LazyComponent<typeof import("reka-ui")['DateRangePickerHeader']>
export const LazyDateRangePickerHeading: LazyComponent<typeof import("reka-ui")['DateRangePickerHeading']>
export const LazyDateRangePickerGrid: LazyComponent<typeof import("reka-ui")['DateRangePickerGrid']>
export const LazyDateRangePickerCell: LazyComponent<typeof import("reka-ui")['DateRangePickerCell']>
export const LazyDateRangePickerHeadCell: LazyComponent<typeof import("reka-ui")['DateRangePickerHeadCell']>
export const LazyDateRangePickerNext: LazyComponent<typeof import("reka-ui")['DateRangePickerNext']>
export const LazyDateRangePickerPrev: LazyComponent<typeof import("reka-ui")['DateRangePickerPrev']>
export const LazyDateRangePickerGridHead: LazyComponent<typeof import("reka-ui")['DateRangePickerGridHead']>
export const LazyDateRangePickerGridBody: LazyComponent<typeof import("reka-ui")['DateRangePickerGridBody']>
export const LazyDateRangePickerGridRow: LazyComponent<typeof import("reka-ui")['DateRangePickerGridRow']>
export const LazyDateRangePickerCellTrigger: LazyComponent<typeof import("reka-ui")['DateRangePickerCellTrigger']>
export const LazyDateRangePickerInput: LazyComponent<typeof import("reka-ui")['DateRangePickerInput']>
export const LazyDateRangePickerCalendar: LazyComponent<typeof import("reka-ui")['DateRangePickerCalendar']>
export const LazyDateRangePickerField: LazyComponent<typeof import("reka-ui")['DateRangePickerField']>
export const LazyDateRangePickerAnchor: LazyComponent<typeof import("reka-ui")['DateRangePickerAnchor']>
export const LazyDateRangePickerArrow: LazyComponent<typeof import("reka-ui")['DateRangePickerArrow']>
export const LazyDateRangePickerClose: LazyComponent<typeof import("reka-ui")['DateRangePickerClose']>
export const LazyDateRangePickerTrigger: LazyComponent<typeof import("reka-ui")['DateRangePickerTrigger']>
export const LazyDateRangePickerContent: LazyComponent<typeof import("reka-ui")['DateRangePickerContent']>
export const LazyDateRangeFieldRoot: LazyComponent<typeof import("reka-ui")['DateRangeFieldRoot']>
export const LazyDateRangeFieldInput: LazyComponent<typeof import("reka-ui")['DateRangeFieldInput']>
export const LazyDialogRoot: LazyComponent<typeof import("reka-ui")['DialogRoot']>
export const LazyDialogTrigger: LazyComponent<typeof import("reka-ui")['DialogTrigger']>
export const LazyDialogPortal: LazyComponent<typeof import("reka-ui")['DialogPortal']>
export const LazyDialogContent: LazyComponent<typeof import("reka-ui")['DialogContent']>
export const LazyDialogOverlay: LazyComponent<typeof import("reka-ui")['DialogOverlay']>
export const LazyDialogClose: LazyComponent<typeof import("reka-ui")['DialogClose']>
export const LazyDialogTitle: LazyComponent<typeof import("reka-ui")['DialogTitle']>
export const LazyDialogDescription: LazyComponent<typeof import("reka-ui")['DialogDescription']>
export const LazyDropdownMenuRoot: LazyComponent<typeof import("reka-ui")['DropdownMenuRoot']>
export const LazyDropdownMenuTrigger: LazyComponent<typeof import("reka-ui")['DropdownMenuTrigger']>
export const LazyDropdownMenuPortal: LazyComponent<typeof import("reka-ui")['DropdownMenuPortal']>
export const LazyDropdownMenuContent: LazyComponent<typeof import("reka-ui")['DropdownMenuContent']>
export const LazyDropdownMenuArrow: LazyComponent<typeof import("reka-ui")['DropdownMenuArrow']>
export const LazyDropdownMenuItem: LazyComponent<typeof import("reka-ui")['DropdownMenuItem']>
export const LazyDropdownMenuGroup: LazyComponent<typeof import("reka-ui")['DropdownMenuGroup']>
export const LazyDropdownMenuSeparator: LazyComponent<typeof import("reka-ui")['DropdownMenuSeparator']>
export const LazyDropdownMenuCheckboxItem: LazyComponent<typeof import("reka-ui")['DropdownMenuCheckboxItem']>
export const LazyDropdownMenuItemIndicator: LazyComponent<typeof import("reka-ui")['DropdownMenuItemIndicator']>
export const LazyDropdownMenuLabel: LazyComponent<typeof import("reka-ui")['DropdownMenuLabel']>
export const LazyDropdownMenuRadioGroup: LazyComponent<typeof import("reka-ui")['DropdownMenuRadioGroup']>
export const LazyDropdownMenuRadioItem: LazyComponent<typeof import("reka-ui")['DropdownMenuRadioItem']>
export const LazyDropdownMenuSub: LazyComponent<typeof import("reka-ui")['DropdownMenuSub']>
export const LazyDropdownMenuSubContent: LazyComponent<typeof import("reka-ui")['DropdownMenuSubContent']>
export const LazyDropdownMenuSubTrigger: LazyComponent<typeof import("reka-ui")['DropdownMenuSubTrigger']>
export const LazyEditableRoot: LazyComponent<typeof import("reka-ui")['EditableRoot']>
export const LazyEditableArea: LazyComponent<typeof import("reka-ui")['EditableArea']>
export const LazyEditableInput: LazyComponent<typeof import("reka-ui")['EditableInput']>
export const LazyEditablePreview: LazyComponent<typeof import("reka-ui")['EditablePreview']>
export const LazyEditableSubmitTrigger: LazyComponent<typeof import("reka-ui")['EditableSubmitTrigger']>
export const LazyEditableCancelTrigger: LazyComponent<typeof import("reka-ui")['EditableCancelTrigger']>
export const LazyEditableEditTrigger: LazyComponent<typeof import("reka-ui")['EditableEditTrigger']>
export const LazyHoverCardRoot: LazyComponent<typeof import("reka-ui")['HoverCardRoot']>
export const LazyHoverCardTrigger: LazyComponent<typeof import("reka-ui")['HoverCardTrigger']>
export const LazyHoverCardPortal: LazyComponent<typeof import("reka-ui")['HoverCardPortal']>
export const LazyHoverCardContent: LazyComponent<typeof import("reka-ui")['HoverCardContent']>
export const LazyHoverCardArrow: LazyComponent<typeof import("reka-ui")['HoverCardArrow']>
export const LazyLabel: LazyComponent<typeof import("reka-ui")['Label']>
export const LazyListboxRoot: LazyComponent<typeof import("reka-ui")['ListboxRoot']>
export const LazyListboxContent: LazyComponent<typeof import("reka-ui")['ListboxContent']>
export const LazyListboxFilter: LazyComponent<typeof import("reka-ui")['ListboxFilter']>
export const LazyListboxItem: LazyComponent<typeof import("reka-ui")['ListboxItem']>
export const LazyListboxItemIndicator: LazyComponent<typeof import("reka-ui")['ListboxItemIndicator']>
export const LazyListboxVirtualizer: LazyComponent<typeof import("reka-ui")['ListboxVirtualizer']>
export const LazyListboxGroup: LazyComponent<typeof import("reka-ui")['ListboxGroup']>
export const LazyListboxGroupLabel: LazyComponent<typeof import("reka-ui")['ListboxGroupLabel']>
export const LazyMenubarRoot: LazyComponent<typeof import("reka-ui")['MenubarRoot']>
export const LazyMenubarTrigger: LazyComponent<typeof import("reka-ui")['MenubarTrigger']>
export const LazyMenubarPortal: LazyComponent<typeof import("reka-ui")['MenubarPortal']>
export const LazyMenubarContent: LazyComponent<typeof import("reka-ui")['MenubarContent']>
export const LazyMenubarArrow: LazyComponent<typeof import("reka-ui")['MenubarArrow']>
export const LazyMenubarItem: LazyComponent<typeof import("reka-ui")['MenubarItem']>
export const LazyMenubarGroup: LazyComponent<typeof import("reka-ui")['MenubarGroup']>
export const LazyMenubarSeparator: LazyComponent<typeof import("reka-ui")['MenubarSeparator']>
export const LazyMenubarCheckboxItem: LazyComponent<typeof import("reka-ui")['MenubarCheckboxItem']>
export const LazyMenubarItemIndicator: LazyComponent<typeof import("reka-ui")['MenubarItemIndicator']>
export const LazyMenubarLabel: LazyComponent<typeof import("reka-ui")['MenubarLabel']>
export const LazyMenubarRadioGroup: LazyComponent<typeof import("reka-ui")['MenubarRadioGroup']>
export const LazyMenubarRadioItem: LazyComponent<typeof import("reka-ui")['MenubarRadioItem']>
export const LazyMenubarSub: LazyComponent<typeof import("reka-ui")['MenubarSub']>
export const LazyMenubarSubContent: LazyComponent<typeof import("reka-ui")['MenubarSubContent']>
export const LazyMenubarSubTrigger: LazyComponent<typeof import("reka-ui")['MenubarSubTrigger']>
export const LazyMenubarMenu: LazyComponent<typeof import("reka-ui")['MenubarMenu']>
export const LazyNavigationMenuRoot: LazyComponent<typeof import("reka-ui")['NavigationMenuRoot']>
export const LazyNavigationMenuContent: LazyComponent<typeof import("reka-ui")['NavigationMenuContent']>
export const LazyNavigationMenuIndicator: LazyComponent<typeof import("reka-ui")['NavigationMenuIndicator']>
export const LazyNavigationMenuItem: LazyComponent<typeof import("reka-ui")['NavigationMenuItem']>
export const LazyNavigationMenuLink: LazyComponent<typeof import("reka-ui")['NavigationMenuLink']>
export const LazyNavigationMenuList: LazyComponent<typeof import("reka-ui")['NavigationMenuList']>
export const LazyNavigationMenuSub: LazyComponent<typeof import("reka-ui")['NavigationMenuSub']>
export const LazyNavigationMenuTrigger: LazyComponent<typeof import("reka-ui")['NavigationMenuTrigger']>
export const LazyNavigationMenuViewport: LazyComponent<typeof import("reka-ui")['NavigationMenuViewport']>
export const LazyNumberFieldRoot: LazyComponent<typeof import("reka-ui")['NumberFieldRoot']>
export const LazyNumberFieldInput: LazyComponent<typeof import("reka-ui")['NumberFieldInput']>
export const LazyNumberFieldIncrement: LazyComponent<typeof import("reka-ui")['NumberFieldIncrement']>
export const LazyNumberFieldDecrement: LazyComponent<typeof import("reka-ui")['NumberFieldDecrement']>
export const LazyPaginationRoot: LazyComponent<typeof import("reka-ui")['PaginationRoot']>
export const LazyPaginationEllipsis: LazyComponent<typeof import("reka-ui")['PaginationEllipsis']>
export const LazyPaginationFirst: LazyComponent<typeof import("reka-ui")['PaginationFirst']>
export const LazyPaginationLast: LazyComponent<typeof import("reka-ui")['PaginationLast']>
export const LazyPaginationList: LazyComponent<typeof import("reka-ui")['PaginationList']>
export const LazyPaginationListItem: LazyComponent<typeof import("reka-ui")['PaginationListItem']>
export const LazyPaginationNext: LazyComponent<typeof import("reka-ui")['PaginationNext']>
export const LazyPaginationPrev: LazyComponent<typeof import("reka-ui")['PaginationPrev']>
export const LazyPinInputRoot: LazyComponent<typeof import("reka-ui")['PinInputRoot']>
export const LazyPinInputInput: LazyComponent<typeof import("reka-ui")['PinInputInput']>
export const LazyPopoverRoot: LazyComponent<typeof import("reka-ui")['PopoverRoot']>
export const LazyPopoverTrigger: LazyComponent<typeof import("reka-ui")['PopoverTrigger']>
export const LazyPopoverPortal: LazyComponent<typeof import("reka-ui")['PopoverPortal']>
export const LazyPopoverContent: LazyComponent<typeof import("reka-ui")['PopoverContent']>
export const LazyPopoverArrow: LazyComponent<typeof import("reka-ui")['PopoverArrow']>
export const LazyPopoverClose: LazyComponent<typeof import("reka-ui")['PopoverClose']>
export const LazyPopoverAnchor: LazyComponent<typeof import("reka-ui")['PopoverAnchor']>
export const LazyProgressRoot: LazyComponent<typeof import("reka-ui")['ProgressRoot']>
export const LazyProgressIndicator: LazyComponent<typeof import("reka-ui")['ProgressIndicator']>
export const LazyRadioGroupRoot: LazyComponent<typeof import("reka-ui")['RadioGroupRoot']>
export const LazyRadioGroupItem: LazyComponent<typeof import("reka-ui")['RadioGroupItem']>
export const LazyRadioGroupIndicator: LazyComponent<typeof import("reka-ui")['RadioGroupIndicator']>
export const LazyRangeCalendarRoot: LazyComponent<typeof import("reka-ui")['RangeCalendarRoot']>
export const LazyRangeCalendarHeader: LazyComponent<typeof import("reka-ui")['RangeCalendarHeader']>
export const LazyRangeCalendarHeading: LazyComponent<typeof import("reka-ui")['RangeCalendarHeading']>
export const LazyRangeCalendarGrid: LazyComponent<typeof import("reka-ui")['RangeCalendarGrid']>
export const LazyRangeCalendarCell: LazyComponent<typeof import("reka-ui")['RangeCalendarCell']>
export const LazyRangeCalendarHeadCell: LazyComponent<typeof import("reka-ui")['RangeCalendarHeadCell']>
export const LazyRangeCalendarNext: LazyComponent<typeof import("reka-ui")['RangeCalendarNext']>
export const LazyRangeCalendarPrev: LazyComponent<typeof import("reka-ui")['RangeCalendarPrev']>
export const LazyRangeCalendarGridHead: LazyComponent<typeof import("reka-ui")['RangeCalendarGridHead']>
export const LazyRangeCalendarGridBody: LazyComponent<typeof import("reka-ui")['RangeCalendarGridBody']>
export const LazyRangeCalendarGridRow: LazyComponent<typeof import("reka-ui")['RangeCalendarGridRow']>
export const LazyRangeCalendarCellTrigger: LazyComponent<typeof import("reka-ui")['RangeCalendarCellTrigger']>
export const LazyScrollAreaRoot: LazyComponent<typeof import("reka-ui")['ScrollAreaRoot']>
export const LazyScrollAreaViewport: LazyComponent<typeof import("reka-ui")['ScrollAreaViewport']>
export const LazyScrollAreaScrollbar: LazyComponent<typeof import("reka-ui")['ScrollAreaScrollbar']>
export const LazyScrollAreaThumb: LazyComponent<typeof import("reka-ui")['ScrollAreaThumb']>
export const LazyScrollAreaCorner: LazyComponent<typeof import("reka-ui")['ScrollAreaCorner']>
export const LazySelectRoot: LazyComponent<typeof import("reka-ui")['SelectRoot']>
export const LazySelectTrigger: LazyComponent<typeof import("reka-ui")['SelectTrigger']>
export const LazySelectPortal: LazyComponent<typeof import("reka-ui")['SelectPortal']>
export const LazySelectContent: LazyComponent<typeof import("reka-ui")['SelectContent']>
export const LazySelectArrow: LazyComponent<typeof import("reka-ui")['SelectArrow']>
export const LazySelectSeparator: LazyComponent<typeof import("reka-ui")['SelectSeparator']>
export const LazySelectItemIndicator: LazyComponent<typeof import("reka-ui")['SelectItemIndicator']>
export const LazySelectLabel: LazyComponent<typeof import("reka-ui")['SelectLabel']>
export const LazySelectGroup: LazyComponent<typeof import("reka-ui")['SelectGroup']>
export const LazySelectItem: LazyComponent<typeof import("reka-ui")['SelectItem']>
export const LazySelectItemText: LazyComponent<typeof import("reka-ui")['SelectItemText']>
export const LazySelectViewport: LazyComponent<typeof import("reka-ui")['SelectViewport']>
export const LazySelectScrollUpButton: LazyComponent<typeof import("reka-ui")['SelectScrollUpButton']>
export const LazySelectScrollDownButton: LazyComponent<typeof import("reka-ui")['SelectScrollDownButton']>
export const LazySelectValue: LazyComponent<typeof import("reka-ui")['SelectValue']>
export const LazySelectIcon: LazyComponent<typeof import("reka-ui")['SelectIcon']>
export const LazySeparator: LazyComponent<typeof import("reka-ui")['Separator']>
export const LazySliderRoot: LazyComponent<typeof import("reka-ui")['SliderRoot']>
export const LazySliderThumb: LazyComponent<typeof import("reka-ui")['SliderThumb']>
export const LazySliderTrack: LazyComponent<typeof import("reka-ui")['SliderTrack']>
export const LazySliderRange: LazyComponent<typeof import("reka-ui")['SliderRange']>
export const LazySplitterGroup: LazyComponent<typeof import("reka-ui")['SplitterGroup']>
export const LazySplitterPanel: LazyComponent<typeof import("reka-ui")['SplitterPanel']>
export const LazySplitterResizeHandle: LazyComponent<typeof import("reka-ui")['SplitterResizeHandle']>
export const LazyStepperRoot: LazyComponent<typeof import("reka-ui")['StepperRoot']>
export const LazyStepperItem: LazyComponent<typeof import("reka-ui")['StepperItem']>
export const LazyStepperTrigger: LazyComponent<typeof import("reka-ui")['StepperTrigger']>
export const LazyStepperDescription: LazyComponent<typeof import("reka-ui")['StepperDescription']>
export const LazyStepperTitle: LazyComponent<typeof import("reka-ui")['StepperTitle']>
export const LazyStepperIndicator: LazyComponent<typeof import("reka-ui")['StepperIndicator']>
export const LazyStepperSeparator: LazyComponent<typeof import("reka-ui")['StepperSeparator']>
export const LazySwitchRoot: LazyComponent<typeof import("reka-ui")['SwitchRoot']>
export const LazySwitchThumb: LazyComponent<typeof import("reka-ui")['SwitchThumb']>
export const LazyTabsRoot: LazyComponent<typeof import("reka-ui")['TabsRoot']>
export const LazyTabsList: LazyComponent<typeof import("reka-ui")['TabsList']>
export const LazyTabsContent: LazyComponent<typeof import("reka-ui")['TabsContent']>
export const LazyTabsTrigger: LazyComponent<typeof import("reka-ui")['TabsTrigger']>
export const LazyTabsIndicator: LazyComponent<typeof import("reka-ui")['TabsIndicator']>
export const LazyTagsInputRoot: LazyComponent<typeof import("reka-ui")['TagsInputRoot']>
export const LazyTagsInputInput: LazyComponent<typeof import("reka-ui")['TagsInputInput']>
export const LazyTagsInputItem: LazyComponent<typeof import("reka-ui")['TagsInputItem']>
export const LazyTagsInputItemText: LazyComponent<typeof import("reka-ui")['TagsInputItemText']>
export const LazyTagsInputItemDelete: LazyComponent<typeof import("reka-ui")['TagsInputItemDelete']>
export const LazyTagsInputClear: LazyComponent<typeof import("reka-ui")['TagsInputClear']>
export const LazyTimeFieldInput: LazyComponent<typeof import("reka-ui")['TimeFieldInput']>
export const LazyTimeFieldRoot: LazyComponent<typeof import("reka-ui")['TimeFieldRoot']>
export const LazyToastProvider: LazyComponent<typeof import("reka-ui")['ToastProvider']>
export const LazyToastRoot: LazyComponent<typeof import("reka-ui")['ToastRoot']>
export const LazyToastPortal: LazyComponent<typeof import("reka-ui")['ToastPortal']>
export const LazyToastAction: LazyComponent<typeof import("reka-ui")['ToastAction']>
export const LazyToastClose: LazyComponent<typeof import("reka-ui")['ToastClose']>
export const LazyToastViewport: LazyComponent<typeof import("reka-ui")['ToastViewport']>
export const LazyToastTitle: LazyComponent<typeof import("reka-ui")['ToastTitle']>
export const LazyToastDescription: LazyComponent<typeof import("reka-ui")['ToastDescription']>
export const LazyToggle: LazyComponent<typeof import("reka-ui")['Toggle']>
export const LazyToggleGroupRoot: LazyComponent<typeof import("reka-ui")['ToggleGroupRoot']>
export const LazyToggleGroupItem: LazyComponent<typeof import("reka-ui")['ToggleGroupItem']>
export const LazyToolbarRoot: LazyComponent<typeof import("reka-ui")['ToolbarRoot']>
export const LazyToolbarButton: LazyComponent<typeof import("reka-ui")['ToolbarButton']>
export const LazyToolbarLink: LazyComponent<typeof import("reka-ui")['ToolbarLink']>
export const LazyToolbarToggleGroup: LazyComponent<typeof import("reka-ui")['ToolbarToggleGroup']>
export const LazyToolbarToggleItem: LazyComponent<typeof import("reka-ui")['ToolbarToggleItem']>
export const LazyToolbarSeparator: LazyComponent<typeof import("reka-ui")['ToolbarSeparator']>
export const LazyTooltipRoot: LazyComponent<typeof import("reka-ui")['TooltipRoot']>
export const LazyTooltipTrigger: LazyComponent<typeof import("reka-ui")['TooltipTrigger']>
export const LazyTooltipContent: LazyComponent<typeof import("reka-ui")['TooltipContent']>
export const LazyTooltipArrow: LazyComponent<typeof import("reka-ui")['TooltipArrow']>
export const LazyTooltipPortal: LazyComponent<typeof import("reka-ui")['TooltipPortal']>
export const LazyTooltipProvider: LazyComponent<typeof import("reka-ui")['TooltipProvider']>
export const LazyTreeRoot: LazyComponent<typeof import("reka-ui")['TreeRoot']>
export const LazyTreeItem: LazyComponent<typeof import("reka-ui")['TreeItem']>
export const LazyTreeVirtualizer: LazyComponent<typeof import("reka-ui")['TreeVirtualizer']>
export const LazyViewport: LazyComponent<typeof import("reka-ui")['Viewport']>
export const LazyConfigProvider: LazyComponent<typeof import("reka-ui")['ConfigProvider']>
export const LazyFocusScope: LazyComponent<typeof import("reka-ui")['FocusScope']>
export const LazyRovingFocusGroup: LazyComponent<typeof import("reka-ui")['RovingFocusGroup']>
export const LazyRovingFocusItem: LazyComponent<typeof import("reka-ui")['RovingFocusItem']>
export const LazyPresence: LazyComponent<typeof import("reka-ui")['Presence']>
export const LazyPrimitive: LazyComponent<typeof import("reka-ui")['Primitive']>
export const LazySlot: LazyComponent<typeof import("reka-ui")['Slot']>
export const LazyVisuallyHidden: LazyComponent<typeof import("reka-ui")['VisuallyHidden']>
export const LazyNuxtLinkLocale: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
export const LazySwitchLocalePathLink: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
export const LazyContentRenderer: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue")['default']>
export const LazyMDC: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue")['default']>
export const LazyMDCRenderer: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue")['default']>
export const LazyMDCSlot: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue")['default']>
export const LazyColorScheme: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
