@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules\gonzales-pe\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules\gonzales-pe\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules\gonzales-pe\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules\gonzales-pe\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\gonzales-pe@4.3.0\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\gonzales-pe\bin\gonzales.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\gonzales-pe\bin\gonzales.js" %*
)
