<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <BaseHeading tag="h3" size="sm" weight="medium" class="text-muted-800 dark:text-white">
        Pending Invitations
      </BaseHeading>
    </div>

    <!-- Loading state for invitations -->
    <div v-if="isLoading" class="flex justify-center py-4">
      <BaseProgress
        :value="50"
        size="sm"
        :indeterminate="true"
        color="primary"
      />
    </div>

    <!-- Error state for invitations -->
    <div v-else-if="error" class="text-danger-500 py-2">
      {{ error }}
      <BaseButton color="muted" size="xs" class="ml-2" @click="$emit('refresh')">
        Try Again
      </BaseButton>
    </div>

    <!-- Empty state for invitations -->
    <div v-else-if="invitations.length === 0" class="text-muted-400 py-4 text-center">
      No pending invitations
    </div>

    <!-- Invitations list -->
    <div v-else class="space-y-3">
      <TransitionGroup
        enter-active-class="transform-gpu"
        enter-from-class="opacity-0 -translate-x-full"
        enter-to-class="opacity-100 translate-x-0"
        leave-active-class="absolute transform-gpu"
        leave-from-class="opacity-100 translate-x-0"
        leave-to-class="opacity-0 -translate-x-full"
      >
        <InvitationRow
          v-for="invitation in invitations"
          :key="invitation.id"
          :invitation="invitation"
          :is-resending="resendingInvitation === invitation.id"
          :is-canceling="cancelingInvitation === invitation.id"
          @resend="$emit('resend', invitation)"
          @cancel="$emit('cancel', invitation)"
        />
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import InvitationRow from './InvitationRow.vue';

defineProps({
  invitations: {
    type: Array,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  resendingInvitation: {
    type: String,
    default: ''
  },
  cancelingInvitation: {
    type: String,
    default: ''
  }
});

defineEmits(['refresh', 'resend', 'cancel']);
</script>
