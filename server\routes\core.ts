import express from "express";
import { auth, wrapController } from "../utils/route-helpers.js";
import {
  getBusinessRules,
  saveBusinessRule,
  getModuleStatus,
  updateModuleStatus,
  getRecentDecisions,
} from "../controllers/core/core.controller.js";
import {
  getSystemHealth,
  getHealthHistory,
} from "../controllers/core/health.controller.js";
import { authorizeRoles } from "../middleware/authorizeRoles.js";
import {
  getAllAccessRights,
  getAccessRightsByRole,
  updateAccessRight,
  bulkUpdateAccessRights,
  getCurrentUserAccessRights,
  initializeAccessRights,
} from "../controllers/core/accessRights.controller.js";

const router = express.Router();

// Business Rules routes
router.get("/business-rules", auth, wrapController(getBusinessRules));
router.post("/business-rules", auth, wrapController(saveBusinessRule));
router.put("/business-rules/:id", auth, wrapController(saveBusinessRule));

// System Integration/Modules routes
router.get("/modules", auth, wrapController(getModuleStatus));
router.put("/modules/:id", auth, wrapController(updateModuleStatus));

// AI Decisions routes
router.get("/ai-decisions", auth, wrapController(getRecentDecisions));

// Health monitoring routes
router.get("/health", auth, wrapController(getSystemHealth));
router.get("/health/history", auth, wrapController(getHealthHistory));

// Access rights endpoints
router.get(
  "/access-rights",
  auth,
  authorizeRoles("SUPERADMIN") as express.RequestHandler,
  wrapController(getAllAccessRights)
);
router.get(
  "/access-rights/role/:role",
  auth,
  authorizeRoles("SUPERADMIN") as express.RequestHandler,
  wrapController(getAccessRightsByRole)
);
router.post(
  "/access-rights",
  auth,
  authorizeRoles("SUPERADMIN") as express.RequestHandler,
  wrapController(updateAccessRight)
);
router.post(
  "/access-rights/bulk",
  auth,
  authorizeRoles("SUPERADMIN") as express.RequestHandler,
  wrapController(bulkUpdateAccessRights)
);
router.get(
  "/access-rights/me",
  auth,
  wrapController(getCurrentUserAccessRights)
);
router.post(
  "/access-rights/initialize",
  auth,
  authorizeRoles("SUPERADMIN") as express.RequestHandler,
  wrapController(initializeAccessRights)
);

export default router;
