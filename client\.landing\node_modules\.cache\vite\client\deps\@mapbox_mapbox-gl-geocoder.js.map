{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/xtend@4.0.2/node_modules/xtend/immutable.js", "../../../../../../node_modules/.pnpm/fuzzy@0.1.3/node_modules/fuzzy/lib/fuzzy.js", "../../../../../../node_modules/.pnpm/suggestions@1.7.1/node_modules/suggestions/src/list.js", "../../../../../../node_modules/.pnpm/suggestions@1.7.1/node_modules/suggestions/src/suggestions.js", "../../../../../../node_modules/.pnpm/suggestions@1.7.1/node_modules/suggestions/index.js", "../../../../../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js", "../../../../../../node_modules/.pnpm/events@3.3.0/node_modules/events/events.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/exceptions.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/helpers/parse-link-header.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/classes/mapi-response.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/constants.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/classes/mapi-error.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/helpers/parse-headers.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/browser/browser-layer.js", "../../../../../../node_modules/.pnpm/base-64@0.1.0/node_modules/base-64/base64.js", "../../../../../../node_modules/.pnpm/@mapbox+parse-mapbox-token@0.2.0/node_modules/@mapbox/parse-mapbox-token/index.js", "../../../../../../node_modules/.pnpm/eventemitter3@3.1.2/node_modules/eventemitter3/index.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/helpers/url-utils.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/classes/mapi-request.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/classes/mapi-client.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/lib/browser/browser-client.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/index.js", "../../../../../../node_modules/.pnpm/is-plain-obj@1.1.0/node_modules/is-plain-obj/index.js", "../../../../../../node_modules/.pnpm/@mapbox+fusspot@0.4.0/node_modules/@mapbox/fusspot/lib/index.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/service-helpers/validator.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/service-helpers/pick.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/service-helpers/object-map.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/service-helpers/stringify-booleans.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/service-helpers/create-service-factory.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-sdk@0.16.1/node_modules/@mapbox/mapbox-sdk/services/geocoding.js", "../../../../../../node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/url-alphabet/index.js", "../../../../../../node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/index.browser.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/events.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/localization.js", "../../../../../../node_modules/.pnpm/subtag@0.5.0/node_modules/subtag/subtag.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/geolocation.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/utils.js", "../../../../../../node_modules/.pnpm/@mapbox+mapbox-gl-geocoder@5.0.3/node_modules/@mapbox/mapbox-gl-geocoder/lib/index.js"], "sourcesContent": ["module.exports = extend\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction extend() {\n    var target = {}\n\n    for (var i = 0; i < arguments.length; i++) {\n        var source = arguments[i]\n\n        for (var key in source) {\n            if (hasOwnProperty.call(source, key)) {\n                target[key] = source[key]\n            }\n        }\n    }\n\n    return target\n}\n", "/*\n * Fuzzy\n * https://github.com/myork/fuzzy\n *\n * Copyright (c) 2012 <PERSON>\n * Licensed under the MIT license.\n */\n\n(function() {\n\nvar root = this;\n\nvar fuzzy = {};\n\n// Use in node or in browser\nif (typeof exports !== 'undefined') {\n  module.exports = fuzzy;\n} else {\n  root.fuzzy = fuzzy;\n}\n\n// Return all elements of `array` that have a fuzzy\n// match against `pattern`.\nfuzzy.simpleFilter = function(pattern, array) {\n  return array.filter(function(str) {\n    return fuzzy.test(pattern, str);\n  });\n};\n\n// Does `pattern` fuzzy match `str`?\nfuzzy.test = function(pattern, str) {\n  return fuzzy.match(pattern, str) !== null;\n};\n\n// If `pattern` matches `str`, wrap each matching character\n// in `opts.pre` and `opts.post`. If no match, return null\nfuzzy.match = function(pattern, str, opts) {\n  opts = opts || {};\n  var patternIdx = 0\n    , result = []\n    , len = str.length\n    , totalScore = 0\n    , currScore = 0\n    // prefix\n    , pre = opts.pre || ''\n    // suffix\n    , post = opts.post || ''\n    // String to compare against. This might be a lowercase version of the\n    // raw string\n    , compareString =  opts.caseSensitive && str || str.toLowerCase()\n    , ch;\n\n  pattern = opts.caseSensitive && pattern || pattern.toLowerCase();\n\n  // For each character in the string, either add it to the result\n  // or wrap in template if it's the next string in the pattern\n  for(var idx = 0; idx < len; idx++) {\n    ch = str[idx];\n    if(compareString[idx] === pattern[patternIdx]) {\n      ch = pre + ch + post;\n      patternIdx += 1;\n\n      // consecutive characters should increase the score more than linearly\n      currScore += 1 + currScore;\n    } else {\n      currScore = 0;\n    }\n    totalScore += currScore;\n    result[result.length] = ch;\n  }\n\n  // return rendered string if we have a match for every char\n  if(patternIdx === pattern.length) {\n    // if the string is an exact match with pattern, totalScore should be maxed\n    totalScore = (compareString === pattern) ? Infinity : totalScore;\n    return {rendered: result.join(''), score: totalScore};\n  }\n\n  return null;\n};\n\n// The normal entry point. Filters `arr` for matches against `pattern`.\n// It returns an array with matching values of the type:\n//\n//     [{\n//         string:   '<b>lah' // The rendered string\n//       , index:    2        // The index of the element in `arr`\n//       , original: 'blah'   // The original element in `arr`\n//     }]\n//\n// `opts` is an optional argument bag. Details:\n//\n//    opts = {\n//        // string to put before a matching character\n//        pre:     '<b>'\n//\n//        // string to put after matching character\n//      , post:    '</b>'\n//\n//        // Optional function. Input is an entry in the given arr`,\n//        // output should be the string to test `pattern` against.\n//        // In this example, if `arr = [{crying: 'koala'}]` we would return\n//        // 'koala'.\n//      , extract: function(arg) { return arg.crying; }\n//    }\nfuzzy.filter = function(pattern, arr, opts) {\n  if(!arr || arr.length === 0) {\n    return [];\n  }\n  if (typeof pattern !== 'string') {\n    return arr;\n  }\n  opts = opts || {};\n  return arr\n    .reduce(function(prev, element, idx, arr) {\n      var str = element;\n      if(opts.extract) {\n        str = opts.extract(element);\n      }\n      var rendered = fuzzy.match(pattern, str, opts);\n      if(rendered != null) {\n        prev[prev.length] = {\n            string: rendered.rendered\n          , score: rendered.score\n          , index: idx\n          , original: element\n        };\n      }\n      return prev;\n    }, [])\n\n    // Sort by score. Browsers are inconsistent wrt stable/unstable\n    // sorting, so force stable by using the index in the case of tie.\n    // See http://ofb.net/~sethml/is-sort-stable.html\n    .sort(function(a,b) {\n      var compare = b.score - a.score;\n      if(compare) return compare;\n      return a.index - b.index;\n    });\n};\n\n\n}());\n\n", "'use strict';\n\nvar List = function(component) {\n  this.component = component;\n  this.items = [];\n  this.active = 0;\n  this.wrapper = document.createElement('div');\n  this.wrapper.className = 'suggestions-wrapper';\n  this.element = document.createElement('ul');\n  this.element.className = 'suggestions';\n  this.wrapper.appendChild(this.element);\n\n  // selectingListItem is set to true in the time between the mousedown and mouseup when clicking an item in the list\n  // mousedown on a list item will cause the input to blur which normally hides the list, so this flag is used to keep\n  // the list open until the mouseup\n  this.selectingListItem = false;\n\n  component.el.parentNode.insertBefore(this.wrapper, component.el.nextSibling);\n  return this;\n};\n\nList.prototype.show = function() {\n  this.element.style.display = 'block';\n};\n\nList.prototype.hide = function() {\n  this.element.style.display = 'none';\n};\n\nList.prototype.add = function(item) {\n  this.items.push(item);\n};\n\nList.prototype.clear = function() {\n  this.items = [];\n  this.active = 0;\n};\n\nList.prototype.isEmpty = function() {\n  return !this.items.length;\n};\n\nList.prototype.isVisible = function() {\n  return this.element.style.display === 'block';\n};\n\nList.prototype.draw = function() {\n  this.element.innerHTML = '';\n\n  if (this.items.length === 0) {\n    this.hide();\n    return;\n  }\n\n  for (var i = 0; i < this.items.length; i++) {\n    this.drawItem(this.items[i], this.active === i);\n  }\n\n  this.show();\n};\n\nList.prototype.drawItem = function(item, active) {\n  var li = document.createElement('li'),\n    a = document.createElement('a');\n\n  if (active) li.className += ' active';\n\n  a.innerHTML = item.string;\n\n  li.appendChild(a);\n  this.element.appendChild(li);\n\n  li.addEventListener('mousedown', function() {\n    this.selectingListItem = true;\n  }.bind(this));\n\n  li.addEventListener('mouseup', function() {\n    this.handleMouseUp.call(this, item);\n  }.bind(this));\n};\n\nList.prototype.handleMouseUp = function(item) {\n  this.selectingListItem = false;\n  this.component.value(item.original);\n  this.clear();\n  this.draw();\n};\n\nList.prototype.move = function(index) {\n  this.active = index;\n  this.draw();\n};\n\nList.prototype.previous = function() {\n  this.move(this.active === 0 ? this.items.length - 1 : this.active - 1);\n};\n\nList.prototype.next = function() {\n  this.move(this.active === this.items.length - 1 ? 0 : this.active + 1);\n};\n\nList.prototype.drawError = function(msg){\n  var li = document.createElement('li');\n\n  li.innerHTML = msg;\n\n  this.element.appendChild(li);\n  this.show();\n}\n\nmodule.exports = List;\n", "'use strict';\n\nvar extend = require('xtend');\nvar fuzzy = require('fuzzy');\nvar List = require('./list');\n\nvar Suggestions = function(el, data, options) {\n  options = options || {};\n\n  this.options = extend({\n    minLength: 2,\n    limit: 5,\n    filter: true,\n    hideOnBlur: true\n  }, options);\n\n  this.el = el;\n  this.data = data || [];\n  this.list = new List(this);\n\n  this.query = '';\n  this.selected = null;\n\n  this.list.draw();\n\n  this.el.addEventListener('keyup', function(e) {\n    this.handleKeyUp(e.keyCode);\n  }.bind(this), false);\n\n  this.el.addEventListener('keydown', function(e) {\n    this.handleKeyDown(e);\n  }.bind(this));\n\n  this.el.addEventListener('focus', function() {\n    this.handleFocus();\n  }.bind(this));\n\n  this.el.addEventListener('blur', function() {\n    this.handleBlur();\n  }.bind(this));\n\n  this.el.addEventListener('paste', function(e) {\n    this.handlePaste(e);\n  }.bind(this));\n\n  // use user-provided render function if given, otherwise just use the default\n  this.render = (this.options.render) ? this.options.render.bind(this) : this.render.bind(this)\n\n  this.getItemValue = (this.options.getItemValue) ? this.options.getItemValue.bind(this) : this.getItemValue.bind(this);\n\n  return this;\n};\n\nSuggestions.prototype.handleKeyUp = function(keyCode) {\n  // 40 - DOWN\n  // 38 - UP\n  // 27 - ESC\n  // 13 - ENTER\n  // 9 - TAB\n\n  if (keyCode === 40 ||\n      keyCode === 38 ||\n      keyCode === 27 ||\n      keyCode === 13 ||\n      keyCode === 9) return;\n\n  this.handleInputChange(this.el.value);\n};\n\nSuggestions.prototype.handleKeyDown = function(e) {\n  switch (e.keyCode) {\n    case 13: // ENTER\n    case 9: // TAB\n      if (!this.list.isEmpty()) {\n        if (this.list.isVisible()) {\n          e.preventDefault();\n        }\n        this.value(this.list.items[this.list.active].original);\n        this.list.hide();\n      }\n    break;\n    case 27: // ESC\n      if (!this.list.isEmpty()) this.list.hide();\n    break;\n    case 38: // UP\n      this.list.previous();\n    break;\n    case 40: // DOWN\n      this.list.next();\n    break;\n  }\n};\n\nSuggestions.prototype.handleBlur = function() {\n  if (!this.list.selectingListItem && this.options.hideOnBlur) {\n    this.list.hide();\n  }\n};\n\nSuggestions.prototype.handlePaste = function(e) {\n  if (e.clipboardData) {\n    this.handleInputChange(e.clipboardData.getData('Text'));\n  } else {\n    var self = this;\n    setTimeout(function () {\n      self.handleInputChange(e.target.value);\n    }, 100);\n  }\n};\n\nSuggestions.prototype.handleInputChange = function(query) {\n  this.query = this.normalize(query);\n\n  this.list.clear();\n\n  if (this.query.length < this.options.minLength) {\n    this.list.draw();\n    return;\n  }\n\n  this.getCandidates(function(data) {\n    for (var i = 0; i < data.length; i++) {\n      this.list.add(data[i]);\n      if (i === (this.options.limit - 1)) break;\n    }\n    this.list.draw();\n  }.bind(this));\n};\n\nSuggestions.prototype.handleFocus = function() {\n  if (!this.list.isEmpty()) this.list.show();\n  this.list.selectingListItem = false;\n};\n\n/**\n * Update data previously passed\n *\n * @param {Array} revisedData\n */\nSuggestions.prototype.update = function(revisedData) {\n  this.data = revisedData;\n  this.handleKeyUp();\n};\n\n/**\n * Clears data\n */\nSuggestions.prototype.clear = function() {\n  this.data = [];\n  this.list.clear();\n};\n\n/**\n * Normalize the results list and input value for matching\n *\n * @param {String} value\n * @return {String}\n */\nSuggestions.prototype.normalize = function(value) {\n  value = value.toLowerCase();\n  return value;\n};\n\n/**\n * Evaluates whether an array item qualifies as a match with the current query\n *\n * @param {String} candidate a possible item from the array passed\n * @param {String} query the current query\n * @return {Boolean}\n */\nSuggestions.prototype.match = function(candidate, query) {\n  return candidate.indexOf(query) > -1;\n};\n\nSuggestions.prototype.value = function(value) {\n  this.selected = value;\n  this.el.value = this.getItemValue(value);\n\n  if (document.createEvent) {\n    var e = document.createEvent('HTMLEvents');\n    e.initEvent('change', true, false);\n    this.el.dispatchEvent(e);\n  } else {\n    this.el.fireEvent('onchange');\n  }\n};\n\nSuggestions.prototype.getCandidates = function(callback) {\n  var options = {\n    pre: '<strong>',\n    post: '</strong>',\n    extract: function(d) { return this.getItemValue(d); }.bind(this)\n  };\n  var results;\n  if(this.options.filter){\n    results = fuzzy.filter(this.query, this.data, options);\n\n    results = results.map(function(item){\n      return {\n        original: item.original,\n        string: this.render(item.original, item.string)\n      };\n    }.bind(this))\n  }else{\n    results = this.data.map(function(d) {\n      var renderedString = this.render(d);\n      return {\n        original: d,\n        string: renderedString\n      };\n    }.bind(this));\n  }\n  callback(results);\n};\n\n/**\n * For a given item in the data array, return what should be used as the candidate string\n *\n * @param {Object|String} item an item from the data array\n * @return {String} item\n */\nSuggestions.prototype.getItemValue = function(item) {\n  return item;\n};\n\n/**\n * For a given item in the data array, return a string of html that should be rendered in the dropdown\n * @param {Object|String} item an item from the data array\n * @param {String} sourceFormatting a string that has pre-formatted html that should be passed directly through the render function \n * @return {String} html\n */\nSuggestions.prototype.render = function(item, sourceFormatting) {\n  if (sourceFormatting){\n    // use existing formatting on the source string\n    return sourceFormatting;\n  }\n  var boldString = (item.original) ? this.getItemValue(item.original) : this.getItemValue(item);\n  var indexString = this.normalize(boldString);\n  var indexOfQuery = indexString.lastIndexOf(this.query);\n  while (indexOfQuery > -1) {\n    var endIndexOfQuery = indexOfQuery + this.query.length;\n    boldString = boldString.slice(0, indexOfQuery) + '<strong>' + boldString.slice(indexOfQuery, endIndexOfQuery) + '</strong>' + boldString.slice(endIndexOfQuery);\n    indexOfQuery = indexString.slice(0, indexOfQuery).lastIndexOf(this.query);\n  }\n  return boldString\n}\n\n/**\n * Render an custom error message in the suggestions list\n * @param {String} msg An html string to render as an error message\n */\nSuggestions.prototype.renderError = function(msg){\n  this.list.drawError(msg);\n}\n\nmodule.exports = Suggestions;\n", "'use strict';\n\n/**\n * A typeahead component for inputs\n * @class Suggestions\n *\n * @param {HTMLInputElement} el A valid HTML input element\n * @param {Array} data An array of data used for results\n * @param {Object} options\n * @param {Number} [options.limit=5] Max number of results to display in the auto suggest list.\n * @param {Number} [options.minLength=2] Number of characters typed into an input to trigger suggestions.\n * @param {Boolean} [options.hideOnBlur=true] If `true`, hides the suggestions when focus is lost.\n * @return {Suggestions} `this`\n * @example\n * // in the browser\n * var input = document.querySelector('input');\n * var data = [\n *   '<PERSON>',\n *   '<PERSON>',\n *   '<PERSON>'\n * ];\n *\n * new Suggestions(input, data);\n *\n * // with options\n * var input = document.querySelector('input');\n * var data = [{\n *   name: '<PERSON>',\n *   year: 1911\n * }, {\n *   name: '<PERSON>',\n *   year: 1969\n * }, {\n *   name: '<PERSON>',\n *   year: 1907\n * }];\n *\n * var typeahead = new Suggestions(input, data, {\n *   filter: false, // Disable filtering\n *   minLength: 3, // Number of characters typed into an input to trigger suggestions.\n *   limit: 3, //  Max number of results to display.\n *   hideOnBlur: false // Don't hide results when input loses focus\n * });\n *\n * // As we're passing an object of an arrays as data, override\n * // `getItemValue` by specifying the specific property to search on.\n * typeahead.getItemValue = function(item) { return item.name };\n *\n * input.addEventListener('change', function() {\n *   console.log(typeahead.selected); // Current selected item.\n * });\n *\n * // With browserify\n * var Suggestions = require('suggestions');\n *\n * new Suggestions(input, data);\n */\nvar Suggestions = require('./src/suggestions');\nmodule.exports = Suggestions;\n\nif (typeof window !== 'undefined') {\n  window.Suggestions = Suggestions;\n}\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "module.exports = {\n  'fr': {\n    'name': 'France',\n    'bbox': [[-4.59235, 41.380007], [9.560016, 51.148506]]\n  },\n  'us': {\n    'name': 'United States',\n    'bbox': [[-171.791111, 18.91619], [-66.96466, 71.357764]]\n  },\n  'ru': {\n    'name': 'Russia',\n    'bbox': [[19.66064, 41.151416], [190.10042, 81.2504]]\n  },\n  'ca': {\n    'name': 'Canada',\n    'bbox': [[-140.99778, 41.675105], [-52.648099, 83.23324]]\n  }\n};\n", "'use strict';\n\n// Like https://github.com/thlorenz/lib/parse-link-header but without any\n// additional dependencies.\n\nfunction parseParam(param) {\n  var parts = param.match(/\\s*(.+)\\s*=\\s*\"?([^\"]+)\"?/);\n  if (!parts) return null;\n\n  return {\n    key: parts[1],\n    value: parts[2]\n  };\n}\n\nfunction parseLink(link) {\n  var parts = link.match(/<?([^>]*)>(.*)/);\n  if (!parts) return null;\n\n  var linkUrl = parts[1];\n  var linkParams = parts[2].split(';');\n  var rel = null;\n  var parsedLinkParams = linkParams.reduce(function(result, param) {\n    var parsed = parseParam(param);\n    if (!parsed) return result;\n    if (parsed.key === 'rel') {\n      if (!rel) {\n        rel = parsed.value;\n      }\n      return result;\n    }\n    result[parsed.key] = parsed.value;\n    return result;\n  }, {});\n  if (!rel) return null;\n\n  return {\n    url: linkUrl,\n    rel: rel,\n    params: parsedLinkParams\n  };\n}\n\n/**\n * Parse a Link header.\n *\n * @param {string} linkHeader\n * @returns {{\n *   [string]: {\n *     url: string,\n *     params: { [string]: string }\n *   }\n * }}\n */\nfunction parseLinkHeader(linkHeader) {\n  if (!linkHeader) return {};\n\n  return linkHeader.split(/,\\s*</).reduce(function(result, link) {\n    var parsed = parseLink(link);\n    if (!parsed) return result;\n    // rel value can be multiple whitespace-separated rels.\n    var splitRel = parsed.rel.split(/\\s+/);\n    splitRel.forEach(function(rel) {\n      if (!result[rel]) {\n        result[rel] = {\n          url: parsed.url,\n          params: parsed.params\n        };\n      }\n    });\n    return result;\n  }, {});\n}\n\nmodule.exports = parseLinkHeader;\n", "'use strict';\n\nvar parseLinkHeader = require('../helpers/parse-link-header');\n\n/**\n * A Mapbox API response.\n *\n * @class MapiResponse\n * @property {Object} body - The response body, parsed as JSON.\n * @property {string} rawBody - The raw response body.\n * @property {number} statusCode - The response's status code.\n * @property {Object} headers - The parsed response headers.\n * @property {Object} links - The parsed response links.\n * @property {MapiRequest} request - The response's originating `MapiRequest`.\n */\n\n/**\n * @ignore\n * @param {MapiRequest} request\n * @param {Object} responseData\n * @param {Object} responseData.headers\n * @param {string} responseData.body\n * @param {number} responseData.statusCode\n */\nfunction MapiResponse(request, responseData) {\n  this.request = request;\n  this.headers = responseData.headers;\n  this.rawBody = responseData.body;\n  this.statusCode = responseData.statusCode;\n  try {\n    this.body = JSON.parse(responseData.body || '{}');\n  } catch (parseError) {\n    this.body = responseData.body;\n  }\n  this.links = parseLinkHeader(this.headers.link);\n}\n\n/**\n * Check if there is a next page that you can fetch.\n *\n * @returns {boolean}\n */\nMapiResponse.prototype.hasNextPage = function hasNextPage() {\n  return !!this.links.next;\n};\n\n/**\n * Create a request for the next page, if there is one.\n * If there is no next page, returns `null`.\n *\n * @returns {MapiRequest | null}\n */\nMapiResponse.prototype.nextPage = function nextPage() {\n  if (!this.hasNextPage()) return null;\n  return this.request._extend({\n    path: this.links.next.url\n  });\n};\n\nmodule.exports = MapiResponse;\n", "'use strict';\n\nmodule.exports = {\n  API_ORIGIN: 'https://api.mapbox.com',\n  EVENT_PROGRESS_DOWNLOAD: 'downloadProgress',\n  EVENT_PROGRESS_UPLOAD: 'uploadProgress',\n  EVENT_ERROR: 'error',\n  EVENT_RESPONSE: 'response',\n  ERROR_HTTP: 'HttpError',\n  ERROR_REQUEST_ABORTED: 'RequestAbortedError'\n};\n", "'use strict';\n\nvar constants = require('../constants');\n\n/**\n * A Mapbox API error.\n *\n * If there's an error during the API transaction,\n * the Promise returned by `MapiRequest`'s [`send`](#send)\n * method should reject with a `MapiError`.\n *\n * @class MapiError\n * @hideconstructor\n * @property {MapiRequest} request - The errored request.\n * @property {string} type - The type of error. Usually this is `'HttpError'`.\n *   If the request was aborted, so the error was\n *   not sent from the server, the type will be\n *   `'RequestAbortedError'`.\n * @property {number} [statusCode] - The numeric status code of\n *   the HTTP response.\n * @property {Object | string} [body] - If the server sent a response body,\n *   this property exposes that response, parsed as JSON if possible.\n * @property {string} [message] - Whatever message could be derived from the\n *   call site and HTTP response.\n *\n * @param {MapiRequest} options.request\n * @param {number} [options.statusCode]\n * @param {string} [options.body]\n * @param {string} [options.message]\n * @param {string} [options.type]\n */\nfunction MapiError(options) {\n  var errorType = options.type || constants.ERROR_HTTP;\n\n  var body;\n  if (options.body) {\n    try {\n      body = JSON.parse(options.body);\n    } catch (e) {\n      body = options.body;\n    }\n  } else {\n    body = null;\n  }\n\n  var message = options.message || null;\n  if (!message) {\n    if (typeof body === 'string') {\n      message = body;\n    } else if (body && typeof body.message === 'string') {\n      message = body.message;\n    } else if (errorType === constants.ERROR_REQUEST_ABORTED) {\n      message = 'Request aborted';\n    }\n  }\n\n  this.message = message;\n  this.type = errorType;\n  this.statusCode = options.statusCode || null;\n  this.request = options.request;\n  this.body = body;\n}\n\nmodule.exports = MapiError;\n", "'use strict';\n\nfunction parseSingleHeader(raw) {\n  var boundary = raw.indexOf(':');\n  var name = raw\n    .substring(0, boundary)\n    .trim()\n    .toLowerCase();\n  var value = raw.substring(boundary + 1).trim();\n  return {\n    name: name,\n    value: value\n  };\n}\n\n/**\n * Parse raw headers into an object with lowercase properties.\n * Does not fully parse headings into more complete data structure,\n * as larger libraries might do. Also does not deal with duplicate\n * headers because Node doesn't seem to deal with those well, so\n * we shouldn't let the browser either, for consistency.\n *\n * @param {string} raw\n * @returns {Object}\n */\nfunction parseHeaders(raw) {\n  var headers = {};\n  if (!raw) {\n    return headers;\n  }\n\n  raw\n    .trim()\n    .split(/[\\r|\\n]+/)\n    .forEach(function(rawHeader) {\n      var parsed = parseSingleHeader(rawHeader);\n      headers[parsed.name] = parsed.value;\n    });\n\n  return headers;\n}\n\nmodule.exports = parseHeaders;\n", "'use strict';\n\nvar MapiResponse = require('../classes/mapi-response');\nvar MapiError = require('../classes/mapi-error');\nvar constants = require('../constants');\nvar parseHeaders = require('../helpers/parse-headers');\n\n// Keys are request IDs, values are XHRs.\nvar requestsUnderway = {};\n\nfunction browserAbort(request) {\n  var xhr = requestsUnderway[request.id];\n  if (!xhr) return;\n  xhr.abort();\n  delete requestsUnderway[request.id];\n}\n\nfunction createResponse(request, xhr) {\n  return new MapiResponse(request, {\n    body: xhr.response,\n    headers: parseHeaders(xhr.getAllResponseHeaders()),\n    statusCode: xhr.status\n  });\n}\n\nfunction normalizeBrowserProgressEvent(event) {\n  var total = event.total;\n  var transferred = event.loaded;\n  var percent = (100 * transferred) / total;\n  return {\n    total: total,\n    transferred: transferred,\n    percent: percent\n  };\n}\n\nfunction sendRequestXhr(request, xhr) {\n  return new Promise(function(resolve, reject) {\n    xhr.onprogress = function(event) {\n      request.emitter.emit(\n        constants.EVENT_PROGRESS_DOWNLOAD,\n        normalizeBrowserProgressEvent(event)\n      );\n    };\n\n    var file = request.file;\n    if (file) {\n      xhr.upload.onprogress = function(event) {\n        request.emitter.emit(\n          constants.EVENT_PROGRESS_UPLOAD,\n          normalizeBrowserProgressEvent(event)\n        );\n      };\n    }\n\n    xhr.onerror = function(error) {\n      reject(error);\n    };\n\n    xhr.onabort = function() {\n      var mapiError = new MapiError({\n        request: request,\n        type: constants.ERROR_REQUEST_ABORTED\n      });\n      reject(mapiError);\n    };\n\n    xhr.onload = function() {\n      delete requestsUnderway[request.id];\n      if (xhr.status < 200 || xhr.status >= 400) {\n        var mapiError = new MapiError({\n          request: request,\n          body: xhr.response,\n          statusCode: xhr.status\n        });\n        reject(mapiError);\n        return;\n      }\n      resolve(xhr);\n    };\n\n    var body = request.body;\n\n    // matching service needs to send a www-form-urlencoded request\n    if (typeof body === 'string') {\n      xhr.send(body);\n    } else if (body) {\n      xhr.send(JSON.stringify(body));\n    } else if (file) {\n      xhr.send(file);\n    } else {\n      xhr.send();\n    }\n\n    requestsUnderway[request.id] = xhr;\n  }).then(function(xhr) {\n    return createResponse(request, xhr);\n  });\n}\n\n// The accessToken argument gives this function flexibility\n// for Mapbox's internal client.\nfunction createRequestXhr(request, accessToken) {\n  var url = request.url(accessToken);\n  var xhr = new window.XMLHttpRequest();\n  xhr.open(request.method, url);\n  Object.keys(request.headers).forEach(function(key) {\n    xhr.setRequestHeader(key, request.headers[key]);\n  });\n  return xhr;\n}\n\nfunction browserSend(request) {\n  return Promise.resolve().then(function() {\n    var xhr = createRequestXhr(request, request.client.accessToken);\n    return sendRequestXhr(request, xhr);\n  });\n}\n\nmodule.exports = {\n  browserAbort: browserAbort,\n  sendRequestXhr: sendRequestXhr,\n  browserSend: browserSend,\n  createRequestXhr: createRequestXhr\n};\n", "/*! http://mths.be/base64 v0.1.0 by @mathias | MIT license */\n;(function(root) {\n\n\t// Detect free variables `exports`.\n\tvar freeExports = typeof exports == 'object' && exports;\n\n\t// Detect free variable `module`.\n\tvar freeModule = typeof module == 'object' && module &&\n\t\tmodule.exports == freeExports && module;\n\n\t// Detect free variable `global`, from Node.js or Browserified code, and use\n\t// it as `root`.\n\tvar freeGlobal = typeof global == 'object' && global;\n\tif (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {\n\t\troot = freeGlobal;\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\tvar InvalidCharacterError = function(message) {\n\t\tthis.message = message;\n\t};\n\tInvalidCharacterError.prototype = new Error;\n\tInvalidCharacterError.prototype.name = 'InvalidCharacterError';\n\n\tvar error = function(message) {\n\t\t// Note: the error messages used throughout this file match those used by\n\t\t// the native `atob`/`btoa` implementation in Chromium.\n\t\tthrow new InvalidCharacterError(message);\n\t};\n\n\tvar TABLE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\t// http://whatwg.org/html/common-microsyntaxes.html#space-character\n\tvar REGEX_SPACE_CHARACTERS = /[\\t\\n\\f\\r ]/g;\n\n\t// `decode` is designed to be fully compatible with `atob` as described in the\n\t// HTML Standard. http://whatwg.org/html/webappapis.html#dom-windowbase64-atob\n\t// The optimized base64-decoding algorithm used is based on @atk’s excellent\n\t// implementation. https://gist.github.com/atk/1020396\n\tvar decode = function(input) {\n\t\tinput = String(input)\n\t\t\t.replace(REGEX_SPACE_CHARACTERS, '');\n\t\tvar length = input.length;\n\t\tif (length % 4 == 0) {\n\t\t\tinput = input.replace(/==?$/, '');\n\t\t\tlength = input.length;\n\t\t}\n\t\tif (\n\t\t\tlength % 4 == 1 ||\n\t\t\t// http://whatwg.org/C#alphanumeric-ascii-characters\n\t\t\t/[^+a-zA-Z0-9/]/.test(input)\n\t\t) {\n\t\t\terror(\n\t\t\t\t'Invalid character: the string to be decoded is not correctly encoded.'\n\t\t\t);\n\t\t}\n\t\tvar bitCounter = 0;\n\t\tvar bitStorage;\n\t\tvar buffer;\n\t\tvar output = '';\n\t\tvar position = -1;\n\t\twhile (++position < length) {\n\t\t\tbuffer = TABLE.indexOf(input.charAt(position));\n\t\t\tbitStorage = bitCounter % 4 ? bitStorage * 64 + buffer : buffer;\n\t\t\t// Unless this is the first of a group of 4 characters…\n\t\t\tif (bitCounter++ % 4) {\n\t\t\t\t// …convert the first 8 bits to a single ASCII character.\n\t\t\t\toutput += String.fromCharCode(\n\t\t\t\t\t0xFF & bitStorage >> (-2 * bitCounter & 6)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t};\n\n\t// `encode` is designed to be fully compatible with `btoa` as described in the\n\t// HTML Standard: http://whatwg.org/html/webappapis.html#dom-windowbase64-btoa\n\tvar encode = function(input) {\n\t\tinput = String(input);\n\t\tif (/[^\\0-\\xFF]/.test(input)) {\n\t\t\t// Note: no need to special-case astral symbols here, as surrogates are\n\t\t\t// matched, and the input is supposed to only contain ASCII anyway.\n\t\t\terror(\n\t\t\t\t'The string to be encoded contains characters outside of the ' +\n\t\t\t\t'Latin1 range.'\n\t\t\t);\n\t\t}\n\t\tvar padding = input.length % 3;\n\t\tvar output = '';\n\t\tvar position = -1;\n\t\tvar a;\n\t\tvar b;\n\t\tvar c;\n\t\tvar d;\n\t\tvar buffer;\n\t\t// Make sure any padding is handled outside of the loop.\n\t\tvar length = input.length - padding;\n\n\t\twhile (++position < length) {\n\t\t\t// Read three bytes, i.e. 24 bits.\n\t\t\ta = input.charCodeAt(position) << 16;\n\t\t\tb = input.charCodeAt(++position) << 8;\n\t\t\tc = input.charCodeAt(++position);\n\t\t\tbuffer = a + b + c;\n\t\t\t// Turn the 24 bits into four chunks of 6 bits each, and append the\n\t\t\t// matching character for each of them to the output.\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 18 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer >> 12 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer >> 6 & 0x3F) +\n\t\t\t\tTABLE.charAt(buffer & 0x3F)\n\t\t\t);\n\t\t}\n\n\t\tif (padding == 2) {\n\t\t\ta = input.charCodeAt(position) << 8;\n\t\t\tb = input.charCodeAt(++position);\n\t\t\tbuffer = a + b;\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 10) +\n\t\t\t\tTABLE.charAt((buffer >> 4) & 0x3F) +\n\t\t\t\tTABLE.charAt((buffer << 2) & 0x3F) +\n\t\t\t\t'='\n\t\t\t);\n\t\t} else if (padding == 1) {\n\t\t\tbuffer = input.charCodeAt(position);\n\t\t\toutput += (\n\t\t\t\tTABLE.charAt(buffer >> 2) +\n\t\t\t\tTABLE.charAt((buffer << 4) & 0x3F) +\n\t\t\t\t'=='\n\t\t\t);\n\t\t}\n\n\t\treturn output;\n\t};\n\n\tvar base64 = {\n\t\t'encode': encode,\n\t\t'decode': decode,\n\t\t'version': '0.1.0'\n\t};\n\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof define == 'function' &&\n\t\ttypeof define.amd == 'object' &&\n\t\tdefine.amd\n\t) {\n\t\tdefine(function() {\n\t\t\treturn base64;\n\t\t});\n\t}\telse if (freeExports && !freeExports.nodeType) {\n\t\tif (freeModule) { // in Node.js or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = base64;\n\t\t} else { // in Narwhal or RingoJS v0.7.0-\n\t\t\tfor (var key in base64) {\n\t\t\t\tbase64.hasOwnProperty(key) && (freeExports[key] = base64[key]);\n\t\t\t}\n\t\t}\n\t} else { // in Rhino or a web browser\n\t\troot.base64 = base64;\n\t}\n\n}(this));\n", "'use strict';\n\nvar base64 = require('base-64');\n\nvar tokenCache = {};\n\nfunction parseToken(token) {\n  if (tokenCache[token]) {\n    return tokenCache[token];\n  }\n\n  var parts = token.split('.');\n  var usage = parts[0];\n  var rawPayload = parts[1];\n  if (!rawPayload) {\n    throw new Error('Invalid token');\n  }\n\n  var parsedPayload = parsePaylod(rawPayload);\n\n  var result = {\n    usage: usage,\n    user: parsedPayload.u\n  };\n  if (has(parsedPayload, 'a')) result.authorization = parsedPayload.a;\n  if (has(parsedPayload, 'exp')) result.expires = parsedPayload.exp * 1000;\n  if (has(parsedPayload, 'iat')) result.created = parsedPayload.iat * 1000;\n  if (has(parsedPayload, 'scopes')) result.scopes = parsedPayload.scopes;\n  if (has(parsedPayload, 'client')) result.client = parsedPayload.client;\n  if (has(parsedPayload, 'll')) result.lastLogin = parsedPayload.ll;\n  if (has(parsedPayload, 'iu')) result.impersonator = parsedPayload.iu;\n\n  tokenCache[token] = result;\n  return result;\n}\n\nfunction parsePaylod(rawPayload) {\n  try {\n    return JSON.parse(base64.decode(rawPayload));\n  } catch (parseError) {\n    throw new Error('Invalid token');\n  }\n}\n\nfunction has(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nmodule.exports = parseToken;\n", "'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "'use strict';\n\n// Encode each item of an array individually. The comma\n// delimiters should not themselves be encoded.\nfunction encodeArray(arrayValue) {\n  return arrayValue.map(encodeURIComponent).join(',');\n}\n\nfunction encodeValue(value) {\n  if (Array.isArray(value)) {\n    return encodeArray(value);\n  }\n  return encodeURIComponent(String(value));\n}\n\n/**\n * Append a query parameter to a URL.\n *\n * @param {string} url\n * @param {string} key\n * @param {string|number|boolean|Array<*>>} [value] - Provide an array\n *   if the value is a list and commas between values need to be\n *   preserved, unencoded.\n * @returns {string} - Modified URL.\n */\nfunction appendQueryParam(url, key, value) {\n  if (value === false || value === null) {\n    return url;\n  }\n  var punctuation = /\\?/.test(url) ? '&' : '?';\n  var query = encodeURIComponent(key);\n  if (value !== undefined && value !== '' && value !== true) {\n    query += '=' + encodeValue(value);\n  }\n  return '' + url + punctuation + query;\n}\n\n/**\n * Derive a query string from an object and append it\n * to a URL.\n *\n * @param {string} url\n * @param {Object} [queryObject] - Values should be primitives.\n * @returns {string} - Modified URL.\n */\nfunction appendQueryObject(url, queryObject) {\n  if (!queryObject) {\n    return url;\n  }\n\n  var result = url;\n  Object.keys(queryObject).forEach(function(key) {\n    var value = queryObject[key];\n    if (value === undefined) {\n      return;\n    }\n    if (Array.isArray(value)) {\n      value = value\n        .filter(function(v) {\n          return v !== null && v !== undefined;\n        })\n        .join(',');\n    }\n    result = appendQueryParam(result, key, value);\n  });\n  return result;\n}\n\n/**\n * Prepend an origin to a URL. If the URL already has an\n * origin, do nothing.\n *\n * @param {string} url\n * @param {string} origin\n * @returns {string} - Modified URL.\n */\nfunction prependOrigin(url, origin) {\n  if (!origin) {\n    return url;\n  }\n\n  if (url.slice(0, 4) === 'http') {\n    return url;\n  }\n\n  var delimiter = url[0] === '/' ? '' : '/';\n  return '' + origin.replace(/\\/$/, '') + delimiter + url;\n}\n\n/**\n * Interpolate values into a route with express-style,\n * colon-prefixed route parameters.\n *\n * @param {string} route\n * @param {Object} [params] - Values should be primitives\n *   or arrays of primitives. Provide an array if the value\n *   is a list and commas between values need to be\n *   preserved, unencoded.\n * @returns {string} - Modified URL.\n */\nfunction interpolateRouteParams(route, params) {\n  if (!params) {\n    return route;\n  }\n  return route.replace(/\\/:([a-zA-Z0-9]+)/g, function(_, paramId) {\n    var value = params[paramId];\n    if (value === undefined) {\n      throw new Error('Unspecified route parameter ' + paramId);\n    }\n    var preppedValue = encodeValue(value);\n    return '/' + preppedValue;\n  });\n}\n\nmodule.exports = {\n  appendQueryObject: appendQueryObject,\n  appendQueryParam: appendQueryParam,\n  prependOrigin: prependOrigin,\n  interpolateRouteParams: interpolateRouteParams\n};\n", "'use strict';\n\nvar parseToken = require('@mapbox/parse-mapbox-token');\nvar xtend = require('xtend');\nvar EventEmitter = require('eventemitter3');\nvar urlUtils = require('../helpers/url-utils');\nvar constants = require('../constants');\n\nvar requestId = 1;\n\n/**\n * A Mapbox API request.\n *\n * Note that creating a `MapiRequest` does *not* send the request automatically.\n * Use the request's `send` method to send it off and get a `Promise`.\n *\n * The `emitter` property is an `EventEmitter` that emits the following events:\n *\n * - `'response'` - Listeners will be called with a `MapiResponse`.\n * - `'error'` - Listeners will be called with a `MapiError`.\n * - `'downloadProgress'` - Listeners will be called with `ProgressEvents`.\n * - `'uploadProgress'` - Listeners will be called with `ProgressEvents`.\n *   Upload events are only available when the request includes a file.\n *\n * @class MapiRequest\n * @property {EventEmitter} emitter - An event emitter. See above.\n * @property {MapiClient} client - This request's `MapiClient`.\n * @property {MapiResponse|null} response - If this request has been sent and received\n *   a response, the response is available on this property.\n * @property {MapiError|Error|null} error - If this request has been sent and\n *   received an error in response, the error is available on this property.\n * @property {boolean} aborted - If the request has been aborted\n *   (via [`abort`](#abort)), this property will be `true`.\n * @property {boolean} sent - If the request has been sent, this property will\n *   be `true`. You cannot send the same request twice, so if you need to create\n *   a new request that is the equivalent of an existing one, use\n *   [`clone`](#clone).\n * @property {string} path - The request's path, including colon-prefixed route\n *   parameters.\n * @property {string} origin - The request's origin.\n * @property {string} method - The request's HTTP method.\n * @property {Object} query - A query object, which will be transformed into\n *   a URL query string.\n * @property {Object} params - A route parameters object, whose values will\n *   be interpolated the path.\n * @property {Object} headers - The request's headers.\n * @property {Object|string|null} body - Data to send with the request.\n *   If the request has a body, it will also be sent with the header\n *   `'Content-Type: application/json'`.\n * @property {Blob|ArrayBuffer|string|ReadStream} file - A file to\n *   send with the request. The browser client accepts Blobs and ArrayBuffers;\n *   the Node client accepts strings (filepaths) and ReadStreams.\n * @property {string} encoding - The encoding of the response.\n * @property {string} sendFileAs - The method to send the `file`. Options are\n *   `data` (x-www-form-urlencoded) or `form` (multipart/form-data).\n */\n\n/**\n * @ignore\n * @param {MapiClient} client\n * @param {Object} options\n * @param {string} options.method\n * @param {string} options.path\n * @param {Object} [options.query={}]\n * @param {Object} [options.params={}]\n * @param {string} [options.origin]\n * @param {Object} [options.headers]\n * @param {Object} [options.body=null]\n * @param {Blob|ArrayBuffer|string|ReadStream} [options.file=null]\n * @param {string} [options.encoding=utf8]\n */\nfunction MapiRequest(client, options) {\n  if (!client) {\n    throw new Error('MapiRequest requires a client');\n  }\n  if (!options || !options.path || !options.method) {\n    throw new Error(\n      'MapiRequest requires an options object with path and method properties'\n    );\n  }\n\n  var defaultHeaders = {};\n  if (options.body) {\n    defaultHeaders['content-type'] = 'application/json';\n  }\n\n  var headersWithDefaults = xtend(defaultHeaders, options.headers);\n\n  // Disallows duplicate header names of mixed case,\n  // e.g. Content-Type and content-type.\n  var headers = Object.keys(headersWithDefaults).reduce(function(memo, name) {\n    memo[name.toLowerCase()] = headersWithDefaults[name];\n    return memo;\n  }, {});\n\n  this.id = requestId++;\n  this._options = options;\n\n  this.emitter = new EventEmitter();\n  this.client = client;\n  this.response = null;\n  this.error = null;\n  this.sent = false;\n  this.aborted = false;\n  this.path = options.path;\n  this.method = options.method;\n  this.origin = options.origin || client.origin;\n  this.query = options.query || {};\n  this.params = options.params || {};\n  this.body = options.body || null;\n  this.file = options.file || null;\n  this.encoding = options.encoding || 'utf8';\n  this.sendFileAs = options.sendFileAs || null;\n  this.headers = headers;\n}\n\n/**\n * Get the URL of the request.\n *\n * @param {string} [accessToken] - By default, the access token of the request's\n *   client is used.\n * @return {string}\n */\nMapiRequest.prototype.url = function url(accessToken) {\n  var url = urlUtils.prependOrigin(this.path, this.origin);\n  url = urlUtils.appendQueryObject(url, this.query);\n  var routeParams = this.params;\n  var actualAccessToken =\n    accessToken == null ? this.client.accessToken : accessToken;\n  if (actualAccessToken) {\n    url = urlUtils.appendQueryParam(url, 'access_token', actualAccessToken);\n    var accessTokenOwnerId = parseToken(actualAccessToken).user;\n    routeParams = xtend({ ownerId: accessTokenOwnerId }, routeParams);\n  }\n  url = urlUtils.interpolateRouteParams(url, routeParams);\n  return url;\n};\n\n/**\n * Send the request. Returns a Promise that resolves with a `MapiResponse`.\n * You probably want to use `response.body`.\n *\n * `send` only retrieves the first page of paginated results. You can get\n * the next page by using the `MapiResponse`'s [`nextPage`](#nextpage)\n * function, or iterate through all pages using [`eachPage`](#eachpage)\n * instead of `send`.\n *\n * @returns {Promise<MapiResponse>}\n */\nMapiRequest.prototype.send = function send() {\n  var self = this;\n\n  if (self.sent) {\n    throw new Error(\n      'This request has already been sent. Check the response and error properties. Create a new request with clone().'\n    );\n  }\n  self.sent = true;\n\n  return self.client.sendRequest(self).then(\n    function(response) {\n      self.response = response;\n      self.emitter.emit(constants.EVENT_RESPONSE, response);\n      return response;\n    },\n    function(error) {\n      self.error = error;\n      self.emitter.emit(constants.EVENT_ERROR, error);\n      throw error;\n    }\n  );\n};\n\n/**\n * Abort the request.\n *\n * Any pending `Promise` returned by [`send`](#send) will be rejected with\n * an error with `type: 'RequestAbortedError'`. If you've created a request\n * that might be aborted, you need to catch and handle such errors.\n *\n * This method will also abort any requests created while fetching subsequent\n * pages via [`eachPage`](#eachpage).\n *\n * If the request has not been sent or has already been aborted, nothing\n * will happen.\n */\nMapiRequest.prototype.abort = function abort() {\n  if (this._nextPageRequest) {\n    this._nextPageRequest.abort();\n    delete this._nextPageRequest;\n  }\n\n  if (this.response || this.error || this.aborted) return;\n\n  this.aborted = true;\n  this.client.abortRequest(this);\n};\n\n/**\n * Invoke a callback for each page of a paginated API response.\n *\n * The callback should have the following signature:\n *\n * ```js\n * (\n *   error: MapiError,\n *   response: MapiResponse,\n *   next: () => void\n * ) => void\n * ```\n *\n * **The next page will not be fetched until you've invoked the\n * `next` callback**, indicating that you're ready for it.\n *\n * @param {Function} callback\n */\nMapiRequest.prototype.eachPage = function eachPage(callback) {\n  var self = this;\n\n  function handleResponse(response) {\n    function getNextPage() {\n      delete self._nextPageRequest;\n      var nextPageRequest = response.nextPage();\n      if (nextPageRequest) {\n        self._nextPageRequest = nextPageRequest;\n        getPage(nextPageRequest);\n      }\n    }\n    callback(null, response, getNextPage);\n  }\n\n  function handleError(error) {\n    callback(error, null, function() {});\n  }\n\n  function getPage(request) {\n    request.send().then(handleResponse, handleError);\n  }\n  getPage(this);\n};\n\n/**\n * Clone this request.\n *\n * Each request can only be sent *once*. So if you'd like to send the\n * same request again, clone it and send away.\n *\n * @returns {MapiRequest} - A new `MapiRequest` configured just like this one.\n */\nMapiRequest.prototype.clone = function clone() {\n  return this._extend();\n};\n\n/**\n * @ignore\n */\nMapiRequest.prototype._extend = function _extend(options) {\n  var extendedOptions = xtend(this._options, options);\n  return new MapiRequest(this.client, extendedOptions);\n};\n\nmodule.exports = MapiRequest;\n", "'use strict';\n\nvar parseToken = require('@mapbox/parse-mapbox-token');\nvar MapiRequest = require('./mapi-request');\nvar constants = require('../constants');\n\n/**\n * A low-level Mapbox API client. Use it to create service clients\n * that share the same configuration.\n *\n * Services and `MapiRequest`s use the underlying `MapiClient` to\n * determine how to create, send, and abort requests in a way\n * that is appropriate to the configuration and environment\n * (Node or the browser).\n *\n * @class MapiClient\n * @property {string} accessToken - The Mapbox access token assigned\n *   to this client.\n * @property {string} [origin] - The origin\n *   to use for API requests. Defaults to https://api.mapbox.com.\n */\n\nfunction MapiClient(options) {\n  if (!options || !options.accessToken) {\n    throw new Error('Cannot create a client without an access token');\n  }\n  // Try parsing the access token to determine right away if it's valid.\n  parseToken(options.accessToken);\n\n  this.accessToken = options.accessToken;\n  this.origin = options.origin || constants.API_ORIGIN;\n}\n\nMapiClient.prototype.createRequest = function createRequest(requestOptions) {\n  return new MapiRequest(this, requestOptions);\n};\n\nmodule.exports = MapiClient;\n", "'use strict';\n\nvar browser = require('./browser-layer');\nvar MapiClient = require('../classes/mapi-client');\n\nfunction BrowserClient(options) {\n  MapiClient.call(this, options);\n}\nBrowserClient.prototype = Object.create(MapiClient.prototype);\nBrowserClient.prototype.constructor = BrowserClient;\n\nBrowserClient.prototype.sendRequest = browser.browserSend;\nBrowserClient.prototype.abortRequest = browser.browserAbort;\n\n/**\n * Create a client for the browser.\n *\n * @param {Object} options\n * @param {string} options.accessToken\n * @param {string} [options.origin]\n * @returns {MapiClient}\n */\nfunction createBrowserClient(options) {\n  return new BrowserClient(options);\n}\n\nmodule.exports = createBrowserClient;\n", "'use strict';\n\nvar client = require('./lib/client');\n\nmodule.exports = client;\n", "'use strict';\nvar toString = Object.prototype.toString;\n\nmodule.exports = function (x) {\n\tvar prototype;\n\treturn toString.call(x) === '[object Object]' && (prototype = Object.getPrototypeOf(x), prototype === null || prototype === Object.getPrototypeOf({}));\n};\n", "'use strict';\n/**\n * Validators are functions which assert certain type.\n * They can return a string which can then be used\n * to display a helpful error message.\n * They can also return a function for a custom error message.\n */\nvar isPlainObject = require('is-plain-obj');\nvar xtend = require('xtend');\n\nvar DEFAULT_ERROR_PATH = 'value';\nvar NEWLINE_INDENT = '\\n  ';\n\nvar v = {};\n\n/**\n * Runners\n *\n * Take root validators and run assertion\n */\nv.assert = function(rootValidator, options) {\n  options = options || {};\n  return function(value) {\n    var message = validate(rootValidator, value);\n    // all good\n    if (!message) {\n      return;\n    }\n\n    var errorMessage = processMessage(message, options);\n\n    if (options.apiName) {\n      errorMessage = options.apiName + ': ' + errorMessage;\n    }\n\n    throw new Error(errorMessage);\n  };\n};\n\n/**\n * Higher Order Validators\n *\n * validators which take other validators as input\n * and output a new validator\n */\nv.shape = function shape(validatorObj) {\n  var validators = objectEntries(validatorObj);\n  return function shapeValidator(value) {\n    var validationResult = validate(v.plainObject, value);\n\n    if (validationResult) {\n      return validationResult;\n    }\n\n    var key, validator;\n    var errorMessages = [];\n\n    for (var i = 0; i < validators.length; i++) {\n      key = validators[i].key;\n      validator = validators[i].value;\n      validationResult = validate(validator, value[key]);\n\n      if (validationResult) {\n        // return [key].concat(validationResult);\n        errorMessages.push([key].concat(validationResult));\n      }\n    }\n\n    if (errorMessages.length < 2) {\n      return errorMessages[0];\n    }\n\n    // enumerate all the error messages\n    return function(options) {\n      errorMessages = errorMessages.map(function(message) {\n        var key = message[0];\n        var renderedMessage = processMessage(message, options)\n          .split('\\n')\n          .join(NEWLINE_INDENT); // indents any inner nesting\n        return '- ' + key + ': ' + renderedMessage;\n      });\n\n      var objectId = options.path.join('.');\n      var ofPhrase = objectId === DEFAULT_ERROR_PATH ? '' : ' of ' + objectId;\n\n      return (\n        'The following properties' +\n        ofPhrase +\n        ' have invalid values:' +\n        NEWLINE_INDENT +\n        errorMessages.join(NEWLINE_INDENT)\n      );\n    };\n  };\n};\n\nv.strictShape = function strictShape(validatorObj) {\n  var shapeValidator = v.shape(validatorObj);\n  return function strictShapeValidator(value) {\n    var shapeResult = shapeValidator(value);\n    if (shapeResult) {\n      return shapeResult;\n    }\n\n    var invalidKeys = Object.keys(value).reduce(function(memo, valueKey) {\n      if (validatorObj[valueKey] === undefined) {\n        memo.push(valueKey);\n      }\n      return memo;\n    }, []);\n\n    if (invalidKeys.length !== 0) {\n      return function() {\n        return 'The following keys are invalid: ' + invalidKeys.join(', ');\n      };\n    }\n  };\n};\n\nv.arrayOf = function arrayOf(validator) {\n  return createArrayValidator(validator);\n};\n\nv.tuple = function tuple() {\n  var validators = Array.isArray(arguments[0])\n    ? arguments[0]\n    : Array.prototype.slice.call(arguments);\n  return createArrayValidator(validators);\n};\n\n// Currently array validation fails when the first invalid item is found.\nfunction createArrayValidator(validators) {\n  var validatingTuple = Array.isArray(validators);\n  var getValidator = function(index) {\n    if (validatingTuple) {\n      return validators[index];\n    }\n    return validators;\n  };\n\n  return function arrayValidator(value) {\n    var validationResult = validate(v.plainArray, value);\n    if (validationResult) {\n      return validationResult;\n    }\n\n    if (validatingTuple && value.length !== validators.length) {\n      return 'an array with ' + validators.length + ' items';\n    }\n\n    for (var i = 0; i < value.length; i++) {\n      validationResult = validate(getValidator(i), value[i]);\n      if (validationResult) {\n        return [i].concat(validationResult);\n      }\n    }\n  };\n}\n\nv.required = function required(validator) {\n  function requiredValidator(value) {\n    if (value == null) {\n      return function(options) {\n        return formatErrorMessage(\n          options,\n          isArrayCulprit(options.path)\n            ? 'cannot be undefined/null.'\n            : 'is required.'\n        );\n      };\n    }\n    return validator.apply(this, arguments);\n  }\n  requiredValidator.__required = true;\n\n  return requiredValidator;\n};\n\nv.oneOfType = function oneOfType() {\n  var validators = Array.isArray(arguments[0])\n    ? arguments[0]\n    : Array.prototype.slice.call(arguments);\n  return function oneOfTypeValidator(value) {\n    var messages = validators\n      .map(function(validator) {\n        return validate(validator, value);\n      })\n      .filter(Boolean);\n\n    // If we don't have as many messages as no. of validators,\n    // then at least one validator was ok with the value.\n    if (messages.length !== validators.length) {\n      return;\n    }\n\n    // check primitive type\n    if (\n      messages.every(function(message) {\n        return message.length === 1 && typeof message[0] === 'string';\n      })\n    ) {\n      return orList(\n        messages.map(function(m) {\n          return m[0];\n        })\n      );\n    }\n\n    // Complex oneOfTypes like\n    // `v.oneOftypes(v.shape({name: v.string})`, `v.shape({name: v.number}))`\n    // are complex ¯\\_(ツ)_/¯. For the current scope only returning the longest message.\n    return messages.reduce(function(max, arr) {\n      return arr.length > max.length ? arr : max;\n    });\n  };\n};\n\n/**\n * Meta Validators\n * which take options as argument (not validators)\n * and return a new primitive validator\n */\nv.equal = function equal(compareWith) {\n  return function equalValidator(value) {\n    if (value !== compareWith) {\n      return JSON.stringify(compareWith);\n    }\n  };\n};\n\nv.oneOf = function oneOf() {\n  var options = Array.isArray(arguments[0])\n    ? arguments[0]\n    : Array.prototype.slice.call(arguments);\n  var validators = options.map(function(value) {\n    return v.equal(value);\n  });\n\n  return v.oneOfType.apply(this, validators);\n};\n\nv.range = function range(compareWith) {\n  var min = compareWith[0];\n  var max = compareWith[1];\n  return function rangeValidator(value) {\n    var validationResult = validate(v.number, value);\n\n    if (validationResult || value < min || value > max) {\n      return 'number between ' + min + ' & ' + max + ' (inclusive)';\n    }\n  };\n};\n\n/**\n * Primitive validators\n *\n * simple validators which return a string or undefined\n */\nv.any = function any() {\n  return;\n};\n\nv.boolean = function boolean(value) {\n  if (typeof value !== 'boolean') {\n    return 'boolean';\n  }\n};\n\nv.number = function number(value) {\n  if (typeof value !== 'number') {\n    return 'number';\n  }\n};\n\nv.plainArray = function plainArray(value) {\n  if (!Array.isArray(value)) {\n    return 'array';\n  }\n};\n\nv.plainObject = function plainObject(value) {\n  if (!isPlainObject(value)) {\n    return 'object';\n  }\n};\n\nv.string = function string(value) {\n  if (typeof value !== 'string') {\n    return 'string';\n  }\n};\n\nv.func = function func(value) {\n  if (typeof value !== 'function') {\n    return 'function';\n  }\n};\n\nfunction validate(validator, value) {\n  // assertions are optional by default unless wrapped in v.require\n  if (value == null && !validator.hasOwnProperty('__required')) {\n    return;\n  }\n\n  var result = validator(value);\n\n  if (result) {\n    return Array.isArray(result) ? result : [result];\n  }\n}\n\nfunction processMessage(message, options) {\n  // message array follows the convention\n  // [...path, result]\n  // path is an array of object keys / array indices\n  // result is output of the validator\n  var len = message.length;\n\n  var result = message[len - 1];\n  var path = message.slice(0, len - 1);\n\n  if (path.length === 0) {\n    path = [DEFAULT_ERROR_PATH];\n  }\n  options = xtend(options, { path: path });\n\n  return typeof result === 'function'\n    ? result(options) // allows customization of result\n    : formatErrorMessage(options, prettifyResult(result));\n}\n\nfunction orList(list) {\n  if (list.length < 2) {\n    return list[0];\n  }\n  if (list.length === 2) {\n    return list.join(' or ');\n  }\n  return list.slice(0, -1).join(', ') + ', or ' + list.slice(-1);\n}\n\nfunction prettifyResult(result) {\n  return 'must be ' + addArticle(result) + '.';\n}\n\nfunction addArticle(nounPhrase) {\n  if (/^an? /.test(nounPhrase)) {\n    return nounPhrase;\n  }\n  if (/^[aeiou]/i.test(nounPhrase)) {\n    return 'an ' + nounPhrase;\n  }\n  if (/^[a-z]/i.test(nounPhrase)) {\n    return 'a ' + nounPhrase;\n  }\n  return nounPhrase;\n}\n\nfunction formatErrorMessage(options, prettyResult) {\n  var arrayCulprit = isArrayCulprit(options.path);\n  var output = options.path.join('.') + ' ' + prettyResult;\n  var prepend = arrayCulprit ? 'Item at position ' : '';\n\n  return prepend + output;\n}\n\nfunction isArrayCulprit(path) {\n  return typeof path[path.length - 1] == 'number' || typeof path[0] == 'number';\n}\n\nfunction objectEntries(obj) {\n  return Object.keys(obj || {}).map(function(key) {\n    return { key: key, value: obj[key] };\n  });\n}\n\nv.validate = validate;\nv.processMessage = processMessage;\n\nmodule.exports = v;\n", "'use strict';\n\nvar xtend = require('xtend');\nvar v = require('@mapbox/fusspot');\n\nfunction file(value) {\n  // If we're in a browser so Blob is available, the file must be that.\n  // In Node, however, it could be a filepath or a pipeable (Readable) stream.\n  if (typeof window !== 'undefined') {\n    if (value instanceof global.Blob || value instanceof global.ArrayBuffer) {\n      return;\n    }\n    return 'Blob or ArrayBuffer';\n  }\n  if (typeof value === 'string' || value.pipe !== undefined) {\n    return;\n  }\n  return 'Filename or Readable stream';\n}\n\nfunction assertShape(validatorObj, apiName) {\n  return v.assert(v.strictShape(validatorObj), apiName);\n}\n\nfunction date(value) {\n  var msg = 'date';\n  if (typeof value === 'boolean') {\n    return msg;\n  }\n  try {\n    var date = new Date(value);\n    if (date.getTime && isNaN(date.getTime())) {\n      return msg;\n    }\n  } catch (e) {\n    return msg;\n  }\n}\n\nfunction coordinates(value) {\n  return v.tuple(v.number, v.number)(value);\n}\n\nmodule.exports = xtend(v, {\n  file: file,\n  date: date,\n  coordinates: coordinates,\n  assertShape: assertShape\n});\n", "'use strict';\n\n/**\n * Create a new object by picking properties off an existing object.\n * The second param can be overloaded as a callback for\n * more fine grained picking of properties.\n * @param {Object} source\n * @param {Array<string>|function(string, Object):boolean} keys\n * @returns {Object}\n */\nfunction pick(source, keys) {\n  var filter = function(key, val) {\n    return keys.indexOf(key) !== -1 && val !== undefined;\n  };\n\n  if (typeof keys === 'function') {\n    filter = keys;\n  }\n\n  return Object.keys(source)\n    .filter(function(key) {\n      return filter(key, source[key]);\n    })\n    .reduce(function(result, key) {\n      result[key] = source[key];\n      return result;\n    }, {});\n}\n\nmodule.exports = pick;\n", "'use strict';\n\nfunction objectMap(obj, cb) {\n  return Object.keys(obj).reduce(function(result, key) {\n    result[key] = cb(key, obj[key]);\n    return result;\n  }, {});\n}\n\nmodule.exports = objectMap;\n", "'use strict';\n\nvar objectMap = require('./object-map');\n\n/**\n * Stringify all the boolean values in an object, so true becomes \"true\".\n *\n * @param {Object} obj\n * @returns {Object}\n */\nfunction stringifyBoolean(obj) {\n  return objectMap(obj, function(_, value) {\n    return typeof value === 'boolean' ? JSON.stringify(value) : value;\n  });\n}\n\nmodule.exports = stringifyBoolean;\n", "'use strict';\n\nvar MapiClient = require('../../lib/classes/mapi-client');\n// This will create the environment-appropriate client.\nvar createClient = require('../../lib/client');\n\nfunction createServiceFactory(ServicePrototype) {\n  return function(clientOrConfig) {\n    var client;\n    if (MapiClient.prototype.isPrototypeOf(clientOrConfig)) {\n      client = clientOrConfig;\n    } else {\n      client = createClient(clientOrConfig);\n    }\n    var service = Object.create(ServicePrototype);\n    service.client = client;\n    return service;\n  };\n}\n\nmodule.exports = createServiceFactory;\n", "'use strict';\n\nvar xtend = require('xtend');\nvar v = require('./service-helpers/validator');\nvar pick = require('./service-helpers/pick');\nvar stringifyBooleans = require('./service-helpers/stringify-booleans');\nvar createServiceFactory = require('./service-helpers/create-service-factory');\n\n/**\n * Geocoding API service.\n *\n * Learn more about this service and its responses in\n * [the HTTP service documentation](https://docs.mapbox.com/api/search/#geocoding).\n */\nvar Geocoding = {};\n\nvar featureTypes = [\n  'country',\n  'region',\n  'postcode',\n  'district',\n  'place',\n  'locality',\n  'neighborhood',\n  'address',\n  'poi',\n  'poi.landmark'\n];\n\n/**\n * Search for a place.\n *\n * See the [public documentation](https://docs.mapbox.com/api/search/#forward-geocoding).\n *\n * @param {Object} config\n * @param {string} config.query - A place name.\n * @param {'mapbox.places'|'mapbox.places-permanent'} [config.mode=\"mapbox.places\"] - Either `mapbox.places` for ephemeral geocoding, or `mapbox.places-permanent` for storing results and batch geocoding.\n * @param {Array<string>} [config.countries] - Limits results to the specified countries.\n *   Each item in the array should be an [ISO 3166 alpha 2 country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2).\n * @param {Coordinates|'ip'} [config.proximity] - Bias local results based on a provided coordinate location or a user's IP address.\n * @param {Array<'country'|'region'|'postcode'|'district'|'place'|'locality'|'neighborhood'|'address'|'poi'|'poi.landmark'>} [config.types] - Filter results by feature types.\n * @param {boolean} [config.autocomplete=true] - Return autocomplete results or not.\n * @param {BoundingBox} [config.bbox] - Limit results to a bounding box.\n * @param {number} [config.limit=5] - Limit the number of results returned.\n * @param {Array<string>} [config.language] - Specify the language to use for response text and, for forward geocoding, query result weighting.\n *  Options are [IETF language tags](https://en.wikipedia.org/wiki/IETF_language_tag) comprised of a mandatory\n *  [ISO 639-1 language code](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) and optionally one or more IETF subtags for country or script.\n * @param {boolean} [config.routing=false] - Specify whether to request additional metadata about the recommended navigation destination. Only applicable for address features.\n * @param {boolean} [config.fuzzyMatch=true] - Specify whether the Geocoding API should attempt approximate, as well as exact, matching.\n * @param {String} [config.worldview=\"us\"] - Filter results to geographic features whose characteristics are defined differently by audiences belonging to various regional, cultural, or political groups.\n * @param {String} [config.session_token] - A unique session identifier generated by the client.\n * @return {MapiRequest}\n *\n * @example\n * geocodingClient.forwardGeocode({\n *   query: 'Paris, France',\n *   limit: 2\n * })\n *   .send()\n *   .then(response => {\n *     const match = response.body;\n *   });\n *\n * @example\n * // geocoding with proximity\n * geocodingClient.forwardGeocode({\n *   query: 'Paris, France',\n *   proximity: [-95.4431142, 33.6875431]\n * })\n *   .send()\n *   .then(response => {\n *     const match = response.body;\n *   });\n *\n * // geocoding with countries\n * geocodingClient.forwardGeocode({\n *   query: 'Paris, France',\n *   countries: ['fr']\n * })\n *   .send()\n *   .then(response => {\n *     const match = response.body;\n *   });\n *\n * // geocoding with bounding box\n * geocodingClient.forwardGeocode({\n *   query: 'Paris, France',\n *   bbox: [2.14, 48.72, 2.55, 48.96]\n * })\n *   .send()\n *   .then(response => {\n *     const match = response.body;\n *   });\n */\nGeocoding.forwardGeocode = function(config) {\n  v.assertShape({\n    query: v.required(v.string),\n    mode: v.oneOf('mapbox.places', 'mapbox.places-permanent'),\n    countries: v.arrayOf(v.string),\n    proximity: v.oneOf(v.coordinates, 'ip'),\n    types: v.arrayOf(v.oneOf(featureTypes)),\n    autocomplete: v.boolean,\n    bbox: v.arrayOf(v.number),\n    limit: v.number,\n    language: v.arrayOf(v.string),\n    routing: v.boolean,\n    fuzzyMatch: v.boolean,\n    worldview: v.string,\n    session_token: v.string\n  })(config);\n\n  config.mode = config.mode || 'mapbox.places';\n\n  var query = stringifyBooleans(\n    xtend(\n      { country: config.countries },\n      pick(config, [\n        'proximity',\n        'types',\n        'autocomplete',\n        'bbox',\n        'limit',\n        'language',\n        'routing',\n        'fuzzyMatch',\n        'worldview',\n        'session_token'\n      ])\n    )\n  );\n\n  return this.client.createRequest({\n    method: 'GET',\n    path: '/geocoding/v5/:mode/:query.json',\n    params: pick(config, ['mode', 'query']),\n    query: query\n  });\n};\n\n/**\n * Search for places near coordinates.\n *\n * See the [public documentation](https://docs.mapbox.com/api/search/#reverse-geocoding).\n *\n * @param {Object} config\n * @param {Coordinates} config.query - Coordinates at which features will be searched.\n * @param {'mapbox.places'|'mapbox.places-permanent'} [config.mode=\"mapbox.places\"] - Either `mapbox.places` for ephemeral geocoding, or `mapbox.places-permanent` for storing results and batch geocoding.\n * @param {Array<string>} [config.countries] - Limits results to the specified countries.\n *   Each item in the array should be an [ISO 3166 alpha 2 country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2).\n * @param {Array<'country'|'region'|'postcode'|'district'|'place'|'locality'|'neighborhood'|'address'|'poi'|'poi.landmark'>} [config.types] - Filter results by feature types.\n * @param {BoundingBox} [config.bbox] - Limit results to a bounding box.\n * @param {number} [config.limit=1] - Limit the number of results returned. If using this option, you must provide a single item for `types`.\n * @param {Array<string>} [config.language] - Specify the language to use for response text and, for forward geocoding, query result weighting.\n *  Options are [IETF language tags](https://en.wikipedia.org/wiki/IETF_language_tag) comprised of a mandatory\n *  [ISO 639-1 language code](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) and optionally one or more IETF subtags for country or script.\n * @param {'distance'|'score'} [config.reverseMode='distance'] - Set the factors that are used to sort nearby results.\n * @param {boolean} [config.routing=false] - Specify whether to request additional metadata about the recommended navigation destination. Only applicable for address features.\n * @param {String} [config.worldview=\"us\"] - Filter results to geographic features whose characteristics are defined differently by audiences belonging to various regional, cultural, or political groups.\n * @param {String} [config.session_token] - A unique session identifier generated by the client.\n * @return {MapiRequest}\n *\n * @example\n * geocodingClient.reverseGeocode({\n *   query: [-95.4431142, 33.6875431]\n * })\n *   .send()\n *   .then(response => {\n *     // GeoJSON document with geocoding matches\n *     const match = response.body;\n *   });\n */\nGeocoding.reverseGeocode = function(config) {\n  v.assertShape({\n    query: v.required(v.coordinates),\n    mode: v.oneOf('mapbox.places', 'mapbox.places-permanent'),\n    countries: v.arrayOf(v.string),\n    types: v.arrayOf(v.oneOf(featureTypes)),\n    bbox: v.arrayOf(v.number),\n    limit: v.number,\n    language: v.arrayOf(v.string),\n    reverseMode: v.oneOf('distance', 'score'),\n    routing: v.boolean,\n    worldview: v.string,\n    session_token: v.string\n  })(config);\n\n  config.mode = config.mode || 'mapbox.places';\n\n  var query = stringifyBooleans(\n    xtend(\n      { country: config.countries },\n      pick(config, [\n        'country',\n        'types',\n        'bbox',\n        'limit',\n        'language',\n        'reverseMode',\n        'routing',\n        'worldview',\n        'session_token'\n      ])\n    )\n  );\n\n  return this.client.createRequest({\n    method: 'GET',\n    path: '/geocoding/v5/:mode/:query.json',\n    params: pick(config, ['mode', 'query']),\n    query: query\n  });\n};\n\nmodule.exports = createServiceFactory(Geocoding);\n", "let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nexport { urlAlphabet }\n", "import { urlAlphabet } from './url-alphabet/index.js'\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    byte &= 63\n    if (byte < 36) {\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n", "'use strict';\nvar nanoid = require('nanoid').nanoid;\n\n/**\n * Construct a new mapbox event client to send interaction events to the mapbox event service\n * @param {Object} options options with which to create the service\n * @param {String} options.accessToken the mapbox access token to make requests\n * @param {Number} [options.flushInterval=1000] the number of ms after which to flush the event queue\n * @param {Number} [options.maxQueueSize=100] the number of events to queue before flushing\n * @private\n */\nfunction MapboxEventManager(options) {\n  this.origin = options.origin || 'https://api.mapbox.com';\n  this.endpoint = 'events/v2';\n  this.access_token = options.accessToken;\n  this.version = '0.3.0'\n  this.pluginSessionID = this.generateSessionID();\n  this.sessionIncrementer = 0;\n  this.userAgent = this.getUserAgent();\n\n  this.options = options;\n  this.send = this.send.bind(this);\n\n\n  // parse global options to be sent with each request\n  this.countries = (options.countries) ? options.countries.split(\",\") : null;\n  this.types = (options.types) ? options.types.split(\",\") : null;\n  this.bbox = (options.bbox) ? options.bbox : null;\n  this.language = (options.language) ? options.language.split(\",\") : null;\n  this.limit = (options.limit) ? +options.limit : null;\n  this.locale = navigator.language || null;\n  this.enableEventLogging = this.shouldEnableLogging(options);\n  this.eventQueue = new Array();\n  this.flushInterval = options.flushInterval || 1000;\n  this.maxQueueSize = options.maxQueueSize || 100;\n  this.timer = (this.flushInterval) ? setTimeout(this.flush.bind(this), this.flushInterval) : null;\n  // keep some state to deduplicate requests if necessary\n  this.lastSentInput = \"\";\n  this.lastSentIndex = 0;\n}\n\nMapboxEventManager.prototype = {\n  /**\n     * Send a search.select event to the mapbox events service\n     * This event marks the array index of the item selected by the user out of the array of possible options\n     * @private\n     * @param {Object} selected the geojson feature selected by the user\n     * @param {Object} geocoder a mapbox-gl-geocoder instance\n     * @returns {Promise}\n     */\n  select: function(selected, geocoder){\n    var payload = this.getEventPayload('search.select', geocoder, { selectedFeature: selected });\n    if (!payload) return; // reject malformed event\n    if ((payload.resultIndex === this.lastSentIndex && payload.queryString === this.lastSentInput) || payload.resultIndex == -1) {\n      // don't log duplicate events if the user re-selected the same feature on the same search\n      return;\n    }\n    this.lastSentIndex = payload.resultIndex;\n    this.lastSentInput = payload.queryString;\n    return this.push(payload)\n  },\n\n  /**\n     * Send a search-start event to the mapbox events service\n     * This turnstile event marks when a user starts a new search\n     * @private\n     * @param {Object} geocoder a mapbox-gl-geocoder instance\n     * @returns {Promise}\n     */\n  start: function(geocoder){\n    var payload = this.getEventPayload('search.start', geocoder);\n    if (!payload) return; // reject malformed event\n    return this.push(payload);\n  },\n\n  /**\n   * Send a search-keyevent event to the mapbox events service\n   * This event records each keypress in sequence\n   * @private\n   * @param {Object} keyEvent the keydown event to log\n   * @param {Object} geocoder a mapbox-gl-geocoder instance\n   * \n   */\n  keyevent: function(keyEvent, geocoder){\n\n    //pass invalid event\n    if (!keyEvent.key) return;\n    // don't send events for keys that don't change the input\n    // TAB, ESC, LEFT, RIGHT, ENTER, UP, DOWN\n    if (keyEvent.metaKey || [9, 27, 37, 39, 13, 38, 40].indexOf(keyEvent.keyCode) !== -1) return;\n    var payload = this.getEventPayload('search.keystroke', geocoder, { key: keyEvent.key });\n    if (!payload) return; // reject malformed event\n    return this.push(payload);\n  },\n\n  /**\n   * Send an event to the events service\n   *\n   * The event is skipped if the instance is not enabled to send logging events\n   *\n   * @private\n   * @param {Object} payload the http POST body of the event\n   * @param {Function} [callback] a callback function to invoke when the send has completed\n   * @returns {Promise}\n   */\n  send: function (payload, callback) {\n    if (!this.enableEventLogging) {\n      if (callback) return callback();\n      return;\n    }\n    var options = this.getRequestOptions(payload);\n    this.request(options, function(err){\n      if (err) return this.handleError(err, callback);\n      if (callback) {\n        return callback();\n      }\n    }.bind(this))\n  },\n  /**\n   * Get http request options\n   * @private\n   * @param {*} payload\n   */\n  getRequestOptions: function(payload){\n    if (!Array.isArray(payload)) payload = [payload];\n    var options = {\n      // events must be sent with POST\n      method: \"POST\",\n      host: this.origin,\n      path: this.endpoint +  \"?access_token=\" + this.access_token,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body:JSON.stringify(payload) //events are arrays\n    }\n    return options\n  },\n\n  /**\n   * Get the event payload to send to the events service\n   * Most payload properties are shared across all events\n   * @private\n   * @param {String} event the name of the event to send to the events service. Valid options are 'search.start', 'search.select', 'search.feedback'.\n   * @param {Object} geocoder a mapbox-gl-geocoder instance\n   * @param {Object} eventArgs Additional arguments needed for certain event types\n   * @param {Object} eventArgs.key The key pressed by the user\n   * @param {Object} eventArgs.selectedFeature GeoJSON Feature selected by the user\n   * @returns {Object} an event payload\n   */\n  getEventPayload: function (event, geocoder, eventArgs = {}) {\n    // Make sure required arguments are present for certain event types\n    if (\n      (event === 'search.select' && !eventArgs.selectedFeature) ||\n      (event === 'search.keystroke' && !eventArgs.key)\n    ) {\n      return null;\n    }\n\n    // Handle proximity, whether null, lat/lng coordinate object, or 'ip'\n    var proximity;\n    if (!geocoder.options.proximity) {\n      proximity = null;\n    } else if (typeof geocoder.options.proximity === 'object') {\n      proximity = [geocoder.options.proximity.longitude, geocoder.options.proximity.latitude];\n    } else if (geocoder.options.proximity === 'ip') {\n      var ipProximityHeader = geocoder._headers ? geocoder._headers['ip-proximity'] : null;\n      if (ipProximityHeader && typeof ipProximityHeader === 'string') {\n        proximity = ipProximityHeader.split(',').map(parseFloat);\n      } else {\n        proximity = [999,999];  // Alias for 'ip' in event logs\n      }\n    } else {\n      proximity = geocoder.options.proximity;\n    }\n\n    var zoom = (geocoder._map) ? geocoder._map.getZoom() : undefined;\n    var payload = {\n      event: event,\n      version: this.getEventSchemaVersion(event),\n      created: +new Date(),\n      sessionIdentifier: this.getSessionId(),\n      country: this.countries,\n      userAgent: this.userAgent,\n      language: this.language,\n      bbox: this.bbox,\n      types: this.types,\n      endpoint: 'mapbox.places',\n      autocomplete: geocoder.options.autocomplete,\n      fuzzyMatch: geocoder.options.fuzzyMatch,\n      proximity: proximity,\n      limit: geocoder.options.limit,\n      routing: geocoder.options.routing,\n      worldview: geocoder.options.worldview,\n      mapZoom: zoom,\n      keyboardLocale: this.locale\n    }\n\n    // get the text in the search bar\n    if (event === \"search.select\"){\n      payload.queryString = geocoder.inputString;\n    } else if (event != \"search.select\" && geocoder._inputEl){\n      payload.queryString = geocoder._inputEl.value;\n    } else {\n      payload.queryString = geocoder.inputString;\n    }\n\n    // add additional properties for certain event types\n    if (['search.keystroke', 'search.select'].includes(event)) {\n      payload.path = 'geocoding/v5/mapbox.places';\n    }\n    if (event === 'search.keystroke' && eventArgs.key) {\n      payload.lastAction = eventArgs.key;\n    } else if (event === 'search.select' && eventArgs.selectedFeature) {\n      var selected = eventArgs.selectedFeature;\n      var resultIndex = this.getSelectedIndex(selected, geocoder);\n      payload.resultIndex = resultIndex;\n      payload.resultPlaceName = selected.place_name;\n      payload.resultId = selected.id;\n      if (selected.properties) {\n        payload.resultMapboxId = selected.properties.mapbox_id;\n      }\n      if (geocoder._typeahead) {\n        var results = geocoder._typeahead.data;\n        if (results && results.length > 0) {\n          payload.suggestionIds = this.getSuggestionIds(results);\n          payload.suggestionNames = this.getSuggestionNames(results);\n          payload.suggestionTypes = this.getSuggestionTypes(results);\n          payload.suggestionSources = this.getSuggestionSources(results);\n        }\n      }\n    }\n\n    // Finally, validate that required properties are present for API compatibility\n    if (!this.validatePayload(payload)) {\n      return null;\n    }\n\n    return payload;\n  },\n\n  /**\n   * Wraps the request function for easier testing\n   * Make an http request and invoke a callback\n   * @private\n   * @param {Object} opts options describing the http request to be made\n   * @param {Function} callback the callback to invoke when the http request is completed\n   */\n  request: function (opts, callback) {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function() {\n      if (this.readyState == 4 ) {\n        if (this.status == 204){\n          //success\n          return callback(null);\n        }else {\n          return callback(this.statusText);\n        }\n      }\n    };\n\n    xhttp.open(opts.method, opts.host + '/' + opts.path, true);\n    for (var header in opts.headers){\n      var headerValue = opts.headers[header];\n      xhttp.setRequestHeader(header, headerValue)\n    }\n    xhttp.send(opts.body);\n  },\n\n  /**\n   * Handle an error that occurred while making a request\n   * @param {Object} err an error instance to log\n   * @private\n   */\n  handleError: function (err, callback) {\n    if (callback) return callback(err);\n  },\n\n  /**\n   * Generate a session ID to be returned with all of the searches made by this geocoder instance\n   * ID is random and cannot be tracked across sessions\n   * @private\n   */\n  generateSessionID: function () {\n    return nanoid();\n  },\n\n  /**\n   * Get the a unique session ID for the current plugin session and increment the session counter.\n   *\n   * @returns {String} The session ID\n   */\n  getSessionId: function(){\n    return this.pluginSessionID + '.' + this.sessionIncrementer;\n  },\n\n  /**\n   * Get a user agent string to send with the request to the events service\n   * @private\n   */\n  getUserAgent: function () {\n    return 'mapbox-gl-geocoder.' + this.version + \".\" + navigator.userAgent;\n  },\n\n  /**\n     * Get the 0-based numeric index of the item that the user selected out of the list of options\n     * @private\n     * @param {Object} selected the geojson feature selected by the user\n     * @param {Object} geocoder a Mapbox-GL-Geocoder instance\n     * @returns {Number} the index of the selected result\n     */\n  getSelectedIndex: function(selected, geocoder){\n    if (!geocoder._typeahead) return;\n    var results = geocoder._typeahead.data;\n    var selectedID = selected.id;\n    var resultIDs = results.map(function (feature) {\n      return feature.id;\n    });\n    var selectedIdx = resultIDs.indexOf(selectedID);\n    return selectedIdx;\n  },\n\n  getSuggestionIds: function (results) {\n    return results.map(function (feature) {\n      if (feature.properties) {\n        return feature.properties.mapbox_id || '';\n      }\n      return feature.id || '';\n    });\n  },\n\n  getSuggestionNames: function (results) {\n    return results.map(function (feature) {\n      return feature.place_name || '';\n    });\n  },\n  \n  getSuggestionTypes: function (results) {\n    return results.map(function (feature) {\n      if (feature.place_type && Array.isArray(feature.place_type)) {\n        return feature.place_type[0] || '';\n      }\n      return '';\n    });\n  },\n  \n  getSuggestionSources: function (results) {\n    return results.map(function (feature) {\n      return feature._source || '';\n    });\n  },\n\n  /**\n   * Get the correct schema version for the event\n   * @private\n   * @param {String} event Name of the event\n   * @returns \n   */\n  getEventSchemaVersion: function(event) {\n    if (['search.keystroke', 'search.select'].includes(event)) {\n      return '2.2';\n    } else {\n      return '2.0';\n    }\n  },\n\n  /**\n   * Checks if a payload has all the required properties for the event type\n   * @private\n   * @param {Object} payload \n   * @returns \n   */\n  validatePayload: function(payload) {\n    if (!payload || !payload.event) return false;\n\n    var searchStartRequiredProps = ['event', 'created', 'sessionIdentifier', 'queryString'];\n    var searchKeystrokeRequiredProps = ['event', 'created', 'sessionIdentifier', 'queryString', 'lastAction'];\n    var searchSelectRequiredProps = ['event', 'created', 'sessionIdentifier', 'queryString', 'resultIndex', 'path', 'suggestionIds'];\n\n    var event = payload.event;\n    if (event === 'search.start') {\n      return this.objectHasRequiredProps(payload, searchStartRequiredProps);\n    } else if (event === 'search.keystroke') {\n      return this.objectHasRequiredProps(payload, searchKeystrokeRequiredProps);\n    } else if (event === 'search.select') {\n      return this.objectHasRequiredProps(payload, searchSelectRequiredProps);\n    }\n\n    return true;\n  },\n\n  /**\n   * Checks of an object has all the required properties\n   * @private\n   * @param {Object} obj \n   * @param {Array<String>} requiredProps \n   * @returns \n   */\n  objectHasRequiredProps: function(obj, requiredProps) {\n    return requiredProps.every(function(prop) {\n      if (prop === 'queryString') {\n        return typeof obj[prop] === 'string' && obj[prop].length > 0;\n      }\n      return obj[prop] !== undefined;\n    });\n  },\n\n  /**\n     * Check whether events should be logged\n     * Clients using a localGeocoder or an origin other than mapbox should not have events logged\n     * @private\n     */\n  shouldEnableLogging: function(options){\n    if (options.enableEventLogging === false) return false;\n    if (options.origin && options.origin !== 'https://api.mapbox.com') return false;\n    return true;\n  },\n\n  /**\n   * Flush out the event queue by sending events to the events service\n   * @private\n   */\n  flush: function(){\n    if (this.eventQueue.length > 0){\n      this.send(this.eventQueue);\n      this.eventQueue = new Array();\n    }\n    // //reset the timer\n    if (this.timer)  clearTimeout(this.timer);\n    if (this.flushInterval) this.timer = setTimeout(this.flush.bind(this), this.flushInterval)\n  },\n\n  /**\n   * Push event into the pending queue\n   * @param {Object} evt the event to send to the events service\n   * @param {Boolean} forceFlush indicates that the event queue should be flushed after adding this event regardless of size of the queue\n   * @private\n   */\n  push: function(evt, forceFlush){\n    this.eventQueue.push(evt);\n    if (this.eventQueue.length >= this.maxQueueSize || forceFlush){\n      this.flush();\n    }\n  },\n\n  /**\n   * Flush any remaining events from the queue before it is removed\n   * @private\n   */\n  remove: function(){\n    this.flush();\n  }\n}\n\n\n\nmodule.exports = MapboxEventManager;\n", "'use strict';\n\n/**\n * Localized values for the placeholder string\n * \n * @private\n */\nvar placeholder = {\n  // list drawn from https://docs.mapbox.com/api/search/#language-coverage\n  'de': '<PERSON><PERSON>', // german\n  'it': 'Ricerca', //italian\n  'en': 'Search', // english\n  'nl': '<PERSON><PERSON>', //dutch\n  'fr': 'Chercher',  //french\n  'ca': '<PERSON>rca', //catalan\n  'he': 'לחפש', //hebrew\n  'ja': 'サーチ',  //japanese\n  'lv': 'Meklēt', //latvian\n  'pt': 'Procurar', //portuguese \n  'sr': 'Претрага', //serbian\n  'zh': '搜索', //chinese-simplified\n  'cs': 'Vyhledávání', //czech\n  'hu': 'Keresés', //hungarian\n  'ka': 'ძიება', // georgian\n  'nb': '<PERSON><PERSON><PERSON>', //norwegian\n  'sk': 'Vyhľadávanie', //slovak\n  'th': 'ค้นหา', //thai\n  'fi': 'Hae',//finnish\n  'is': '<PERSON><PERSON>',//icelandic\n  'ko': '수색',//korean\n  'pl':  'Szukaj', //polish\n  'sl': 'Iskanje', //slovenian\n  'fa': 'جستجو',  //persian(aka farsi)\n  'ru': 'Поиск'//russian\n}\n\nmodule.exports = {placeholder: placeholder};\n", "!function(root, name, make) {\r\n  if (typeof module != 'undefined' && module.exports) module.exports = make()\r\n  else root[name] = make()\r\n}(this, 'subtag', function() {\r\n\r\n  var empty = ''\r\n  var pattern = /^([a-zA-Z]{2,3})(?:[_-]+([a-zA-Z]{3})(?=$|[_-]+))?(?:[_-]+([a-zA-Z]{4})(?=$|[_-]+))?(?:[_-]+([a-zA-Z]{2}|[0-9]{3})(?=$|[_-]+))?/\r\n\r\n  function match(tag) {\r\n    return tag.match(pattern) || []\r\n  }\r\n\r\n  function split(tag) {\r\n    return match(tag).filter(function(v, i) { return v && i })\r\n  }\r\n\r\n  function api(tag) {\r\n    tag = match(tag)\r\n    return {\r\n      language: tag[1] || empty,\r\n      extlang: tag[2] || empty,\r\n      script: tag[3] || empty,\r\n      region: tag[4] || empty\r\n    }\r\n  }\r\n\r\n  function expose(target, key, value) {\r\n    Object.defineProperty(target, key, {\r\n      value: value,\r\n      enumerable: true\r\n    })\r\n  }\r\n\r\n  function part(position, pattern, type) {\r\n    function method(tag) {\r\n      return match(tag)[position] || empty\r\n    }\r\n    expose(method, 'pattern', pattern)\r\n    expose(api, type, method)\r\n  }\r\n\r\n  part(1, /^[a-zA-Z]{2,3}$/, 'language')\r\n  part(2, /^[a-zA-Z]{3}$/, 'extlang')\r\n  part(3, /^[a-zA-Z]{4}$/, 'script')\r\n  part(4, /^[a-zA-Z]{2}$|^[0-9]{3}$/, 'region')\r\n\r\n  expose(api, 'split', split)\r\n\r\n  return api\r\n});\r\n", "function Geolocation() {}\n\nGeolocation.prototype = {\n\n  isSupport: function() {\n    return Boolean(window.navigator.geolocation);\n  },\n\n  getCurrentPosition: function() {\n    const positionOptions = {\n      enableHighAccuracy: true\n    };\n\n    return new Promise(function(resolve, reject) {\n      window.navigator.geolocation.getCurrentPosition(resolve, reject, positionOptions);\n    });\n  },\n}\n\nmodule.exports = Geolocation;\n", "/**\n * This function transforms the feature from reverse geocoding to plain text with specified accuracy\n * @param {object} feature \n * @param {string} accuracy \n * @returns \n */\nfunction transformFeatureToGeolocationText(feature, accuracy) {\n  const addrInfo = getAddressInfo(feature);\n\n  const addressAccuracy =  ['address', 'street', 'place', 'country'];\n  var currentAccuracy;\n\n  if (typeof accuracy === 'function') {\n    return accuracy(addrInfo)\n  }\n\n  const accuracyIndex = addressAccuracy.indexOf(accuracy);\n\n  if (accuracyIndex === -1) {\n    currentAccuracy = addressAccuracy;\n  } else {\n    currentAccuracy = addressAccuracy.slice(accuracyIndex);\n  }\n\n  return currentAccuracy.reduce(function(acc, name) {\n    if (!addrInfo[name]) {\n      return acc;\n    }\n\n    if (acc !== '') {\n      acc = acc + ', ';\n    }\n\n    return acc + addrInfo[name];\n  }, '');\n}\n/**\n * This function transforms the feature from reverse geocoding to AddressInfo object\n * @param {object} feature \n * @returns {object}\n */\nfunction getAddressInfo(feature) {\n  const houseNumber = feature.address || '';\n  const street = feature.text || '';\n  const placeName = feature.place_name || '';\n  const address = placeName.split(',')[0];\n\n  const addrInfo = {\n    address: address,\n    houseNumber: houseNumber,\n    street: street,\n    placeName: placeName,\n  }\n\n  feature.context.forEach(function (context) {\n    const layer = context.id.split('.')[0];\n    addrInfo[layer] = context.text;\n  });\n\n  return addrInfo;\n}\n\nconst REVERSE_GEOCODE_COORD_RGX = /^[ ]*(-?\\d{1,3}(\\.\\d{0,256})?)[, ]+(-?\\d{1,3}(\\.\\d{0,256})?)[ ]*$/;\n\nmodule.exports = {\n  transformFeatureToGeolocationText: transformFeatureToGeolocationText,\n  getAddressInfo: getAddressInfo,\n  REVERSE_GEOCODE_COORD_RGX: REVERSE_GEOCODE_COORD_RGX,\n}", "'use strict';\n\nvar Typeahead = require('suggestions');\nvar debounce = require('lodash.debounce');\nvar extend = require('xtend');\nvar EventEmitter = require('events').EventEmitter;\nvar exceptions = require('./exceptions');\nvar MapboxClient = require('@mapbox/mapbox-sdk');\nvar mbxGeocoder = require('@mapbox/mapbox-sdk/services/geocoding');\nvar MapboxEventManager = require('./events');\nvar localization = require('./localization');\nvar subtag = require('subtag');\nvar Geolocation = require('./geolocation');\nvar utils = require('./utils');\n\n\nconst GEOCODE_REQUEST_TYPE = {\n  FORWARD: 0,\n  LOCAL: 1,\n  REVERSE: 2,\n};\n\n/**\n * Don't include this as part of the options object when creating a new MapboxGeocoder instance.\n */\nfunction getFooterNode() {\n  var div = document.createElement('div');\n  div.className = 'mapboxgl-ctrl-geocoder--powered-by';\n  div.innerHTML = '<a href=\"https://www.mapbox.com/search-service\" target=\"_blank\">Powered by Mapbox</a>';\n\n  return div;\n}\n\n/**\n * A geocoder component using the [Mapbox Geocoding API](https://docs.mapbox.com/api/search/#geocoding)\n * @class MapboxGeocoder\n * @param {Object} options\n * @param {String} options.accessToken Required.\n * @param {String} [options.origin=https://api.mapbox.com] Use to set a custom API origin.\n * @param {Object} [options.mapboxgl] A [mapbox-gl](https://github.com/mapbox/mapbox-gl-js) instance to use when creating [Markers](https://docs.mapbox.com/mapbox-gl-js/api/#marker). Required if `options.marker` is `true`.\n * @param {Number} [options.zoom=16] On geocoded result what zoom level should the map animate to when a `bbox` isn't found in the response. If a `bbox` is found the map will fit to the `bbox`.\n * @param {Boolean|Object} [options.flyTo=true] If `false`, animating the map to a selected result is disabled. If `true`, animating the map will use the default animation parameters. If an object, it will be passed as `options` to the map [`flyTo`](https://docs.mapbox.com/mapbox-gl-js/api/#map#flyto) or [`fitBounds`](https://docs.mapbox.com/mapbox-gl-js/api/#map#fitbounds) method providing control over the animation of the transition.\n * @param {String} [options.placeholder=Search] Override the default placeholder attribute value.\n * @param {Object|'ip'} [options.proximity] a geographical point given as an object with `latitude` and `longitude` properties, or the string 'ip' to use a user's IP address location. Search results closer to this point will be given higher priority.\n * @param {Boolean} [options.trackProximity=true] If `true`, the geocoder proximity will dynamically update based on the current map view or user's IP location, depending on zoom level.\n * @param {Boolean} [options.collapsed=false] If `true`, the geocoder control will collapse until hovered or in focus.\n * @param {Boolean} [options.clearAndBlurOnEsc=false] If `true`, the geocoder control will clear it's contents and blur when user presses the escape key.\n * @param {Boolean} [options.clearOnBlur=false] If `true`, the geocoder control will clear its value when the input blurs.\n * @param {Array} [options.bbox] a bounding box argument: this is\n * a bounding box given as an array in the format `[minX, minY, maxX, maxY]`.\n * Search results will be limited to the bounding box.\n * @param {string} [options.countries] a comma separated list of country codes to\n * limit results to specified country or countries.\n * @param {string} [options.types] a comma seperated list of types that filter\n * results to match those specified. See https://docs.mapbox.com/api/search/#data-types\n * for available types.\n * If reverseGeocode is enabled and no type is specified, the type defaults to POIs. Otherwise, if you configure more than one type, the first type will be used.\n * @param {Number} [options.minLength=2] Minimum number of characters to enter before results are shown.\n * @param {Number} [options.limit=5] Maximum number of results to show.\n * @param {string} [options.language] Specify the language to use for response text and query result weighting. Options are IETF language tags comprised of a mandatory ISO 639-1 language code and optionally one or more IETF subtags for country or script. More than one value can also be specified, separated by commas. Defaults to the browser's language settings.\n * @param {Function} [options.filter] A function which accepts a Feature in the [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) format to filter out results from the Geocoding API response before they are included in the suggestions list. Return `true` to keep the item, `false` otherwise.\n * @param {Function} [options.localGeocoder] A function accepting the query string which performs local geocoding to supplement results from the Mapbox Geocoding API. Expected to return an Array of GeoJSON Features in the [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) format.\n * @param {Function} [options.externalGeocoder] A function accepting the query string and current features list which performs geocoding to supplement results from the Mapbox Geocoding API. Expected to return a Promise which resolves to an Array of GeoJSON Features in the [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) format.\n * @param {distance|score} [options.reverseMode=distance] - Set the factors that are used to sort nearby results.\n * @param {boolean} [options.reverseGeocode=false] If `true`, enable reverse geocoding mode. In reverse geocoding, search input is expected to be coordinates in the form `lat, lon`, with suggestions being the reverse geocodes.\n * @param {boolean} [options.flipCoordinates=false] If `true`, search input coordinates for reverse geocoding is expected to be in the form `lon, lat` instead of the default `lat, lon`.\n * @param {Boolean} [options.enableEventLogging=true] Allow Mapbox to collect anonymous usage statistics from the plugin.\n * @param {Boolean|Object} [options.marker=true]  If `true`, a [Marker](https://docs.mapbox.com/mapbox-gl-js/api/#marker) will be added to the map at the location of the user-selected result using a default set of Marker options.  If the value is an object, the marker will be constructed using these options. If `false`, no marker will be added to the map. Requires that `options.mapboxgl` also be set.\n * @param {Function} [options.render] A function that specifies how the results should be rendered in the dropdown menu. This function should accepts a single [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) object as input and return a string. Any HTML in the returned string will be rendered.\n * @param {Function} [options.getItemValue] A function that specifies how the selected result should be rendered in the search bar. This function should accept a single [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) object as input and return a string. HTML tags in the output string will not be rendered. Defaults to `(item) => item.place_name`.\n * @param {String} [options.mode=mapbox.places] A string specifying the geocoding [endpoint](https://docs.mapbox.com/api/search/#endpoints) to query. Options are `mapbox.places` and `mapbox.places-permanent`. The `mapbox.places-permanent` mode requires an enterprise license for permanent geocodes.\n * @param {Boolean} [options.localGeocoderOnly=false] If `true`, indicates that the `localGeocoder` results should be the only ones returned to the user. If `false`, indicates that the `localGeocoder` results should be combined with those from the Mapbox API with the `localGeocoder` results ranked higher.\n * @param {Boolean} [options.autocomplete=true] Specify whether to return autocomplete results or not. When autocomplete is enabled, results will be included that start with the requested string, rather than just responses that match it exactly.\n * @param {Boolean} [options.fuzzyMatch=true] Specify whether the Geocoding API should attempt approximate, as well as exact, matching when performing searches, or whether it should opt out of this behavior and only attempt exact matching.\n * @param {Boolean} [options.routing=false] Specify whether to request additional metadata about the recommended navigation destination corresponding to the feature or not. Only applicable for address features.\n * @param {String} [options.worldview=\"us\"] Filter results to geographic features whose characteristics are defined differently by audiences belonging to various regional, cultural, or political groups.\n * @param {Boolean} [options.enableGeolocation=false] If `true` enable user geolocation feature.\n * @param {('address'|'street'|'place'|'country')} [options.addressAccuracy=\"street\"] The accuracy for the geolocation feature with which we define the address line to fill. The browser API returns the user's position with accuracy, and sometimes we can get the neighbor's address. To prevent receiving an incorrect address, you can reduce the accuracy of the definition.\n * @example\n * var geocoder = new MapboxGeocoder({ accessToken: mapboxgl.accessToken });\n * map.addControl(geocoder);\n * @return {MapboxGeocoder} `this`\n *\n */\n\nfunction MapboxGeocoder(options) {\n  this._eventEmitter = new EventEmitter();\n  this.options = extend({}, this.options, options);\n  this.inputString = '';\n  this.fresh = true;\n  this.lastSelected = null;\n  this.geolocation = new Geolocation();\n}\n\nMapboxGeocoder.prototype = {\n  options: {\n    zoom: 16,\n    flyTo: true,\n    trackProximity: true,\n    minLength: 2,\n    reverseGeocode: false,\n    flipCoordinates: false,\n    limit: 5,\n    origin: 'https://api.mapbox.com',\n    enableEventLogging: true,\n    marker: true,\n    mapboxgl: null,\n    collapsed: false,\n    clearAndBlurOnEsc: false,\n    clearOnBlur: false,\n    enableGeolocation: false,\n    addressAccuracy: 'street',\n    getItemValue: function(item) {\n      return item.place_name\n    },\n    render: function(item) {\n      var placeName = item.place_name.split(',');\n      return '<div class=\"mapboxgl-ctrl-geocoder--suggestion\"><div class=\"mapboxgl-ctrl-geocoder--suggestion-title\">' + placeName[0]+ '</div><div class=\"mapboxgl-ctrl-geocoder--suggestion-address\">' + placeName.splice(1, placeName.length).join(',') + '</div></div>';\n    }\n  },\n  \n  _headers: {},\n\n  /**\n   * Add the geocoder to a container. The container can be either a `mapboxgl.Map`, an `HTMLElement` or a CSS selector string.\n   *\n   * If the container is a [`mapboxgl.Map`](https://docs.mapbox.com/mapbox-gl-js/api/map/), this function will behave identically to [`Map.addControl(geocoder)`](https://docs.mapbox.com/mapbox-gl-js/api/map/#map#addcontrol).\n   * If the container is an instance of [`HTMLElement`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement), then the geocoder will be appended as a child of that [`HTMLElement`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement).\n   * If the container is a [CSS selector string](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Selectors), the geocoder will be appended to the element returned from the query.\n   *\n   * This function will throw an error if the container is none of the above.\n   * It will also throw an error if the referenced HTML element cannot be found in the `document.body`.\n   *\n   * For example, if the HTML body contains the element `<div id='geocoder-container'></div>`, the following script will append the geocoder to `#geocoder-container`:\n   *\n   * ```javascript\n   * var geocoder = new MapboxGeocoder({ accessToken: mapboxgl.accessToken });\n   * geocoder.addTo('#geocoder-container');\n   * ```\n   * @param {String|HTMLElement|mapboxgl.Map} container A reference to the container to which to add the geocoder\n   */\n  addTo: function(container){\n\n    function addToExistingContainer (geocoder, container) {\n      if (!document.body.contains(container)) {\n        throw new Error(\"Element provided to #addTo() exists, but is not in the DOM\")\n      }\n      const el = geocoder.onAdd(); //returns the input elements, which are then added to the requested html container\n      container.appendChild(el);\n    }\n\n    // if the container is a map, add the control like normal\n    if (container._controlContainer){\n      //  it's a mapbox-gl map, add like normal\n      container.addControl(this);\n    }\n    // if the container is an HTMLElement, then set the parent to be that element\n    else if (container instanceof HTMLElement) {\n      addToExistingContainer(this, container);\n    }\n    // if the container is a string, treat it as a CSS query\n    else if (typeof container == 'string'){\n      const parent = document.querySelectorAll(container);\n      if (parent.length === 0){\n        throw new Error(\"Element \", container, \"not found.\")\n      }\n\n      if (parent.length > 1){\n        throw new Error(\"Geocoder can only be added to a single html element\")\n      }\n\n      addToExistingContainer(this, parent[0]);\n    }else{\n      throw new Error(\"Error: addTo must be a mapbox-gl-js map, an html element, or a CSS selector query for a single html element\")\n    }\n  },\n\n  onAdd: function(map) {\n    if (map && typeof map != 'string'){\n      this._map = map;\n    }\n\n    this.setLanguage();\n\n    if (!this.options.localGeocoderOnly){\n      this.geocoderService = mbxGeocoder(\n        MapboxClient({\n          accessToken: this.options.accessToken,\n          origin: this.options.origin\n        })\n      );\n    }\n\n    if (this.options.localGeocoderOnly && !this.options.localGeocoder){\n      throw new Error(\"A localGeocoder function must be specified to use localGeocoderOnly mode\")\n    }\n\n    this.eventManager = new MapboxEventManager(this.options);\n\n    this._onChange = this._onChange.bind(this);\n    this._onKeyDown = this._onKeyDown.bind(this);\n    this._onPaste = this._onPaste.bind(this);\n    this._onBlur = this._onBlur.bind(this);\n    this._showButton = this._showButton.bind(this);\n    this._hideButton = this._hideButton.bind(this);\n    this._onQueryResult = this._onQueryResult.bind(this);\n    this.clear = this.clear.bind(this);\n    this._updateProximity = this._updateProximity.bind(this);\n    this._collapse = this._collapse.bind(this);\n    this._unCollapse = this._unCollapse.bind(this);\n    this._clear = this._clear.bind(this);\n    this._clearOnBlur = this._clearOnBlur.bind(this);\n    this._geolocateUser = this._geolocateUser.bind(this);\n\n    var el = (this.container = document.createElement('div'));\n    el.className = 'mapboxgl-ctrl-geocoder mapboxgl-ctrl';\n\n    var searchIcon = this.createIcon('search', '<path d=\"M7.4 2.5c-2.7 0-4.9 2.2-4.9 4.9s2.2 4.9 4.9 4.9c1 0 1.8-.2 2.5-.8l3.7 3.7c.2.2.4.3.8.3.7 0 1.1-.4 1.1-1.1 0-.3-.1-.5-.3-.8L11.4 10c.4-.8.8-1.6.8-2.5.1-2.8-2.1-5-4.8-5zm0 1.6c1.8 0 3.2 1.4 3.2 3.2s-1.4 3.2-3.2 3.2-3.3-1.3-3.3-3.1 1.4-3.3 3.3-3.3z\"/>')\n\n    this._inputEl = document.createElement('input');\n    this._inputEl.type = 'text';\n    this._inputEl.className = 'mapboxgl-ctrl-geocoder--input';\n\n    this.setPlaceholder();\n\n    if (this.options.collapsed) {\n      this._collapse();\n      this.container.addEventListener('mouseenter', this._unCollapse);\n      this.container.addEventListener('mouseleave', this._collapse);\n      this._inputEl.addEventListener('focus', this._unCollapse);\n    }\n\n    if (this.options.collapsed || this.options.clearOnBlur) {\n      this._inputEl.addEventListener('blur', this._onBlur);\n    }\n\n    this._inputEl.addEventListener('keydown', debounce(this._onKeyDown, 200));\n    this._inputEl.addEventListener('paste', this._onPaste);\n    this._inputEl.addEventListener('change', this._onChange);\n    this.container.addEventListener('mouseenter', this._showButton);\n    this.container.addEventListener('mouseleave', this._hideButton);\n    this._inputEl.addEventListener('keyup', function(e){\n      this.eventManager.keyevent(e, this);\n    }.bind(this));\n\n    var actions = document.createElement('div');\n    actions.classList.add('mapboxgl-ctrl-geocoder--pin-right');\n\n    this._clearEl = document.createElement('button');\n    this._clearEl.setAttribute('aria-label', 'Clear');\n    this._clearEl.addEventListener('click', this.clear);\n    this._clearEl.className = 'mapboxgl-ctrl-geocoder--button';\n\n    var buttonIcon = this.createIcon('close', '<path d=\"M3.8 2.5c-.6 0-1.3.7-1.3 1.3 0 .3.2.7.5.8L7.2 9 3 13.2c-.3.3-.5.7-.5 1 0 .6.7 1.3 1.3 1.3.3 0 .7-.2 1-.5L9 10.8l4.2 4.2c.2.3.7.3 1 .3.6 0 1.3-.7 1.3-1.3 0-.3-.2-.7-.3-1l-4.4-4L15 4.6c.3-.2.5-.5.5-.8 0-.7-.7-1.3-1.3-1.3-.3 0-.7.2-1 .3L9 7.1 4.8 2.8c-.3-.1-.7-.3-1-.3z\"/>')\n    this._clearEl.appendChild(buttonIcon);\n\n    this._loadingEl = this.createIcon('loading', '<path fill=\"#333\" d=\"M4.4 4.4l.8.8c2.1-2.1 5.5-2.1 7.6 0l.8-.8c-2.5-2.5-6.7-2.5-9.2 0z\"/><path opacity=\".1\" d=\"M12.8 12.9c-2.1 2.1-5.5 2.1-7.6 0-2.1-2.1-2.1-5.5 0-7.7l-.8-.8c-2.5 2.5-2.5 6.7 0 9.2s6.6 2.5 9.2 0 2.5-6.6 0-9.2l-.8.8c2.2 2.1 2.2 5.6 0 7.7z\"/>');\n\n    actions.appendChild(this._clearEl);\n    actions.appendChild(this._loadingEl);\n\n    el.appendChild(searchIcon);\n    el.appendChild(this._inputEl);\n    el.appendChild(actions);\n\n    if (this.options.enableGeolocation && this.geolocation.isSupport()) {\n      this._geolocateEl = document.createElement('button');\n      this._geolocateEl.setAttribute('aria-label', 'Geolocate');\n      this._geolocateEl.addEventListener('click', this._geolocateUser);\n      this._geolocateEl.className = 'mapboxgl-ctrl-geocoder--button';\n\n      var geolocateIcon = this.createIcon('geolocate', '<path d=\"M12.999 3.677L2.042 8.269c-.962.403-.747 1.823.29 1.912l5.032.431.431 5.033c.089 1.037 1.509 1.252 1.912.29l4.592-10.957c.345-.822-.477-1.644-1.299-1.299z\" fill=\"#4264fb\"/>');\n      this._geolocateEl.appendChild(geolocateIcon);\n\n      actions.appendChild(this._geolocateEl);\n      this._showGeolocateButton();\n    }\n\n    var typeahead = this._typeahead = new Typeahead(this._inputEl, [], {\n      filter: false,\n      minLength: this.options.minLength,\n      limit: this.options.limit\n    });\n\n    this.setRenderFunction(this.options.render);\n    typeahead.getItemValue = this.options.getItemValue;\n\n    // Add support for footer.\n    var parentDraw = typeahead.list.draw;\n    var footerNode = this._footerNode = getFooterNode();\n    typeahead.list.draw = function() {\n      parentDraw.call(this);\n\n      footerNode.addEventListener('mousedown', function() {\n        this.selectingListItem = true;\n      }.bind(this));\n    \n      footerNode.addEventListener('mouseup', function() {\n        this.selectingListItem = false;\n      }.bind(this));\n\n      this.element.appendChild(footerNode);\n    };\n\n    this.mapMarker = null;\n    this._handleMarker = this._handleMarker.bind(this);\n    if (this._map){\n      if (this.options.trackProximity ) {\n        this._updateProximity();\n        this._map.on('moveend', this._updateProximity);\n      }\n      this._mapboxgl = this.options.mapboxgl;\n      if (!this._mapboxgl && this.options.marker) {\n        // eslint-disable-next-line no-console\n        console.error(\"No mapboxgl detected in options. Map markers are disabled. Please set options.mapboxgl.\");\n        this.options.marker = false;\n      }\n    }\n    return el;\n  },\n\n  _geolocateUser: function () {\n    this._hideGeolocateButton();\n    this._showLoadingIcon();\n\n    this.geolocation.getCurrentPosition().then(function(geolocationPosition) {\n      this._hideLoadingIcon();\n\n      const geojson = {\n        geometry: {\n          type: 'Point',\n          coordinates: [geolocationPosition.coords.longitude, geolocationPosition.coords.latitude]\n        }\n      };\n\n      this._handleMarker(geojson);\n      this._fly(geojson);\n\n      this._typeahead.clear();\n      this._typeahead.selected = true;\n      this.lastSelected = JSON.stringify(geojson);\n      this._showClearButton();\n      this.fresh = false;\n\n      const config = {\n        limit: 1,\n        language: [this.options.language],\n        query: geojson.geometry.coordinates,\n        types: [\"address\"]\n      };\n\n      if (this.options.localGeocoderOnly) {\n        const text = geojson.geometry.coordinates[0] + ',' + geojson.geometry.coordinates[1]\n        this._setInputValue(text);\n\n        this._eventEmitter.emit('result', { result: geojson });\n      } else {\n        this.geocoderService.reverseGeocode(config).send().then(function (resp) {\n          const feature = resp.body.features[0];\n  \n          if (feature) {\n            const locationText = utils.transformFeatureToGeolocationText(feature, this.options.addressAccuracy);\n            this._setInputValue(locationText);\n  \n            feature.user_coordinates = geojson.geometry.coordinates;\n            this._eventEmitter.emit('result', { result: feature });\n          } else {\n            this._eventEmitter.emit('result', { result: { user_coordinates: geojson.geometry.coordinates } });\n          }\n        }.bind(this));\n      }\n    }.bind(this)).catch(function(error) {\n      if (error.code === 1) {\n        this._renderUserDeniedGeolocationError();\n      } else {\n        this._renderLocationError();\n      }\n\n      this._hideLoadingIcon();\n      this._showGeolocateButton();\n      this._hideAttribution();\n    }.bind(this));\n  },\n\n  createIcon: function(name, path) {\n    var icon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    icon.setAttribute('class', 'mapboxgl-ctrl-geocoder--icon mapboxgl-ctrl-geocoder--icon-' + name);\n    icon.setAttribute('viewBox', '0 0 18 18');\n    icon.setAttribute('xml:space','preserve');\n    icon.setAttribute('width', 18);\n    icon.setAttribute('height', 18);\n    icon.innerHTML = path;\n    return icon;\n  },\n\n  onRemove: function() {\n    this.container.parentNode.removeChild(this.container);\n\n    if (this.options.trackProximity && this._map) {\n      this._map.off('moveend', this._updateProximity);\n    }\n\n    this._removeMarker();\n\n    this._map = null;\n\n    return this;\n  },\n\n  _setInputValue: function (value) {\n    this._inputEl.value = value;\n  \n    setTimeout(function () {\n      this._inputEl.focus();\n      this._inputEl.scrollLeft = 0;\n      this._inputEl.setSelectionRange(0, 0);\n    }.bind(this), 1);\n  },\n\n  _onPaste: function(e){\n    var value = (e.clipboardData || window.clipboardData).getData('text');\n    if (value.length >= this.options.minLength) {\n      this._geocode(value);\n    }\n  },\n\n  _onKeyDown: function(e) {\n    var ESC_KEY_CODE = 27,\n      TAB_KEY_CODE = 9;\n\n    if (e.keyCode === ESC_KEY_CODE && this.options.clearAndBlurOnEsc) {\n      this._clear(e);\n      return this._inputEl.blur();\n    }\n\n    // if target has shadowRoot, then get the actual active element inside the shadowRoot\n    var target = e.target && e.target.shadowRoot\n      ? e.target.shadowRoot.activeElement\n      : e.target;\n    var value = target ? target.value : '';\n\n    if (!value) {\n      this.fresh = true;\n      // the user has removed all the text\n      if (e.keyCode !== TAB_KEY_CODE) this.clear(e);\n      this._showGeolocateButton();\n      return this._hideClearButton();\n    }\n\n    this._hideGeolocateButton();\n\n    // TAB, ESC, LEFT, RIGHT, ENTER, UP, DOWN\n    if ((e.metaKey || [TAB_KEY_CODE, ESC_KEY_CODE, 37, 39, 13, 38, 40].indexOf(e.keyCode) !== -1))\n      return;\n\n    if (target.value.length >= this.options.minLength) {\n      this._geocode(target.value);\n    }\n  },\n\n  _showButton: function() {\n    if (this._typeahead.selected) this._showClearButton();\n  },\n\n  _hideButton: function() {\n    if (this._typeahead.selected) this._hideClearButton();\n  },\n\n  _showClearButton: function() {\n    this._clearEl.style.display = 'block';\n  },\n\n  _hideClearButton: function() {\n    this._clearEl.style.display = 'none'\n  },\n\n  _showGeolocateButton: function() {\n    if (this._geolocateEl && this.geolocation.isSupport()) {\n      this._geolocateEl.style.display = 'block';\n    }\n  },\n\n  _hideGeolocateButton: function() {\n    if (this._geolocateEl) {\n      this._geolocateEl.style.display = 'none';\n    }\n  },\n\n  _showLoadingIcon: function() {\n    this._loadingEl.style.display = 'block';\n  },\n  \n  _hideLoadingIcon: function() {\n    this._loadingEl.style.display = 'none';\n  },\n\n  _showAttribution: function() {\n    this._footerNode.style.display = 'block'\n  },\n  \n  _hideAttribution: function() {\n    this._footerNode.style.display = 'none'\n  },\n\n  _onBlur: function(e) {\n    if (this.options.clearOnBlur) {\n      this._clearOnBlur(e);\n    }\n    if (this.options.collapsed) {\n      this._collapse();\n    }\n  },\n  _onChange: function() {\n    var selected = this._typeahead.selected;\n    if (selected  && JSON.stringify(selected) !== this.lastSelected) {\n      this._hideClearButton();\n      if (this.options.flyTo) {\n        this._fly(selected);\n      }\n      if (this.options.marker && this._mapboxgl){\n        this._handleMarker(selected);\n      }\n\n      // After selecting a feature, re-focus the textarea and set\n      // cursor at start.\n      this._inputEl.focus();\n      this._inputEl.scrollLeft = 0;\n      this._inputEl.setSelectionRange(0, 0);\n      this.lastSelected = JSON.stringify(selected);\n      this._eventEmitter.emit('result', { result: selected });\n      this.eventManager.select(selected, this);\n    }\n  },\n\n  _fly: function(selected) {\n    var flyOptions;\n    if (selected.properties && exceptions[selected.properties.short_code]) {\n      // Certain geocoder search results return (and therefore zoom to fit)\n      // an unexpectedly large bounding box: for example, both Russia and the\n      // USA span both sides of -180/180, or France includes the island of\n      // Reunion in the Indian Ocean. An incomplete list of these exceptions\n      // at ./exceptions.json provides \"reasonable\" bounding boxes as a\n      // short-term solution; this may be amended as necessary.\n      flyOptions = extend({}, this.options.flyTo);\n      if (this._map){\n        this._map.fitBounds(exceptions[selected.properties.short_code].bbox, flyOptions);\n      }\n    } else if (selected.bbox) {\n      var bbox = selected.bbox;\n      flyOptions = extend({}, this.options.flyTo);\n      if (this._map){\n        this._map.fitBounds([[bbox[0], bbox[1]], [bbox[2], bbox[3]]], flyOptions);\n      }\n    } else {\n      var defaultFlyOptions = {\n        zoom: this.options.zoom\n      }\n      flyOptions = extend({}, defaultFlyOptions, this.options.flyTo);\n      //  ensure that center is not overriden by custom options\n      if (selected.center) {\n        flyOptions.center = selected.center;\n      } else if (selected.geometry && selected.geometry.type && selected.geometry.type === 'Point' && selected.geometry.coordinates) {\n        flyOptions.center = selected.geometry.coordinates;\n      }\n\n      if (this._map){\n        this._map.flyTo(flyOptions);\n      }\n    }\n  },\n\n  _requestType: function(options, search) {\n    var type;\n    if (options.localGeocoderOnly) {\n      type = GEOCODE_REQUEST_TYPE.LOCAL;\n    } else if (options.reverseGeocode && utils.REVERSE_GEOCODE_COORD_RGX.test(search)) {\n      type = GEOCODE_REQUEST_TYPE.REVERSE;\n    } else {\n      type = GEOCODE_REQUEST_TYPE.FORWARD;\n    }\n    return type;\n  },\n\n  _setupConfig: function(requestType, search) {\n    // Possible config properties to pass to client\n    const keys = [\n      'bbox',\n      'limit',\n      'proximity',\n      'countries',\n      'types',\n      'language',\n      'reverseMode',\n      'mode',\n      'autocomplete',\n      'fuzzyMatch',\n      'routing',\n      'worldview'\n    ];\n    const spacesOrCommaRgx = /[\\s,]+/;\n\n    var self = this;\n    var config = keys.reduce(function(config, key) {\n      // don't include undefined/null params, but allow boolean, among other, values\n      if (self.options[key] === undefined || self.options[key] === null) {\n        return config;\n      }\n\n      // countries, types, and language need to be passed in as arrays to client\n      // https://github.com/mapbox/mapbox-sdk-js/blob/master/services/geocoding.js#L38-L47\n      ['countries', 'types', 'language'].indexOf(key) > -1\n        ? (config[key] = self.options[key].split(spacesOrCommaRgx))\n        : (config[key] = self.options[key]);\n\n      const isCoordKey =\n        typeof self.options[key].longitude === 'number' &&\n        typeof self.options[key].latitude  === 'number';\n\n      if (key === 'proximity' && isCoordKey) {\n        const lng = self.options[key].longitude;\n        const lat = self.options[key].latitude;\n\n        config[key] = [lng, lat];\n      }\n\n      return config;\n    }, {});\n\n    switch (requestType) {\n    case GEOCODE_REQUEST_TYPE.REVERSE: {\n      var coords = search.split(spacesOrCommaRgx).map(function(c) {\n        return parseFloat(c, 10);\n      })\n      if (!self.options.flipCoordinates) {\n        coords.reverse();\n      }\n\n      // client only accepts one type for reverseGeocode, so\n      // use first config type if one, if not default to poi\n      config.types ? [config.types[0]] : [\"poi\"];\n      config = extend(config, { query: coords, limit: 1 });\n\n      // Remove config options not supported by the reverseGeocoder\n      ['proximity', 'autocomplete', 'fuzzyMatch', 'bbox'].forEach(function(key) {\n        if (key in config) {\n          delete config[key]\n        }\n      });\n    } break;\n    case GEOCODE_REQUEST_TYPE.FORWARD: {\n      // Ensure that any reverse geocoding looking request is cleaned up\n      // to be processed as only a forward geocoding request by the server.\n      const trimmedSearch = search.trim();\n      const reverseGeocodeCoordRgx = /^(-?\\d{1,3}(\\.\\d{0,256})?)[, ]+(-?\\d{1,3}(\\.\\d{0,256})?)?$/;\n      if (reverseGeocodeCoordRgx.test(trimmedSearch)) {\n        search = search.replace(/,/g, ' ');\n      }\n      config = extend(config, { query: search });\n    } break;\n    }\n\n    config.session_token = this.eventManager.getSessionId();\n\n    return config;\n  },\n\n  _geocode: function(searchInput) {\n    this.inputString = searchInput;\n    this._showLoadingIcon();\n    this._eventEmitter.emit('loading', { query: searchInput });\n\n    const requestType = this._requestType(this.options, searchInput);\n    const config = this._setupConfig(requestType, searchInput);\n\n    var request;\n    switch (requestType) {\n    case GEOCODE_REQUEST_TYPE.LOCAL:\n      request = Promise.resolve();\n      break;\n    case GEOCODE_REQUEST_TYPE.FORWARD:\n      request = this.geocoderService.forwardGeocode(config).send();\n      break;\n    case GEOCODE_REQUEST_TYPE.REVERSE:\n      request = this.geocoderService.reverseGeocode(config).send();\n      break;\n    }\n\n    var localGeocoderRes = this.options.localGeocoder ? this.options.localGeocoder(searchInput) || [] : [];\n    var externalGeocoderRes = [];\n\n    var geocoderError = null;\n    request.catch(function(error) {\n      geocoderError = error;\n    }.bind(this))\n      .then(\n        function(response) {\n          this._hideLoadingIcon();\n          var res = {};\n\n          if (!response){\n            res = {\n              type: 'FeatureCollection',\n              features: []\n            }\n          } else if (response.statusCode == '200') {\n            res = response.body;\n            res.request = response.request;\n            res.headers = response.headers;\n            this._headers = response.headers;\n          }\n\n          res.config = config;\n\n          if (this.fresh){\n            this.eventManager.start(this);\n            this.fresh = false;\n          }\n\n          // Tag Mapbox as the source for Geocoding API results, to differentiate from local or external geocoder federated results\n          if (res.features && res.features.length) {\n            res.features.map(function (feature) {\n              feature._source = 'mapbox';\n            })\n          }\n\n          // supplement Mapbox Geocoding API results with locally populated results\n          res.features = res.features\n            ? localGeocoderRes.concat(res.features)\n            : localGeocoderRes;\n\n          if (this.options.externalGeocoder) {\n\n            externalGeocoderRes = this.options.externalGeocoder(searchInput, res.features) || Promise.resolve([]);\n            // supplement Mapbox Geocoding API results with features returned by a promise\n            return externalGeocoderRes.then(function(features) {\n              res.features = res.features ? features.concat(res.features) : features;\n              return res;\n            }, function(){\n              // on error, display the original result\n              return res;\n            });\n          }\n          return res;\n\n        }.bind(this)).then(\n        function(res) {\n          if (geocoderError) {\n            throw geocoderError;\n          }\n\n          // apply results filter if provided\n          if (this.options.filter && res.features.length) {\n            res.features = res.features.filter(this.options.filter);\n          }\n\n          if (res.features.length) {\n            this._showClearButton();\n            this._hideGeolocateButton();\n            this._showAttribution();\n            this._eventEmitter.emit('results', res);\n            this._typeahead.update(res.features);\n          } else {\n            this._hideClearButton();\n            this._hideAttribution();\n            this._typeahead.selected = null;\n            this._renderNoResults();\n            this._eventEmitter.emit('results', res);\n          }\n\n        }.bind(this)\n      ).catch(\n        function(err) {\n          this._hideLoadingIcon();\n          this._hideAttribution();\n\n          // in the event of an error in the Mapbox Geocoding API still display results from the localGeocoder\n          if ((localGeocoderRes.length && this.options.localGeocoder) || (externalGeocoderRes.length && this.options.externalGeocoder) ) {\n            this._showClearButton();\n            this._hideGeolocateButton();\n            this._typeahead.update(localGeocoderRes);\n          } else {\n            this._hideClearButton();\n            this._typeahead.selected = null;\n            this._renderError();\n          }\n\n          this._eventEmitter.emit('results', { features: localGeocoderRes });\n          this._eventEmitter.emit('error', { error: err });\n        }.bind(this)\n      );\n\n    return request;\n  },\n\n  /**\n   * Shared logic for clearing input\n   * @param {Event} [ev] the event that triggered the clear, if available\n   * @private\n   *\n   */\n  _clear: function(ev) {\n    if (ev) ev.preventDefault();\n    this._inputEl.value = '';\n    this._typeahead.selected = null;\n    this._typeahead.clear();\n    this.eventManager.sessionIncrementer++;\n    this._onChange();\n    this._hideClearButton();\n    this._showGeolocateButton();\n    this._removeMarker();\n    this.lastSelected = null;\n    this._eventEmitter.emit('clear');\n    this.fresh = true;\n  },\n\n  /**\n   * Clear and then focus the input.\n   * @param {Event} [ev] the event that triggered the clear, if available\n   *\n   */\n  clear: function(ev) {\n    this._clear(ev);\n    this._inputEl.focus();\n  },\n\n\n  /**\n   * Clear the input, without refocusing it. Used to implement clearOnBlur\n   * constructor option.\n   * @param {Event} [ev] the blur event\n   * @private\n   */\n  _clearOnBlur: function(ev) {\n    var ctx = this;\n\n    /*\n     * If relatedTarget is not found, assume user targeted the suggestions list.\n     * In that case, do not clear on blur. There are other edge cases where\n     * ev.relatedTarget could be null. Clicking on list always results in null\n     * relatedtarget because of upstream behavior in `suggestions`.\n     *\n     * The ideal solution would be to check if ev.relatedTarget is a child of\n     * the list. See issue #258 for details on why we can't do that yet.\n     */\n    if (ev.relatedTarget) {\n      ctx._clear(ev);\n    }\n  },\n\n  _onQueryResult: function(response) {\n    var results = response.body;\n    if (!results.features.length) return;\n    var result = results.features[0];\n    this._typeahead.selected = result;\n    this._inputEl.value = result.place_name;\n    this._onChange();\n  },\n\n  _updateProximity: function() {\n    // proximity is designed for local scale, if the user is looking at the whole world,\n    // it doesn't make sense to factor in the arbitrary centre of the map\n    if (!this._map || !this.options.trackProximity){\n      return;\n    }\n    if (this._map.getZoom() > 9) {\n      var center = this._map.getCenter().wrap();\n      this.setProximity({ longitude: center.lng, latitude: center.lat }, false);\n    } else {\n      this.setProximity(null, false);\n    }\n  },\n\n  _collapse: function() {\n    // do not collapse if input is in focus\n    if (!this._inputEl.value && this._inputEl !== document.activeElement) this.container.classList.add('mapboxgl-ctrl-geocoder--collapsed');\n  },\n\n  _unCollapse: function() {\n    this.container.classList.remove('mapboxgl-ctrl-geocoder--collapsed');\n  },\n\n  /**\n   * Set & query the input\n   * @param {string} searchInput location name or other search input\n   * @returns {MapboxGeocoder} this\n   */\n  query: function(searchInput) {\n    this._geocode(searchInput).then(this._onQueryResult);\n    return this;\n  },\n\n  _renderError: function(){\n    var errorMessage = \"<div class='mapbox-gl-geocoder--error'>There was an error reaching the server</div>\"\n    this._renderMessage(errorMessage);\n  },\n\n  _renderLocationError: function(){\n    var errorMessage = \"<div class='mapbox-gl-geocoder--error'>A location error has occurred</div>\"\n    this._renderMessage(errorMessage);\n  },\n\n  _renderNoResults: function(){\n    var errorMessage = \"<div class='mapbox-gl-geocoder--error mapbox-gl-geocoder--no-results'>No results found</div>\";\n    this._renderMessage(errorMessage);\n  },\n\n  _renderUserDeniedGeolocationError: function() {\n    var errorMessage = \"<div class='mapbox-gl-geocoder--error'>Geolocation permission denied</div>\"\n    this._renderMessage(errorMessage);\n  },\n\n  _renderMessage: function(msg){\n    this._typeahead.update([]);\n    this._typeahead.selected = null;\n    this._typeahead.clear();\n    this._typeahead.renderError(msg);\n  },\n\n  /**\n   * Get the text to use as the search bar placeholder\n   *\n   * If placeholder is provided in options, then use options.placeholder\n   * Otherwise, if language is provided in options, then use the localized string of the first language if available\n   * Otherwise use the default\n   *\n   * @returns {String} the value to use as the search bar placeholder\n   * @private\n   */\n  _getPlaceholderText: function(){\n    if (this.options.placeholder) return this.options.placeholder;\n    if (this.options.language){\n      var firstLanguage = this.options.language.split(\",\")[0];\n      var language = subtag.language(firstLanguage);\n      var localizedValue = localization.placeholder[language];\n      if (localizedValue)  return localizedValue;\n    }\n    return 'Search';\n  },\n\n  /**\n   * Set input\n   * @param {string} searchInput location name or other search input\n   * @param {boolean} [showSuggestions=false] display suggestion on setInput call\n   * @returns {MapboxGeocoder} this\n   */\n  setInput: function(searchInput, showSuggestions) {\n    if (showSuggestions === undefined) {\n      showSuggestions = false\n    }\n    // Set input value to passed value and clear everything else.\n    this._inputEl.value = searchInput;\n    this._typeahead.selected = null;\n    this._typeahead.clear();\n    if (searchInput.length >= this.options.minLength) {\n      showSuggestions ? this._geocode(searchInput) : this._onChange();\n    }\n    return this;\n  },\n\n  /**\n   * Set proximity\n   * @param {Object|'ip'} proximity The new `options.proximity` value. This is a geographical point given as an object with `latitude` and `longitude` properties or the string 'ip'.\n   * @param {Boolean} disableTrackProximity If true, sets `trackProximity` to false. True by default to prevent `trackProximity` from unintentionally overriding an explicitly set proximity value.\n   * @returns {MapboxGeocoder} this\n   */\n  setProximity: function(proximity, disableTrackProximity = true) {\n    this.options.proximity = proximity;\n    if (disableTrackProximity) {\n      this.options.trackProximity = false;\n    }\n    return this;\n  },\n\n  /**\n   * Get proximity\n   * @returns {Object} The geocoder proximity\n   */\n  getProximity: function() {\n    return this.options.proximity;\n  },\n\n  /**\n   * Set the render function used in the results dropdown\n   * @param {Function} fn The function to use as a render function. This function accepts a single [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) object as input and returns a string.\n   * @returns {MapboxGeocoder} this\n   */\n  setRenderFunction: function(fn){\n    if (fn && typeof(fn) == \"function\"){\n      this._typeahead.render = fn;\n    }\n    return this;\n  },\n\n  /**\n   * Get the function used to render the results dropdown\n   *\n   * @returns {Function} the render function\n   */\n  getRenderFunction: function(){\n    return this._typeahead.render;\n  },\n\n  /**\n   * Get the language to use in UI elements and when making search requests\n   *\n   * Look first at the explicitly set options otherwise use the browser's language settings\n   * @param {String} language Specify the language to use for response text and query result weighting. Options are IETF language tags comprised of a mandatory ISO 639-1 language code and optionally one or more IETF subtags for country or script. More than one value can also be specified, separated by commas.\n   * @returns {MapboxGeocoder} this\n   */\n  setLanguage: function(language){\n    var browserLocale = navigator.language || navigator.userLanguage || navigator.browserLanguage;\n    this.options.language = language || this.options.language || browserLocale;\n    return this;\n  },\n\n  /**\n   * Get the language to use in UI elements and when making search requests\n   * @returns {String} The language(s) used by the plugin, if any\n   */\n  getLanguage: function(){\n    return this.options.language;\n  },\n\n  /**\n   * Get the zoom level the map will move to when there is no bounding box on the selected result\n   * @returns {Number} the map zoom\n   */\n  getZoom: function(){\n    return this.options.zoom;\n  },\n\n  /**\n   * Set the zoom level\n   * @param {Number} zoom The zoom level that the map should animate to when a `bbox` isn't found in the response. If a `bbox` is found the map will fit to the `bbox`.\n   * @returns {MapboxGeocoder} this\n   */\n  setZoom: function(zoom){\n    this.options.zoom = zoom;\n    return this;\n  },\n\n  /**\n   * Get the parameters used to fly to the selected response, if any\n   * @returns {Boolean|Object} The `flyTo` option\n   */\n  getFlyTo: function(){\n    return this.options.flyTo;\n  },\n\n  /**\n   * Set the flyTo options\n   * @param {Boolean|Object} flyTo If false, animating the map to a selected result is disabled. If true, animating the map will use the default animation parameters. If an object, it will be passed as `options` to the map [`flyTo`](https://docs.mapbox.com/mapbox-gl-js/api/#map#flyto) or [`fitBounds`](https://docs.mapbox.com/mapbox-gl-js/api/#map#fitbounds) method providing control over the animation of the transition.\n   */\n  setFlyTo: function(flyTo){\n    this.options.flyTo = flyTo;\n    return this;\n  },\n\n  /**\n   * Get the value of the placeholder string\n   * @returns {String} The input element's placeholder value\n   */\n  getPlaceholder: function(){\n    return this.options.placeholder;\n  },\n\n  /**\n   * Set the value of the input element's placeholder\n   * @param {String} placeholder the text to use as the input element's placeholder\n   * @returns {MapboxGeocoder} this\n   */\n  setPlaceholder: function(placeholder){\n    this.options.placeholder = (placeholder) ? placeholder : this._getPlaceholderText();\n    this._inputEl.placeholder = this.options.placeholder;\n    this._inputEl.setAttribute('aria-label', this.options.placeholder);\n    return this\n  },\n\n  /**\n   * Get the bounding box used by the plugin\n   * @returns {Array<Number>} the bounding box, if any\n   */\n  getBbox: function(){\n    return this.options.bbox;\n  },\n\n  /**\n   * Set the bounding box to limit search results to\n   * @param {Array<Number>} bbox a bounding box given as an array in the format [minX, minY, maxX, maxY].\n   * @returns {MapboxGeocoder} this\n   */\n  setBbox: function(bbox){\n    this.options.bbox = bbox;\n    return this;\n  },\n\n  /**\n   * Get a list of the countries to limit search results to\n   * @returns {String} a comma separated list of countries to limit to, if any\n   */\n  getCountries: function(){\n    return this.options.countries;\n  },\n\n  /**\n   * Set the countries to limit search results to\n   * @param {String} countries a comma separated list of countries to limit to\n   * @returns {MapboxGeocoder} this\n   */\n  setCountries: function(countries){\n    this.options.countries = countries;\n    return this;\n  },\n\n  /**\n   * Get a list of the types to limit search results to\n   * @returns {String} a comma separated list of types to limit to\n   */\n  getTypes: function(){\n    return this.options.types;\n  },\n\n  /**\n   * Set the types to limit search results to\n   * @param {String} countries a comma separated list of types to limit to\n   * @returns {MapboxGeocoder} this\n   */\n  setTypes: function(types){\n    this.options.types = types;\n    return this;\n  },\n\n  /**\n   * Get the minimum number of characters typed to trigger results used in the plugin\n   * @returns {Number} The minimum length in characters before a search is triggered\n   */\n  getMinLength: function(){\n    return this.options.minLength;\n  },\n\n  /**\n   * Set the minimum number of characters typed to trigger results used by the plugin\n   * @param {Number} minLength the minimum length in characters\n   * @returns {MapboxGeocoder} this\n   */\n  setMinLength: function(minLength){\n    this.options.minLength = minLength;\n    if (this._typeahead)  this._typeahead.options.minLength = minLength;\n    return this;\n  },\n\n  /**\n   * Get the limit value for the number of results to display used by the plugin\n   * @returns {Number} The limit value for the number of results to display used by the plugin\n   */\n  getLimit: function(){\n    return this.options.limit;\n  },\n\n  /**\n   * Set the limit value for the number of results to display used by the plugin\n   * @param {Number} limit the number of search results to return\n   * @returns {MapboxGeocoder}\n   */\n  setLimit: function(limit){\n    this.options.limit = limit;\n    if (this._typeahead) this._typeahead.options.limit = limit;\n    return this;\n  },\n\n  /**\n   * Get the filter function used by the plugin\n   * @returns {Function} the filter function\n   */\n  getFilter: function(){\n    return this.options.filter;\n  },\n\n  /**\n   * Set the filter function used by the plugin.\n   * @param {Function} filter A function which accepts a Feature in the [extended GeoJSON](https://docs.mapbox.com/api/search/geocoding-v5/#geocoding-response-object) format to filter out results from the Geocoding API response before they are included in the suggestions list. Return `true` to keep the item, `false` otherwise.\n   * @returns {MapboxGeocoder} this\n   */\n  setFilter: function(filter){\n    this.options.filter = filter;\n    return this;\n  },\n\n  /**\n   * Set the geocoding endpoint used by the plugin.\n   * @param {Function} origin A function which accepts an HTTPS URL to specify the endpoint to query results from.\n   * @returns {MapboxGeocoder} this\n   */\n  setOrigin: function(origin){\n    this.options.origin = origin;\n    this.geocoderService = mbxGeocoder(\n      MapboxClient({\n        accessToken: this.options.accessToken,\n        origin: this.options.origin\n      })\n    );\n    return this;\n  },\n\n  /**\n   * Get the geocoding endpoint the plugin is currently set to\n   * @returns {Function} the endpoint URL\n   */\n  getOrigin: function(){\n    return this.options.origin;\n  },\n\n  /**\n   * Set the accessToken option used for the geocoding request endpoint.\n   * @param {String} accessToken value\n   * @returns {MapboxGeocoder} this\n   */\n  setAccessToken: function(accessToken){\n    this.options.accessToken = accessToken;\n    this.geocoderService = mbxGeocoder(\n      MapboxClient({\n        accessToken: this.options.accessToken,\n        origin: this.options.origin\n      })\n    );\n    return this;\n  },\n\n  /**\n   * Set the autocomplete option used for geocoding requests\n   * @param {Boolean} value The boolean value to set autocomplete to\n   * @returns\n   */\n  setAutocomplete: function(value){\n    this.options.autocomplete = value;\n    return this;\n  },\n\n  /**\n   * Get the current autocomplete parameter value used for requests\n   * @returns {Boolean} The autocomplete parameter value\n   */\n  getAutocomplete: function(){\n    return this.options.autocomplete\n  },\n\n  /**\n   * Set the fuzzyMatch option used for approximate matching in geocoding requests\n   * @param {Boolean} value The boolean value to set fuzzyMatch to\n   * @returns\n   */\n  setFuzzyMatch: function(value){\n    this.options.fuzzyMatch = value;\n    return this;\n  },\n\n  /**\n   * Get the current fuzzyMatch parameter value used for requests\n   * @returns {Boolean} The fuzzyMatch parameter value\n   */\n  getFuzzyMatch: function(){\n    return this.options.fuzzyMatch\n  },\n\n  /**\n   * Set the routing parameter used to ask for routable point metadata in geocoding requests\n   * @param {Boolean} value The boolean value to set routing to\n   * @returns\n   */\n  setRouting: function(value){\n    this.options.routing = value;\n    return this;\n  },\n\n  /**\n   * Get the current routing parameter value used for requests\n   * @returns {Boolean} The routing parameter value\n   */\n  getRouting: function(){\n    return this.options.routing\n  },\n\n  /**\n   * Set the worldview parameter\n   * @param {String} code The country code representing the worldview (e.g. \"us\" | \"cn\" | \"jp\", \"in\")\n   * @returns\n   */\n  setWorldview: function(code){\n    this.options.worldview = code;\n    return this;\n  },\n\n  /**\n   * Get the current worldview parameter value used for requests\n   * @returns {String} The worldview parameter value\n   */\n  getWorldview: function(){\n    return this.options.worldview\n  },\n\n  /**\n   * Handle the placement of a result marking the selected result\n   * @private\n   * @param {Object} selected the selected geojson feature\n   * @returns {MapboxGeocoder} this\n   */\n  _handleMarker: function(selected){\n    // clean up any old marker that might be present\n    if (!this._map){\n      return;\n    }\n    this._removeMarker();\n    var defaultMarkerOptions = {\n      color: '#4668F2'\n    }\n    var markerOptions = extend({}, defaultMarkerOptions, this.options.marker)\n    this.mapMarker = new this._mapboxgl.Marker(markerOptions);\n    if (selected.center) {\n      this.mapMarker\n        .setLngLat(selected.center)\n        .addTo(this._map);\n    } else if (selected.geometry && selected.geometry.type && selected.geometry.type === 'Point' && selected.geometry.coordinates) {\n      this.mapMarker\n        .setLngLat(selected.geometry.coordinates)\n        .addTo(this._map);\n    }\n    return this;\n  },\n\n  /**\n   * Handle the removal of a result marker\n   * @private\n   */\n  _removeMarker: function(){\n    if (this.mapMarker){\n      this.mapMarker.remove();\n      this.mapMarker = null;\n    }\n  },\n\n  /**\n   * Subscribe to events that happen within the plugin.\n   * @param {String} type name of event. Available events and the data passed into their respective event objects are:\n   *\n   * - __clear__ `Emitted when the input is cleared`\n   * - __loading__ `{ query } Emitted when the geocoder is looking up a query`\n   * - __results__ `{ results } Fired when the geocoder returns a response`\n   * - __result__ `{ result } Fired when input is set`\n   * - __error__ `{ error } Error as string`\n   * @param {Function} fn function that's called when the event is emitted.\n   * @returns {MapboxGeocoder} this;\n   */\n  on: function(type, fn) {\n    this._eventEmitter.on(type, fn);\n    return this;\n  },\n\n  /**\n   * Remove an event\n   * @returns {MapboxGeocoder} this\n   * @param {String} type Event name.\n   * @param {Function} fn Function that should unsubscribe to the event emitted.\n   */\n  off: function(type, fn) {\n    this._eventEmitter.removeListener(type, fn);\n    this.eventManager.remove();\n    return this;\n  }\n};\n\nmodule.exports = MapboxGeocoder;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAEjB,QAAI,iBAAiB,OAAO,UAAU;AAEtC,aAAS,SAAS;AACd,UAAI,SAAS,CAAC;AAEd,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAI,SAAS,UAAU,CAAC;AAExB,iBAAS,OAAO,QAAQ;AACpB,cAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AAClC,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;AClBA;AAAA;AAQA,KAAC,WAAW;AAEZ,UAAI,OAAO;AAEX,UAAI,QAAQ,CAAC;AAGb,UAAI,OAAO,YAAY,aAAa;AAClC,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAIA,YAAM,eAAe,SAAS,SAAS,OAAO;AAC5C,eAAO,MAAM,OAAO,SAAS,KAAK;AAChC,iBAAO,MAAM,KAAK,SAAS,GAAG;AAAA,QAChC,CAAC;AAAA,MACH;AAGA,YAAM,OAAO,SAAS,SAAS,KAAK;AAClC,eAAO,MAAM,MAAM,SAAS,GAAG,MAAM;AAAA,MACvC;AAIA,YAAM,QAAQ,SAAS,SAAS,KAAK,MAAM;AACzC,eAAO,QAAQ,CAAC;AAChB,YAAI,aAAa,GACb,SAAS,CAAC,GACV,MAAM,IAAI,QACV,aAAa,GACb,YAAY,GAEZ,MAAM,KAAK,OAAO,IAElB,OAAO,KAAK,QAAQ,IAGpB,gBAAiB,KAAK,iBAAiB,OAAO,IAAI,YAAY,GAC9D;AAEJ,kBAAU,KAAK,iBAAiB,WAAW,QAAQ,YAAY;AAI/D,iBAAQ,MAAM,GAAG,MAAM,KAAK,OAAO;AACjC,eAAK,IAAI,GAAG;AACZ,cAAG,cAAc,GAAG,MAAM,QAAQ,UAAU,GAAG;AAC7C,iBAAK,MAAM,KAAK;AAChB,0BAAc;AAGd,yBAAa,IAAI;AAAA,UACnB,OAAO;AACL,wBAAY;AAAA,UACd;AACA,wBAAc;AACd,iBAAO,OAAO,MAAM,IAAI;AAAA,QAC1B;AAGA,YAAG,eAAe,QAAQ,QAAQ;AAEhC,uBAAc,kBAAkB,UAAW,WAAW;AACtD,iBAAO,EAAC,UAAU,OAAO,KAAK,EAAE,GAAG,OAAO,WAAU;AAAA,QACtD;AAEA,eAAO;AAAA,MACT;AA0BA,YAAM,SAAS,SAAS,SAAS,KAAK,MAAM;AAC1C,YAAG,CAAC,OAAO,IAAI,WAAW,GAAG;AAC3B,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,OAAO,YAAY,UAAU;AAC/B,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,CAAC;AAChB,eAAO,IACJ,OAAO,SAAS,MAAM,SAAS,KAAKA,MAAK;AACxC,cAAI,MAAM;AACV,cAAG,KAAK,SAAS;AACf,kBAAM,KAAK,QAAQ,OAAO;AAAA,UAC5B;AACA,cAAI,WAAW,MAAM,MAAM,SAAS,KAAK,IAAI;AAC7C,cAAG,YAAY,MAAM;AACnB,iBAAK,KAAK,MAAM,IAAI;AAAA,cAChB,QAAQ,SAAS;AAAA,cACjB,OAAO,SAAS;AAAA,cAChB,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACF;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC,EAKJ,KAAK,SAAS,GAAE,GAAG;AAClB,cAAI,UAAU,EAAE,QAAQ,EAAE;AAC1B,cAAG,QAAS,QAAO;AACnB,iBAAO,EAAE,QAAQ,EAAE;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IAGA,GAAE;AAAA;AAAA;;;AC9IF;AAAA;AAAA;AAEA,QAAI,OAAO,SAAS,WAAW;AAC7B,WAAK,YAAY;AACjB,WAAK,QAAQ,CAAC;AACd,WAAK,SAAS;AACd,WAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,WAAK,QAAQ,YAAY;AACzB,WAAK,UAAU,SAAS,cAAc,IAAI;AAC1C,WAAK,QAAQ,YAAY;AACzB,WAAK,QAAQ,YAAY,KAAK,OAAO;AAKrC,WAAK,oBAAoB;AAEzB,gBAAU,GAAG,WAAW,aAAa,KAAK,SAAS,UAAU,GAAG,WAAW;AAC3E,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,OAAO,WAAW;AAC/B,WAAK,QAAQ,MAAM,UAAU;AAAA,IAC/B;AAEA,SAAK,UAAU,OAAO,WAAW;AAC/B,WAAK,QAAQ,MAAM,UAAU;AAAA,IAC/B;AAEA,SAAK,UAAU,MAAM,SAAS,MAAM;AAClC,WAAK,MAAM,KAAK,IAAI;AAAA,IACtB;AAEA,SAAK,UAAU,QAAQ,WAAW;AAChC,WAAK,QAAQ,CAAC;AACd,WAAK,SAAS;AAAA,IAChB;AAEA,SAAK,UAAU,UAAU,WAAW;AAClC,aAAO,CAAC,KAAK,MAAM;AAAA,IACrB;AAEA,SAAK,UAAU,YAAY,WAAW;AACpC,aAAO,KAAK,QAAQ,MAAM,YAAY;AAAA,IACxC;AAEA,SAAK,UAAU,OAAO,WAAW;AAC/B,WAAK,QAAQ,YAAY;AAEzB,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,aAAK,KAAK;AACV;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,aAAK,SAAS,KAAK,MAAM,CAAC,GAAG,KAAK,WAAW,CAAC;AAAA,MAChD;AAEA,WAAK,KAAK;AAAA,IACZ;AAEA,SAAK,UAAU,WAAW,SAAS,MAAM,QAAQ;AAC/C,UAAI,KAAK,SAAS,cAAc,IAAI,GAClC,IAAI,SAAS,cAAc,GAAG;AAEhC,UAAI,OAAQ,IAAG,aAAa;AAE5B,QAAE,YAAY,KAAK;AAEnB,SAAG,YAAY,CAAC;AAChB,WAAK,QAAQ,YAAY,EAAE;AAE3B,SAAG,iBAAiB,aAAa,WAAW;AAC1C,aAAK,oBAAoB;AAAA,MAC3B,EAAE,KAAK,IAAI,CAAC;AAEZ,SAAG,iBAAiB,WAAW,WAAW;AACxC,aAAK,cAAc,KAAK,MAAM,IAAI;AAAA,MACpC,EAAE,KAAK,IAAI,CAAC;AAAA,IACd;AAEA,SAAK,UAAU,gBAAgB,SAAS,MAAM;AAC5C,WAAK,oBAAoB;AACzB,WAAK,UAAU,MAAM,KAAK,QAAQ;AAClC,WAAK,MAAM;AACX,WAAK,KAAK;AAAA,IACZ;AAEA,SAAK,UAAU,OAAO,SAAS,OAAO;AACpC,WAAK,SAAS;AACd,WAAK,KAAK;AAAA,IACZ;AAEA,SAAK,UAAU,WAAW,WAAW;AACnC,WAAK,KAAK,KAAK,WAAW,IAAI,KAAK,MAAM,SAAS,IAAI,KAAK,SAAS,CAAC;AAAA,IACvE;AAEA,SAAK,UAAU,OAAO,WAAW;AAC/B,WAAK,KAAK,KAAK,WAAW,KAAK,MAAM,SAAS,IAAI,IAAI,KAAK,SAAS,CAAC;AAAA,IACvE;AAEA,SAAK,UAAU,YAAY,SAAS,KAAI;AACtC,UAAI,KAAK,SAAS,cAAc,IAAI;AAEpC,SAAG,YAAY;AAEf,WAAK,QAAQ,YAAY,EAAE;AAC3B,WAAK,KAAK;AAAA,IACZ;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9GjB;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,OAAO;AAEX,QAAI,cAAc,SAAS,IAAI,MAAM,SAAS;AAC5C,gBAAU,WAAW,CAAC;AAEtB,WAAK,UAAU,OAAO;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,GAAG,OAAO;AAEV,WAAK,KAAK;AACV,WAAK,OAAO,QAAQ,CAAC;AACrB,WAAK,OAAO,IAAI,KAAK,IAAI;AAEzB,WAAK,QAAQ;AACb,WAAK,WAAW;AAEhB,WAAK,KAAK,KAAK;AAEf,WAAK,GAAG,iBAAiB,SAAS,SAAS,GAAG;AAC5C,aAAK,YAAY,EAAE,OAAO;AAAA,MAC5B,EAAE,KAAK,IAAI,GAAG,KAAK;AAEnB,WAAK,GAAG,iBAAiB,WAAW,SAAS,GAAG;AAC9C,aAAK,cAAc,CAAC;AAAA,MACtB,EAAE,KAAK,IAAI,CAAC;AAEZ,WAAK,GAAG,iBAAiB,SAAS,WAAW;AAC3C,aAAK,YAAY;AAAA,MACnB,EAAE,KAAK,IAAI,CAAC;AAEZ,WAAK,GAAG,iBAAiB,QAAQ,WAAW;AAC1C,aAAK,WAAW;AAAA,MAClB,EAAE,KAAK,IAAI,CAAC;AAEZ,WAAK,GAAG,iBAAiB,SAAS,SAAS,GAAG;AAC5C,aAAK,YAAY,CAAC;AAAA,MACpB,EAAE,KAAK,IAAI,CAAC;AAGZ,WAAK,SAAU,KAAK,QAAQ,SAAU,KAAK,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK,OAAO,KAAK,IAAI;AAE5F,WAAK,eAAgB,KAAK,QAAQ,eAAgB,KAAK,QAAQ,aAAa,KAAK,IAAI,IAAI,KAAK,aAAa,KAAK,IAAI;AAEpH,aAAO;AAAA,IACT;AAEA,gBAAY,UAAU,cAAc,SAAS,SAAS;AAOpD,UAAI,YAAY,MACZ,YAAY,MACZ,YAAY,MACZ,YAAY,MACZ,YAAY,EAAG;AAEnB,WAAK,kBAAkB,KAAK,GAAG,KAAK;AAAA,IACtC;AAEA,gBAAY,UAAU,gBAAgB,SAAS,GAAG;AAChD,cAAQ,EAAE,SAAS;AAAA,QACjB,KAAK;AAAA;AAAA,QACL,KAAK;AACH,cAAI,CAAC,KAAK,KAAK,QAAQ,GAAG;AACxB,gBAAI,KAAK,KAAK,UAAU,GAAG;AACzB,gBAAE,eAAe;AAAA,YACnB;AACA,iBAAK,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE,QAAQ;AACrD,iBAAK,KAAK,KAAK;AAAA,UACjB;AACF;AAAA,QACA,KAAK;AACH,cAAI,CAAC,KAAK,KAAK,QAAQ,EAAG,MAAK,KAAK,KAAK;AAC3C;AAAA,QACA,KAAK;AACH,eAAK,KAAK,SAAS;AACrB;AAAA,QACA,KAAK;AACH,eAAK,KAAK,KAAK;AACjB;AAAA,MACF;AAAA,IACF;AAEA,gBAAY,UAAU,aAAa,WAAW;AAC5C,UAAI,CAAC,KAAK,KAAK,qBAAqB,KAAK,QAAQ,YAAY;AAC3D,aAAK,KAAK,KAAK;AAAA,MACjB;AAAA,IACF;AAEA,gBAAY,UAAU,cAAc,SAAS,GAAG;AAC9C,UAAI,EAAE,eAAe;AACnB,aAAK,kBAAkB,EAAE,cAAc,QAAQ,MAAM,CAAC;AAAA,MACxD,OAAO;AACL,YAAIC,QAAO;AACX,mBAAW,WAAY;AACrB,UAAAA,MAAK,kBAAkB,EAAE,OAAO,KAAK;AAAA,QACvC,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAEA,gBAAY,UAAU,oBAAoB,SAAS,OAAO;AACxD,WAAK,QAAQ,KAAK,UAAU,KAAK;AAEjC,WAAK,KAAK,MAAM;AAEhB,UAAI,KAAK,MAAM,SAAS,KAAK,QAAQ,WAAW;AAC9C,aAAK,KAAK,KAAK;AACf;AAAA,MACF;AAEA,WAAK,cAAc,SAAS,MAAM;AAChC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,KAAK,IAAI,KAAK,CAAC,CAAC;AACrB,cAAI,MAAO,KAAK,QAAQ,QAAQ,EAAI;AAAA,QACtC;AACA,aAAK,KAAK,KAAK;AAAA,MACjB,EAAE,KAAK,IAAI,CAAC;AAAA,IACd;AAEA,gBAAY,UAAU,cAAc,WAAW;AAC7C,UAAI,CAAC,KAAK,KAAK,QAAQ,EAAG,MAAK,KAAK,KAAK;AACzC,WAAK,KAAK,oBAAoB;AAAA,IAChC;AAOA,gBAAY,UAAU,SAAS,SAAS,aAAa;AACnD,WAAK,OAAO;AACZ,WAAK,YAAY;AAAA,IACnB;AAKA,gBAAY,UAAU,QAAQ,WAAW;AACvC,WAAK,OAAO,CAAC;AACb,WAAK,KAAK,MAAM;AAAA,IAClB;AAQA,gBAAY,UAAU,YAAY,SAAS,OAAO;AAChD,cAAQ,MAAM,YAAY;AAC1B,aAAO;AAAA,IACT;AASA,gBAAY,UAAU,QAAQ,SAAS,WAAW,OAAO;AACvD,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AAEA,gBAAY,UAAU,QAAQ,SAAS,OAAO;AAC5C,WAAK,WAAW;AAChB,WAAK,GAAG,QAAQ,KAAK,aAAa,KAAK;AAEvC,UAAI,SAAS,aAAa;AACxB,YAAI,IAAI,SAAS,YAAY,YAAY;AACzC,UAAE,UAAU,UAAU,MAAM,KAAK;AACjC,aAAK,GAAG,cAAc,CAAC;AAAA,MACzB,OAAO;AACL,aAAK,GAAG,UAAU,UAAU;AAAA,MAC9B;AAAA,IACF;AAEA,gBAAY,UAAU,gBAAgB,SAAS,UAAU;AACvD,UAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,SAAS,GAAG;AAAE,iBAAO,KAAK,aAAa,CAAC;AAAA,QAAG,EAAE,KAAK,IAAI;AAAA,MACjE;AACA,UAAI;AACJ,UAAG,KAAK,QAAQ,QAAO;AACrB,kBAAU,MAAM,OAAO,KAAK,OAAO,KAAK,MAAM,OAAO;AAErD,kBAAU,QAAQ,IAAI,SAAS,MAAK;AAClC,iBAAO;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK,OAAO,KAAK,UAAU,KAAK,MAAM;AAAA,UAChD;AAAA,QACF,EAAE,KAAK,IAAI,CAAC;AAAA,MACd,OAAK;AACH,kBAAU,KAAK,KAAK,IAAI,SAAS,GAAG;AAClC,cAAI,iBAAiB,KAAK,OAAO,CAAC;AAClC,iBAAO;AAAA,YACL,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,QACF,EAAE,KAAK,IAAI,CAAC;AAAA,MACd;AACA,eAAS,OAAO;AAAA,IAClB;AAQA,gBAAY,UAAU,eAAe,SAAS,MAAM;AAClD,aAAO;AAAA,IACT;AAQA,gBAAY,UAAU,SAAS,SAAS,MAAM,kBAAkB;AAC9D,UAAI,kBAAiB;AAEnB,eAAO;AAAA,MACT;AACA,UAAI,aAAc,KAAK,WAAY,KAAK,aAAa,KAAK,QAAQ,IAAI,KAAK,aAAa,IAAI;AAC5F,UAAI,cAAc,KAAK,UAAU,UAAU;AAC3C,UAAI,eAAe,YAAY,YAAY,KAAK,KAAK;AACrD,aAAO,eAAe,IAAI;AACxB,YAAI,kBAAkB,eAAe,KAAK,MAAM;AAChD,qBAAa,WAAW,MAAM,GAAG,YAAY,IAAI,aAAa,WAAW,MAAM,cAAc,eAAe,IAAI,cAAc,WAAW,MAAM,eAAe;AAC9J,uBAAe,YAAY,MAAM,GAAG,YAAY,EAAE,YAAY,KAAK,KAAK;AAAA,MAC1E;AACA,aAAO;AAAA,IACT;AAMA,gBAAY,UAAU,cAAc,SAAS,KAAI;AAC/C,WAAK,KAAK,UAAU,GAAG;AAAA,IACzB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/PjB,IAAAC,uBAAA;AAAA;AAAA;AAyDA,QAAI,cAAc;AAClB,WAAO,UAAU;AAEjB,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,cAAc;AAAA,IACvB;AAAA;AAAA;;;AC9DA;AAAA;AAUA,QAAI,kBAAkB;AAGtB,QAAI,MAAM,IAAI;AAGd,QAAI,YAAY;AAGhB,QAAI,SAAS;AAGb,QAAI,aAAa;AAGjB,QAAI,aAAa;AAGjB,QAAI,YAAY;AAGhB,QAAI,eAAe;AAGnB,QAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAGpF,QAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,QAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAG7D,QAAI,cAAc,OAAO;AAOzB,QAAI,iBAAiB,YAAY;AAGjC,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAkBrB,QAAI,MAAM,WAAW;AACnB,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAwDA,aAAS,SAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,aAAO,SAAS,IAAI,KAAK;AACzB,UAAI,SAAS,OAAO,GAAG;AACrB,kBAAU,CAAC,CAAC,QAAQ;AACpB,iBAAS,aAAa;AACtB,kBAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AAEA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,UACP,UAAU;AAEd,mBAAW,WAAW;AACtB,yBAAiB;AACjB,iBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM;AAEzB,yBAAiB;AAEjB,kBAAU,WAAW,cAAc,IAAI;AAEvC,eAAO,UAAU,WAAW,IAAI,IAAI;AAAA,MACtC;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7BC,UAAS,OAAO;AAEpB,eAAO,SAAS,UAAUA,SAAQ,UAAU,mBAAmB,IAAIA;AAAA,MACrE;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,eAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,MACjE;AAEA,eAAS,eAAe;AACtB,YAAI,OAAO,IAAI;AACf,YAAI,aAAa,IAAI,GAAG;AACtB,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAEA,kBAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,MACxD;AAEA,eAAS,aAAa,MAAM;AAC1B,kBAAU;AAIV,YAAI,YAAY,UAAU;AACxB,iBAAO,WAAW,IAAI;AAAA,QACxB;AACA,mBAAW,WAAW;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,QAAW;AACzB,uBAAa,OAAO;AAAA,QACtB;AACA,yBAAiB;AACjB,mBAAW,eAAe,WAAW,UAAU;AAAA,MACjD;AAEA,eAAS,QAAQ;AACf,eAAO,YAAY,SAAY,SAAS,aAAa,IAAI,CAAC;AAAA,MAC5D;AAEA,eAAS,YAAY;AACnB,YAAI,OAAO,IAAI,GACX,aAAa,aAAa,IAAI;AAElC,mBAAW;AACX,mBAAW;AACX,uBAAe;AAEf,YAAI,YAAY;AACd,cAAI,YAAY,QAAW;AACzB,mBAAO,YAAY,YAAY;AAAA,UACjC;AACA,cAAI,QAAQ;AAEV,sBAAU,WAAW,cAAc,IAAI;AACvC,mBAAO,WAAW,YAAY;AAAA,UAChC;AAAA,QACF;AACA,YAAI,YAAY,QAAW;AACzB,oBAAU,WAAW,cAAc,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AA2BA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO;AAClB,aAAO,CAAC,CAAC,UAAU,QAAQ,YAAY,QAAQ;AAAA,IACjD;AA0BA,aAAS,aAAa,OAAO;AAC3B,aAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AAAA,IACpC;AAmBA,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,eAAe,KAAK,KAAK,KAAK;AAAA,IAC1D;AAyBA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,YAAI,QAAQ,OAAO,MAAM,WAAW,aAAa,MAAM,QAAQ,IAAI;AACnE,gBAAQ,SAAS,KAAK,IAAK,QAAQ,KAAM;AAAA,MAC3C;AACA,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,UAAU,IAAI,QAAQ,CAAC;AAAA,MAChC;AACA,cAAQ,MAAM,QAAQ,QAAQ,EAAE;AAChC,UAAI,WAAW,WAAW,KAAK,KAAK;AACpC,aAAQ,YAAY,UAAU,KAAK,KAAK,IACpC,aAAa,MAAM,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,IAC5C,WAAW,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,IACvC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxXjB;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASC,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ,KAAM,SAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,eAAe;AACtB,mBAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAU;AACjB,WAAO,QAAQ,OAAO;AAGtB,iBAAa,eAAe;AAE5B,iBAAa,UAAU,UAAU;AACjC,iBAAa,UAAU,eAAe;AACtC,iBAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAe,cAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,iBAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,iBAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAO,aAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,iBAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,iBAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,MAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,iBAAa,UAAU,KAAK,aAAa,UAAU;AAEnD,iBAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,iBAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,iBAAa,UAAU,MAAM,aAAa,UAAU;AAEpD,iBAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ,iBAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,iBAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,iBAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,iBAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,iBAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;;;AChfA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ,CAAC,CAAC,UAAU,SAAS,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,MACvD;AAAA,MACA,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ,CAAC,CAAC,aAAa,QAAQ,GAAG,CAAC,WAAW,SAAS,CAAC;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ,CAAC,CAAC,UAAU,SAAS,GAAG,CAAC,WAAW,OAAO,CAAC;AAAA,MACtD;AAAA,MACA,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ,CAAC,CAAC,YAAY,SAAS,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAKA,aAAS,WAAW,OAAO;AACzB,UAAI,QAAQ,MAAM,MAAM,2BAA2B;AACnD,UAAI,CAAC,MAAO,QAAO;AAEnB,aAAO;AAAA,QACL,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,aAAS,UAAU,MAAM;AACvB,UAAI,QAAQ,KAAK,MAAM,gBAAgB;AACvC,UAAI,CAAC,MAAO,QAAO;AAEnB,UAAI,UAAU,MAAM,CAAC;AACrB,UAAI,aAAa,MAAM,CAAC,EAAE,MAAM,GAAG;AACnC,UAAI,MAAM;AACV,UAAI,mBAAmB,WAAW,OAAO,SAAS,QAAQ,OAAO;AAC/D,YAAI,SAAS,WAAW,KAAK;AAC7B,YAAI,CAAC,OAAQ,QAAO;AACpB,YAAI,OAAO,QAAQ,OAAO;AACxB,cAAI,CAAC,KAAK;AACR,kBAAM,OAAO;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AACA,eAAO,OAAO,GAAG,IAAI,OAAO;AAC5B,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,UAAI,CAAC,IAAK,QAAO;AAEjB,aAAO;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAaA,aAAS,gBAAgB,YAAY;AACnC,UAAI,CAAC,WAAY,QAAO,CAAC;AAEzB,aAAO,WAAW,MAAM,OAAO,EAAE,OAAO,SAAS,QAAQ,MAAM;AAC7D,YAAI,SAAS,UAAU,IAAI;AAC3B,YAAI,CAAC,OAAQ,QAAO;AAEpB,YAAI,WAAW,OAAO,IAAI,MAAM,KAAK;AACrC,iBAAS,QAAQ,SAAS,KAAK;AAC7B,cAAI,CAAC,OAAO,GAAG,GAAG;AAChB,mBAAO,GAAG,IAAI;AAAA,cACZ,KAAK,OAAO;AAAA,cACZ,QAAQ,OAAO;AAAA,YACjB;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1EjB;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAsBtB,aAAS,aAAa,SAAS,cAAc;AAC3C,WAAK,UAAU;AACf,WAAK,UAAU,aAAa;AAC5B,WAAK,UAAU,aAAa;AAC5B,WAAK,aAAa,aAAa;AAC/B,UAAI;AACF,aAAK,OAAO,KAAK,MAAM,aAAa,QAAQ,IAAI;AAAA,MAClD,SAAS,YAAY;AACnB,aAAK,OAAO,aAAa;AAAA,MAC3B;AACA,WAAK,QAAQ,gBAAgB,KAAK,QAAQ,IAAI;AAAA,IAChD;AAOA,iBAAa,UAAU,cAAc,SAAS,cAAc;AAC1D,aAAO,CAAC,CAAC,KAAK,MAAM;AAAA,IACtB;AAQA,iBAAa,UAAU,WAAW,SAAS,WAAW;AACpD,UAAI,CAAC,KAAK,YAAY,EAAG,QAAO;AAChC,aAAO,KAAK,QAAQ,QAAQ;AAAA,QAC1B,MAAM,KAAK,MAAM,KAAK;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3DjB;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MACf,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,uBAAuB;AAAA,IACzB;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAI,YAAY;AA6BhB,aAAS,UAAU,SAAS;AAC1B,UAAI,YAAY,QAAQ,QAAQ,UAAU;AAE1C,UAAI;AACJ,UAAI,QAAQ,MAAM;AAChB,YAAI;AACF,iBAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,QAChC,SAAS,GAAG;AACV,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,QAAQ,WAAW;AACjC,UAAI,CAAC,SAAS;AACZ,YAAI,OAAO,SAAS,UAAU;AAC5B,oBAAU;AAAA,QACZ,WAAW,QAAQ,OAAO,KAAK,YAAY,UAAU;AACnD,oBAAU,KAAK;AAAA,QACjB,WAAW,cAAc,UAAU,uBAAuB;AACxD,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,aAAa,QAAQ,cAAc;AACxC,WAAK,UAAU,QAAQ;AACvB,WAAK,OAAO;AAAA,IACd;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/DjB;AAAA;AAAA;AAEA,aAAS,kBAAkB,KAAK;AAC9B,UAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,UAAI,OAAO,IACR,UAAU,GAAG,QAAQ,EACrB,KAAK,EACL,YAAY;AACf,UAAI,QAAQ,IAAI,UAAU,WAAW,CAAC,EAAE,KAAK;AAC7C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAYA,aAAS,aAAa,KAAK;AACzB,UAAI,UAAU,CAAC;AACf,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AAEA,UACG,KAAK,EACL,MAAM,UAAU,EAChB,QAAQ,SAAS,WAAW;AAC3B,YAAI,SAAS,kBAAkB,SAAS;AACxC,gBAAQ,OAAO,IAAI,IAAI,OAAO;AAAA,MAChC,CAAC;AAEH,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1CjB;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,eAAe;AAGnB,QAAI,mBAAmB,CAAC;AAExB,aAAS,aAAa,SAAS;AAC7B,UAAI,MAAM,iBAAiB,QAAQ,EAAE;AACrC,UAAI,CAAC,IAAK;AACV,UAAI,MAAM;AACV,aAAO,iBAAiB,QAAQ,EAAE;AAAA,IACpC;AAEA,aAAS,eAAe,SAAS,KAAK;AACpC,aAAO,IAAI,aAAa,SAAS;AAAA,QAC/B,MAAM,IAAI;AAAA,QACV,SAAS,aAAa,IAAI,sBAAsB,CAAC;AAAA,QACjD,YAAY,IAAI;AAAA,MAClB,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,OAAO;AAC5C,UAAI,QAAQ,MAAM;AAClB,UAAI,cAAc,MAAM;AACxB,UAAI,UAAW,MAAM,cAAe;AACpC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,aAAS,eAAe,SAAS,KAAK;AACpC,aAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,YAAI,aAAa,SAAS,OAAO;AAC/B,kBAAQ,QAAQ;AAAA,YACd,UAAU;AAAA,YACV,8BAA8B,KAAK;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,OAAO,QAAQ;AACnB,YAAI,MAAM;AACR,cAAI,OAAO,aAAa,SAAS,OAAO;AACtC,oBAAQ,QAAQ;AAAA,cACd,UAAU;AAAA,cACV,8BAA8B,KAAK;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,UAAU,SAAS,OAAO;AAC5B,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,UAAU,WAAW;AACvB,cAAI,YAAY,IAAI,UAAU;AAAA,YAC5B;AAAA,YACA,MAAM,UAAU;AAAA,UAClB,CAAC;AACD,iBAAO,SAAS;AAAA,QAClB;AAEA,YAAI,SAAS,WAAW;AACtB,iBAAO,iBAAiB,QAAQ,EAAE;AAClC,cAAI,IAAI,SAAS,OAAO,IAAI,UAAU,KAAK;AACzC,gBAAI,YAAY,IAAI,UAAU;AAAA,cAC5B;AAAA,cACA,MAAM,IAAI;AAAA,cACV,YAAY,IAAI;AAAA,YAClB,CAAC;AACD,mBAAO,SAAS;AAChB;AAAA,UACF;AACA,kBAAQ,GAAG;AAAA,QACb;AAEA,YAAI,OAAO,QAAQ;AAGnB,YAAI,OAAO,SAAS,UAAU;AAC5B,cAAI,KAAK,IAAI;AAAA,QACf,WAAW,MAAM;AACf,cAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,QAC/B,WAAW,MAAM;AACf,cAAI,KAAK,IAAI;AAAA,QACf,OAAO;AACL,cAAI,KAAK;AAAA,QACX;AAEA,yBAAiB,QAAQ,EAAE,IAAI;AAAA,MACjC,CAAC,EAAE,KAAK,SAASC,MAAK;AACpB,eAAO,eAAe,SAASA,IAAG;AAAA,MACpC,CAAC;AAAA,IACH;AAIA,aAAS,iBAAiB,SAAS,aAAa;AAC9C,UAAI,MAAM,QAAQ,IAAI,WAAW;AACjC,UAAI,MAAM,IAAI,OAAO,eAAe;AACpC,UAAI,KAAK,QAAQ,QAAQ,GAAG;AAC5B,aAAO,KAAK,QAAQ,OAAO,EAAE,QAAQ,SAAS,KAAK;AACjD,YAAI,iBAAiB,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,MAChD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,SAAS;AAC5B,aAAO,QAAQ,QAAQ,EAAE,KAAK,WAAW;AACvC,YAAI,MAAM,iBAAiB,SAAS,QAAQ,OAAO,WAAW;AAC9D,eAAO,eAAe,SAAS,GAAG;AAAA,MACpC,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC5HA;AAAA;AACC,KAAC,SAAS,MAAM;AAGhB,UAAI,cAAc,OAAO,WAAW,YAAY;AAGhD,UAAI,aAAa,OAAO,UAAU,YAAY,UAC7C,OAAO,WAAW,eAAe;AAIlC,UAAI,aAAa,OAAO,UAAU,YAAY;AAC9C,UAAI,WAAW,WAAW,cAAc,WAAW,WAAW,YAAY;AACzE,eAAO;AAAA,MACR;AAIA,UAAI,wBAAwB,SAAS,SAAS;AAC7C,aAAK,UAAU;AAAA,MAChB;AACA,4BAAsB,YAAY,IAAI;AACtC,4BAAsB,UAAU,OAAO;AAEvC,UAAI,QAAQ,SAAS,SAAS;AAG7B,cAAM,IAAI,sBAAsB,OAAO;AAAA,MACxC;AAEA,UAAI,QAAQ;AAEZ,UAAI,yBAAyB;AAM7B,UAAI,SAAS,SAAS,OAAO;AAC5B,gBAAQ,OAAO,KAAK,EAClB,QAAQ,wBAAwB,EAAE;AACpC,YAAI,SAAS,MAAM;AACnB,YAAI,SAAS,KAAK,GAAG;AACpB,kBAAQ,MAAM,QAAQ,QAAQ,EAAE;AAChC,mBAAS,MAAM;AAAA,QAChB;AACA,YACC,SAAS,KAAK;AAAA,QAEd,iBAAiB,KAAK,KAAK,GAC1B;AACD;AAAA,YACC;AAAA,UACD;AAAA,QACD;AACA,YAAI,aAAa;AACjB,YAAI;AACJ,YAAI;AACJ,YAAI,SAAS;AACb,YAAI,WAAW;AACf,eAAO,EAAE,WAAW,QAAQ;AAC3B,mBAAS,MAAM,QAAQ,MAAM,OAAO,QAAQ,CAAC;AAC7C,uBAAa,aAAa,IAAI,aAAa,KAAK,SAAS;AAEzD,cAAI,eAAe,GAAG;AAErB,sBAAU,OAAO;AAAA,cAChB,MAAO,eAAe,KAAK,aAAa;AAAA,YACzC;AAAA,UACD;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAIA,UAAI,SAAS,SAAS,OAAO;AAC5B,gBAAQ,OAAO,KAAK;AACpB,YAAI,aAAa,KAAK,KAAK,GAAG;AAG7B;AAAA,YACC;AAAA,UAED;AAAA,QACD;AACA,YAAI,UAAU,MAAM,SAAS;AAC7B,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,YAAI,SAAS,MAAM,SAAS;AAE5B,eAAO,EAAE,WAAW,QAAQ;AAE3B,cAAI,MAAM,WAAW,QAAQ,KAAK;AAClC,cAAI,MAAM,WAAW,EAAE,QAAQ,KAAK;AACpC,cAAI,MAAM,WAAW,EAAE,QAAQ;AAC/B,mBAAS,IAAI,IAAI;AAGjB,oBACC,MAAM,OAAO,UAAU,KAAK,EAAI,IAChC,MAAM,OAAO,UAAU,KAAK,EAAI,IAChC,MAAM,OAAO,UAAU,IAAI,EAAI,IAC/B,MAAM,OAAO,SAAS,EAAI;AAAA,QAE5B;AAEA,YAAI,WAAW,GAAG;AACjB,cAAI,MAAM,WAAW,QAAQ,KAAK;AAClC,cAAI,MAAM,WAAW,EAAE,QAAQ;AAC/B,mBAAS,IAAI;AACb,oBACC,MAAM,OAAO,UAAU,EAAE,IACzB,MAAM,OAAQ,UAAU,IAAK,EAAI,IACjC,MAAM,OAAQ,UAAU,IAAK,EAAI,IACjC;AAAA,QAEF,WAAW,WAAW,GAAG;AACxB,mBAAS,MAAM,WAAW,QAAQ;AAClC,oBACC,MAAM,OAAO,UAAU,CAAC,IACxB,MAAM,OAAQ,UAAU,IAAK,EAAI,IACjC;AAAA,QAEF;AAEA,eAAO;AAAA,MACR;AAEA,UAAI,SAAS;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAIA,UACC,OAAO,UAAU,cACjB,OAAO,OAAO,OAAO,YACrB,OAAO,KACN;AACD,eAAO,WAAW;AACjB,iBAAO;AAAA,QACR,CAAC;AAAA,MACF,WAAW,eAAe,CAAC,YAAY,UAAU;AAChD,YAAI,YAAY;AACf,qBAAW,UAAU;AAAA,QACtB,OAAO;AACN,mBAAS,OAAO,QAAQ;AACvB,mBAAO,eAAe,GAAG,MAAM,YAAY,GAAG,IAAI,OAAO,GAAG;AAAA,UAC7D;AAAA,QACD;AAAA,MACD,OAAO;AACN,aAAK,SAAS;AAAA,MACf;AAAA,IAED,GAAE,OAAI;AAAA;AAAA;;;ACpKN;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,QAAI,aAAa,CAAC;AAElB,aAAS,WAAW,OAAO;AACzB,UAAI,WAAW,KAAK,GAAG;AACrB,eAAO,WAAW,KAAK;AAAA,MACzB;AAEA,UAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,UAAI,QAAQ,MAAM,CAAC;AACnB,UAAI,aAAa,MAAM,CAAC;AACxB,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAEA,UAAI,gBAAgB,YAAY,UAAU;AAE1C,UAAI,SAAS;AAAA,QACX;AAAA,QACA,MAAM,cAAc;AAAA,MACtB;AACA,UAAI,IAAI,eAAe,GAAG,EAAG,QAAO,gBAAgB,cAAc;AAClE,UAAI,IAAI,eAAe,KAAK,EAAG,QAAO,UAAU,cAAc,MAAM;AACpE,UAAI,IAAI,eAAe,KAAK,EAAG,QAAO,UAAU,cAAc,MAAM;AACpE,UAAI,IAAI,eAAe,QAAQ,EAAG,QAAO,SAAS,cAAc;AAChE,UAAI,IAAI,eAAe,QAAQ,EAAG,QAAO,SAAS,cAAc;AAChE,UAAI,IAAI,eAAe,IAAI,EAAG,QAAO,YAAY,cAAc;AAC/D,UAAI,IAAI,eAAe,IAAI,EAAG,QAAO,eAAe,cAAc;AAElE,iBAAW,KAAK,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,YAAY;AAC/B,UAAI;AACF,eAAO,KAAK,MAAM,OAAO,OAAO,UAAU,CAAC;AAAA,MAC7C,SAAS,YAAY;AACnB,cAAM,IAAI,MAAM,eAAe;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,IAAI,KAAK,KAAK;AACrB,aAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AAAA,IACtD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChDjB;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE,UAAW,UAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG,EAAG,SAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE,GAAI,SAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA,UAChE,SAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB,EAAG,SAAQ,UAAU,IAAI,OAAO;AAAA,UAC1D,QAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAAS,eAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,iBAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB,EAAG,QAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI,EAAG,OAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,iBAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC,SAAU,QAAO,CAAC;AACvB,UAAI,SAAS,GAAI,QAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,iBAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC,UAAW,QAAO;AACvB,UAAI,UAAU,GAAI,QAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,iBAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU,KAAM,MAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE,KAAM,MAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC,KAAM,MAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,qBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,cAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,iBAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,iBAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,iBAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG,EAAG,QAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO,OAAQ,MAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA,YACpE,YAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,iBAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG,EAAG,YAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,iBAAa,UAAU,MAAM,aAAa,UAAU;AACpD,iBAAa,UAAU,cAAc,aAAa,UAAU;AAK5D,iBAAa,WAAW;AAKxB,iBAAa,eAAe;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;AC/UA;AAAA;AAAA;AAIA,aAAS,YAAY,YAAY;AAC/B,aAAO,WAAW,IAAI,kBAAkB,EAAE,KAAK,GAAG;AAAA,IACpD;AAEA,aAAS,YAAY,OAAO;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAO,YAAY,KAAK;AAAA,MAC1B;AACA,aAAO,mBAAmB,OAAO,KAAK,CAAC;AAAA,IACzC;AAYA,aAAS,iBAAiB,KAAK,KAAK,OAAO;AACzC,UAAI,UAAU,SAAS,UAAU,MAAM;AACrC,eAAO;AAAA,MACT;AACA,UAAI,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM;AACzC,UAAI,QAAQ,mBAAmB,GAAG;AAClC,UAAI,UAAU,UAAa,UAAU,MAAM,UAAU,MAAM;AACzD,iBAAS,MAAM,YAAY,KAAK;AAAA,MAClC;AACA,aAAO,KAAK,MAAM,cAAc;AAAA,IAClC;AAUA,aAAS,kBAAkB,KAAK,aAAa;AAC3C,UAAI,CAAC,aAAa;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AACb,aAAO,KAAK,WAAW,EAAE,QAAQ,SAAS,KAAK;AAC7C,YAAI,QAAQ,YAAY,GAAG;AAC3B,YAAI,UAAU,QAAW;AACvB;AAAA,QACF;AACA,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,kBAAQ,MACL,OAAO,SAAS,GAAG;AAClB,mBAAO,MAAM,QAAQ,MAAM;AAAA,UAC7B,CAAC,EACA,KAAK,GAAG;AAAA,QACb;AACA,iBAAS,iBAAiB,QAAQ,KAAK,KAAK;AAAA,MAC9C,CAAC;AACD,aAAO;AAAA,IACT;AAUA,aAAS,cAAc,KAAK,QAAQ;AAClC,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,MAAM,GAAG,CAAC,MAAM,QAAQ;AAC9B,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,IAAI,CAAC,MAAM,MAAM,KAAK;AACtC,aAAO,KAAK,OAAO,QAAQ,OAAO,EAAE,IAAI,YAAY;AAAA,IACtD;AAaA,aAAS,uBAAuB,OAAO,QAAQ;AAC7C,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,aAAO,MAAM,QAAQ,sBAAsB,SAAS,GAAG,SAAS;AAC9D,YAAI,QAAQ,OAAO,OAAO;AAC1B,YAAI,UAAU,QAAW;AACvB,gBAAM,IAAI,MAAM,iCAAiC,OAAO;AAAA,QAC1D;AACA,YAAI,eAAe,YAAY,KAAK;AACpC,eAAO,MAAM;AAAA,MACf,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACvHA;AAAA;AAAA;AAEA,QAAI,aAAa;AACjB,QAAI,QAAQ;AACZ,QAAI,eAAe;AACnB,QAAI,WAAW;AACf,QAAI,YAAY;AAEhB,QAAI,YAAY;AA+DhB,aAAS,YAAY,QAAQ,SAAS;AACpC,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AACA,UAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ;AAChD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,iBAAiB,CAAC;AACtB,UAAI,QAAQ,MAAM;AAChB,uBAAe,cAAc,IAAI;AAAA,MACnC;AAEA,UAAI,sBAAsB,MAAM,gBAAgB,QAAQ,OAAO;AAI/D,UAAI,UAAU,OAAO,KAAK,mBAAmB,EAAE,OAAO,SAAS,MAAM,MAAM;AACzE,aAAK,KAAK,YAAY,CAAC,IAAI,oBAAoB,IAAI;AACnD,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,WAAK,KAAK;AACV,WAAK,WAAW;AAEhB,WAAK,UAAU,IAAI,aAAa;AAChC,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AACpB,WAAK,SAAS,QAAQ;AACtB,WAAK,SAAS,QAAQ,UAAU,OAAO;AACvC,WAAK,QAAQ,QAAQ,SAAS,CAAC;AAC/B,WAAK,SAAS,QAAQ,UAAU,CAAC;AACjC,WAAK,OAAO,QAAQ,QAAQ;AAC5B,WAAK,OAAO,QAAQ,QAAQ;AAC5B,WAAK,WAAW,QAAQ,YAAY;AACpC,WAAK,aAAa,QAAQ,cAAc;AACxC,WAAK,UAAU;AAAA,IACjB;AASA,gBAAY,UAAU,MAAM,SAAS,IAAI,aAAa;AACpD,UAAIC,OAAM,SAAS,cAAc,KAAK,MAAM,KAAK,MAAM;AACvD,MAAAA,OAAM,SAAS,kBAAkBA,MAAK,KAAK,KAAK;AAChD,UAAI,cAAc,KAAK;AACvB,UAAI,oBACF,eAAe,OAAO,KAAK,OAAO,cAAc;AAClD,UAAI,mBAAmB;AACrB,QAAAA,OAAM,SAAS,iBAAiBA,MAAK,gBAAgB,iBAAiB;AACtE,YAAI,qBAAqB,WAAW,iBAAiB,EAAE;AACvD,sBAAc,MAAM,EAAE,SAAS,mBAAmB,GAAG,WAAW;AAAA,MAClE;AACA,MAAAA,OAAM,SAAS,uBAAuBA,MAAK,WAAW;AACtD,aAAOA;AAAA,IACT;AAaA,gBAAY,UAAU,OAAO,SAAS,OAAO;AAC3C,UAAIC,QAAO;AAEX,UAAIA,MAAK,MAAM;AACb,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,MAAAA,MAAK,OAAO;AAEZ,aAAOA,MAAK,OAAO,YAAYA,KAAI,EAAE;AAAA,QACnC,SAAS,UAAU;AACjB,UAAAA,MAAK,WAAW;AAChB,UAAAA,MAAK,QAAQ,KAAK,UAAU,gBAAgB,QAAQ;AACpD,iBAAO;AAAA,QACT;AAAA,QACA,SAAS,OAAO;AACd,UAAAA,MAAK,QAAQ;AACb,UAAAA,MAAK,QAAQ,KAAK,UAAU,aAAa,KAAK;AAC9C,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAeA,gBAAY,UAAU,QAAQ,SAAS,QAAQ;AAC7C,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,MAAM;AAC5B,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,KAAK,YAAY,KAAK,SAAS,KAAK,QAAS;AAEjD,WAAK,UAAU;AACf,WAAK,OAAO,aAAa,IAAI;AAAA,IAC/B;AAoBA,gBAAY,UAAU,WAAW,SAAS,SAAS,UAAU;AAC3D,UAAIA,QAAO;AAEX,eAAS,eAAe,UAAU;AAChC,iBAAS,cAAc;AACrB,iBAAOA,MAAK;AACZ,cAAI,kBAAkB,SAAS,SAAS;AACxC,cAAI,iBAAiB;AACnB,YAAAA,MAAK,mBAAmB;AACxB,oBAAQ,eAAe;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,MAAM,UAAU,WAAW;AAAA,MACtC;AAEA,eAAS,YAAY,OAAO;AAC1B,iBAAS,OAAO,MAAM,WAAW;AAAA,QAAC,CAAC;AAAA,MACrC;AAEA,eAAS,QAAQ,SAAS;AACxB,gBAAQ,KAAK,EAAE,KAAK,gBAAgB,WAAW;AAAA,MACjD;AACA,cAAQ,IAAI;AAAA,IACd;AAUA,gBAAY,UAAU,QAAQ,SAAS,QAAQ;AAC7C,aAAO,KAAK,QAAQ;AAAA,IACtB;AAKA,gBAAY,UAAU,UAAU,SAAS,QAAQ,SAAS;AACxD,UAAI,kBAAkB,MAAM,KAAK,UAAU,OAAO;AAClD,aAAO,IAAI,YAAY,KAAK,QAAQ,eAAe;AAAA,IACrD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrQjB;AAAA;AAAA;AAEA,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,YAAY;AAkBhB,aAAS,WAAW,SAAS;AAC3B,UAAI,CAAC,WAAW,CAAC,QAAQ,aAAa;AACpC,cAAM,IAAI,MAAM,gDAAgD;AAAA,MAClE;AAEA,iBAAW,QAAQ,WAAW;AAE9B,WAAK,cAAc,QAAQ;AAC3B,WAAK,SAAS,QAAQ,UAAU,UAAU;AAAA,IAC5C;AAEA,eAAW,UAAU,gBAAgB,SAAS,cAAc,gBAAgB;AAC1E,aAAO,IAAI,YAAY,MAAM,cAAc;AAAA,IAC7C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA;AAEA,QAAI,UAAU;AACd,QAAI,aAAa;AAEjB,aAAS,cAAc,SAAS;AAC9B,iBAAW,KAAK,MAAM,OAAO;AAAA,IAC/B;AACA,kBAAc,YAAY,OAAO,OAAO,WAAW,SAAS;AAC5D,kBAAc,UAAU,cAAc;AAEtC,kBAAc,UAAU,cAAc,QAAQ;AAC9C,kBAAc,UAAU,eAAe,QAAQ;AAU/C,aAAS,oBAAoB,SAAS;AACpC,aAAO,IAAI,cAAc,OAAO;AAAA,IAClC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA;AAAA;;;ACJjB;AAAA;AAAA;AACA,QAAI,WAAW,OAAO,UAAU;AAEhC,WAAO,UAAU,SAAU,GAAG;AAC7B,UAAI;AACJ,aAAO,SAAS,KAAK,CAAC,MAAM,sBAAsB,YAAY,OAAO,eAAe,CAAC,GAAG,cAAc,QAAQ,cAAc,OAAO,eAAe,CAAC,CAAC;AAAA,IACrJ;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAOA,QAAI,gBAAgB;AACpB,QAAI,QAAQ;AAEZ,QAAI,qBAAqB;AACzB,QAAI,iBAAiB;AAErB,QAAI,IAAI,CAAC;AAOT,MAAE,SAAS,SAAS,eAAe,SAAS;AAC1C,gBAAU,WAAW,CAAC;AACtB,aAAO,SAAS,OAAO;AACrB,YAAI,UAAU,SAAS,eAAe,KAAK;AAE3C,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AAEA,YAAI,eAAe,eAAe,SAAS,OAAO;AAElD,YAAI,QAAQ,SAAS;AACnB,yBAAe,QAAQ,UAAU,OAAO;AAAA,QAC1C;AAEA,cAAM,IAAI,MAAM,YAAY;AAAA,MAC9B;AAAA,IACF;AAQA,MAAE,QAAQ,SAAS,MAAM,cAAc;AACrC,UAAI,aAAa,cAAc,YAAY;AAC3C,aAAO,SAAS,eAAe,OAAO;AACpC,YAAI,mBAAmB,SAAS,EAAE,aAAa,KAAK;AAEpD,YAAI,kBAAkB;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK;AACT,YAAI,gBAAgB,CAAC;AAErB,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,gBAAM,WAAW,CAAC,EAAE;AACpB,sBAAY,WAAW,CAAC,EAAE;AAC1B,6BAAmB,SAAS,WAAW,MAAM,GAAG,CAAC;AAEjD,cAAI,kBAAkB;AAEpB,0BAAc,KAAK,CAAC,GAAG,EAAE,OAAO,gBAAgB,CAAC;AAAA,UACnD;AAAA,QACF;AAEA,YAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,cAAc,CAAC;AAAA,QACxB;AAGA,eAAO,SAAS,SAAS;AACvB,0BAAgB,cAAc,IAAI,SAAS,SAAS;AAClD,gBAAIC,OAAM,QAAQ,CAAC;AACnB,gBAAI,kBAAkB,eAAe,SAAS,OAAO,EAClD,MAAM,IAAI,EACV,KAAK,cAAc;AACtB,mBAAO,OAAOA,OAAM,OAAO;AAAA,UAC7B,CAAC;AAED,cAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AACpC,cAAI,WAAW,aAAa,qBAAqB,KAAK,SAAS;AAE/D,iBACE,6BACA,WACA,0BACA,iBACA,cAAc,KAAK,cAAc;AAAA,QAErC;AAAA,MACF;AAAA,IACF;AAEA,MAAE,cAAc,SAAS,YAAY,cAAc;AACjD,UAAI,iBAAiB,EAAE,MAAM,YAAY;AACzC,aAAO,SAAS,qBAAqB,OAAO;AAC1C,YAAI,cAAc,eAAe,KAAK;AACtC,YAAI,aAAa;AACf,iBAAO;AAAA,QACT;AAEA,YAAI,cAAc,OAAO,KAAK,KAAK,EAAE,OAAO,SAAS,MAAM,UAAU;AACnE,cAAI,aAAa,QAAQ,MAAM,QAAW;AACxC,iBAAK,KAAK,QAAQ;AAAA,UACpB;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAEL,YAAI,YAAY,WAAW,GAAG;AAC5B,iBAAO,WAAW;AAChB,mBAAO,qCAAqC,YAAY,KAAK,IAAI;AAAA,UACnE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,MAAE,UAAU,SAAS,QAAQ,WAAW;AACtC,aAAO,qBAAqB,SAAS;AAAA,IACvC;AAEA,MAAE,QAAQ,SAAS,QAAQ;AACzB,UAAI,aAAa,MAAM,QAAQ,UAAU,CAAC,CAAC,IACvC,UAAU,CAAC,IACX,MAAM,UAAU,MAAM,KAAK,SAAS;AACxC,aAAO,qBAAqB,UAAU;AAAA,IACxC;AAGA,aAAS,qBAAqB,YAAY;AACxC,UAAI,kBAAkB,MAAM,QAAQ,UAAU;AAC9C,UAAI,eAAe,SAAS,OAAO;AACjC,YAAI,iBAAiB;AACnB,iBAAO,WAAW,KAAK;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AAEA,aAAO,SAAS,eAAe,OAAO;AACpC,YAAI,mBAAmB,SAAS,EAAE,YAAY,KAAK;AACnD,YAAI,kBAAkB;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,MAAM,WAAW,WAAW,QAAQ;AACzD,iBAAO,mBAAmB,WAAW,SAAS;AAAA,QAChD;AAEA,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,6BAAmB,SAAS,aAAa,CAAC,GAAG,MAAM,CAAC,CAAC;AACrD,cAAI,kBAAkB;AACpB,mBAAO,CAAC,CAAC,EAAE,OAAO,gBAAgB;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,MAAE,WAAW,SAAS,SAAS,WAAW;AACxC,eAAS,kBAAkB,OAAO;AAChC,YAAI,SAAS,MAAM;AACjB,iBAAO,SAAS,SAAS;AACvB,mBAAO;AAAA,cACL;AAAA,cACA,eAAe,QAAQ,IAAI,IACvB,8BACA;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,eAAO,UAAU,MAAM,MAAM,SAAS;AAAA,MACxC;AACA,wBAAkB,aAAa;AAE/B,aAAO;AAAA,IACT;AAEA,MAAE,YAAY,SAAS,YAAY;AACjC,UAAI,aAAa,MAAM,QAAQ,UAAU,CAAC,CAAC,IACvC,UAAU,CAAC,IACX,MAAM,UAAU,MAAM,KAAK,SAAS;AACxC,aAAO,SAAS,mBAAmB,OAAO;AACxC,YAAI,WAAW,WACZ,IAAI,SAAS,WAAW;AACvB,iBAAO,SAAS,WAAW,KAAK;AAAA,QAClC,CAAC,EACA,OAAO,OAAO;AAIjB,YAAI,SAAS,WAAW,WAAW,QAAQ;AACzC;AAAA,QACF;AAGA,YACE,SAAS,MAAM,SAAS,SAAS;AAC/B,iBAAO,QAAQ,WAAW,KAAK,OAAO,QAAQ,CAAC,MAAM;AAAA,QACvD,CAAC,GACD;AACA,iBAAO;AAAA,YACL,SAAS,IAAI,SAAS,GAAG;AACvB,qBAAO,EAAE,CAAC;AAAA,YACZ,CAAC;AAAA,UACH;AAAA,QACF;AAKA,eAAO,SAAS,OAAO,SAAS,KAAK,KAAK;AACxC,iBAAO,IAAI,SAAS,IAAI,SAAS,MAAM;AAAA,QACzC,CAAC;AAAA,MACH;AAAA,IACF;AAOA,MAAE,QAAQ,SAAS,MAAM,aAAa;AACpC,aAAO,SAAS,eAAe,OAAO;AACpC,YAAI,UAAU,aAAa;AACzB,iBAAO,KAAK,UAAU,WAAW;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,MAAE,QAAQ,SAAS,QAAQ;AACzB,UAAI,UAAU,MAAM,QAAQ,UAAU,CAAC,CAAC,IACpC,UAAU,CAAC,IACX,MAAM,UAAU,MAAM,KAAK,SAAS;AACxC,UAAI,aAAa,QAAQ,IAAI,SAAS,OAAO;AAC3C,eAAO,EAAE,MAAM,KAAK;AAAA,MACtB,CAAC;AAED,aAAO,EAAE,UAAU,MAAM,MAAM,UAAU;AAAA,IAC3C;AAEA,MAAE,QAAQ,SAAS,MAAM,aAAa;AACpC,UAAI,MAAM,YAAY,CAAC;AACvB,UAAI,MAAM,YAAY,CAAC;AACvB,aAAO,SAAS,eAAe,OAAO;AACpC,YAAI,mBAAmB,SAAS,EAAE,QAAQ,KAAK;AAE/C,YAAI,oBAAoB,QAAQ,OAAO,QAAQ,KAAK;AAClD,iBAAO,oBAAoB,MAAM,QAAQ,MAAM;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAOA,MAAE,MAAM,SAAS,MAAM;AACrB;AAAA,IACF;AAEA,MAAE,UAAU,SAAS,QAAQ,OAAO;AAClC,UAAI,OAAO,UAAU,WAAW;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,MAAE,SAAS,SAAS,OAAO,OAAO;AAChC,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,MAAE,aAAa,SAAS,WAAW,OAAO;AACxC,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,MAAE,cAAc,SAAS,YAAY,OAAO;AAC1C,UAAI,CAAC,cAAc,KAAK,GAAG;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,MAAE,SAAS,SAAS,OAAO,OAAO;AAChC,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,MAAE,OAAO,SAAS,KAAK,OAAO;AAC5B,UAAI,OAAO,UAAU,YAAY;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,SAAS,WAAW,OAAO;AAElC,UAAI,SAAS,QAAQ,CAAC,UAAU,eAAe,YAAY,GAAG;AAC5D;AAAA,MACF;AAEA,UAAI,SAAS,UAAU,KAAK;AAE5B,UAAI,QAAQ;AACV,eAAO,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAAA,MACjD;AAAA,IACF;AAEA,aAAS,eAAe,SAAS,SAAS;AAKxC,UAAI,MAAM,QAAQ;AAElB,UAAI,SAAS,QAAQ,MAAM,CAAC;AAC5B,UAAI,OAAO,QAAQ,MAAM,GAAG,MAAM,CAAC;AAEnC,UAAI,KAAK,WAAW,GAAG;AACrB,eAAO,CAAC,kBAAkB;AAAA,MAC5B;AACA,gBAAU,MAAM,SAAS,EAAE,KAAW,CAAC;AAEvC,aAAO,OAAO,WAAW,aACrB,OAAO,OAAO,IACd,mBAAmB,SAAS,eAAe,MAAM,CAAC;AAAA,IACxD;AAEA,aAAS,OAAO,MAAM;AACpB,UAAI,KAAK,SAAS,GAAG;AACnB,eAAO,KAAK,CAAC;AAAA,MACf;AACA,UAAI,KAAK,WAAW,GAAG;AACrB,eAAO,KAAK,KAAK,MAAM;AAAA,MACzB;AACA,aAAO,KAAK,MAAM,GAAG,EAAE,EAAE,KAAK,IAAI,IAAI,UAAU,KAAK,MAAM,EAAE;AAAA,IAC/D;AAEA,aAAS,eAAe,QAAQ;AAC9B,aAAO,aAAa,WAAW,MAAM,IAAI;AAAA,IAC3C;AAEA,aAAS,WAAW,YAAY;AAC9B,UAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,UAAU,GAAG;AAChC,eAAO,QAAQ;AAAA,MACjB;AACA,UAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,eAAO,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,SAAS,cAAc;AACjD,UAAI,eAAe,eAAe,QAAQ,IAAI;AAC9C,UAAI,SAAS,QAAQ,KAAK,KAAK,GAAG,IAAI,MAAM;AAC5C,UAAI,UAAU,eAAe,sBAAsB;AAEnD,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,eAAe,MAAM;AAC5B,aAAO,OAAO,KAAK,KAAK,SAAS,CAAC,KAAK,YAAY,OAAO,KAAK,CAAC,KAAK;AAAA,IACvE;AAEA,aAAS,cAAc,KAAK;AAC1B,aAAO,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,SAAS,KAAK;AAC9C,eAAO,EAAE,KAAU,OAAO,IAAI,GAAG,EAAE;AAAA,MACrC,CAAC;AAAA,IACH;AAEA,MAAE,WAAW;AACb,MAAE,iBAAiB;AAEnB,WAAO,UAAU;AAAA;AAAA;;;AC3XjB;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,IAAI;AAER,aAAS,KAAK,OAAO;AAGnB,UAAI,OAAO,WAAW,aAAa;AACjC,YAAI,iBAAiB,OAAO,QAAQ,iBAAiB,OAAO,aAAa;AACvE;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,UAAI,OAAO,UAAU,YAAY,MAAM,SAAS,QAAW;AACzD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,cAAc,SAAS;AAC1C,aAAO,EAAE,OAAO,EAAE,YAAY,YAAY,GAAG,OAAO;AAAA,IACtD;AAEA,aAAS,KAAK,OAAO;AACnB,UAAI,MAAM;AACV,UAAI,OAAO,UAAU,WAAW;AAC9B,eAAO;AAAA,MACT;AACA,UAAI;AACF,YAAIC,QAAO,IAAI,KAAK,KAAK;AACzB,YAAIA,MAAK,WAAW,MAAMA,MAAK,QAAQ,CAAC,GAAG;AACzC,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,YAAY,OAAO;AAC1B,aAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,IAC1C;AAEA,WAAO,UAAU,MAAM,GAAG;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA;AAAA;;;AChDD;AAAA;AAAA;AAUA,aAAS,KAAK,QAAQ,MAAM;AAC1B,UAAI,SAAS,SAAS,KAAK,KAAK;AAC9B,eAAO,KAAK,QAAQ,GAAG,MAAM,MAAM,QAAQ;AAAA,MAC7C;AAEA,UAAI,OAAO,SAAS,YAAY;AAC9B,iBAAS;AAAA,MACX;AAEA,aAAO,OAAO,KAAK,MAAM,EACtB,OAAO,SAAS,KAAK;AACpB,eAAO,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,MAChC,CAAC,EACA,OAAO,SAAS,QAAQ,KAAK;AAC5B,eAAO,GAAG,IAAI,OAAO,GAAG;AACxB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA;AAEA,aAAS,UAAU,KAAK,IAAI;AAC1B,aAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAS,QAAQ,KAAK;AACnD,eAAO,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC;AAC9B,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACTjB;AAAA;AAAA;AAEA,QAAI,YAAY;AAQhB,aAAS,iBAAiB,KAAK;AAC7B,aAAO,UAAU,KAAK,SAAS,GAAG,OAAO;AACvC,eAAO,OAAO,UAAU,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,MAC9D,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA;AAEA,QAAI,aAAa;AAEjB,QAAI,eAAe;AAEnB,aAAS,qBAAqB,kBAAkB;AAC9C,aAAO,SAAS,gBAAgB;AAC9B,YAAI;AACJ,YAAI,WAAW,UAAU,cAAc,cAAc,GAAG;AACtD,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS,aAAa,cAAc;AAAA,QACtC;AACA,YAAI,UAAU,OAAO,OAAO,gBAAgB;AAC5C,gBAAQ,SAAS;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,IAAI;AACR,QAAI,OAAO;AACX,QAAI,oBAAoB;AACxB,QAAI,uBAAuB;AAQ3B,QAAI,YAAY,CAAC;AAEjB,QAAI,eAAe;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAmEA,cAAU,iBAAiB,SAAS,QAAQ;AAC1C,QAAE,YAAY;AAAA,QACZ,OAAO,EAAE,SAAS,EAAE,MAAM;AAAA,QAC1B,MAAM,EAAE,MAAM,iBAAiB,yBAAyB;AAAA,QACxD,WAAW,EAAE,QAAQ,EAAE,MAAM;AAAA,QAC7B,WAAW,EAAE,MAAM,EAAE,aAAa,IAAI;AAAA,QACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAAA,QACtC,cAAc,EAAE;AAAA,QAChB,MAAM,EAAE,QAAQ,EAAE,MAAM;AAAA,QACxB,OAAO,EAAE;AAAA,QACT,UAAU,EAAE,QAAQ,EAAE,MAAM;AAAA,QAC5B,SAAS,EAAE;AAAA,QACX,YAAY,EAAE;AAAA,QACd,WAAW,EAAE;AAAA,QACb,eAAe,EAAE;AAAA,MACnB,CAAC,EAAE,MAAM;AAET,aAAO,OAAO,OAAO,QAAQ;AAE7B,UAAI,QAAQ;AAAA,QACV;AAAA,UACE,EAAE,SAAS,OAAO,UAAU;AAAA,UAC5B,KAAK,QAAQ;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO,KAAK,OAAO,cAAc;AAAA,QAC/B,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ,KAAK,QAAQ,CAAC,QAAQ,OAAO,CAAC;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAkCA,cAAU,iBAAiB,SAAS,QAAQ;AAC1C,QAAE,YAAY;AAAA,QACZ,OAAO,EAAE,SAAS,EAAE,WAAW;AAAA,QAC/B,MAAM,EAAE,MAAM,iBAAiB,yBAAyB;AAAA,QACxD,WAAW,EAAE,QAAQ,EAAE,MAAM;AAAA,QAC7B,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAAA,QACtC,MAAM,EAAE,QAAQ,EAAE,MAAM;AAAA,QACxB,OAAO,EAAE;AAAA,QACT,UAAU,EAAE,QAAQ,EAAE,MAAM;AAAA,QAC5B,aAAa,EAAE,MAAM,YAAY,OAAO;AAAA,QACxC,SAAS,EAAE;AAAA,QACX,WAAW,EAAE;AAAA,QACb,eAAe,EAAE;AAAA,MACnB,CAAC,EAAE,MAAM;AAET,aAAO,OAAO,OAAO,QAAQ;AAE7B,UAAI,QAAQ;AAAA,QACV;AAAA,UACE,EAAE,SAAS,OAAO,UAAU;AAAA,UAC5B,KAAK,QAAQ;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO,KAAK,OAAO,cAAc;AAAA,QAC/B,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ,KAAK,QAAQ,CAAC,QAAQ,OAAO,CAAC;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,UAAU,qBAAqB,SAAS;AAAA;AAAA;;;ACrN/C,IAAI;AAAJ;AAAA;AAAA,IAAI,cACF;AAAA;AAAA;;;ACDF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACI,QACA,cAeA,gBAEA;AAnBJ;AAAA;AAAA;AACA,IAAI,SAAS,WAAS,OAAO,gBAAgB,IAAI,WAAW,KAAK,CAAC;AAClE,IAAI,eAAe,CAAC,UAAU,aAAa,cAAc;AACvD,UAAI,QAAQ,KAAM,KAAK,IAAI,SAAS,SAAS,CAAC,IAAI,KAAK,OAAQ;AAC/D,UAAI,OAAO,CAAC,EAAG,MAAM,OAAO,cAAe,SAAS;AACpD,aAAO,CAAC,OAAO,gBAAgB;AAC7B,YAAI,KAAK;AACT,eAAO,MAAM;AACX,cAAI,QAAQ,UAAU,IAAI;AAC1B,cAAI,IAAI,OAAO;AACf,iBAAO,KAAK;AACV,kBAAM,SAAS,MAAM,CAAC,IAAI,IAAI,KAAK;AACnC,gBAAI,GAAG,WAAW,KAAM,QAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,iBAAiB,CAAC,UAAU,OAAO,OACrC,aAAa,UAAU,MAAM,MAAM;AACrC,IAAI,SAAS,CAAC,OAAO,OACnB,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS;AAChE,cAAQ;AACR,UAAI,OAAO,IAAI;AACb,cAAM,KAAK,SAAS,EAAE;AAAA,MACxB,WAAW,OAAO,IAAI;AACpB,eAAO,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY;AAAA,MAC7C,WAAW,OAAO,IAAI;AACpB,cAAM;AAAA,MACR,OAAO;AACL,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT,GAAG,EAAE;AAAA;AAAA;;;AChCP,IAAAC,kBAAA;AAAA;AAAA;AACA,QAAIC,UAAS,4DAAkB;AAU/B,aAAS,mBAAmB,SAAS;AACnC,WAAK,SAAS,QAAQ,UAAU;AAChC,WAAK,WAAW;AAChB,WAAK,eAAe,QAAQ;AAC5B,WAAK,UAAU;AACf,WAAK,kBAAkB,KAAK,kBAAkB;AAC9C,WAAK,qBAAqB;AAC1B,WAAK,YAAY,KAAK,aAAa;AAEnC,WAAK,UAAU;AACf,WAAK,OAAO,KAAK,KAAK,KAAK,IAAI;AAI/B,WAAK,YAAa,QAAQ,YAAa,QAAQ,UAAU,MAAM,GAAG,IAAI;AACtE,WAAK,QAAS,QAAQ,QAAS,QAAQ,MAAM,MAAM,GAAG,IAAI;AAC1D,WAAK,OAAQ,QAAQ,OAAQ,QAAQ,OAAO;AAC5C,WAAK,WAAY,QAAQ,WAAY,QAAQ,SAAS,MAAM,GAAG,IAAI;AACnE,WAAK,QAAS,QAAQ,QAAS,CAAC,QAAQ,QAAQ;AAChD,WAAK,SAAS,UAAU,YAAY;AACpC,WAAK,qBAAqB,KAAK,oBAAoB,OAAO;AAC1D,WAAK,aAAa,IAAI,MAAM;AAC5B,WAAK,gBAAgB,QAAQ,iBAAiB;AAC9C,WAAK,eAAe,QAAQ,gBAAgB;AAC5C,WAAK,QAAS,KAAK,gBAAiB,WAAW,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,aAAa,IAAI;AAE5F,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AAAA,IACvB;AAEA,uBAAmB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS7B,QAAQ,SAAS,UAAU,UAAS;AAClC,YAAI,UAAU,KAAK,gBAAgB,iBAAiB,UAAU,EAAE,iBAAiB,SAAS,CAAC;AAC3F,YAAI,CAAC,QAAS;AACd,YAAK,QAAQ,gBAAgB,KAAK,iBAAiB,QAAQ,gBAAgB,KAAK,iBAAkB,QAAQ,eAAe,IAAI;AAE3H;AAAA,QACF;AACA,aAAK,gBAAgB,QAAQ;AAC7B,aAAK,gBAAgB,QAAQ;AAC7B,eAAO,KAAK,KAAK,OAAO;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,SAAS,UAAS;AACvB,YAAI,UAAU,KAAK,gBAAgB,gBAAgB,QAAQ;AAC3D,YAAI,CAAC,QAAS;AACd,eAAO,KAAK,KAAK,OAAO;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,UAAU,SAAS,UAAU,UAAS;AAGpC,YAAI,CAAC,SAAS,IAAK;AAGnB,YAAI,SAAS,WAAW,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,QAAQ,SAAS,OAAO,MAAM,GAAI;AACtF,YAAI,UAAU,KAAK,gBAAgB,oBAAoB,UAAU,EAAE,KAAK,SAAS,IAAI,CAAC;AACtF,YAAI,CAAC,QAAS;AACd,eAAO,KAAK,KAAK,OAAO;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYA,MAAM,SAAU,SAAS,UAAU;AACjC,YAAI,CAAC,KAAK,oBAAoB;AAC5B,cAAI,SAAU,QAAO,SAAS;AAC9B;AAAA,QACF;AACA,YAAI,UAAU,KAAK,kBAAkB,OAAO;AAC5C,aAAK,QAAQ,SAAS,SAAS,KAAI;AACjC,cAAI,IAAK,QAAO,KAAK,YAAY,KAAK,QAAQ;AAC9C,cAAI,UAAU;AACZ,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF,EAAE,KAAK,IAAI,CAAC;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,mBAAmB,SAAS,SAAQ;AAClC,YAAI,CAAC,MAAM,QAAQ,OAAO,EAAG,WAAU,CAAC,OAAO;AAC/C,YAAI,UAAU;AAAA;AAAA,UAEZ,QAAQ;AAAA,UACR,MAAM,KAAK;AAAA,UACX,MAAM,KAAK,WAAY,mBAAmB,KAAK;AAAA,UAC/C,SAAS;AAAA,YACP,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAK,KAAK,UAAU,OAAO;AAAA;AAAA,QAC7B;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA,iBAAiB,SAAU,OAAO,UAAU,YAAY,CAAC,GAAG;AAE1D,YACG,UAAU,mBAAmB,CAAC,UAAU,mBACxC,UAAU,sBAAsB,CAAC,UAAU,KAC5C;AACA,iBAAO;AAAA,QACT;AAGA,YAAI;AACJ,YAAI,CAAC,SAAS,QAAQ,WAAW;AAC/B,sBAAY;AAAA,QACd,WAAW,OAAO,SAAS,QAAQ,cAAc,UAAU;AACzD,sBAAY,CAAC,SAAS,QAAQ,UAAU,WAAW,SAAS,QAAQ,UAAU,QAAQ;AAAA,QACxF,WAAW,SAAS,QAAQ,cAAc,MAAM;AAC9C,cAAI,oBAAoB,SAAS,WAAW,SAAS,SAAS,cAAc,IAAI;AAChF,cAAI,qBAAqB,OAAO,sBAAsB,UAAU;AAC9D,wBAAY,kBAAkB,MAAM,GAAG,EAAE,IAAI,UAAU;AAAA,UACzD,OAAO;AACL,wBAAY,CAAC,KAAI,GAAG;AAAA,UACtB;AAAA,QACF,OAAO;AACL,sBAAY,SAAS,QAAQ;AAAA,QAC/B;AAEA,YAAI,OAAQ,SAAS,OAAQ,SAAS,KAAK,QAAQ,IAAI;AACvD,YAAI,UAAU;AAAA,UACZ;AAAA,UACA,SAAS,KAAK,sBAAsB,KAAK;AAAA,UACzC,SAAS,CAAC,oBAAI,KAAK;AAAA,UACnB,mBAAmB,KAAK,aAAa;AAAA,UACrC,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,UAAU;AAAA,UACV,cAAc,SAAS,QAAQ;AAAA,UAC/B,YAAY,SAAS,QAAQ;AAAA,UAC7B;AAAA,UACA,OAAO,SAAS,QAAQ;AAAA,UACxB,SAAS,SAAS,QAAQ;AAAA,UAC1B,WAAW,SAAS,QAAQ;AAAA,UAC5B,SAAS;AAAA,UACT,gBAAgB,KAAK;AAAA,QACvB;AAGA,YAAI,UAAU,iBAAgB;AAC5B,kBAAQ,cAAc,SAAS;AAAA,QACjC,WAAW,SAAS,mBAAmB,SAAS,UAAS;AACvD,kBAAQ,cAAc,SAAS,SAAS;AAAA,QAC1C,OAAO;AACL,kBAAQ,cAAc,SAAS;AAAA,QACjC;AAGA,YAAI,CAAC,oBAAoB,eAAe,EAAE,SAAS,KAAK,GAAG;AACzD,kBAAQ,OAAO;AAAA,QACjB;AACA,YAAI,UAAU,sBAAsB,UAAU,KAAK;AACjD,kBAAQ,aAAa,UAAU;AAAA,QACjC,WAAW,UAAU,mBAAmB,UAAU,iBAAiB;AACjE,cAAI,WAAW,UAAU;AACzB,cAAI,cAAc,KAAK,iBAAiB,UAAU,QAAQ;AAC1D,kBAAQ,cAAc;AACtB,kBAAQ,kBAAkB,SAAS;AACnC,kBAAQ,WAAW,SAAS;AAC5B,cAAI,SAAS,YAAY;AACvB,oBAAQ,iBAAiB,SAAS,WAAW;AAAA,UAC/C;AACA,cAAI,SAAS,YAAY;AACvB,gBAAI,UAAU,SAAS,WAAW;AAClC,gBAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,sBAAQ,gBAAgB,KAAK,iBAAiB,OAAO;AACrD,sBAAQ,kBAAkB,KAAK,mBAAmB,OAAO;AACzD,sBAAQ,kBAAkB,KAAK,mBAAmB,OAAO;AACzD,sBAAQ,oBAAoB,KAAK,qBAAqB,OAAO;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AAGA,YAAI,CAAC,KAAK,gBAAgB,OAAO,GAAG;AAClC,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,SAAU,MAAM,UAAU;AACjC,YAAI,QAAQ,IAAI,eAAe;AAC/B,cAAM,qBAAqB,WAAW;AACpC,cAAI,KAAK,cAAc,GAAI;AACzB,gBAAI,KAAK,UAAU,KAAI;AAErB,qBAAO,SAAS,IAAI;AAAA,YACtB,OAAM;AACJ,qBAAO,SAAS,KAAK,UAAU;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,cAAM,KAAK,KAAK,QAAQ,KAAK,OAAO,MAAM,KAAK,MAAM,IAAI;AACzD,iBAAS,UAAU,KAAK,SAAQ;AAC9B,cAAI,cAAc,KAAK,QAAQ,MAAM;AACrC,gBAAM,iBAAiB,QAAQ,WAAW;AAAA,QAC5C;AACA,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,aAAa,SAAU,KAAK,UAAU;AACpC,YAAI,SAAU,QAAO,SAAS,GAAG;AAAA,MACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,mBAAmB,WAAY;AAC7B,eAAOA,QAAO;AAAA,MAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc,WAAU;AACtB,eAAO,KAAK,kBAAkB,MAAM,KAAK;AAAA,MAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,WAAY;AACxB,eAAO,wBAAwB,KAAK,UAAU,MAAM,UAAU;AAAA,MAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,kBAAkB,SAAS,UAAU,UAAS;AAC5C,YAAI,CAAC,SAAS,WAAY;AAC1B,YAAI,UAAU,SAAS,WAAW;AAClC,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,QAAQ,IAAI,SAAU,SAAS;AAC7C,iBAAO,QAAQ;AAAA,QACjB,CAAC;AACD,YAAI,cAAc,UAAU,QAAQ,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,MAEA,kBAAkB,SAAU,SAAS;AACnC,eAAO,QAAQ,IAAI,SAAU,SAAS;AACpC,cAAI,QAAQ,YAAY;AACtB,mBAAO,QAAQ,WAAW,aAAa;AAAA,UACzC;AACA,iBAAO,QAAQ,MAAM;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,MAEA,oBAAoB,SAAU,SAAS;AACrC,eAAO,QAAQ,IAAI,SAAU,SAAS;AACpC,iBAAO,QAAQ,cAAc;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,MAEA,oBAAoB,SAAU,SAAS;AACrC,eAAO,QAAQ,IAAI,SAAU,SAAS;AACpC,cAAI,QAAQ,cAAc,MAAM,QAAQ,QAAQ,UAAU,GAAG;AAC3D,mBAAO,QAAQ,WAAW,CAAC,KAAK;AAAA,UAClC;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MAEA,sBAAsB,SAAU,SAAS;AACvC,eAAO,QAAQ,IAAI,SAAU,SAAS;AACpC,iBAAO,QAAQ,WAAW;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,uBAAuB,SAAS,OAAO;AACrC,YAAI,CAAC,oBAAoB,eAAe,EAAE,SAAS,KAAK,GAAG;AACzD,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,iBAAiB,SAAS,SAAS;AACjC,YAAI,CAAC,WAAW,CAAC,QAAQ,MAAO,QAAO;AAEvC,YAAI,2BAA2B,CAAC,SAAS,WAAW,qBAAqB,aAAa;AACtF,YAAI,+BAA+B,CAAC,SAAS,WAAW,qBAAqB,eAAe,YAAY;AACxG,YAAI,4BAA4B,CAAC,SAAS,WAAW,qBAAqB,eAAe,eAAe,QAAQ,eAAe;AAE/H,YAAI,QAAQ,QAAQ;AACpB,YAAI,UAAU,gBAAgB;AAC5B,iBAAO,KAAK,uBAAuB,SAAS,wBAAwB;AAAA,QACtE,WAAW,UAAU,oBAAoB;AACvC,iBAAO,KAAK,uBAAuB,SAAS,4BAA4B;AAAA,QAC1E,WAAW,UAAU,iBAAiB;AACpC,iBAAO,KAAK,uBAAuB,SAAS,yBAAyB;AAAA,QACvE;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,wBAAwB,SAAS,KAAK,eAAe;AACnD,eAAO,cAAc,MAAM,SAAS,MAAM;AACxC,cAAI,SAAS,eAAe;AAC1B,mBAAO,OAAO,IAAI,IAAI,MAAM,YAAY,IAAI,IAAI,EAAE,SAAS;AAAA,UAC7D;AACA,iBAAO,IAAI,IAAI,MAAM;AAAA,QACvB,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,qBAAqB,SAAS,SAAQ;AACpC,YAAI,QAAQ,uBAAuB,MAAO,QAAO;AACjD,YAAI,QAAQ,UAAU,QAAQ,WAAW,yBAA0B,QAAO;AAC1E,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,OAAO,WAAU;AACf,YAAI,KAAK,WAAW,SAAS,GAAE;AAC7B,eAAK,KAAK,KAAK,UAAU;AACzB,eAAK,aAAa,IAAI,MAAM;AAAA,QAC9B;AAEA,YAAI,KAAK,MAAQ,cAAa,KAAK,KAAK;AACxC,YAAI,KAAK,cAAe,MAAK,QAAQ,WAAW,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,aAAa;AAAA,MAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,MAAM,SAAS,KAAK,YAAW;AAC7B,aAAK,WAAW,KAAK,GAAG;AACxB,YAAI,KAAK,WAAW,UAAU,KAAK,gBAAgB,YAAW;AAC5D,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,WAAU;AAChB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAIA,WAAO,UAAU;AAAA;AAAA;;;ACvcjB;AAAA;AAAA;AAOA,QAAI,cAAc;AAAA;AAAA,MAEhB,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAO;AAAA;AAAA,MACP,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,IACR;AAEA,WAAO,UAAU,EAAC,YAAwB;AAAA;AAAA;;;ACpC1C;AAAA;AAAA,KAAC,SAAS,MAAM,MAAM,MAAM;AAC1B,UAAI,OAAO,UAAU,eAAe,OAAO,QAAS,QAAO,UAAU,KAAK;AAAA,UACrE,MAAK,IAAI,IAAI,KAAK;AAAA,IACzB,EAAE,SAAM,UAAU,WAAW;AAE3B,UAAI,QAAQ;AACZ,UAAI,UAAU;AAEd,eAAS,MAAM,KAAK;AAClB,eAAO,IAAI,MAAM,OAAO,KAAK,CAAC;AAAA,MAChC;AAEA,eAAS,MAAM,KAAK;AAClB,eAAO,MAAM,GAAG,EAAE,OAAO,SAAS,GAAG,GAAG;AAAE,iBAAO,KAAK;AAAA,QAAE,CAAC;AAAA,MAC3D;AAEA,eAAS,IAAI,KAAK;AAChB,cAAM,MAAM,GAAG;AACf,eAAO;AAAA,UACL,UAAU,IAAI,CAAC,KAAK;AAAA,UACpB,SAAS,IAAI,CAAC,KAAK;AAAA,UACnB,QAAQ,IAAI,CAAC,KAAK;AAAA,UAClB,QAAQ,IAAI,CAAC,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,eAAS,OAAO,QAAQ,KAAK,OAAO;AAClC,eAAO,eAAe,QAAQ,KAAK;AAAA,UACjC;AAAA,UACA,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAEA,eAAS,KAAK,UAAUC,UAAS,MAAM;AACrC,iBAAS,OAAO,KAAK;AACnB,iBAAO,MAAM,GAAG,EAAE,QAAQ,KAAK;AAAA,QACjC;AACA,eAAO,QAAQ,WAAWA,QAAO;AACjC,eAAO,KAAK,MAAM,MAAM;AAAA,MAC1B;AAEA,WAAK,GAAG,mBAAmB,UAAU;AACrC,WAAK,GAAG,iBAAiB,SAAS;AAClC,WAAK,GAAG,iBAAiB,QAAQ;AACjC,WAAK,GAAG,4BAA4B,QAAQ;AAE5C,aAAO,KAAK,SAAS,KAAK;AAE1B,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACjDD;AAAA;AAAA,aAAS,cAAc;AAAA,IAAC;AAExB,gBAAY,YAAY;AAAA,MAEtB,WAAW,WAAW;AACpB,eAAO,QAAQ,OAAO,UAAU,WAAW;AAAA,MAC7C;AAAA,MAEA,oBAAoB,WAAW;AAC7B,cAAM,kBAAkB;AAAA,UACtB,oBAAoB;AAAA,QACtB;AAEA,eAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,iBAAO,UAAU,YAAY,mBAAmB,SAAS,QAAQ,eAAe;AAAA,QAClF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAMA,aAAS,kCAAkC,SAAS,UAAU;AAC5D,YAAM,WAAW,eAAe,OAAO;AAEvC,YAAM,kBAAmB,CAAC,WAAW,UAAU,SAAS,SAAS;AACjE,UAAI;AAEJ,UAAI,OAAO,aAAa,YAAY;AAClC,eAAO,SAAS,QAAQ;AAAA,MAC1B;AAEA,YAAM,gBAAgB,gBAAgB,QAAQ,QAAQ;AAEtD,UAAI,kBAAkB,IAAI;AACxB,0BAAkB;AAAA,MACpB,OAAO;AACL,0BAAkB,gBAAgB,MAAM,aAAa;AAAA,MACvD;AAEA,aAAO,gBAAgB,OAAO,SAAS,KAAK,MAAM;AAChD,YAAI,CAAC,SAAS,IAAI,GAAG;AACnB,iBAAO;AAAA,QACT;AAEA,YAAI,QAAQ,IAAI;AACd,gBAAM,MAAM;AAAA,QACd;AAEA,eAAO,MAAM,SAAS,IAAI;AAAA,MAC5B,GAAG,EAAE;AAAA,IACP;AAMA,aAAS,eAAe,SAAS;AAC/B,YAAM,cAAc,QAAQ,WAAW;AACvC,YAAM,SAAS,QAAQ,QAAQ;AAC/B,YAAM,YAAY,QAAQ,cAAc;AACxC,YAAM,UAAU,UAAU,MAAM,GAAG,EAAE,CAAC;AAEtC,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,cAAQ,QAAQ,QAAQ,SAAU,SAAS;AACzC,cAAM,QAAQ,QAAQ,GAAG,MAAM,GAAG,EAAE,CAAC;AACrC,iBAAS,KAAK,IAAI,QAAQ;AAAA,MAC5B,CAAC;AAED,aAAO;AAAA,IACT;AAEA,QAAM,4BAA4B;AAElC,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACpEA,IAAAC,eAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,QAAI,SAAS;AACb,QAAI,eAAe,iBAAkB;AACrC,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,cAAc;AAClB,QAAI,qBAAqB;AACzB,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,cAAc;AAClB,QAAI,QAAQ;AAGZ,QAAM,uBAAuB;AAAA,MAC3B,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAKA,aAAS,gBAAgB;AACvB,UAAI,MAAM,SAAS,cAAc,KAAK;AACtC,UAAI,YAAY;AAChB,UAAI,YAAY;AAEhB,aAAO;AAAA,IACT;AAsDA,aAAS,eAAe,SAAS;AAC/B,WAAK,gBAAgB,IAAI,aAAa;AACtC,WAAK,UAAU,OAAO,CAAC,GAAG,KAAK,SAAS,OAAO;AAC/C,WAAK,cAAc;AACnB,WAAK,QAAQ;AACb,WAAK,eAAe;AACpB,WAAK,cAAc,IAAI,YAAY;AAAA,IACrC;AAEA,mBAAe,YAAY;AAAA,MACzB,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,QACjB,cAAc,SAAS,MAAM;AAC3B,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,QAAQ,SAAS,MAAM;AACrB,cAAI,YAAY,KAAK,WAAW,MAAM,GAAG;AACzC,iBAAO,2GAA2G,UAAU,CAAC,IAAG,mEAAmE,UAAU,OAAO,GAAG,UAAU,MAAM,EAAE,KAAK,GAAG,IAAI;AAAA,QACvP;AAAA,MACF;AAAA,MAEA,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBX,OAAO,SAAS,WAAU;AAExB,iBAAS,uBAAwB,UAAUC,YAAW;AACpD,cAAI,CAAC,SAAS,KAAK,SAASA,UAAS,GAAG;AACtC,kBAAM,IAAI,MAAM,4DAA4D;AAAA,UAC9E;AACA,gBAAM,KAAK,SAAS,MAAM;AAC1B,UAAAA,WAAU,YAAY,EAAE;AAAA,QAC1B;AAGA,YAAI,UAAU,mBAAkB;AAE9B,oBAAU,WAAW,IAAI;AAAA,QAC3B,WAES,qBAAqB,aAAa;AACzC,iCAAuB,MAAM,SAAS;AAAA,QACxC,WAES,OAAO,aAAa,UAAS;AACpC,gBAAM,SAAS,SAAS,iBAAiB,SAAS;AAClD,cAAI,OAAO,WAAW,GAAE;AACtB,kBAAM,IAAI,MAAM,YAAY,WAAW,YAAY;AAAA,UACrD;AAEA,cAAI,OAAO,SAAS,GAAE;AACpB,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACvE;AAEA,iCAAuB,MAAM,OAAO,CAAC,CAAC;AAAA,QACxC,OAAK;AACH,gBAAM,IAAI,MAAM,6GAA6G;AAAA,QAC/H;AAAA,MACF;AAAA,MAEA,OAAO,SAAS,KAAK;AACnB,YAAI,OAAO,OAAO,OAAO,UAAS;AAChC,eAAK,OAAO;AAAA,QACd;AAEA,aAAK,YAAY;AAEjB,YAAI,CAAC,KAAK,QAAQ,mBAAkB;AAClC,eAAK,kBAAkB;AAAA,YACrB,aAAa;AAAA,cACX,aAAa,KAAK,QAAQ;AAAA,cAC1B,QAAQ,KAAK,QAAQ;AAAA,YACvB,CAAC;AAAA,UACH;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,qBAAqB,CAAC,KAAK,QAAQ,eAAc;AAChE,gBAAM,IAAI,MAAM,0EAA0E;AAAA,QAC5F;AAEA,aAAK,eAAe,IAAI,mBAAmB,KAAK,OAAO;AAEvD,aAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,aAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,aAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,aAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AACrC,aAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,aAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,aAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,aAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,aAAK,mBAAmB,KAAK,iBAAiB,KAAK,IAAI;AACvD,aAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,aAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,aAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,aAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,aAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AAEnD,YAAI,KAAM,KAAK,YAAY,SAAS,cAAc,KAAK;AACvD,WAAG,YAAY;AAEf,YAAI,aAAa,KAAK,WAAW,UAAU,mQAAmQ;AAE9S,aAAK,WAAW,SAAS,cAAc,OAAO;AAC9C,aAAK,SAAS,OAAO;AACrB,aAAK,SAAS,YAAY;AAE1B,aAAK,eAAe;AAEpB,YAAI,KAAK,QAAQ,WAAW;AAC1B,eAAK,UAAU;AACf,eAAK,UAAU,iBAAiB,cAAc,KAAK,WAAW;AAC9D,eAAK,UAAU,iBAAiB,cAAc,KAAK,SAAS;AAC5D,eAAK,SAAS,iBAAiB,SAAS,KAAK,WAAW;AAAA,QAC1D;AAEA,YAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,aAAa;AACtD,eAAK,SAAS,iBAAiB,QAAQ,KAAK,OAAO;AAAA,QACrD;AAEA,aAAK,SAAS,iBAAiB,WAAW,SAAS,KAAK,YAAY,GAAG,CAAC;AACxE,aAAK,SAAS,iBAAiB,SAAS,KAAK,QAAQ;AACrD,aAAK,SAAS,iBAAiB,UAAU,KAAK,SAAS;AACvD,aAAK,UAAU,iBAAiB,cAAc,KAAK,WAAW;AAC9D,aAAK,UAAU,iBAAiB,cAAc,KAAK,WAAW;AAC9D,aAAK,SAAS,iBAAiB,SAAS,SAAS,GAAE;AACjD,eAAK,aAAa,SAAS,GAAG,IAAI;AAAA,QACpC,EAAE,KAAK,IAAI,CAAC;AAEZ,YAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,gBAAQ,UAAU,IAAI,mCAAmC;AAEzD,aAAK,WAAW,SAAS,cAAc,QAAQ;AAC/C,aAAK,SAAS,aAAa,cAAc,OAAO;AAChD,aAAK,SAAS,iBAAiB,SAAS,KAAK,KAAK;AAClD,aAAK,SAAS,YAAY;AAE1B,YAAI,aAAa,KAAK,WAAW,SAAS,wRAAwR;AAClU,aAAK,SAAS,YAAY,UAAU;AAEpC,aAAK,aAAa,KAAK,WAAW,WAAW,kQAAkQ;AAE/S,gBAAQ,YAAY,KAAK,QAAQ;AACjC,gBAAQ,YAAY,KAAK,UAAU;AAEnC,WAAG,YAAY,UAAU;AACzB,WAAG,YAAY,KAAK,QAAQ;AAC5B,WAAG,YAAY,OAAO;AAEtB,YAAI,KAAK,QAAQ,qBAAqB,KAAK,YAAY,UAAU,GAAG;AAClE,eAAK,eAAe,SAAS,cAAc,QAAQ;AACnD,eAAK,aAAa,aAAa,cAAc,WAAW;AACxD,eAAK,aAAa,iBAAiB,SAAS,KAAK,cAAc;AAC/D,eAAK,aAAa,YAAY;AAE9B,cAAI,gBAAgB,KAAK,WAAW,aAAa,uLAAuL;AACxO,eAAK,aAAa,YAAY,aAAa;AAE3C,kBAAQ,YAAY,KAAK,YAAY;AACrC,eAAK,qBAAqB;AAAA,QAC5B;AAEA,YAAI,YAAY,KAAK,aAAa,IAAI,UAAU,KAAK,UAAU,CAAC,GAAG;AAAA,UACjE,QAAQ;AAAA,UACR,WAAW,KAAK,QAAQ;AAAA,UACxB,OAAO,KAAK,QAAQ;AAAA,QACtB,CAAC;AAED,aAAK,kBAAkB,KAAK,QAAQ,MAAM;AAC1C,kBAAU,eAAe,KAAK,QAAQ;AAGtC,YAAI,aAAa,UAAU,KAAK;AAChC,YAAI,aAAa,KAAK,cAAc,cAAc;AAClD,kBAAU,KAAK,OAAO,WAAW;AAC/B,qBAAW,KAAK,IAAI;AAEpB,qBAAW,iBAAiB,aAAa,WAAW;AAClD,iBAAK,oBAAoB;AAAA,UAC3B,EAAE,KAAK,IAAI,CAAC;AAEZ,qBAAW,iBAAiB,WAAW,WAAW;AAChD,iBAAK,oBAAoB;AAAA,UAC3B,EAAE,KAAK,IAAI,CAAC;AAEZ,eAAK,QAAQ,YAAY,UAAU;AAAA,QACrC;AAEA,aAAK,YAAY;AACjB,aAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,YAAI,KAAK,MAAK;AACZ,cAAI,KAAK,QAAQ,gBAAiB;AAChC,iBAAK,iBAAiB;AACtB,iBAAK,KAAK,GAAG,WAAW,KAAK,gBAAgB;AAAA,UAC/C;AACA,eAAK,YAAY,KAAK,QAAQ;AAC9B,cAAI,CAAC,KAAK,aAAa,KAAK,QAAQ,QAAQ;AAE1C,oBAAQ,MAAM,yFAAyF;AACvG,iBAAK,QAAQ,SAAS;AAAA,UACxB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MAEA,gBAAgB,WAAY;AAC1B,aAAK,qBAAqB;AAC1B,aAAK,iBAAiB;AAEtB,aAAK,YAAY,mBAAmB,EAAE,KAAK,SAAS,qBAAqB;AACvE,eAAK,iBAAiB;AAEtB,gBAAM,UAAU;AAAA,YACd,UAAU;AAAA,cACR,MAAM;AAAA,cACN,aAAa,CAAC,oBAAoB,OAAO,WAAW,oBAAoB,OAAO,QAAQ;AAAA,YACzF;AAAA,UACF;AAEA,eAAK,cAAc,OAAO;AAC1B,eAAK,KAAK,OAAO;AAEjB,eAAK,WAAW,MAAM;AACtB,eAAK,WAAW,WAAW;AAC3B,eAAK,eAAe,KAAK,UAAU,OAAO;AAC1C,eAAK,iBAAiB;AACtB,eAAK,QAAQ;AAEb,gBAAM,SAAS;AAAA,YACb,OAAO;AAAA,YACP,UAAU,CAAC,KAAK,QAAQ,QAAQ;AAAA,YAChC,OAAO,QAAQ,SAAS;AAAA,YACxB,OAAO,CAAC,SAAS;AAAA,UACnB;AAEA,cAAI,KAAK,QAAQ,mBAAmB;AAClC,kBAAM,OAAO,QAAQ,SAAS,YAAY,CAAC,IAAI,MAAM,QAAQ,SAAS,YAAY,CAAC;AACnF,iBAAK,eAAe,IAAI;AAExB,iBAAK,cAAc,KAAK,UAAU,EAAE,QAAQ,QAAQ,CAAC;AAAA,UACvD,OAAO;AACL,iBAAK,gBAAgB,eAAe,MAAM,EAAE,KAAK,EAAE,KAAK,SAAU,MAAM;AACtE,oBAAM,UAAU,KAAK,KAAK,SAAS,CAAC;AAEpC,kBAAI,SAAS;AACX,sBAAM,eAAe,MAAM,kCAAkC,SAAS,KAAK,QAAQ,eAAe;AAClG,qBAAK,eAAe,YAAY;AAEhC,wBAAQ,mBAAmB,QAAQ,SAAS;AAC5C,qBAAK,cAAc,KAAK,UAAU,EAAE,QAAQ,QAAQ,CAAC;AAAA,cACvD,OAAO;AACL,qBAAK,cAAc,KAAK,UAAU,EAAE,QAAQ,EAAE,kBAAkB,QAAQ,SAAS,YAAY,EAAE,CAAC;AAAA,cAClG;AAAA,YACF,EAAE,KAAK,IAAI,CAAC;AAAA,UACd;AAAA,QACF,EAAE,KAAK,IAAI,CAAC,EAAE,MAAM,SAAS,OAAO;AAClC,cAAI,MAAM,SAAS,GAAG;AACpB,iBAAK,kCAAkC;AAAA,UACzC,OAAO;AACL,iBAAK,qBAAqB;AAAA,UAC5B;AAEA,eAAK,iBAAiB;AACtB,eAAK,qBAAqB;AAC1B,eAAK,iBAAiB;AAAA,QACxB,EAAE,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,MAEA,YAAY,SAAS,MAAM,MAAM;AAC/B,YAAI,OAAO,SAAS,gBAAgB,8BAA8B,KAAK;AACvE,aAAK,aAAa,SAAS,+DAA+D,IAAI;AAC9F,aAAK,aAAa,WAAW,WAAW;AACxC,aAAK,aAAa,aAAY,UAAU;AACxC,aAAK,aAAa,SAAS,EAAE;AAC7B,aAAK,aAAa,UAAU,EAAE;AAC9B,aAAK,YAAY;AACjB,eAAO;AAAA,MACT;AAAA,MAEA,UAAU,WAAW;AACnB,aAAK,UAAU,WAAW,YAAY,KAAK,SAAS;AAEpD,YAAI,KAAK,QAAQ,kBAAkB,KAAK,MAAM;AAC5C,eAAK,KAAK,IAAI,WAAW,KAAK,gBAAgB;AAAA,QAChD;AAEA,aAAK,cAAc;AAEnB,aAAK,OAAO;AAEZ,eAAO;AAAA,MACT;AAAA,MAEA,gBAAgB,SAAU,OAAO;AAC/B,aAAK,SAAS,QAAQ;AAEtB,mBAAW,WAAY;AACrB,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,aAAa;AAC3B,eAAK,SAAS,kBAAkB,GAAG,CAAC;AAAA,QACtC,EAAE,KAAK,IAAI,GAAG,CAAC;AAAA,MACjB;AAAA,MAEA,UAAU,SAAS,GAAE;AACnB,YAAI,SAAS,EAAE,iBAAiB,OAAO,eAAe,QAAQ,MAAM;AACpE,YAAI,MAAM,UAAU,KAAK,QAAQ,WAAW;AAC1C,eAAK,SAAS,KAAK;AAAA,QACrB;AAAA,MACF;AAAA,MAEA,YAAY,SAAS,GAAG;AACtB,YAAI,eAAe,IACjB,eAAe;AAEjB,YAAI,EAAE,YAAY,gBAAgB,KAAK,QAAQ,mBAAmB;AAChE,eAAK,OAAO,CAAC;AACb,iBAAO,KAAK,SAAS,KAAK;AAAA,QAC5B;AAGA,YAAI,SAAS,EAAE,UAAU,EAAE,OAAO,aAC9B,EAAE,OAAO,WAAW,gBACpB,EAAE;AACN,YAAI,QAAQ,SAAS,OAAO,QAAQ;AAEpC,YAAI,CAAC,OAAO;AACV,eAAK,QAAQ;AAEb,cAAI,EAAE,YAAY,aAAc,MAAK,MAAM,CAAC;AAC5C,eAAK,qBAAqB;AAC1B,iBAAO,KAAK,iBAAiB;AAAA,QAC/B;AAEA,aAAK,qBAAqB;AAG1B,YAAK,EAAE,WAAW,CAAC,cAAc,cAAc,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,OAAO,MAAM;AACxF;AAEF,YAAI,OAAO,MAAM,UAAU,KAAK,QAAQ,WAAW;AACjD,eAAK,SAAS,OAAO,KAAK;AAAA,QAC5B;AAAA,MACF;AAAA,MAEA,aAAa,WAAW;AACtB,YAAI,KAAK,WAAW,SAAU,MAAK,iBAAiB;AAAA,MACtD;AAAA,MAEA,aAAa,WAAW;AACtB,YAAI,KAAK,WAAW,SAAU,MAAK,iBAAiB;AAAA,MACtD;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,SAAS,MAAM,UAAU;AAAA,MAChC;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,SAAS,MAAM,UAAU;AAAA,MAChC;AAAA,MAEA,sBAAsB,WAAW;AAC/B,YAAI,KAAK,gBAAgB,KAAK,YAAY,UAAU,GAAG;AACrD,eAAK,aAAa,MAAM,UAAU;AAAA,QACpC;AAAA,MACF;AAAA,MAEA,sBAAsB,WAAW;AAC/B,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa,MAAM,UAAU;AAAA,QACpC;AAAA,MACF;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,WAAW,MAAM,UAAU;AAAA,MAClC;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,WAAW,MAAM,UAAU;AAAA,MAClC;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,YAAY,MAAM,UAAU;AAAA,MACnC;AAAA,MAEA,kBAAkB,WAAW;AAC3B,aAAK,YAAY,MAAM,UAAU;AAAA,MACnC;AAAA,MAEA,SAAS,SAAS,GAAG;AACnB,YAAI,KAAK,QAAQ,aAAa;AAC5B,eAAK,aAAa,CAAC;AAAA,QACrB;AACA,YAAI,KAAK,QAAQ,WAAW;AAC1B,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA,MACA,WAAW,WAAW;AACpB,YAAI,WAAW,KAAK,WAAW;AAC/B,YAAI,YAAa,KAAK,UAAU,QAAQ,MAAM,KAAK,cAAc;AAC/D,eAAK,iBAAiB;AACtB,cAAI,KAAK,QAAQ,OAAO;AACtB,iBAAK,KAAK,QAAQ;AAAA,UACpB;AACA,cAAI,KAAK,QAAQ,UAAU,KAAK,WAAU;AACxC,iBAAK,cAAc,QAAQ;AAAA,UAC7B;AAIA,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,aAAa;AAC3B,eAAK,SAAS,kBAAkB,GAAG,CAAC;AACpC,eAAK,eAAe,KAAK,UAAU,QAAQ;AAC3C,eAAK,cAAc,KAAK,UAAU,EAAE,QAAQ,SAAS,CAAC;AACtD,eAAK,aAAa,OAAO,UAAU,IAAI;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,MAAM,SAAS,UAAU;AACvB,YAAI;AACJ,YAAI,SAAS,cAAc,WAAW,SAAS,WAAW,UAAU,GAAG;AAOrE,uBAAa,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK;AAC1C,cAAI,KAAK,MAAK;AACZ,iBAAK,KAAK,UAAU,WAAW,SAAS,WAAW,UAAU,EAAE,MAAM,UAAU;AAAA,UACjF;AAAA,QACF,WAAW,SAAS,MAAM;AACxB,cAAI,OAAO,SAAS;AACpB,uBAAa,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK;AAC1C,cAAI,KAAK,MAAK;AACZ,iBAAK,KAAK,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;AAAA,UAC1E;AAAA,QACF,OAAO;AACL,cAAI,oBAAoB;AAAA,YACtB,MAAM,KAAK,QAAQ;AAAA,UACrB;AACA,uBAAa,OAAO,CAAC,GAAG,mBAAmB,KAAK,QAAQ,KAAK;AAE7D,cAAI,SAAS,QAAQ;AACnB,uBAAW,SAAS,SAAS;AAAA,UAC/B,WAAW,SAAS,YAAY,SAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,WAAW,SAAS,SAAS,aAAa;AAC7H,uBAAW,SAAS,SAAS,SAAS;AAAA,UACxC;AAEA,cAAI,KAAK,MAAK;AACZ,iBAAK,KAAK,MAAM,UAAU;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAAA,MAEA,cAAc,SAAS,SAAS,QAAQ;AACtC,YAAI;AACJ,YAAI,QAAQ,mBAAmB;AAC7B,iBAAO,qBAAqB;AAAA,QAC9B,WAAW,QAAQ,kBAAkB,MAAM,0BAA0B,KAAK,MAAM,GAAG;AACjF,iBAAO,qBAAqB;AAAA,QAC9B,OAAO;AACL,iBAAO,qBAAqB;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAAA,MAEA,cAAc,SAAS,aAAa,QAAQ;AAE1C,cAAM,OAAO;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM,mBAAmB;AAEzB,YAAIC,QAAO;AACX,YAAI,SAAS,KAAK,OAAO,SAASC,SAAQ,KAAK;AAE7C,cAAID,MAAK,QAAQ,GAAG,MAAM,UAAaA,MAAK,QAAQ,GAAG,MAAM,MAAM;AACjE,mBAAOC;AAAA,UACT;AAIA,WAAC,aAAa,SAAS,UAAU,EAAE,QAAQ,GAAG,IAAI,KAC7CA,QAAO,GAAG,IAAID,MAAK,QAAQ,GAAG,EAAE,MAAM,gBAAgB,IACtDC,QAAO,GAAG,IAAID,MAAK,QAAQ,GAAG;AAEnC,gBAAM,aACJ,OAAOA,MAAK,QAAQ,GAAG,EAAE,cAAc,YACvC,OAAOA,MAAK,QAAQ,GAAG,EAAE,aAAc;AAEzC,cAAI,QAAQ,eAAe,YAAY;AACrC,kBAAM,MAAMA,MAAK,QAAQ,GAAG,EAAE;AAC9B,kBAAM,MAAMA,MAAK,QAAQ,GAAG,EAAE;AAE9B,YAAAC,QAAO,GAAG,IAAI,CAAC,KAAK,GAAG;AAAA,UACzB;AAEA,iBAAOA;AAAA,QACT,GAAG,CAAC,CAAC;AAEL,gBAAQ,aAAa;AAAA,UACrB,KAAK,qBAAqB;AAAS;AACjC,kBAAI,SAAS,OAAO,MAAM,gBAAgB,EAAE,IAAI,SAAS,GAAG;AAC1D,uBAAO,WAAW,GAAG,EAAE;AAAA,cACzB,CAAC;AACD,kBAAI,CAACD,MAAK,QAAQ,iBAAiB;AACjC,uBAAO,QAAQ;AAAA,cACjB;AAIA,qBAAO,QAAQ,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK;AACzC,uBAAS,OAAO,QAAQ,EAAE,OAAO,QAAQ,OAAO,EAAE,CAAC;AAGnD,eAAC,aAAa,gBAAgB,cAAc,MAAM,EAAE,QAAQ,SAAS,KAAK;AACxE,oBAAI,OAAO,QAAQ;AACjB,yBAAO,OAAO,GAAG;AAAA,gBACnB;AAAA,cACF,CAAC;AAAA,YACH;AAAE;AAAA,UACF,KAAK,qBAAqB;AAAS;AAGjC,oBAAM,gBAAgB,OAAO,KAAK;AAClC,oBAAM,yBAAyB;AAC/B,kBAAI,uBAAuB,KAAK,aAAa,GAAG;AAC9C,yBAAS,OAAO,QAAQ,MAAM,GAAG;AAAA,cACnC;AACA,uBAAS,OAAO,QAAQ,EAAE,OAAO,OAAO,CAAC;AAAA,YAC3C;AAAE;AAAA,QACF;AAEA,eAAO,gBAAgB,KAAK,aAAa,aAAa;AAEtD,eAAO;AAAA,MACT;AAAA,MAEA,UAAU,SAAS,aAAa;AAC9B,aAAK,cAAc;AACnB,aAAK,iBAAiB;AACtB,aAAK,cAAc,KAAK,WAAW,EAAE,OAAO,YAAY,CAAC;AAEzD,cAAM,cAAc,KAAK,aAAa,KAAK,SAAS,WAAW;AAC/D,cAAM,SAAS,KAAK,aAAa,aAAa,WAAW;AAEzD,YAAI;AACJ,gBAAQ,aAAa;AAAA,UACrB,KAAK,qBAAqB;AACxB,sBAAU,QAAQ,QAAQ;AAC1B;AAAA,UACF,KAAK,qBAAqB;AACxB,sBAAU,KAAK,gBAAgB,eAAe,MAAM,EAAE,KAAK;AAC3D;AAAA,UACF,KAAK,qBAAqB;AACxB,sBAAU,KAAK,gBAAgB,eAAe,MAAM,EAAE,KAAK;AAC3D;AAAA,QACF;AAEA,YAAI,mBAAmB,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,cAAc,WAAW,KAAK,CAAC,IAAI,CAAC;AACrG,YAAI,sBAAsB,CAAC;AAE3B,YAAI,gBAAgB;AACpB,gBAAQ,MAAM,SAAS,OAAO;AAC5B,0BAAgB;AAAA,QAClB,EAAE,KAAK,IAAI,CAAC,EACT;AAAA,UACC,SAAS,UAAU;AACjB,iBAAK,iBAAiB;AACtB,gBAAI,MAAM,CAAC;AAEX,gBAAI,CAAC,UAAS;AACZ,oBAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,UAAU,CAAC;AAAA,cACb;AAAA,YACF,WAAW,SAAS,cAAc,OAAO;AACvC,oBAAM,SAAS;AACf,kBAAI,UAAU,SAAS;AACvB,kBAAI,UAAU,SAAS;AACvB,mBAAK,WAAW,SAAS;AAAA,YAC3B;AAEA,gBAAI,SAAS;AAEb,gBAAI,KAAK,OAAM;AACb,mBAAK,aAAa,MAAM,IAAI;AAC5B,mBAAK,QAAQ;AAAA,YACf;AAGA,gBAAI,IAAI,YAAY,IAAI,SAAS,QAAQ;AACvC,kBAAI,SAAS,IAAI,SAAU,SAAS;AAClC,wBAAQ,UAAU;AAAA,cACpB,CAAC;AAAA,YACH;AAGA,gBAAI,WAAW,IAAI,WACf,iBAAiB,OAAO,IAAI,QAAQ,IACpC;AAEJ,gBAAI,KAAK,QAAQ,kBAAkB;AAEjC,oCAAsB,KAAK,QAAQ,iBAAiB,aAAa,IAAI,QAAQ,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAEpG,qBAAO,oBAAoB,KAAK,SAAS,UAAU;AACjD,oBAAI,WAAW,IAAI,WAAW,SAAS,OAAO,IAAI,QAAQ,IAAI;AAC9D,uBAAO;AAAA,cACT,GAAG,WAAU;AAEX,uBAAO;AAAA,cACT,CAAC;AAAA,YACH;AACA,mBAAO;AAAA,UAET,EAAE,KAAK,IAAI;AAAA,QAAC,EAAE;AAAA,UACd,SAAS,KAAK;AACZ,gBAAI,eAAe;AACjB,oBAAM;AAAA,YACR;AAGA,gBAAI,KAAK,QAAQ,UAAU,IAAI,SAAS,QAAQ;AAC9C,kBAAI,WAAW,IAAI,SAAS,OAAO,KAAK,QAAQ,MAAM;AAAA,YACxD;AAEA,gBAAI,IAAI,SAAS,QAAQ;AACvB,mBAAK,iBAAiB;AACtB,mBAAK,qBAAqB;AAC1B,mBAAK,iBAAiB;AACtB,mBAAK,cAAc,KAAK,WAAW,GAAG;AACtC,mBAAK,WAAW,OAAO,IAAI,QAAQ;AAAA,YACrC,OAAO;AACL,mBAAK,iBAAiB;AACtB,mBAAK,iBAAiB;AACtB,mBAAK,WAAW,WAAW;AAC3B,mBAAK,iBAAiB;AACtB,mBAAK,cAAc,KAAK,WAAW,GAAG;AAAA,YACxC;AAAA,UAEF,EAAE,KAAK,IAAI;AAAA,QACb,EAAE;AAAA,UACA,SAAS,KAAK;AACZ,iBAAK,iBAAiB;AACtB,iBAAK,iBAAiB;AAGtB,gBAAK,iBAAiB,UAAU,KAAK,QAAQ,iBAAmB,oBAAoB,UAAU,KAAK,QAAQ,kBAAoB;AAC7H,mBAAK,iBAAiB;AACtB,mBAAK,qBAAqB;AAC1B,mBAAK,WAAW,OAAO,gBAAgB;AAAA,YACzC,OAAO;AACL,mBAAK,iBAAiB;AACtB,mBAAK,WAAW,WAAW;AAC3B,mBAAK,aAAa;AAAA,YACpB;AAEA,iBAAK,cAAc,KAAK,WAAW,EAAE,UAAU,iBAAiB,CAAC;AACjE,iBAAK,cAAc,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC;AAAA,UACjD,EAAE,KAAK,IAAI;AAAA,QACb;AAEF,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,SAAS,IAAI;AACnB,YAAI,GAAI,IAAG,eAAe;AAC1B,aAAK,SAAS,QAAQ;AACtB,aAAK,WAAW,WAAW;AAC3B,aAAK,WAAW,MAAM;AACtB,aAAK,aAAa;AAClB,aAAK,UAAU;AACf,aAAK,iBAAiB;AACtB,aAAK,qBAAqB;AAC1B,aAAK,cAAc;AACnB,aAAK,eAAe;AACpB,aAAK,cAAc,KAAK,OAAO;AAC/B,aAAK,QAAQ;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,SAAS,IAAI;AAClB,aAAK,OAAO,EAAE;AACd,aAAK,SAAS,MAAM;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,cAAc,SAAS,IAAI;AACzB,YAAI,MAAM;AAWV,YAAI,GAAG,eAAe;AACpB,cAAI,OAAO,EAAE;AAAA,QACf;AAAA,MACF;AAAA,MAEA,gBAAgB,SAAS,UAAU;AACjC,YAAI,UAAU,SAAS;AACvB,YAAI,CAAC,QAAQ,SAAS,OAAQ;AAC9B,YAAI,SAAS,QAAQ,SAAS,CAAC;AAC/B,aAAK,WAAW,WAAW;AAC3B,aAAK,SAAS,QAAQ,OAAO;AAC7B,aAAK,UAAU;AAAA,MACjB;AAAA,MAEA,kBAAkB,WAAW;AAG3B,YAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,QAAQ,gBAAe;AAC7C;AAAA,QACF;AACA,YAAI,KAAK,KAAK,QAAQ,IAAI,GAAG;AAC3B,cAAI,SAAS,KAAK,KAAK,UAAU,EAAE,KAAK;AACxC,eAAK,aAAa,EAAE,WAAW,OAAO,KAAK,UAAU,OAAO,IAAI,GAAG,KAAK;AAAA,QAC1E,OAAO;AACL,eAAK,aAAa,MAAM,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,MAEA,WAAW,WAAW;AAEpB,YAAI,CAAC,KAAK,SAAS,SAAS,KAAK,aAAa,SAAS,cAAe,MAAK,UAAU,UAAU,IAAI,mCAAmC;AAAA,MACxI;AAAA,MAEA,aAAa,WAAW;AACtB,aAAK,UAAU,UAAU,OAAO,mCAAmC;AAAA,MACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,SAAS,aAAa;AAC3B,aAAK,SAAS,WAAW,EAAE,KAAK,KAAK,cAAc;AACnD,eAAO;AAAA,MACT;AAAA,MAEA,cAAc,WAAU;AACtB,YAAI,eAAe;AACnB,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,MAEA,sBAAsB,WAAU;AAC9B,YAAI,eAAe;AACnB,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,MAEA,kBAAkB,WAAU;AAC1B,YAAI,eAAe;AACnB,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,MAEA,mCAAmC,WAAW;AAC5C,YAAI,eAAe;AACnB,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,MAEA,gBAAgB,SAAS,KAAI;AAC3B,aAAK,WAAW,OAAO,CAAC,CAAC;AACzB,aAAK,WAAW,WAAW;AAC3B,aAAK,WAAW,MAAM;AACtB,aAAK,WAAW,YAAY,GAAG;AAAA,MACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYA,qBAAqB,WAAU;AAC7B,YAAI,KAAK,QAAQ,YAAa,QAAO,KAAK,QAAQ;AAClD,YAAI,KAAK,QAAQ,UAAS;AACxB,cAAI,gBAAgB,KAAK,QAAQ,SAAS,MAAM,GAAG,EAAE,CAAC;AACtD,cAAI,WAAW,OAAO,SAAS,aAAa;AAC5C,cAAI,iBAAiB,aAAa,YAAY,QAAQ;AACtD,cAAI,eAAiB,QAAO;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,UAAU,SAAS,aAAa,iBAAiB;AAC/C,YAAI,oBAAoB,QAAW;AACjC,4BAAkB;AAAA,QACpB;AAEA,aAAK,SAAS,QAAQ;AACtB,aAAK,WAAW,WAAW;AAC3B,aAAK,WAAW,MAAM;AACtB,YAAI,YAAY,UAAU,KAAK,QAAQ,WAAW;AAChD,4BAAkB,KAAK,SAAS,WAAW,IAAI,KAAK,UAAU;AAAA,QAChE;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,cAAc,SAAS,WAAW,wBAAwB,MAAM;AAC9D,aAAK,QAAQ,YAAY;AACzB,YAAI,uBAAuB;AACzB,eAAK,QAAQ,iBAAiB;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,WAAW;AACvB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,mBAAmB,SAAS,IAAG;AAC7B,YAAI,MAAM,OAAO,MAAO,YAAW;AACjC,eAAK,WAAW,SAAS;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,mBAAmB,WAAU;AAC3B,eAAO,KAAK,WAAW;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,aAAa,SAAS,UAAS;AAC7B,YAAI,gBAAgB,UAAU,YAAY,UAAU,gBAAgB,UAAU;AAC9E,aAAK,QAAQ,WAAW,YAAY,KAAK,QAAQ,YAAY;AAC7D,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,WAAU;AACrB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,WAAU;AACjB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS,SAAS,MAAK;AACrB,aAAK,QAAQ,OAAO;AACpB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,WAAU;AAClB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,SAAS,OAAM;AACvB,aAAK,QAAQ,QAAQ;AACrB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,gBAAgB,WAAU;AACxB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,gBAAgB,SAAS,aAAY;AACnC,aAAK,QAAQ,cAAe,cAAe,cAAc,KAAK,oBAAoB;AAClF,aAAK,SAAS,cAAc,KAAK,QAAQ;AACzC,aAAK,SAAS,aAAa,cAAc,KAAK,QAAQ,WAAW;AACjE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,WAAU;AACjB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS,SAAS,MAAK;AACrB,aAAK,QAAQ,OAAO;AACpB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,WAAU;AACtB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc,SAAS,WAAU;AAC/B,aAAK,QAAQ,YAAY;AACzB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,WAAU;AAClB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,SAAS,OAAM;AACvB,aAAK,QAAQ,QAAQ;AACrB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,WAAU;AACtB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc,SAAS,WAAU;AAC/B,aAAK,QAAQ,YAAY;AACzB,YAAI,KAAK,WAAa,MAAK,WAAW,QAAQ,YAAY;AAC1D,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,WAAU;AAClB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,SAAS,OAAM;AACvB,aAAK,QAAQ,QAAQ;AACrB,YAAI,KAAK,WAAY,MAAK,WAAW,QAAQ,QAAQ;AACrD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,WAAW,WAAU;AACnB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,WAAW,SAAS,QAAO;AACzB,aAAK,QAAQ,SAAS;AACtB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,WAAW,SAAS,QAAO;AACzB,aAAK,QAAQ,SAAS;AACtB,aAAK,kBAAkB;AAAA,UACrB,aAAa;AAAA,YACX,aAAa,KAAK,QAAQ;AAAA,YAC1B,QAAQ,KAAK,QAAQ;AAAA,UACvB,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,WAAW,WAAU;AACnB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,gBAAgB,SAAS,aAAY;AACnC,aAAK,QAAQ,cAAc;AAC3B,aAAK,kBAAkB;AAAA,UACrB,aAAa;AAAA,YACX,aAAa,KAAK,QAAQ;AAAA,YAC1B,QAAQ,KAAK,QAAQ;AAAA,UACvB,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,iBAAiB,SAAS,OAAM;AAC9B,aAAK,QAAQ,eAAe;AAC5B,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,iBAAiB,WAAU;AACzB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,eAAe,SAAS,OAAM;AAC5B,aAAK,QAAQ,aAAa;AAC1B,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe,WAAU;AACvB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,YAAY,SAAS,OAAM;AACzB,aAAK,QAAQ,UAAU;AACvB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY,WAAU;AACpB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc,SAAS,MAAK;AAC1B,aAAK,QAAQ,YAAY;AACzB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,WAAU;AACtB,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,eAAe,SAAS,UAAS;AAE/B,YAAI,CAAC,KAAK,MAAK;AACb;AAAA,QACF;AACA,aAAK,cAAc;AACnB,YAAI,uBAAuB;AAAA,UACzB,OAAO;AAAA,QACT;AACA,YAAI,gBAAgB,OAAO,CAAC,GAAG,sBAAsB,KAAK,QAAQ,MAAM;AACxE,aAAK,YAAY,IAAI,KAAK,UAAU,OAAO,aAAa;AACxD,YAAI,SAAS,QAAQ;AACnB,eAAK,UACF,UAAU,SAAS,MAAM,EACzB,MAAM,KAAK,IAAI;AAAA,QACpB,WAAW,SAAS,YAAY,SAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,WAAW,SAAS,SAAS,aAAa;AAC7H,eAAK,UACF,UAAU,SAAS,SAAS,WAAW,EACvC,MAAM,KAAK,IAAI;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe,WAAU;AACvB,YAAI,KAAK,WAAU;AACjB,eAAK,UAAU,OAAO;AACtB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,IAAI,SAAS,MAAM,IAAI;AACrB,aAAK,cAAc,GAAG,MAAM,EAAE;AAC9B,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,KAAK,SAAS,MAAM,IAAI;AACtB,aAAK,cAAc,eAAe,MAAM,EAAE;AAC1C,aAAK,aAAa,OAAO;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["arr", "self", "require_suggestions", "result", "ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "once", "xhr", "url", "self", "key", "date", "require_events", "nanoid", "pattern", "require_lib", "container", "self", "config"]}