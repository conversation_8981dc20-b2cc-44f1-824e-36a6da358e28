<!-- client/.app/app/components/auth/StripePaymentForm.vue -->

<template>
  <div class="w-full">
    <div class="pt-8 text-center">
      <BaseHeading
        tag="h2"
        size="3xl"
        weight="medium"
        class="mb-2 text-black dark:text-white"
      >
        {{
          props.plan === "SUPPORTER"
            ? t("landing.crowdfunding.supporter_modal.payment_title")
            : t("auth.subscription.payment_information")
        }}
      </BaseHeading>
      <BaseParagraph class="text-muted-500 dark:text-muted-300 mb-8">
        {{
          props.plan === "SUPPORTER"
            ? t("landing.crowdfunding.supporter_modal.payment_subtitle")
            : t("auth.subscription.payment_information_desc")
        }}
      </BaseParagraph>
    </div>

    <div class="mx-auto max-w-4xl">
      <div class="grid grid-cols-12 gap-6">
        <!-- Payment Summary -->
        <div class="col-span-12 lg:col-span-5">
          <BaseCard rounded="lg" class="p-6 bg-white dark:bg-muted-800">
            <BaseHeading
              as="h4"
              size="sm"
              weight="semibold"
              class="mb-4 text-black dark:text-white"
            >
              {{
                props.plan === "SUPPORTER"
                  ? t("landing.crowdfunding.supporter_modal.order_summary")
                  : t("auth.subscription.order_summary")
              }}
            </BaseHeading>

            <div class="space-y-4">
              <div class="flex justify-between">
                <BaseText
                  size="sm"
                  class="text-muted-500 dark:text-muted-400"
                  >{{ t("auth.subscription.plan") }}</BaseText
                >
                <BaseText size="sm" weight="medium" class="dark:text-white">{{
                  planName
                }}</BaseText>
              </div>

              <div class="flex justify-between">
                <BaseText
                  size="sm"
                  class="text-muted-500 dark:text-muted-400"
                  >{{ t("auth.subscription.billing_cycle") }}</BaseText
                >
                <BaseText size="sm" weight="medium" class="dark:text-white">{{
                  billingCycle === "MONTHLY"
                    ? t("auth.subscription.monthly")
                    : t("auth.subscription.yearly_save")
                }}</BaseText>
              </div>

              <div class="flex justify-between">
                <BaseText
                  size="sm"
                  class="text-muted-500 dark:text-muted-400"
                  >{{ t("auth.subscription.price") }}</BaseText
                >
                <BaseText size="sm" weight="medium" class="dark:text-white"
                  >€{{ price.toFixed(2) }}</BaseText
                >
              </div>

              <div v-if="discount > 0" class="flex justify-between">
                <BaseText
                  size="sm"
                  class="text-muted-500 dark:text-muted-400"
                  >{{ t("auth.subscription.discount") }}</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-success-500"
                  >-€{{ discount.toFixed(2) }}</BaseText
                >
              </div>

              <div
                class="border-t border-muted-200 dark:border-muted-700 pt-4 flex justify-between"
              >
                <BaseText size="md" weight="medium" class="dark:text-white">{{
                  props.plan === "SUPPORTER"
                    ? t("landing.crowdfunding.supporter_modal.total")
                    : t("auth.subscription.total")
                }}</BaseText>
                <BaseText size="md" weight="semibold" class="text-primary-500"
                  >€{{ total.toFixed(2) }}</BaseText
                >
              </div>
            </div>

            <div class="mt-6">
              <BaseParagraph
                size="xs"
                class="text-muted-400 dark:text-muted-300"
              >
                {{ t("auth.subscription.terms_agreement") }}
                <NuxtLink
                  to="/terms"
                  class="text-primary-500 underline-offset-4 hover:underline"
                  >{{ t("auth.subscription.terms_of_service") }}</NuxtLink
                >
                and
                <NuxtLink
                  to="/privacy"
                  class="text-primary-500 underline-offset-4 hover:underline"
                  >{{ t("auth.subscription.privacy_policy") }}</NuxtLink
                >.
              </BaseParagraph>
            </div>
          </BaseCard>
        </div>

        <!-- Payment Form -->
        <div class="col-span-12 lg:col-span-7">
          <BaseCard rounded="lg" class="p-6 bg-white dark:bg-muted-800">
            <BaseHeading
              as="h4"
              size="sm"
              weight="semibold"
              class="mb-4 text-black dark:text-white"
            >
              {{ t("auth.subscription.payment_method") }}
            </BaseHeading>

            <div
              v-if="!paymentReady"
              class="flex flex-col items-center justify-center py-8"
            >
              <BaseLoader size="lg" />
              <div class="mt-4 text-center text-muted-500 dark:text-muted-300">
                <p>{{ t("auth.subscription.loading_payment_form") }}</p>
                <p class="text-xs mt-2">
                  {{ t("auth.subscription.loading_may_take_time") }}
                </p>
              </div>
            </div>

            <div v-else>
              <!-- Credit Card Form -->
              <div class="mb-6">
                <BaseHeading
                  as="h5"
                  size="xs"
                  weight="medium"
                  class="mb-2 text-black dark:text-white"
                >
                  {{
                    props.plan === "SUPPORTER"
                      ? t(
                          "landing.crowdfunding.supporter_modal.card_information"
                        )
                      : t("auth.subscription.card_information")
                  }}
                </BaseHeading>

                <!-- Card Element Container -->
                <div
                  class="border border-muted-300 dark:border-muted-700 rounded-lg p-5 bg-white dark:bg-neutral-900 min-h-[60px] transition-all duration-300 focus-within:border-primary-500 dark:focus-within:border-primary-500 stripe-card-container"
                >
                  <!-- Empty div for Stripe to mount to -->
                  <div id="card-element" style="min-height: 20px"></div>
                </div>

                <!-- Error Display -->
                <div
                  id="card-errors"
                  class="text-danger-500 text-sm mt-2"
                ></div>
              </div>

              <!-- Billing Address -->
              <div class="space-y-4">
                <BaseHeading
                  as="h5"
                  size="xs"
                  weight="medium"
                  class="mb-2 text-black dark:text-white"
                >
                  {{
                    props.plan === "SUPPORTER"
                      ? t(
                          "landing.crowdfunding.supporter_modal.billing_address"
                        )
                      : t("auth.subscription.billing_address")
                  }}
                </BaseHeading>

                <div class="grid grid-cols-2 gap-4">
                  <div class="col-span-2">
                    <BaseField
                      :label="
                        props.plan === 'SUPPORTER'
                          ? t('landing.crowdfunding.supporter_modal.full_name')
                          : t('auth.subscription.full_name')
                      "
                    >
                      <TairoInput
                        v-model="billingAddress.name"
                        icon="lucide:user"
                        placeholder="John Doe"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div class="col-span-2">
                    <BaseField :label="t('auth.subscription.address_line1')">
                      <TairoInput
                        v-model="billingAddress.line1"
                        icon="lucide:map-pin"
                        placeholder="123 Main St"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div class="col-span-2">
                    <BaseField :label="t('auth.subscription.address_line2')">
                      <TairoInput
                        v-model="billingAddress.line2"
                        icon="lucide:building"
                        placeholder="Apt 4B"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div>
                    <BaseField :label="t('auth.subscription.city')">
                      <TairoInput
                        v-model="billingAddress.city"
                        icon="lucide:landmark"
                        placeholder="New York"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div>
                    <BaseField :label="t('auth.subscription.postal_code')">
                      <TairoInput
                        v-model="billingAddress.postalCode"
                        icon="lucide:mail"
                        placeholder="10001"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div>
                    <BaseField :label="t('auth.subscription.state_province')">
                      <TairoInput
                        v-model="billingAddress.state"
                        icon="lucide:map"
                        placeholder="NY"
                        class="dark:bg-neutral-900"
                      />
                    </BaseField>
                  </div>

                  <div>
                    <BaseField
                      :label="
                        props.plan === 'SUPPORTER'
                          ? t('landing.crowdfunding.supporter_modal.country')
                          : t('auth.subscription.country')
                      "
                    >
                      <TairoSelect
                        v-model="billingAddress.country"
                        icon="lucide:globe"
                        :placeholder="
                          props.plan === 'SUPPORTER'
                            ? t(
                                'landing.crowdfunding.supporter_modal.select_country'
                              )
                            : t('auth.subscription.select_country')
                        "
                        required
                        class="dark:bg-neutral-900"
                      >
                        <template
                          v-for="country in countries"
                          :key="country.code"
                        >
                          <BaseSelectItem :value="country.code">
                            {{ country.name }}
                          </BaseSelectItem>
                        </template>
                      </TairoSelect>
                    </BaseField>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>

          <div class="mt-6 flex justify-center">
            <BaseButton
              type="button"
              rounded="md"
              class="!h-11 w-full"
              variant="primary"
              :disabled="!paymentReady || isProcessing"
              :loading="isProcessing"
              @click="processPayment"
            >
              {{
                props.plan === "SUPPORTER"
                  ? t("landing.crowdfunding.supporter_modal.complete_payment")
                  : t("auth.subscription.complete_payment")
              }}
            </BaseButton>
          </div>

          <!-- Powered by Stripe badge -->
          <div class="mt-6 flex justify-center">
            <a
              href="https://stripe.com"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-block"
            >
              <div
                class="flex items-center text-xs text-muted-500 dark:text-muted-400"
              >
                <span class="mr-1">Powered by</span>
                <svg
                  class="h-5"
                  viewBox="0 0 60 25"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M59.64 14.28h-8.06c.19 1.93 1.6 2.55 3.2 2.55 1.64 0 2.96-.37 4.05-.95v3.32a8.33 8.33 0 0 1-4.56 1.1c-4.01 0-6.83-2.5-6.83-7.48 0-4.19 2.39-7.52 6.3-7.52 3.92 0 5.96 3.28 5.96 7.5 0 .4-.04 1.26-.06 1.48zm-5.92-5.62c-1.03 0-2.17.73-2.17 2.58h4.25c0-1.85-1.07-2.58-2.08-2.58zM40.95 20.3c-1.44 0-2.32-.6-2.9-1.04l-.02 4.63-4.12.87V5.57h3.76l.08 1.02a4.7 4.7 0 0 1 3.23-1.29c2.9 0 5.62 2.6 5.62 7.4 0 5.23-2.7 7.6-5.65 7.6zM40 8.95c-.95 0-1.54.34-1.97.81l.02 6.12c.********** 1.95.78 1.52 0 2.54-1.65 2.54-3.87 0-2.15-1.04-3.84-2.54-3.84zM28.24 5.57h4.13v14.44h-4.13V5.57zm0-4.7L32.37 0v3.36l-4.13.88V.88zm-4.32 9.35v9.79H19.8V5.57h3.7l.12 1.22c1-1.77 3.07-1.41 3.62-1.22v3.79c-.52-.17-2.29-.43-3.32.86zm-8.55 4.72c0 2.43 2.6 1.68 3.12 1.46v3.36c-.55.3-1.54.54-2.89.54a4.15 4.15 0 0 1-4.27-4.24l.02-13.17 4.02-.86v3.54h3.14V9.1h-3.14v5.85zm-4.91.7c0 2.97-2.31 4.66-5.73 4.66a11.2 11.2 0 0 1-4.46-.93v-3.93c1.38.75 3.1 1.31 4.46 1.31.92 0 1.53-.24 1.53-1C6.26 13.77 0 14.51 0 9.95 0 7.04 2.28 5.3 5.62 5.3c1.36 0 2.72.2 4.09.75v3.88a9.23 9.23 0 0 0-4.1-1.06c-.86 0-1.44.25-1.44.9 0 1.85 6.29.97 6.29 5.88z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import BaseLoader from "../BaseLoader.vue";
import { countries } from "~/data/countries";

// Initialize i18n
const { t } = useI18n();

// Initialize toast notifications
const toasts = useNuiToasts();

// Helper functions for toaster
const showSuccessToast = (message: string) => {
  toasts.add({
    title: t("common.success"),
    description: message,
    icon: "lucide:check-circle",
    progress: true,
    duration: 8000, // 8 seconds
  });
};

const showErrorToast = (message: string) => {
  toasts.add({
    title: t("common.error"),
    description: message,
    icon: "lucide:alert-triangle",
    progress: true,
    duration: 8000, // 8 seconds
  });
};

// Props
const props = defineProps({
  plan: {
    type: String,
    required: true,
  },
  billingCycle: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  userId: {
    type: Number,
    required: true,
  },
  packageType: {
    type: String,
    required: false,
    default: null,
  },
});

const emit = defineEmits(["payment-success", "payment-error"]);

// Stripe elements
const stripe = ref<any>(null);
const elements = ref<any>(null);
const cardElement = ref<any>(null);
const paymentReady = ref(false);
const isProcessing = ref(false);
const clientSecret = ref<string | null>(null);

// Billing address
const billingAddress = ref({
  name: "",
  line1: "",
  line2: "",
  city: "",
  state: "",
  postalCode: "",
  country: "EE", // Default to Estonia
});

// Computed properties
const planName = computed(() => {
  // Get plan name from translations if available, otherwise use default names
  if (props.packageType) {
    return props.packageType; // Use the package type directly if provided
  }

  switch (props.plan) {
    case "STARTER":
      return t("auth.subscription.starter_plan", "Starter Plan");
    case "PREMIUM":
      return t("auth.subscription.premium_plan", "Premium Plan");
    case "FREE_TRIAL":
      return t("auth.subscription.free_trial", "Free Trial");
    case "SUPPORTER":
      return t(
        "landing.crowdfunding.supporter_modal.investment",
        "Supporter Package"
      );
    default:
      return t("auth.subscription.unknown_plan", "Unknown Plan");
  }
});

const discount = computed(() => {
  // 15% discount for yearly billing
  if (props.billingCycle === "YEARLY") {
    return props.price * 0.15;
  }
  return 0;
});

const total = computed(() => {
  return props.price - discount.value;
});

// Initialize Stripe and create payment intent
onMounted(async () => {
  try {
    // Load Stripe.js
    const stripeJs = await loadStripe();
    console.log("Stripe.js loaded successfully");

    // Initialize Stripe with publishable key from environment
    const stripeKey =
      process.env.NUXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
      "pk_test_51RC6vBGgPsXO5KMAaAcNj1We3YbmdyAbbJ8IaQ02vhu3IyMrIXQrvNX2aSv9FhekUwCIe26ilr1v3EjxgOKfqPHL00F4Z81VVg";
    console.log("Using Stripe key:", stripeKey);

    // Initialize Stripe with the key
    stripe.value = stripeJs.Stripe(stripeKey);
    console.log("Stripe initialized");

    // Create a payment intent
    await createPaymentIntent();

    // Wait for DOM to be ready
    await nextTick();

    // Initialize Elements and mount Card Element
    await initializeElements();
  } catch (error) {
    console.error("Error initializing payment:", error);
    showErrorToast("Failed to initialize payment. Please try again.");
  }
});

// Create a payment intent on the server
const createPaymentIntent = async () => {
  try {
    console.log("Creating payment intent for amount:", props.price);

    // Use fetch directly for simplicity
    const response = await fetch("/api/v1/payment/create-intent", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        amount: props.price,
        currency: "eur",
      }),
      credentials: "include",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error || `Failed with status: ${response.status}`
      );
    }

    const data = await response.json();
    console.log("Payment intent response:", data);

    if (!data.clientSecret) {
      throw new Error("No client secret returned from server");
    }

    clientSecret.value = data.clientSecret;
    console.log("Payment intent created with client secret");
    return true;
  } catch (error) {
    console.error("Error creating payment intent:", error);
    showErrorToast("Failed to prepare payment. Please try again.");
    return false;
  }
};

// Initialize Stripe Elements and mount Card Element
const initializeElements = async () => {
  try {
    // Create Elements instance with options for HTTP/HTTPS
    const elementsOptions =
      window.location.protocol === "http:"
        ? {
            fonts: [
              {
                cssSrc: "https://fonts.googleapis.com/css?family=Roboto",
              },
            ],
            locale: "auto",
          }
        : {};

    elements.value = stripe.value.elements(elementsOptions);

    // Wait for DOM to be fully rendered
    await nextTick();

    // Set payment as ready to show the form
    paymentReady.value = true;

    // Wait for the DOM to update with the card element container
    await nextTick();

    // Mount the Card Element after the container is visible
    setTimeout(() => {
      // Create and mount the Card Element with enhanced options
      const isDarkMode = document.documentElement.classList.contains("dark");
      const cardOptions = {
        style: {
          base: {
            color: isDarkMode ? "#ffffff" : "#32325d",
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: "antialiased",
            fontSize: "16px",
            "::placeholder": {
              color: isDarkMode ? "#aab7c4" : "#aab7c4",
            },
            iconColor: "#5e72e4",
            backgroundColor: isDarkMode ? "#171717" : "#ffffff", // neutral-900 for dark mode
          },
          invalid: {
            color: "#fa755a",
            iconColor: "#fa755a",
          },
        },
        hidePostalCode: true, // We collect this in our own form
      };

      // Add additional options for HTTP mode
      if (window.location.protocol === "http:") {
        Object.assign(cardOptions, {
          // These options help with development mode
          disabled: false,
          disableLink: false,
          showIcon: true,
          iconStyle: "solid",
        });
      }

      cardElement.value = elements.value.create("card", cardOptions);

      // Mount the Card Element
      const cardElementContainer = document.getElementById("card-element");
      if (!cardElementContainer) {
        console.error("Card element container not found, retrying in 500ms");
        setTimeout(initializeElements, 500);
        return;
      }

      cardElement.value.mount("#card-element");
      console.log("Card element mounted successfully");

      // Handle validation errors
      cardElement.value.on(
        "change",
        (event: { error?: { message: string } }) => {
          const displayError = document.getElementById("card-errors");
          if (displayError) {
            if (event.error) {
              displayError.textContent = event.error.message;
            } else {
              displayError.textContent = "";
            }
          }
        }
      );
    }, 500); // Give the DOM time to update
  } catch (error) {
    console.error("Error initializing card element:", error);
    showErrorToast(
      "Failed to load payment form. Please refresh and try again."
    );
  }
};

// Clean up Stripe elements when component is unmounted
onBeforeUnmount(() => {
  if (cardElement.value) {
    console.log("Unmounting card element");
    try {
      cardElement.value.unmount();
    } catch (error) {
      console.error("Error unmounting card element:", error);
    }
    cardElement.value = null;
  }
});

// Load Stripe.js dynamically
const loadStripe = (): Promise<{ Stripe: any }> => {
  return new Promise((resolve, reject) => {
    try {
      // Check if Stripe is already loaded
      if (window.Stripe) {
        console.log("Stripe already loaded, returning existing instance");
        resolve({ Stripe: window.Stripe });
        return;
      }

      console.log("Loading Stripe.js script");
      const script = document.createElement("script");
      script.src = "https://js.stripe.com/v3/";
      script.crossOrigin = "anonymous";

      // Set up success handler
      script.onload = () => {
        console.log("Stripe.js script loaded successfully");
        if (window.Stripe) {
          resolve({ Stripe: window.Stripe });
        } else {
          reject(
            new Error("Stripe.js script loaded but Stripe is not defined")
          );
        }
      };

      // Set up error handler
      script.onerror = (error) => {
        console.error("Failed to load Stripe.js script:", error);
        reject(new Error("Failed to load Stripe.js script"));
      };

      // Add the script to the document
      document.head.appendChild(script);
    } catch (error) {
      console.error("Error in loadStripe function:", error);
      reject(error);
    }
  });
};

// Validate billing address
const validateBillingAddress = () => {
  const required = ["name", "line1", "city", "postalCode", "country"];
  return required.every(
    (field) => billingAddress.value[field as keyof typeof billingAddress.value]
  );
};

// Process payment
const processPayment = async () => {
  // Define a type for our response to handle both fetch and axios responses
  type ApiResponse = {
    ok?: boolean;
    status: number;
    data?: any;
    json?: () => Promise<any>;
  };

  if (!stripe.value || !elements.value || !cardElement.value) {
    showErrorToast(
      t(
        "auth.subscription.payment_processor_error",
        "Payment processor not initialized. Please try again."
      )
    );
    return;
  }

  // Validate billing address
  if (!validateBillingAddress()) {
    showErrorToast(
      t(
        "auth.subscription.billing_fields_required",
        "Please fill in all required billing address fields."
      )
    );
    return;
  }

  isProcessing.value = true;

  try {
    // Create payment method
    const { paymentMethod, error } = await stripe.value.createPaymentMethod({
      type: "card",
      card: cardElement.value,
      billing_details: {
        name: billingAddress.value.name,
        address: {
          line1: billingAddress.value.line1,
          line2: billingAddress.value.line2,
          city: billingAddress.value.city,
          state: billingAddress.value.state,
          postal_code: billingAddress.value.postalCode,
          country: billingAddress.value.country,
        },
      },
    });

    if (error) {
      throw new Error(error.message);
    }

    console.log("Payment method created successfully:", paymentMethod.id);

    // In a real implementation, you would send the payment method ID to your server
    // and create a subscription or charge the customer there
    // For now, we'll simulate a successful API call

    // Determine if this is a supporter payment or regular subscription
    let response;
    let result;

    // Process payment with the server
    if (props.plan === "SUPPORTER" && props.packageType) {
      // Get the authentication token from localStorage
      const userDataStr = localStorage.getItem("user");
      let token = "";

      if (userDataStr) {
        try {
          const userData = JSON.parse(userDataStr);
          token = userData.token || "";
        } catch (e) {
          console.error("Error parsing user data from localStorage:", e);
        }
      }

      // Import axios to make a direct API call with the token
      const axios = await import("axios");

      // Create supporter payment
      try {
        const axiosResponse = await axios.default.post(
          "http://localhost:4004/api/v1/crowdfunding/supporter-payment",
          {
            paymentMethodId: paymentMethod.id,
            userId: props.userId,
            amount: props.price,
            packageType: props.packageType,
            stripePaymentId: paymentMethod.id,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: token ? `Bearer ${token}` : "",
            },
          }
        );

        // Convert axios response to our ApiResponse type
        response = {
          ok: axiosResponse.status >= 200 && axiosResponse.status < 300,
          status: axiosResponse.status,
          data: axiosResponse.data,
        } as ApiResponse;
      } catch (axiosError: any) {
        console.error("Axios error:", axiosError);
        // Create a Response-like object for compatibility with the rest of the code
        response = {
          ok: false,
          status: axiosError.response?.status || 500,
          data: axiosError.response?.data || { message: axiosError.message },
        } as ApiResponse;
      }
    } else {
      // Regular subscription payment
      response = await fetch("/api/v1/payment/confirm", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // We don't need to add the token here since this endpoint is public
        },
        body: JSON.stringify({
          paymentMethodId: paymentMethod.id,
          plan: props.plan,
          billingCycle: props.billingCycle,
          userId: props.userId,
        }),
        credentials: "include",
      });
    }

    // Use the ApiResponse type defined above

    // Check if the response is ok
    if (
      (response as ApiResponse).ok === false ||
      (response.status && response.status >= 400)
    ) {
      let errorMessage = `Request failed with status ${response.status}`;

      try {
        let errorData;
        if (typeof (response as any).json === "function") {
          // Fetch response
          errorData = await (response as Response).json();
        } else if ((response as ApiResponse).data) {
          // Axios response
          errorData = (response as ApiResponse).data;
        }

        console.error("Payment API error:", errorData);

        // Handle specific error cases
        if (response.status === 401) {
          errorMessage = "Authentication failed. Please try logging in again.";
        } else if (errorData && errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        console.error("Error parsing error response:", e);
      }

      throw new Error(errorMessage);
    }

    // Parse the response - we don't use this yet but might need it in the future
    let responseData;
    try {
      if (typeof (response as any).json === "function") {
        // Fetch response
        responseData = await (response as Response).json();
      } else if ((response as ApiResponse).data) {
        // Axios response
        responseData = (response as ApiResponse).data;
      }
      console.log("Payment API response:", responseData);
    } catch (e) {
      console.log("No JSON response body or already consumed");
    }

    // Prepare result based on payment type
    if (props.plan === "SUPPORTER") {
      // Supporter payment result
      result = {
        success: true,
        paymentId: "pay_" + Math.random().toString(36).substring(2, 15),
        packageType: props.packageType,
        amount: props.price,
        status: "succeeded",
      };
    } else {
      // Regular subscription result
      result = {
        success: true,
        subscriptionId: "sub_" + Math.random().toString(36).substring(2, 15),
        plan: props.plan,
        billingCycle: props.billingCycle,
        status: "active",
        currentPeriodEnd: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      };
    }

    // Handle successful payment
    if (props.plan === "SUPPORTER") {
      showSuccessToast(
        t(
          "landing.crowdfunding.supporter_modal.payment_success",
          "Payment successful! Thank you for your support."
        )
      );
    } else {
      showSuccessToast(
        t(
          "auth.subscription.payment_success",
          "Payment successful! Your subscription is now active."
        )
      );
    }
    emit("payment-success", result);
  } catch (error: any) {
    console.error("Payment error:", error);
    showErrorToast(
      error.message ||
        t(
          "auth.subscription.payment_failed",
          "Payment failed. Please try again."
        )
    );
    emit("payment-error", error);
  } finally {
    isProcessing.value = false;
  }
};
</script>

<script lang="ts">
// No global declarations needed here
</script>

<style>
/* Stripe card element styling for dark mode */
.dark .StripeElement--focus {
  border-color: #5e72e4;
}

.dark .StripeElement--webkit-autofill {
  background-color: #171717 !important; /* neutral-900 */
}

/* Ensure card numbers have better contrast in dark mode */
.dark #card-element {
  color: white !important;
}

/* Ensure the card element container has proper styling */
.dark #card-element iframe {
  background-color: transparent !important;
}

/* Ensure the card element has proper styling */
.dark .StripeElement {
  background-color: #171717 !important; /* neutral-900 */
}

/* Fix for the card element background */
.dark .stripe-card-container .StripeElement {
  background-color: #171717 !important; /* neutral-900 */
}

/* Fix for the card element iframe background */
.dark .stripe-card-container iframe {
  background-color: #171717 !important; /* neutral-900 */
}

/* Fix for any internal elements */
.dark .stripe-card-container * {
  background-color: #171717 !important; /* neutral-900 */
}

/* Fix for the iframe background */
.dark iframe[name^="__privateStripeFrame"] {
  background-color: #171717 !important; /* neutral-900 */
}

/* Consistent placeholder color */
.dark ::placeholder {
  color: #9ca3af !important; /* text-gray-400 */
}

/* Light mode placeholder color */
::placeholder {
  color: #6b7280 !important; /* text-gray-500 */
}
</style>
