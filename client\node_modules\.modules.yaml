hoistPattern:
  - '*'
hoistedDependencies:
  .app:
    app: private
  .demo:
    demo: private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.3':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.27.4)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.4':
    '@babel/helpers': private
  '@babel/parser@7.27.4':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.4(@babel/core@7.27.4)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.27.2(@babel/core@7.27.4)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    '@babel/preset-modules': private
  '@babel/runtime@7.27.4':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.3':
    '@babel/types': private
  '@capsizecss/metrics@3.5.0':
    '@capsizecss/metrics': private
  '@capsizecss/unpack@2.4.0':
    '@capsizecss/unpack': private
  '@clack/core@0.4.2':
    '@clack/core': private
  '@clack/prompts@0.10.1':
    '@clack/prompts': private
  '@cloudflare/kv-asset-handler@0.4.0':
    '@cloudflare/kv-asset-handler': private
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@dependents/detective-less@5.0.1':
    '@dependents/detective-less': private
  '@dprint/formatter@0.3.0':
    '@dprint/formatter': private
  '@dprint/markdown@0.17.8':
    '@dprint/markdown': private
  '@dprint/toml@0.6.4':
    '@dprint/toml': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-plugin-eslint-comments@4.5.0(eslint@9.24.0(jiti@2.4.2))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.24.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.2.9(eslint@9.24.0(jiti@2.4.2))':
    '@eslint/compat': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': private
  '@eslint/core@0.12.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.24.0':
    '@eslint/js': private
  '@eslint/markdown@6.4.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': private
  '@fastify/accept-negotiator@1.1.0':
    '@fastify/accept-negotiator': private
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@floating-ui/vue@1.1.6(vue@3.5.16(typescript@5.8.3))':
    '@floating-ui/vue': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify-json/fa6-brands@1.2.5':
    '@iconify-json/fa6-brands': private
  '@iconify-json/ph@1.2.2':
    '@iconify-json/ph': private
  '@iconify-json/simple-icons@1.2.37':
    '@iconify-json/simple-icons': private
  '@iconify-json/solar@1.2.2':
    '@iconify-json/solar': private
  '@iconify/collections@1.0.555':
    '@iconify/collections': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@iconify/vue@5.0.0(vue@3.5.16(typescript@5.8.3))':
    '@iconify/vue': private
  '@internationalized/date@3.8.1':
    '@internationalized/date': private
  '@internationalized/number@3.6.2':
    '@internationalized/number': private
  '@intlify/bundle-utils@10.0.1(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))':
    '@intlify/bundle-utils': private
  '@intlify/core-base@10.0.7':
    '@intlify/core-base': private
  '@intlify/core@10.0.7':
    '@intlify/core': private
  '@intlify/h3@0.6.1':
    '@intlify/h3': private
  '@intlify/message-compiler@11.1.5':
    '@intlify/message-compiler': private
  '@intlify/shared@10.0.7':
    '@intlify/shared': private
  '@intlify/unplugin-vue-i18n@6.0.8(@vue/compiler-dom@3.5.16)(eslint@9.24.0(jiti@2.4.2))(rollup@4.41.1)(typescript@5.8.3)(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3))':
    '@intlify/unplugin-vue-i18n': private
  '@intlify/utils@0.13.0':
    '@intlify/utils': private
  '@intlify/vue-i18n-extensions@8.0.0(@intlify/shared@11.1.5)(@vue/compiler-dom@3.5.16)(vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3))':
    '@intlify/vue-i18n-extensions': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@kwsites/file-exists@1.1.1':
    '@kwsites/file-exists': private
  '@kwsites/promise-deferred@1.1.1':
    '@kwsites/promise-deferred': private
  '@mapbox/jsonlint-lines-primitives@2.0.2':
    '@mapbox/jsonlint-lines-primitives': private
  '@mapbox/mapbox-gl-supported@3.0.0':
    '@mapbox/mapbox-gl-supported': private
  '@mapbox/node-pre-gyp@2.0.0':
    '@mapbox/node-pre-gyp': private
  '@mapbox/point-geometry@0.1.0':
    '@mapbox/point-geometry': private
  '@mapbox/tiny-sdf@2.0.6':
    '@mapbox/tiny-sdf': private
  '@mapbox/unitbezier@0.0.1':
    '@mapbox/unitbezier': private
  '@mapbox/vector-tile@1.3.1':
    '@mapbox/vector-tile': private
  '@mapbox/whoots-js@3.1.0':
    '@mapbox/whoots-js': private
  '@miyaneee/rollup-plugin-json5@1.2.0(rollup@4.41.1)':
    '@miyaneee/rollup-plugin-json5': private
  '@netlify/binary-info@1.0.0':
    '@netlify/binary-info': private
  '@netlify/blobs@9.1.2':
    '@netlify/blobs': private
  '@netlify/dev-utils@2.2.0':
    '@netlify/dev-utils': private
  '@netlify/functions@3.1.10(rollup@4.41.1)':
    '@netlify/functions': private
  '@netlify/open-api@2.37.0':
    '@netlify/open-api': private
  '@netlify/runtime-utils@1.3.1':
    '@netlify/runtime-utils': private
  '@netlify/serverless-functions-api@1.41.2':
    '@netlify/serverless-functions-api': private
  '@netlify/zip-it-and-ship-it@12.1.1(rollup@4.41.1)':
    '@netlify/zip-it-and-ship-it': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nuxt/cli@3.25.1(magicast@0.3.5)':
    '@nuxt/cli': private
  '@nuxt/content@3.4.0(magicast@0.3.5)(typescript@5.8.3)':
    '@nuxt/content': private
  '@nuxt/devalue@2.0.2':
    '@nuxt/devalue': private
  '@nuxt/devtools-kit@2.4.1(magicast@0.3.5)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))':
    '@nuxt/devtools-kit': private
  '@nuxt/devtools-wizard@2.4.1':
    '@nuxt/devtools-wizard': private
  '@nuxt/devtools@2.4.1(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@nuxt/devtools': private
  '@nuxt/fonts@0.11.4(db0@0.3.2(better-sqlite3@11.10.0))(ioredis@5.6.1)(magicast@0.3.5)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))':
    '@nuxt/fonts': private
  '@nuxt/icon@1.13.0(magicast@0.3.5)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@nuxt/icon': private
  '@nuxt/image@1.10.0(db0@0.3.2(better-sqlite3@11.10.0))(ioredis@5.6.1)(magicast@0.3.5)':
    '@nuxt/image': private
  '@nuxt/kit@3.17.4(magicast@0.3.5)':
    '@nuxt/kit': private
  '@nuxt/schema@3.16.2':
    '@nuxt/schema': private
  '@nuxt/telemetry@2.6.6(magicast@0.3.5)':
    '@nuxt/telemetry': private
  '@nuxt/vite-builder@3.16.2(@types/node@22.15.29)(eslint@9.24.0(jiti@2.4.2))(lightningcss@1.30.1)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.41.1)(terser@5.40.0)(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3))(yaml@2.8.0)':
    '@nuxt/vite-builder': private
  '@nuxtjs/color-mode@3.5.2(magicast@0.3.5)':
    '@nuxtjs/color-mode': private
  '@nuxtjs/i18n@9.5.5(@vue/compiler-dom@3.5.16)(eslint@9.24.0(jiti@2.4.2))(magicast@0.3.5)(rollup@4.41.1)(vue@3.5.16(typescript@5.8.3))':
    '@nuxtjs/i18n': private
  '@nuxtjs/mdc@0.16.1(magicast@0.3.5)':
    '@nuxtjs/mdc': private
  '@oxc-parser/binding-darwin-arm64@0.56.5':
    '@oxc-parser/binding-darwin-arm64': private
  '@oxc-parser/binding-darwin-x64@0.56.5':
    '@oxc-parser/binding-darwin-x64': private
  '@oxc-parser/binding-freebsd-x64@0.70.0':
    '@oxc-parser/binding-freebsd-x64': private
  '@oxc-parser/binding-linux-arm-gnueabihf@0.56.5':
    '@oxc-parser/binding-linux-arm-gnueabihf': private
  '@oxc-parser/binding-linux-arm-musleabihf@0.70.0':
    '@oxc-parser/binding-linux-arm-musleabihf': private
  '@oxc-parser/binding-linux-arm64-gnu@0.56.5':
    '@oxc-parser/binding-linux-arm64-gnu': private
  '@oxc-parser/binding-linux-arm64-musl@0.56.5':
    '@oxc-parser/binding-linux-arm64-musl': private
  '@oxc-parser/binding-linux-riscv64-gnu@0.70.0':
    '@oxc-parser/binding-linux-riscv64-gnu': private
  '@oxc-parser/binding-linux-s390x-gnu@0.70.0':
    '@oxc-parser/binding-linux-s390x-gnu': private
  '@oxc-parser/binding-linux-x64-gnu@0.56.5':
    '@oxc-parser/binding-linux-x64-gnu': private
  '@oxc-parser/binding-linux-x64-musl@0.56.5':
    '@oxc-parser/binding-linux-x64-musl': private
  '@oxc-parser/binding-wasm32-wasi@0.56.5':
    '@oxc-parser/binding-wasm32-wasi': private
  '@oxc-parser/binding-win32-arm64-msvc@0.56.5':
    '@oxc-parser/binding-win32-arm64-msvc': private
  '@oxc-parser/binding-win32-x64-msvc@0.56.5':
    '@oxc-parser/binding-win32-x64-msvc': private
  '@oxc-parser/wasm@0.60.0':
    '@oxc-parser/wasm': private
  '@oxc-project/types@0.60.0':
    '@oxc-project/types': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-wasm@2.5.1':
    '@parcel/watcher-wasm': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pinia/nuxt@0.11.0(magicast@0.3.5)(pinia@3.0.2(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3)))':
    '@pinia/nuxt': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.1.2':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@poppinss/colors@4.1.4':
    '@poppinss/colors': private
  '@poppinss/dumper@0.6.3':
    '@poppinss/dumper': private
  '@poppinss/exception@1.2.1':
    '@poppinss/exception': private
  '@rolldown/pluginutils@1.0.0-beta.10':
    '@rolldown/pluginutils': private
  '@rollup/plugin-alias@5.1.1(rollup@4.41.1)':
    '@rollup/plugin-alias': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.27.4)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-commonjs@28.0.3(rollup@4.41.1)':
    '@rollup/plugin-commonjs': private
  '@rollup/plugin-inject@5.0.5(rollup@4.41.1)':
    '@rollup/plugin-inject': private
  '@rollup/plugin-json@6.1.0(rollup@4.41.1)':
    '@rollup/plugin-json': private
  '@rollup/plugin-node-resolve@16.0.1(rollup@4.41.1)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@6.0.2(rollup@4.41.1)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@4.41.1)':
    '@rollup/plugin-terser': private
  '@rollup/plugin-yaml@4.1.2(rollup@4.41.1)':
    '@rollup/plugin-yaml': private
  '@rollup/pluginutils@5.1.4(rollup@4.41.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@shikijs/core@3.2.1':
    '@shikijs/core': private
  '@shikijs/engine-javascript@3.2.1':
    '@shikijs/engine-javascript': private
  '@shikijs/engine-oniguruma@3.2.1':
    '@shikijs/engine-oniguruma': private
  '@shikijs/langs@3.4.2':
    '@shikijs/langs': private
  '@shikijs/themes@3.2.1':
    '@shikijs/themes': private
  '@shikijs/transformers@3.4.2':
    '@shikijs/transformers': private
  '@shikijs/types@3.4.2':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@shuriken-ui/nuxt@4.0.0-beta.4(magicast@0.3.5)(typescript@5.8.3)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@shuriken-ui/nuxt': private
  '@sindresorhus/is@7.0.1':
    '@sindresorhus/is': private
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@speed-highlight/core@1.2.7':
    '@speed-highlight/core': private
  '@sqlite.org/sqlite-wasm@3.49.1-build2':
    '@sqlite.org/sqlite-wasm': private
  '@stripe/stripe-js@2.4.0':
    '@stripe/stripe-js': private
  '@stylistic/eslint-plugin@4.4.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@stylistic/eslint-plugin': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.8':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.8':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.8':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.8':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.8':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.8':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.8':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.8':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.8':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.8':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.8':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.8':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.8':
    '@tailwindcss/oxide': private
  '@tailwindcss/typography@0.5.16(tailwindcss@4.1.8)':
    '@tailwindcss/typography': private
  '@tailwindcss/vite@4.1.8(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))':
    '@tailwindcss/vite': private
  '@tanstack/virtual-core@3.13.9':
    '@tanstack/virtual-core': private
  '@tanstack/vue-virtual@3.13.9(vue@3.5.16(typescript@5.8.3))':
    '@tanstack/vue-virtual': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/geojson-vt@3.2.5':
    '@types/geojson-vt': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash@4.17.17':
    '@types/lodash': private
  '@types/mapbox__point-geometry@0.1.4':
    '@types/mapbox__point-geometry': private
  '@types/mapbox__vector-tile@1.3.4':
    '@types/mapbox__vector-tile': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node@22.15.29':
    '@types/node': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/parse-path@7.1.0':
    '@types/parse-path': private
  '@types/pbf@3.0.5':
    '@types/pbf': private
  '@types/resize-observer-browser@0.1.11':
    '@types/resize-observer-browser': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/supercluster@7.1.3':
    '@types/supercluster': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.33.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.33.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.33.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.33.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unhead/vue@2.0.10(vue@3.5.16(typescript@5.8.3))':
    '@unhead/vue': private
  '@unrs/resolver-binding-darwin-arm64@1.7.8':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.7.8':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.7.8':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.7.8':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.7.8':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.7.8':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.7.8':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.7.8':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vee-validate/zod@4.15.0(vue@3.5.16(typescript@5.8.3))(zod@3.25.48)':
    '@vee-validate/zod': private
  '@vercel/nft@0.29.4(rollup@4.41.1)':
    '@vercel/nft': private
  '@vite-pwa/nuxt@1.0.3(magicast@0.3.5)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(workbox-build@7.3.0)(workbox-window@7.3.0)':
    '@vite-pwa/nuxt': private
  '@vitejs/plugin-vue-jsx@4.2.0(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vitejs/plugin-vue-jsx': private
  '@vitejs/plugin-vue@5.2.4(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vitejs/plugin-vue': private
  '@vitest/eslint-plugin@1.2.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@vitest/eslint-plugin': private
  '@volar/language-core@2.4.14':
    '@volar/language-core': private
  '@volar/source-map@2.4.14':
    '@volar/source-map': private
  '@volar/typescript@2.4.14':
    '@volar/typescript': private
  '@vue-macros/common@1.16.1(vue@3.5.16(typescript@5.8.3))':
    '@vue-macros/common': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.4)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.4)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.6':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.6(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.6':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.6':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.8(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  '@vueuse/core@13.3.0(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/core': private
  '@vueuse/metadata@13.3.0':
    '@vueuse/metadata': private
  '@vueuse/nuxt@13.3.0(magicast@0.3.5)(nuxt@3.16.2(@parcel/watcher@2.5.1)(@types/node@22.15.29)(better-sqlite3@11.10.0)(db0@0.3.2(better-sqlite3@11.10.0))(eslint@9.24.0(jiti@2.4.2))(ioredis@5.6.1)(lightningcss@1.30.1)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.41.1)(terser@5.40.0)(typescript@5.8.3)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/nuxt': private
  '@vueuse/shared@13.3.0(vue@3.5.16(typescript@5.8.3))':
    '@vueuse/shared': private
  '@webcontainer/env@1.1.1':
    '@webcontainer/env': private
  '@whatwg-node/disposablestack@0.0.6':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/fetch@0.10.8':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.21':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.2':
    '@whatwg-node/promise-helpers': private
  '@whatwg-node/server@0.9.71':
    '@whatwg-node/server': private
  '@yr/monotone-cubic-spline@1.0.3':
    '@yr/monotone-cubic-spline': private
  '@zxcvbn-ts/core@3.0.4':
    '@zxcvbn-ts/core': private
  '@zxcvbn-ts/language-common@3.0.4':
    '@zxcvbn-ts/language-common': private
  '@zxcvbn-ts/language-en@3.0.2':
    '@zxcvbn-ts/language-en': private
  abbrev@3.0.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  alien-signals@1.0.13:
    alien-signals: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@3.2.1:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  apexcharts@3.54.1:
    apexcharts: private
  archiver-utils@5.0.2:
    archiver-utils: private
  archiver@7.0.1:
    archiver: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-kit@1.4.3:
    ast-kit: private
  ast-module-types@6.0.1:
    ast-module-types: private
  ast-walker-scope@0.6.2:
    ast-walker-scope: private
  async-function@1.0.0:
    async-function: private
  async-sema@3.1.1:
    async-sema: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  autoprefixer@10.4.21(postcss@8.5.4):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@1.9.0(debug@4.4.1):
    axios: private
  b4a@1.6.7:
    b4a: private
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.27.4):
    babel-plugin-polyfill-regenerator: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.1.5:
    bare-fs: private
  bare-os@3.6.1:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  better-sqlite3@11.10.0:
    better-sqlite3: private
  bindings@1.5.0:
    bindings: private
  birpc@2.3.0:
    birpc: private
  bl@4.1.0:
    bl: private
  blob-to-buffer@1.2.9:
    blob-to-buffer: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  brotli@1.3.3:
    brotli: private
  browserslist@4.25.0:
    browserslist: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  builtin-modules@5.0.0:
    builtin-modules: private
  bundle-name@4.1.0:
    bundle-name: private
  c12@3.0.4(magicast@0.3.5):
    c12: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsite@1.0.0:
    callsite: private
  callsites@3.1.0:
    callsites: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  cheap-ruler@4.0.0:
    cheap-ruler: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  ci-info@4.2.0:
    ci-info: private
  citty@0.1.6:
    citty: private
  clean-regexp@1.0.0:
    clean-regexp: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  clipboardy@4.0.0:
    clipboardy: private
  cliui@8.0.1:
    cliui: private
  clone@2.1.2:
    clone: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@13.1.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  common-path-prefix@3.0.0:
    common-path-prefix: private
  common-tags@1.8.2:
    common-tags: private
  commondir@1.0.1:
    commondir: private
  compatx@0.1.8:
    compatx: private
  compress-commons@6.0.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-es@2.0.0:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  copy-anything@3.0.5:
    copy-anything: private
  copy-file@11.0.0:
    copy-file: private
  core-js-compat@3.42.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  country-flag-emoji@1.0.3:
    country-flag-emoji: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  create-require@1.1.1:
    create-require: private
  cron-parser@4.9.0:
    cron-parser: private
  croner@9.0.0:
    croner: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crossws@0.3.5:
    crossws: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-declaration-sorter@7.2.0(postcss@8.5.4):
    css-declaration-sorter: private
  css-select@5.1.0:
    css-select: private
  css-tree@3.1.0:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  csscolorparser@1.0.3:
    csscolorparser: private
  cssesc@3.0.0:
    cssesc: private
  cssfilter@0.0.10:
    cssfilter: private
  cssnano-preset-default@7.0.7(postcss@8.5.4):
    cssnano-preset-default: private
  cssnano-utils@5.0.1(postcss@8.5.4):
    cssnano-utils: private
  cssnano@7.0.7(postcss@8.5.4):
    cssnano: private
  csso@5.0.5:
    csso: private
  csstype@3.1.3:
    csstype: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  date-fns-tz@2.0.1(date-fns@2.30.0):
    date-fns-tz: private
  date-fns@2.30.0:
    date-fns: private
  db0@0.3.2(better-sqlite3@11.10.0):
    db0: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1:
    debug: private
  decache@4.6.2:
    decache: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deep-pick-omit@1.2.1:
    deep-pick-omit: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detab@3.0.2:
    detab: private
  detect-libc@1.0.3:
    detect-libc: private
  detective-amd@6.0.1:
    detective-amd: private
  detective-cjs@6.0.1:
    detective-cjs: private
  detective-es6@5.0.1:
    detective-es6: private
  detective-postcss@7.0.1(postcss@8.5.4):
    detective-postcss: private
  detective-sass@6.0.1:
    detective-sass: private
  detective-scss@5.0.1:
    detective-scss: private
  detective-stylus@5.0.1:
    detective-stylus: private
  detective-typescript@14.0.0(typescript@5.8.3):
    detective-typescript: private
  detective-vue2@2.2.0(typescript@5.8.3):
    detective-vue2: private
  devalue@5.1.1:
    devalue: private
  devlop@1.1.0:
    devlop: private
  dfa@1.2.0:
    dfa: private
  diff@4.0.2:
    diff: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-prop@9.0.0:
    dot-prop: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  earcut@3.0.1:
    earcut: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  emoji-regex@10.4.0:
    emoji-regex: private
  emojilib@2.4.0:
    emojilib: private
  emoticon@4.1.0:
    emoticon: private
  enabled@2.0.0:
    enabled: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.4:
    end-of-stream: private
  engine.io-client@6.6.3:
    engine.io-client: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@3.0.0:
    env-paths: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  errx@0.1.0:
    errx: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-compat-utils@0.6.5(eslint@9.24.0(jiti@2.4.2)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.0:
    eslint-flat-config-utils: private
  eslint-formatting-reporter@0.0.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-formatting-reporter: private
  eslint-import-context@0.1.6(unrs-resolver@1.7.8):
    eslint-import-context: private
  eslint-json-compat-utils@0.2.1(eslint@9.24.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-merge-processors: private
  eslint-parser-plain@0.1.1:
    eslint-parser-plain: private
  eslint-plugin-antfu@3.1.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-antfu: private
  eslint-plugin-command@3.2.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-es-x: private
  eslint-plugin-import-x@4.15.0(@typescript-eslint/utils@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.7.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.19.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.13.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-perfectionist: private
  eslint-plugin-pnpm@0.3.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-pnpm: private
  eslint-plugin-regexp@2.7.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-toml@0.12.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@59.0.1(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.33.0(@typescript-eslint/parser@8.33.0(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@10.1.0(eslint@9.24.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.24.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-plugin-yml@1.18.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.16)(eslint@9.24.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@8.0.1:
    execa: private
  expand-template@2.0.3:
    expand-template: private
  exsolve@1.0.5:
    exsolve: private
  extend@3.0.2:
    extend: private
  externality@1.0.2:
    externality: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-npm-meta@0.4.3:
    fast-npm-meta: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  fecha@4.2.3:
    fecha: private
  fetch-blob@3.2.0:
    fetch-blob: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@6.1.0:
    filter-obj: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flat@6.0.1:
    flat: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  fontaine@0.6.0:
    fontaine: private
  fontkit@2.0.4:
    fontkit: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.2:
    form-data: private
  format@0.2.2:
    format: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@2.0.0:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@9.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  fuse.js@7.1.0:
    fuse.js: private
  gensync@1.0.0-beta.2:
    gensync: private
  geojson-vt@4.0.2:
    geojson-vt: private
  get-amd-module-type@6.0.1:
    get-amd-module-type: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-port-please@3.1.2:
    get-port-please: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  git-up@8.1.1:
    git-up: private
  git-url-parse@16.1.0:
    git-url-parse: private
  github-from-package@0.0.0:
    github-from-package: private
  github-slugger@2.0.0:
    github-slugger: private
  gl-matrix@3.4.3:
    gl-matrix: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.2:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  globals@16.2.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@14.1.0:
    globby: private
  gonzales-pe@4.3.0:
    gonzales-pe: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  grid-index@1.1.0:
    grid-index: private
  gzip-size@7.0.0:
    gzip-size: private
  h3@1.15.3:
    h3: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@3.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-embedded@3.0.0:
    hast-util-embedded: private
  hast-util-format@1.1.0:
    hast-util-format: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-has-property@3.0.0:
    hast-util-has-property: private
  hast-util-heading-rank@3.0.0:
    hast-util-heading-rank: private
  hast-util-is-body-ok-link@3.0.1:
    hast-util-is-body-ok-link: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-minify-whitespace@1.0.1:
    hast-util-minify-whitespace: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-phrasing@3.0.1:
    hast-util-phrasing: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-mdast@10.1.2:
    hast-util-to-mdast: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-to-string@3.0.1:
    hast-util-to-string: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  html-void-elements@3.0.0:
    html-void-elements: private
  html-whitespace-sensitive-tag-names@3.0.1:
    html-whitespace-sensitive-tag-names: private
  http-errors@2.0.0:
    http-errors: private
  http-shutdown@1.2.2:
    http-shutdown: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  httpxy@0.1.7:
    httpxy: private
  human-signals@5.0.0:
    human-signals: private
  idb@7.1.1:
    idb: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-meta@0.2.1:
    image-meta: private
  import-fresh@3.3.1:
    import-fresh: private
  impound@0.2.2(rollup@4.41.1):
    impound: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  index-to-position@1.1.0:
    index-to-position: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@4.1.1:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  ioredis@5.6.1:
    ioredis: private
  ipx@2.1.0(db0@0.3.2(better-sqlite3@11.10.0))(ioredis@5.6.1):
    ipx: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-absolute-url@4.0.1:
    is-absolute-url: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-installed-globally@1.0.0:
    is-installed-globally: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-path-inside@4.0.0:
    is-path-inside: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-ssh@1.4.1:
    is-ssh: private
  is-stream@3.0.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-url-superb@4.0.0:
    is-url-superb: private
  is-url@1.2.4:
    is-url: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  is64bit@2.0.0:
    is64bit: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@4.1.1:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jiti@2.4.2:
    jiti: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonpointer@5.0.1:
    jsonpointer: private
  junk@4.0.1:
    junk: private
  jwt-decode@4.0.0:
    jwt-decode: private
  kdbush@4.0.2:
    kdbush: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  klona@2.0.6:
    klona: private
  knitwork@1.2.0:
    knitwork: private
  kolorist@1.8.0:
    kolorist: private
  kuler@2.0.0:
    kuler: private
  lambda-local@2.2.0:
    lambda-local: private
  launch-editor@2.10.0:
    launch-editor: private
  layers/accounting:
    '@comanager/accounting-layer': private
  layers/budget:
    budget-layer: private
  layers/communication:
    '@comanager/communication-layer': private
  layers/companies:
    '@comanager/companies-layer': private
  layers/core:
    core-layer: private
  layers/hr:
    '@comanager/hr-layer': private
  layers/production:
    '@comanager/production-layer': private
  layers/recruitment:
    '@comanager/recruitment-layer': private
  layers/sales:
    '@comanager/sales-layer': private
  layers/tairo:
    '@cssninja/tairo': private
  layers/timemanagement:
    '@comanager/timemanagement-layer': private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  listhen@1.9.0:
    listhen: private
  listr2@8.3.3:
    listr2: private
  load-json-file@4.0.0:
    load-json-file: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-update@6.1.0:
    log-update: private
  logform@2.7.0:
    logform: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@10.4.3:
    lru-cache: private
  luxon@3.6.1:
    luxon: private
  magic-regexp@0.10.0:
    magic-regexp: private
  magic-string-ast@0.7.1:
    magic-string-ast: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  make-error@1.3.6:
    make-error: private
  mapbox-gl@3.12.0:
    mapbox-gl: private
  markdown-table@3.0.4:
    markdown-table: private
  martinez-polygon-clipping@0.7.4:
    martinez-polygon-clipping: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@2.0.1:
    mdast-util-frontmatter: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  memorystream@0.3.1:
    memorystream: private
  merge-options@3.0.4:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micro-api-client@3.3.0:
    micro-api-client: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@2.0.0:
    micromark-extension-frontmatter: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@4.0.7:
    mime: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  mimic-response@3.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@10.0.1:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@3.0.1:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  mocked-exports@0.1.1:
    mocked-exports: private
  module-definition@6.0.1:
    module-definition: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  murmurhash-js@1.0.0:
    murmurhash-js: private
  nanoid@3.3.11:
    nanoid: private
  nanotar@0.2.0:
    nanotar: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  napi-postinstall@0.2.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  netlify@13.3.5:
    netlify: private
  nice-try@1.0.5:
    nice-try: private
  nitropack@2.11.12(better-sqlite3@11.10.0):
    nitropack: private
  node-abi@3.75.0:
    node-abi: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-domexception@1.0.0:
    node-domexception: private
  node-emoji@2.2.0:
    node-emoji: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-mock-http@1.0.0:
    node-mock-http: private
  node-releases@2.0.19:
    node-releases: private
  node-source-walk@7.0.1:
    node-source-walk: private
  nopt@8.1.0:
    nopt: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nuxt-component-meta@0.10.1(magicast@0.3.5):
    nuxt-component-meta: private
  nuxt@3.16.2(@parcel/watcher@2.5.1)(@types/node@22.15.29)(better-sqlite3@11.10.0)(db0@0.3.2(better-sqlite3@11.10.0))(eslint@9.24.0(jiti@2.4.2))(ioredis@5.6.1)(lightningcss@1.30.1)(magicast@0.3.5)(optionator@0.9.4)(rollup@4.41.1)(terser@5.40.0)(typescript@5.8.3)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(yaml@2.8.0):
    nuxt: private
  nypm@0.6.0:
    nypm: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  on-change@5.0.1:
    on-change: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@6.0.0:
    onetime: private
  oniguruma-parser@0.12.1:
    oniguruma-parser: private
  oniguruma-to-es@4.3.3:
    oniguruma-to-es: private
  open@8.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  oxc-parser@0.70.0:
    oxc-parser: private
  p-event@6.0.1:
    p-event: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@7.0.3:
    p-map: private
  p-timeout@6.1.4:
    p-timeout: private
  p-wait-for@5.0.2:
    p-wait-for: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@4.0.0:
    parse-json: private
  parse-path@7.1.0:
    parse-path: private
  parse-statements@1.0.11:
    parse-statements: private
  parse-url@9.2.0:
    parse-url: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@2.0.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-type@6.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pbf@3.3.0:
    pbf: private
  pend@1.2.0:
    pend: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@3.0.0:
    pify: private
  pinia-plugin-persistedstate@4.3.0(@pinia/nuxt@0.11.0(magicast@0.3.5)(pinia@3.0.2(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3))))(magicast@0.3.5)(pinia@3.0.2(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3))):
    pinia-plugin-persistedstate: private
  pinia@3.0.2(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3)):
    pinia: private
  pkg-types@2.1.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  pnpm-workspace-yaml@0.3.1:
    pnpm-workspace-yaml: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-calc@10.1.1(postcss@8.5.4):
    postcss-calc: private
  postcss-colormin@7.0.3(postcss@8.5.4):
    postcss-colormin: private
  postcss-convert-values@7.0.5(postcss@8.5.4):
    postcss-convert-values: private
  postcss-discard-comments@7.0.4(postcss@8.5.4):
    postcss-discard-comments: private
  postcss-discard-duplicates@7.0.2(postcss@8.5.4):
    postcss-discard-duplicates: private
  postcss-discard-empty@7.0.1(postcss@8.5.4):
    postcss-discard-empty: private
  postcss-discard-overridden@7.0.1(postcss@8.5.4):
    postcss-discard-overridden: private
  postcss-merge-longhand@7.0.5(postcss@8.5.4):
    postcss-merge-longhand: private
  postcss-merge-rules@7.0.5(postcss@8.5.4):
    postcss-merge-rules: private
  postcss-minify-font-values@7.0.1(postcss@8.5.4):
    postcss-minify-font-values: private
  postcss-minify-gradients@7.0.1(postcss@8.5.4):
    postcss-minify-gradients: private
  postcss-minify-params@7.0.3(postcss@8.5.4):
    postcss-minify-params: private
  postcss-minify-selectors@7.0.5(postcss@8.5.4):
    postcss-minify-selectors: private
  postcss-normalize-charset@7.0.1(postcss@8.5.4):
    postcss-normalize-charset: private
  postcss-normalize-display-values@7.0.1(postcss@8.5.4):
    postcss-normalize-display-values: private
  postcss-normalize-positions@7.0.1(postcss@8.5.4):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@7.0.1(postcss@8.5.4):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@7.0.1(postcss@8.5.4):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@7.0.1(postcss@8.5.4):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@7.0.3(postcss@8.5.4):
    postcss-normalize-unicode: private
  postcss-normalize-url@7.0.1(postcss@8.5.4):
    postcss-normalize-url: private
  postcss-normalize-whitespace@7.0.1(postcss@8.5.4):
    postcss-normalize-whitespace: private
  postcss-ordered-values@7.0.2(postcss@8.5.4):
    postcss-ordered-values: private
  postcss-reduce-initial@7.0.3(postcss@8.5.4):
    postcss-reduce-initial: private
  postcss-reduce-transforms@7.0.1(postcss@8.5.4):
    postcss-reduce-transforms: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-svgo@7.0.2(postcss@8.5.4):
    postcss-svgo: private
  postcss-unique-selectors@7.0.4(postcss@8.5.4):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss-values-parser@6.0.2(postcss@8.5.4):
    postcss-values-parser: private
  postcss@8.5.4:
    postcss: private
  potpack@2.0.0:
    potpack: private
  prebuild-install@7.1.3:
    prebuild-install: private
  precinct@12.2.0:
    precinct: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier@3.5.3:
    prettier: private
  pretty-bytes@6.1.1:
    pretty-bytes: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  prompts@2.4.2:
    prompts: private
  property-information@7.1.0:
    property-information: private
  protocol-buffers-schema@3.6.0:
    protocol-buffers-schema: private
  protocols@2.0.2:
    protocols: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quickselect@3.0.0:
    quickselect: private
  quote-unquote@1.0.0:
    quote-unquote: private
  radix3@1.1.2:
    radix3: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  rc9@2.1.2:
    rc9: private
  rc@1.2.8:
    rc: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@3.0.0:
    read-pkg: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@4.1.2:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  refa@0.12.1:
    refa: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regex-recursion@6.0.2:
    regex-recursion: private
  regex-utilities@2.3.0:
    regex-utilities: private
  regex@6.0.1:
    regex: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  rehype-external-links@3.0.0:
    rehype-external-links: private
  rehype-minify-whitespace@6.0.2:
    rehype-minify-whitespace: private
  rehype-raw@7.0.0:
    rehype-raw: private
  rehype-remark@10.0.1:
    rehype-remark: private
  rehype-slug@6.0.0:
    rehype-slug: private
  rehype-sort-attribute-values@5.0.1:
    rehype-sort-attribute-values: private
  rehype-sort-attributes@5.0.1:
    rehype-sort-attributes: private
  reka-ui@2.3.0(typescript@5.8.3)(vue@3.5.16(typescript@5.8.3)):
    reka-ui: private
  remark-emoji@5.0.1:
    remark-emoji: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-mdc@3.6.0:
    remark-mdc: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-package-name@2.0.1:
    require-package-name: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve-protobuf-schema@2.1.0:
    resolve-protobuf-schema: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  restructure@3.0.2:
    restructure: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  robust-predicates@2.0.4:
    robust-predicates: private
  rollup-plugin-visualizer@5.14.0(rollup@4.41.1):
    rollup-plugin-visualizer: private
  rollup@4.41.1:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  scslre@0.3.0:
    scslre: private
  scule@1.3.0:
    scule: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serialize-to-js@3.1.2:
    serialize-to-js: private
  serve-placeholder@2.0.2:
    serve-placeholder: private
  serve-static@2.2.0:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sharp@0.32.6:
    sharp: private
  shebang-command@1.2.0:
    shebang-command: private
  shebang-regex@1.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  shiki@3.2.1:
    shiki: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-git@3.27.0:
    simple-git: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  skin-tone@2.0.0:
    skin-tone: private
  slash@5.1.0:
    slash: private
  slice-ansi@5.0.0:
    slice-ansi: private
  slugify@1.6.6:
    slugify: private
  smob@1.5.0:
    smob: private
  smooth-dnd@0.12.1(patch_hash=84c2402ddf2a58cc2d1e63fb7e4a6a5035706676c173774b831af5486e7365a0):
    smooth-dnd: private
  socket.io-client@4.8.1:
    socket.io-client: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  speakingurl@14.0.1:
    speakingurl: private
  splaytree@0.1.4:
    splaytree: private
  stable-hash@0.0.5:
    stable-hash: private
  stack-trace@0.0.10:
    stack-trace: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.1:
    statuses: private
  std-env@3.9.0:
    std-env: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamx@2.22.0:
    streamx: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.padend@3.1.6:
    string.prototype.padend: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@3.0.0:
    strip-literal: private
  stripe@14.25.0:
    stripe: private
  structured-clone-es@1.0.0:
    structured-clone-es: private
  stylehacks@7.0.5(postcss@8.5.4):
    stylehacks: private
  supercluster@8.0.1:
    supercluster: private
  superjson@2.2.2:
    superjson: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg.draggable.js@2.2.2:
    svg.draggable.js: private
  svg.easing.js@2.0.0:
    svg.easing.js: private
  svg.filter.js@2.0.2:
    svg.filter.js: private
  svg.js@2.7.1:
    svg.js: private
  svg.pathmorphing.js@0.1.3:
    svg.pathmorphing.js: private
  svg.resize.js@1.4.3:
    svg.resize.js: private
  svg.select.js@3.0.1:
    svg.select.js: private
  svgo@3.3.2:
    svgo: private
  synckit@0.9.3:
    synckit: private
  system-architecture@0.1.0:
    system-architecture: private
  tailwind-merge@3.3.0:
    tailwind-merge: private
  tailwindcss@4.1.8:
    tailwindcss: private
  tairo-component-meta:
    '@cssninja/tairo-component-meta': private
  tapable@2.2.2:
    tapable: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@7.4.3:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terser@5.40.0:
    terser: private
  text-decoder@1.2.3:
    text-decoder: private
  text-hex@1.0.0:
    text-hex: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinyqueue@3.0.0:
    tinyqueue: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  toml@3.0.0:
    toml: private
  tosource@2.0.0-alpha.3:
    tosource: private
  totalist@3.0.1:
    totalist: private
  tr46@1.0.1:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trim-trailing-lines@2.1.0:
    trim-trailing-lines: private
  triple-beam@1.4.1:
    triple-beam: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.8.3):
    ts-declaration-location: private
  ts-node@10.9.2(@types/node@22.15.29)(typescript@5.8.3):
    ts-node: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type-level-regexp@0.1.17:
    type-level-regexp: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typescript@5.8.3:
    typescript: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  uncrypto@0.1.3:
    uncrypto: private
  unctx@2.4.1:
    unctx: private
  undici-types@6.21.0:
    undici-types: private
  unenv@2.0.0-rc.17:
    unenv: private
  unhead@2.0.10:
    unhead: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-emoji-modifier-base@1.0.0:
    unicode-emoji-modifier-base: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  unified@11.0.5:
    unified: private
  unifont@0.4.1:
    unifont: private
  unimport@4.2.0:
    unimport: private
  unique-string@2.0.0:
    unique-string: private
  unist-builder@4.0.0:
    unist-builder: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unixify@1.0.0:
    unixify: private
  unplugin-utils@0.2.4:
    unplugin-utils: private
  unplugin-vue-router@0.12.0(vue-router@4.5.1(vue@3.5.16(typescript@5.8.3)))(vue@3.5.16(typescript@5.8.3)):
    unplugin-vue-router: private
  unplugin@2.3.5:
    unplugin: private
  unrs-resolver@1.7.8:
    unrs-resolver: private
  unstorage@1.16.0(db0@0.3.2(better-sqlite3@11.10.0))(ioredis@5.6.1):
    unstorage: private
  untun@0.1.3:
    untun: private
  untyped@2.0.0:
    untyped: private
  unwasm@0.3.9:
    unwasm: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uqr@0.1.2:
    uqr: private
  uri-js@4.4.1:
    uri-js: private
  urlpattern-polyfill@8.0.2:
    urlpattern-polyfill: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@11.1.0:
    uuid: private
  v-calendar@3.1.2(@popperjs/core@2.11.8)(vue@3.5.16(typescript@5.8.3)):
    v-calendar: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vee-validate@4.15.0(vue@3.5.16(typescript@5.8.3)):
    vee-validate: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vite-dev-rpc@1.0.7(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0)):
    vite-dev-rpc: private
  vite-hot-client@2.0.4(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0)):
    vite-hot-client: private
  vite-node@3.2.0(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0):
    vite-node: private
  vite-plugin-checker@0.9.3(eslint@9.24.0(jiti@2.4.2))(optionator@0.9.4)(typescript@5.8.3)(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0)):
    vite-plugin-checker: private
  vite-plugin-inspect@11.1.0(@nuxt/kit@3.17.4(magicast@0.3.5))(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0)):
    vite-plugin-inspect: private
  vite-plugin-pwa@1.0.0(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(workbox-build@7.3.0)(workbox-window@7.3.0):
    vite-plugin-pwa: private
  vite-plugin-vue-tracer@0.1.3(vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0))(vue@3.5.16(typescript@5.8.3)):
    vite-plugin-vue-tracer: private
  vite@6.3.5(@types/node@22.15.29)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.40.0)(yaml@2.8.0):
    vite: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vt-pbf@3.1.3:
    vt-pbf: private
  vue-bundle-renderer@2.1.1:
    vue-bundle-renderer: private
  vue-component-meta@2.2.8(typescript@5.8.3):
    vue-component-meta: private
  vue-component-type-helpers@2.2.8:
    vue-component-type-helpers: private
  vue-demi@0.14.10(vue@3.5.16(typescript@5.8.3)):
    vue-demi: private
  vue-devtools-stub@0.1.0:
    vue-devtools-stub: private
  vue-eslint-parser@10.1.3(eslint@9.24.0(jiti@2.4.2)):
    vue-eslint-parser: private
  vue-i18n@10.0.7(vue@3.5.16(typescript@5.8.3)):
    vue-i18n: private
  vue-router@4.5.1(vue@3.5.16(typescript@5.8.3)):
    vue-router: private
  vue-screen-utils@1.0.0-beta.13(vue@3.5.16(typescript@5.8.3)):
    vue-screen-utils: private
  vue3-apexcharts@1.8.0(apexcharts@3.54.1)(vue@3.5.16(typescript@5.8.3)):
    vue3-apexcharts: private
  vue3-smooth-dnd@0.0.6(vue@3.5.16(typescript@5.8.3)):
    vue3-smooth-dnd: private
  vue@3.5.16(typescript@5.8.3):
    vue: private
  web-namespaces@2.0.1:
    web-namespaces: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@4.0.2:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  whatwg-url@7.1.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@5.0.0:
    which: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.17.0:
    winston: private
  word-wrap@1.2.5:
    word-wrap: private
  workbox-background-sync@7.3.0:
    workbox-background-sync: private
  workbox-broadcast-update@7.3.0:
    workbox-broadcast-update: private
  workbox-build@7.3.0:
    workbox-build: private
  workbox-cacheable-response@7.3.0:
    workbox-cacheable-response: private
  workbox-core@7.3.0:
    workbox-core: private
  workbox-expiration@7.3.0:
    workbox-expiration: private
  workbox-google-analytics@7.3.0:
    workbox-google-analytics: private
  workbox-navigation-preload@7.3.0:
    workbox-navigation-preload: private
  workbox-precaching@7.3.0:
    workbox-precaching: private
  workbox-range-requests@7.3.0:
    workbox-range-requests: private
  workbox-recipes@7.3.0:
    workbox-recipes: private
  workbox-routing@7.3.0:
    workbox-routing: private
  workbox-strategies@7.3.0:
    workbox-strategies: private
  workbox-streams@7.3.0:
    workbox-streams: private
  workbox-sw@7.3.0:
    workbox-sw: private
  workbox-window@7.3.0:
    workbox-window: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@6.0.0:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xmlhttprequest-ssl@2.1.2:
    xmlhttprequest-ssl: private
  xss@1.0.15:
    xss: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  youch-core@0.3.2:
    youch-core: private
  youch@4.1.0-beta.8:
    youch: private
  zip-stream@6.0.1:
    zip-stream: private
  zod-to-json-schema@3.24.5(zod@3.25.48):
    zod-to-json-schema: private
  zod-to-ts@1.2.0(typescript@5.8.3)(zod@3.25.48):
    zod-to-ts: private
  zod@3.25.48:
    zod: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.5.2
pendingBuilds:
  - better-sqlite3@11.9.1
  - esbuild@0.19.11
  - esbuild@0.25.3
  - '@parcel/watcher@2.5.1'
  - vue-demi@0.14.10(vue@3.5.13(typescript@5.8.3))
prunedAt: Tue, 10 Jun 2025 09:20:34 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.19.11'
  - '@esbuild/aix-ppc64@0.25.3'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.19.11'
  - '@esbuild/android-arm64@0.25.3'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.19.11'
  - '@esbuild/android-arm@0.25.3'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.19.11'
  - '@esbuild/android-x64@0.25.3'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.19.11'
  - '@esbuild/darwin-arm64@0.25.3'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.19.11'
  - '@esbuild/darwin-x64@0.25.3'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.19.11'
  - '@esbuild/freebsd-arm64@0.25.3'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.19.11'
  - '@esbuild/freebsd-x64@0.25.3'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.19.11'
  - '@esbuild/linux-arm64@0.25.3'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.19.11'
  - '@esbuild/linux-arm@0.25.3'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.19.11'
  - '@esbuild/linux-ia32@0.25.3'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.19.11'
  - '@esbuild/linux-loong64@0.25.3'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.19.11'
  - '@esbuild/linux-mips64el@0.25.3'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.19.11'
  - '@esbuild/linux-ppc64@0.25.3'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.19.11'
  - '@esbuild/linux-riscv64@0.25.3'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.19.11'
  - '@esbuild/linux-s390x@0.25.3'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.19.11'
  - '@esbuild/linux-x64@0.25.3'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.3'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.19.11'
  - '@esbuild/netbsd-x64@0.25.3'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.3'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.19.11'
  - '@esbuild/openbsd-x64@0.25.3'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.19.11'
  - '@esbuild/sunos-x64@0.25.3'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.19.11'
  - '@esbuild/win32-arm64@0.25.3'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.19.11'
  - '@esbuild/win32-ia32@0.25.3'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-ia32@0.25.5'
  - '@img/sharp-darwin-arm64@0.34.0'
  - '@img/sharp-darwin-x64@0.34.0'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.0'
  - '@img/sharp-linux-arm@0.34.0'
  - '@img/sharp-linux-s390x@0.34.0'
  - '@img/sharp-linux-x64@0.34.0'
  - '@img/sharp-linuxmusl-arm64@0.34.0'
  - '@img/sharp-linuxmusl-x64@0.34.0'
  - '@img/sharp-wasm32@0.34.0'
  - '@img/sharp-win32-ia32@0.34.0'
  - '@napi-rs/wasm-runtime@0.2.10'
  - '@napi-rs/wasm-runtime@0.2.9'
  - '@oxc-parser/binding-darwin-arm64@0.56.5'
  - '@oxc-parser/binding-darwin-arm64@0.61.2'
  - '@oxc-parser/binding-darwin-arm64@0.70.0'
  - '@oxc-parser/binding-darwin-x64@0.56.5'
  - '@oxc-parser/binding-darwin-x64@0.61.2'
  - '@oxc-parser/binding-darwin-x64@0.70.0'
  - '@oxc-parser/binding-freebsd-x64@0.70.0'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.56.5'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.61.2'
  - '@oxc-parser/binding-linux-arm-gnueabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm-musleabihf@0.70.0'
  - '@oxc-parser/binding-linux-arm64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-arm64-gnu@0.61.2'
  - '@oxc-parser/binding-linux-arm64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-arm64-musl@0.56.5'
  - '@oxc-parser/binding-linux-arm64-musl@0.61.2'
  - '@oxc-parser/binding-linux-arm64-musl@0.70.0'
  - '@oxc-parser/binding-linux-riscv64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-s390x-gnu@0.70.0'
  - '@oxc-parser/binding-linux-x64-gnu@0.56.5'
  - '@oxc-parser/binding-linux-x64-gnu@0.61.2'
  - '@oxc-parser/binding-linux-x64-gnu@0.70.0'
  - '@oxc-parser/binding-linux-x64-musl@0.56.5'
  - '@oxc-parser/binding-linux-x64-musl@0.61.2'
  - '@oxc-parser/binding-linux-x64-musl@0.70.0'
  - '@oxc-parser/binding-wasm32-wasi@0.56.5'
  - '@oxc-parser/binding-wasm32-wasi@0.61.2'
  - '@oxc-parser/binding-wasm32-wasi@0.70.0'
  - '@oxc-parser/binding-win32-arm64-msvc@0.56.5'
  - '@oxc-parser/binding-win32-arm64-msvc@0.61.2'
  - '@oxc-parser/binding-win32-arm64-msvc@0.70.0'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@resvg/resvg-js-android-arm-eabi@2.6.2'
  - '@resvg/resvg-js-android-arm64@2.6.2'
  - '@resvg/resvg-js-darwin-arm64@2.6.2'
  - '@resvg/resvg-js-darwin-x64@2.6.2'
  - '@resvg/resvg-js-linux-arm-gnueabihf@2.6.2'
  - '@resvg/resvg-js-linux-arm64-gnu@2.6.2'
  - '@resvg/resvg-js-linux-arm64-musl@2.6.2'
  - '@resvg/resvg-js-linux-x64-gnu@2.6.2'
  - '@resvg/resvg-js-linux-x64-musl@2.6.2'
  - '@resvg/resvg-js-win32-arm64-msvc@2.6.2'
  - '@resvg/resvg-js-win32-ia32-msvc@2.6.2'
  - '@rollup/rollup-android-arm-eabi@4.40.1'
  - '@rollup/rollup-android-arm-eabi@4.40.2'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.40.1'
  - '@rollup/rollup-android-arm64@4.40.2'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.40.1'
  - '@rollup/rollup-darwin-arm64@4.40.2'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.40.1'
  - '@rollup/rollup-darwin-x64@4.40.2'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.40.1'
  - '@rollup/rollup-freebsd-arm64@4.40.2'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.40.1'
  - '@rollup/rollup-freebsd-x64@4.40.2'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.40.1'
  - '@rollup/rollup-linux-arm64-gnu@4.40.2'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.40.1'
  - '@rollup/rollup-linux-arm64-musl@4.40.2'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.40.1'
  - '@rollup/rollup-linux-riscv64-musl@4.40.2'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.40.1'
  - '@rollup/rollup-linux-s390x-gnu@4.40.2'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.40.1'
  - '@rollup/rollup-linux-x64-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.40.1'
  - '@rollup/rollup-linux-x64-musl@4.40.2'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.40.1'
  - '@rollup/rollup-win32-arm64-msvc@4.40.2'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.40.1'
  - '@rollup/rollup-win32-ia32-msvc@4.40.2'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@tailwindcss/oxide-android-arm64@4.0.0-beta.8'
  - '@tailwindcss/oxide-android-arm64@4.1.5'
  - '@tailwindcss/oxide-android-arm64@4.1.6'
  - '@tailwindcss/oxide-android-arm64@4.1.7'
  - '@tailwindcss/oxide-android-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-arm64@4.0.0-beta.8'
  - '@tailwindcss/oxide-darwin-arm64@4.1.5'
  - '@tailwindcss/oxide-darwin-arm64@4.1.6'
  - '@tailwindcss/oxide-darwin-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-x64@4.0.0-beta.8'
  - '@tailwindcss/oxide-darwin-x64@4.1.5'
  - '@tailwindcss/oxide-darwin-x64@4.1.6'
  - '@tailwindcss/oxide-darwin-x64@4.1.7'
  - '@tailwindcss/oxide-darwin-x64@4.1.8'
  - '@tailwindcss/oxide-freebsd-x64@4.0.0-beta.8'
  - '@tailwindcss/oxide-freebsd-x64@4.1.5'
  - '@tailwindcss/oxide-freebsd-x64@4.1.6'
  - '@tailwindcss/oxide-freebsd-x64@4.1.7'
  - '@tailwindcss/oxide-freebsd-x64@4.1.8'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.5'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.6'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.5'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-musl@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.5'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.8'
  - '@tailwindcss/oxide-linux-x64-gnu@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.5'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-x64-musl@4.0.0-beta.8'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.5'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.6'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.8'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.5'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.6'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.7'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.8'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.0.0-beta.8'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.5'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.6'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.7'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.8'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.7.2'
  - '@unrs/resolver-binding-darwin-arm64@1.7.8'
  - '@unrs/resolver-binding-darwin-x64@1.7.2'
  - '@unrs/resolver-binding-darwin-x64@1.7.8'
  - '@unrs/resolver-binding-freebsd-x64@1.7.2'
  - '@unrs/resolver-binding-freebsd-x64@1.7.8'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.8'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.8'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.8'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.8'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.29.2
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\comanager\client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
