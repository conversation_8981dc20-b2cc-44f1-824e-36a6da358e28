<template>
  <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
    <div class="flex justify-between items-center mb-6">
      <BaseHeading tag="h2" size="md" weight="medium" class="text-muted-800 dark:text-white">
        Company Documents
      </BaseHeading>

      <BaseButton color="primary" @click="showUploader = !showUploader">
        <Icon :name="showUploader ? 'ph:x-duotone' : 'ph:upload-duotone'" class="h-4 w-4 mr-1" />
        {{ showUploader ? 'Cancel' : 'Upload Document' }}
      </BaseButton>
    </div>

    <div v-if="showUploader" class="mb-6">
      <DocumentUploader :company-id="companyId || ''" @document-uploaded="onDocumentUploaded" />
    </div>

    <DocumentList ref="documentListRef" :company-id="companyId || ''" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DocumentUploader from './documents/DocumentUploader.vue'
import DocumentList from './documents/DocumentList.vue'

const props = defineProps<{
  companyId?: string
}>()

// State
const showUploader = ref(false)
const documentListRef = ref<InstanceType<typeof DocumentList> | null>(null)

// Methods
const onDocumentUploaded = () => {
  // Hide uploader after successful upload
  showUploader.value = false

  // Refresh document list
  if (documentListRef.value) {
    documentListRef.value.fetchDocuments()
  }
}
</script>
