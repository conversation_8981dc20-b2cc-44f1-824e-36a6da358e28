<!-- client/.app/app/pages/users/profile/edit.vue -->

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Profile Content -->
    <div class="mt-6 grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-3">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center gap-2">
            <h4
              class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
            >
              {{ $t("profile.edit_sections") }}
            </h4>
          </div>
          <ul class="space-y-1 font-sans text-sm">
            <li>
              <button
                @click="activeTab = 'general'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'general'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:user-rounded-linear" class="size-5" />
                <span>{{ $t("profile.general") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'address'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'address'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:map-point-linear" class="size-5" />
                <span>{{ $t("profile.address") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'security'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'security'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:shield-check-linear" class="size-5" />
                <span>{{ $t("profile.security") }}</span>
              </button>
            </li>
          </ul>
        </BaseCard>
      </div>
      <div class="col-span-12 lg:col-span-9">
        <!-- General Tab -->
        <div v-show="activeTab === 'general'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.general_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.general_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.personal_information") }}
                </BaseHeading>
                <div class="p-4">
                  <form @submit.prevent="updateProfile">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="profileForm.firstName"
                        :label="$t('profile.first_name')"
                        :placeholder="$t('profile.first_name_placeholder')"
                        :error="profileErrors.firstName"
                        required
                      />
                      <BaseInput
                        v-model="profileForm.lastName"
                        :label="$t('profile.last_name')"
                        :placeholder="$t('profile.last_name_placeholder')"
                        :error="profileErrors.lastName"
                        required
                      />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="profileForm.email"
                        :label="$t('profile.email')"
                        type="email"
                        :placeholder="$t('profile.email_placeholder')"
                        :error="profileErrors.email"
                        required
                        disabled
                      />
                      <BaseInput
                        v-model="profileForm.phone"
                        :label="$t('profile.phone')"
                        :placeholder="$t('profile.phone_placeholder')"
                        :error="profileErrors.phone"
                      />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="profileForm.birthdate"
                        :label="$t('profile.birthdate')"
                        type="date"
                        :error="profileErrors.birthdate"
                      />
                    </div>

                    <div class="flex justify-end gap-3">
                      <BaseButton variant="muted" @click="$router.back()">
                        {{ $t("profile.cancel") }}
                      </BaseButton>
                      <BaseButton
                        type="submit"
                        variant="primary"
                        color="primary"
                        :loading="isUpdatingProfile"
                      >
                        <Icon name="mage:save-floppy" class="size-4 mr-2" />
                        {{ $t("profile.save_changes") }}
                      </BaseButton>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Address Tab -->
        <div v-show="activeTab === 'address'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.address_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.address_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.address_information") }}
                </BaseHeading>
                <div class="p-4">
                  <form @submit.prevent="updateAddress">
                    <div class="mb-6">
                      <BaseInput
                        v-model="addressForm.address"
                        :label="$t('profile.address_line_1')"
                        :placeholder="$t('profile.address_line_1_placeholder')"
                        :error="addressErrors.address"
                      />
                    </div>

                    <div class="mb-6">
                      <BaseInput
                        v-model="addressForm.address2"
                        :label="$t('profile.address_line_2')"
                        :placeholder="$t('profile.address_line_2_placeholder')"
                        :error="addressErrors.address2"
                      />
                    </div>

                    <div class="mb-6">
                      <BaseInput
                        v-model="addressForm.street"
                        :label="$t('profile.street')"
                        :placeholder="$t('profile.street_placeholder')"
                        :error="addressErrors.street"
                      />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="addressForm.city"
                        :label="$t('profile.city')"
                        :placeholder="$t('profile.city_placeholder')"
                        :error="addressErrors.city"
                      />
                      <BaseInput
                        v-model="addressForm.postalCode"
                        :label="$t('profile.postal_code')"
                        :placeholder="$t('profile.postal_code_placeholder')"
                        :error="addressErrors.postalCode"
                      />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="addressForm.state"
                        :label="$t('profile.state_province')"
                        :placeholder="$t('profile.state_province_placeholder')"
                        :error="addressErrors.state"
                      />
                      <TairoSelect
                        v-model="addressForm.country"
                        :label="$t('profile.country')"
                        :placeholder="$t('profile.country_placeholder')"
                        :error="addressErrors.country"
                        icon="solar:global-linear"
                      >
                        <TairoSelectItem
                          v-for="country in countries"
                          :key="country.code"
                          :value="country.code"
                        >
                          <span class="flex items-center gap-2">
                            <span class="text-lg">{{
                              countryFlagEmoji.get(country.code)?.emoji || "🌍"
                            }}</span>
                            <span>{{ country.name }}</span>
                          </span>
                        </TairoSelectItem>
                      </TairoSelect>
                    </div>

                    <div class="flex justify-end gap-3">
                      <BaseButton variant="muted" @click="$router.back()">
                        {{ $t("profile.cancel") }}
                      </BaseButton>
                      <BaseButton
                        type="submit"
                        variant="primary"
                        color="primary"
                        :loading="isUpdatingAddress"
                      >
                        <Icon name="mage:save-floppy" class="size-4 mr-2" />
                        {{ $t("profile.save_address") }}
                      </BaseButton>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Security Tab -->
        <div v-show="activeTab === 'security'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("navigation.security_settings") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.security_settings_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.change_password") }}
                </BaseHeading>
                <div class="p-4">
                  <form @submit.prevent="updatePassword">
                    <div class="mb-6">
                      <BaseInput
                        v-model="passwordForm.currentPassword"
                        :label="$t('profile.current_password')"
                        type="password"
                        :placeholder="
                          $t('profile.current_password_placeholder')
                        "
                        :error="passwordErrors.currentPassword"
                        required
                      />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <BaseInput
                        v-model="passwordForm.newPassword"
                        :label="$t('profile.new_password')"
                        type="password"
                        :placeholder="$t('profile.new_password_placeholder')"
                        :error="passwordErrors.newPassword"
                        required
                      />
                      <BaseInput
                        v-model="passwordForm.confirmPassword"
                        :label="$t('profile.confirm_password')"
                        type="password"
                        :placeholder="
                          $t('profile.confirm_password_placeholder')
                        "
                        :error="passwordErrors.confirmPassword"
                        required
                      />
                    </div>

                    <div class="flex justify-end gap-3">
                      <BaseButton variant="muted" @click="$router.back()">
                        {{ $t("profile.cancel") }}
                      </BaseButton>
                      <BaseButton
                        type="submit"
                        variant="primary"
                        color="primary"
                        :loading="isUpdatingPassword"
                      >
                        <Icon
                          name="solar:shield-check-linear"
                          class="size-4 mr-2"
                        />
                        {{ $t("profile.update_password") }}
                      </BaseButton>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "../../../../stores/useUserStore";
import { useApi } from "../../../../app/composables/useApi";
import { countries } from "../../../../app/data/countries";
import countryFlagEmoji from "country-flag-emoji";

// Set page title
useHead({
  title: "Edit Profile - CoManager",
});

// Initialize stores and composables
const userStore = useUserStore();
const toaster = useNuiToasts();
const api = useApi();
const route = useRoute();
const { t } = useI18n();

// Helper function to format date for input
const formatDateForInput = (dateString: string | null) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
};

// State
const activeTab = ref(route.query.tab || "general");

// User data
const user = computed(() => userStore.user);

// Form data
const profileForm = ref({
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  birthdate: "",
});

const addressForm = ref({
  address: "",
  address2: "",
  street: "",
  city: "",
  postalCode: "",
  state: "",
  country: "",
});

const passwordForm = ref({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// Error states
const profileErrors = ref({});
const addressErrors = ref({});
const passwordErrors = ref({});

// Loading states
const isUpdatingProfile = ref(false);
const isUpdatingAddress = ref(false);
const isUpdatingPassword = ref(false);

// Initialize forms with user data
const initForms = () => {
  if (user.value) {
    profileForm.value = {
      firstName: user.value.firstName || "",
      lastName: user.value.lastName || "",
      email: user.value.email || "",
      phone: user.value.phone || "",
      birthdate: formatDateForInput(user.value.birthdate),
    };

    addressForm.value = {
      address: user.value.address || "",
      address2: user.value.address2 || "",
      street: user.value.street || "",
      city: user.value.city || "",
      postalCode: user.value.postalCode || "",
      state: user.value.state || "",
      country: user.value.country || "",
    };
  }
};

// Form submission methods
const updateProfile = async () => {
  try {
    isUpdatingProfile.value = true;

    await userStore.updateProfile({
      firstName: profileForm.value.firstName,
      lastName: profileForm.value.lastName,
      phone: profileForm.value.phone,
      birthdate: profileForm.value.birthdate,
    });

    toaster.add({
      title: t("common.success"),
      description: t("profile.profile_updated_success"),
      icon: "solar:check-circle-linear",
      progress: true,
    });

    // Navigate back to profile page after successful update
    await navigateTo("/users/profile");
  } catch (error) {
    console.error("Error updating profile:", error);
    toaster.add({
      title: t("common.error"),
      description: t("profile.profile_update_failed"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isUpdatingProfile.value = false;
  }
};

const updateAddress = async () => {
  try {
    isUpdatingAddress.value = true;

    await userStore.updateProfile({
      address: addressForm.value.address,
      address2: addressForm.value.address2,
      street: addressForm.value.street,
      city: addressForm.value.city,
      postalCode: addressForm.value.postalCode,
      state: addressForm.value.state,
      country: addressForm.value.country,
    });

    toaster.add({
      title: t("common.success"),
      description: t("profile.address_updated_success"),
      icon: "solar:check-circle-linear",
      progress: true,
    });

    // Navigate back to profile page after successful update
    await navigateTo("/users/profile");
  } catch (error) {
    console.error("Error updating address:", error);
    toaster.add({
      title: t("common.error"),
      description: t("profile.address_update_failed"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isUpdatingAddress.value = false;
  }
};

const updatePassword = async () => {
  try {
    isUpdatingPassword.value = true;

    await api.post("/auth/change-password", {
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword,
    });

    // Clear form
    passwordForm.value = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };

    toaster.add({
      title: t("common.success"),
      description: t("profile.password_updated_success"),
      icon: "solar:check-circle-linear",
      progress: true,
    });
  } catch (error) {
    console.error("Error updating password:", error);
    toaster.add({
      title: t("common.error"),
      description: t("profile.password_update_failed"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isUpdatingPassword.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  if (!userStore.user) {
    await userStore.fetchUser();
  }
  initForms();
});
</script>
