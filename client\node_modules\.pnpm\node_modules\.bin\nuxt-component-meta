#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nuxt-component-meta/bin/nuxt-component-meta.mjs" "$@"
else
  exec node  "$basedir/../nuxt-component-meta/bin/nuxt-component-meta.mjs" "$@"
fi
