<script setup lang="ts">
import { format, formatDistanceToNow } from "date-fns";
import { onMounted, onUnmounted, ref } from "vue";

// Define page meta
definePageMeta({
  title: "System Health",
  layout: "default",
});

// Composables
const { add: addToast } = useNuiToasts();

// State
const loading = ref(false);
const lastUpdated = ref(new Date());

// Metrics data
const metrics = ref({
  systemHealth: 98,
  systemHealthTrend: 2,
  activeUsers: 42,
  activeUsersTrend: 8,
  apiRequests: 1250,
  apiRequestsTrend: 15,
  systemAlerts: 3,
  systemAlertsTrend: -2,
});

// Services data
const services = ref([
  {
    name: "Database",
    description: "Primary database server",
    status: "operational",
    metric: "Response: 45ms",
  },
  {
    name: "API Server",
    description: "REST API endpoints",
    status: "operational",
    metric: "Uptime: 99.9%",
  },
  {
    name: "File Storage",
    description: "Document storage service",
    status: "degraded",
    metric: "I/O: High",
  },
  {
    name: "Authentication",
    description: "User authentication service",
    status: "operational",
    metric: "Response: 120ms",
  },
  {
    name: "Email Service",
    description: "Notification emails",
    status: "operational",
    metric: "Queue: 0",
  },
  {
    name: "Background Jobs",
    description: "Scheduled tasks and jobs",
    status: "operational",
    metric: "Jobs: 5/min",
  },
  {
    name: "Search Service",
    description: "Full-text search engine",
    status: "operational",
    metric: "Latency: 85ms",
  },
  {
    name: "Cache Service",
    description: "Data caching layer",
    status: "operational",
    metric: "Hit rate: 92%",
  },
]);

// Resource usage data
const resources = ref({
  cpu: {
    percentage: 45,
    cores: 8,
    load: 2.34,
  },
  memory: {
    percentage: 62,
    used: 8 * 1024 * 1024 * 1024, // 8 GB
    total: 16 * 1024 * 1024 * 1024, // 16 GB
  },
  disk: {
    percentage: 72,
    used: 720 * 1024 * 1024 * 1024, // 720 GB
    total: 1000 * 1024 * 1024 * 1024, // 1 TB
  },
  network: {
    download: 5 * 1024 * 1024, // 5 MB/s
    upload: 2 * 1024 * 1024, // 2 MB/s
    downloadPercentage: 50,
    uploadPercentage: 40,
  },
});

// Performance metrics data
const performanceMetrics = ref({
  apiResponseTime: 120,
  apiResponseTimeTrend: -5,
  dbQueryTime: 45,
  dbQueryTimeTrend: -8,
  errorRate: 0.5,
  errorRateTrend: -10,
  pageLoadTime: 850,
  pageLoadTimeTrend: -3,
  timeLabels: [
    "1h ago",
    "50m ago",
    "40m ago",
    "30m ago",
    "20m ago",
    "10m ago",
    "Now",
  ],
  apiResponseTimeHistory: [145, 140, 135, 130, 125, 122, 120],
  dbQueryTimeHistory: [60, 55, 52, 50, 48, 46, 45],
  errorRateHistory: [0.8, 0.7, 0.7, 0.6, 0.6, 0.5, 0.5],
  pageLoadTimeHistory: [920, 900, 890, 880, 870, 860, 850],
});

// Database health data
const database = ref({
  connectionPool: {
    active: 12,
    max: 50,
  },
  queryCacheHitRate: 85,
  avgQueryTime: 45,
  slowQueries: 3,
  failedQueries: 0,
  size: 2.5 * 1024 * 1024 * 1024, // 2.5 GB
  lastBackup: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
});

// Alerts data
const alerts = ref([
  {
    id: "alert-1",
    severity: "High",
    title: "Storage Space Low",
    message:
      "File storage is running low on space. Consider cleaning up unused files.",
    source: "File Storage",
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: "Active",
  },
  {
    id: "alert-2",
    severity: "Medium",
    title: "High CPU Usage",
    message: "CPU usage has been above 80% for the last 15 minutes.",
    source: "System Monitor",
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    status: "Active",
  },
  {
    id: "alert-3",
    severity: "Low",
    title: "Slow Database Queries",
    message: "3 database queries took longer than 1 second to execute.",
    source: "Database",
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: "Acknowledged",
  },
]);

// Methods

function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

function formatDate(date: Date | string) {
  if (!date) return "N/A";

  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return format(dateObj, "MMM d, yyyy");
  } catch (error) {
    return "N/A";
  }
}

function formatTime(date: Date | string) {
  if (!date) return "N/A";

  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true });
  } catch (error) {
    return "N/A";
  }
}

function getSeverityColor(severity: string) {
  const colorMap: Record<string, string> = {
    Critical: "danger",
    High: "danger",
    Medium: "warning",
    Low: "info",
  };

  return colorMap[severity] || "muted";
}

function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    Active: "danger",
    Acknowledged: "warning",
    Resolved: "success",
    Closed: "muted",
  };

  return colorMap[status] || "muted";
}

function refreshAllData() {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date();

    // Update metrics
    metrics.value = {
      ...metrics.value,
      systemHealth: 99,
      systemHealthTrend: 3,
    };

    // Update services
    services.value = services.value.map((service) => {
      if (service.name === "File Storage") {
        return { ...service, status: "operational", metric: "I/O: Normal" };
      }
      return service;
    });

    loading.value = false;
    addToast({
      title: "Success",
      description: "System health data refreshed",
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 1500);
}

function refreshServices() {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date();

    // Update services
    services.value = services.value.map((service) => {
      if (service.name === "File Storage") {
        return { ...service, status: "operational", metric: "I/O: Normal" };
      }
      return service;
    });

    loading.value = false;
    addToast({
      title: "Success",
      description: "Service status refreshed",
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 1000);
}

function refreshResources() {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date();

    // Update resource usage
    resources.value = {
      ...resources.value,
      cpu: {
        ...resources.value.cpu,
        percentage: 42,
        load: 2.1,
      },
      memory: {
        ...resources.value.memory,
        percentage: 60,
      },
    };

    loading.value = false;
    addToast({
      title: "Success",
      description: "Resource usage refreshed",
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 1000);
}

function refreshPerformanceMetrics() {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date();

    // Update performance metrics
    performanceMetrics.value = {
      ...performanceMetrics.value,
      apiResponseTime: 115,
      apiResponseTimeTrend: -8,
      dbQueryTime: 42,
      dbQueryTimeTrend: -10,
    };

    loading.value = false;
    addToast({
      title: "Success",
      description: "Performance metrics refreshed",
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 1000);
}

function acknowledgeAlert(alert: any) {
  // Simulate API call
  setTimeout(() => {
    // Update alert status
    alerts.value = alerts.value.map((a) => {
      if (a.id === alert.id) {
        return { ...a, status: "Acknowledged" };
      }
      return a;
    });

    addToast({
      title: "Success",
      description: `Alert "${alert.title}" acknowledged`,
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 500);
}

function resolveAlert(alert: any) {
  // Simulate API call
  setTimeout(() => {
    // Update alert status
    alerts.value = alerts.value.map((a) => {
      if (a.id === alert.id) {
        return { ...a, status: "Resolved" };
      }
      return a;
    });

    addToast({
      title: "Success",
      description: `Alert "${alert.title}" resolved`,
      icon: "ph:check-circle-fill",
      progress: true,
    });
  }, 500);
}

// Fetch data on mount
onMounted(() => {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    loading.value = false;
  }, 1500);
});

// Clean up on unmount
onUnmounted(() => {
  // Cleanup if needed
});
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Controls -->
    <div class="mb-6 flex items-center justify-end gap-3">
      <BaseButton
        size="sm"
        variant="muted"
        :loading="loading"
        @click="refreshAllData"
      >
        <Icon name="ph:arrow-clockwise-duotone" class="size-4" />
        <span>Refresh All</span>
      </BaseButton>
    </div>

    <!-- Health Overview KPIs -->
    <div class="grid grid-cols-12 gap-4 mb-6">
      <!-- System Health Score -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-green-500 relative">
              <BaseProgressCircle
                :max="100"
                :model-value="metrics.systemHealth"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-green-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:heartbeat-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                System Health
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.systemHealth }}%
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-green-500">
                +{{ metrics.systemHealthTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Active Users -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-primary-500 relative">
              <BaseProgressCircle
                :max="100"
                :model-value="75"
                :size="75"
                :thickness="1"
                variant="primary"
              />
              <Icon
                name="ph:users-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                Active Users
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.activeUsers }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-primary-500">
                +{{ metrics.activeUsersTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- API Requests -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-orange-500 relative">
              <BaseProgressCircle
                :max="2000"
                :model-value="metrics.apiRequests"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-orange-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:globe-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                API Requests
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.apiRequests }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-orange-500">
                +{{ metrics.apiRequestsTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- System Alerts -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-destructive-500 relative">
              <BaseProgressCircle
                :max="10"
                :model-value="metrics.systemAlerts"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-destructive-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:bell-ringing-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                System Alerts
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.systemAlerts }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-destructive-500">
                {{ metrics.systemAlertsTrend }}
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Left Column -->
      <div class="col-span-12 lg:col-span-8">
        <!-- Service Status -->
        <BaseCard rounded="md" class="p-4 md:p-6 mb-4">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>Service Status</span>
            </BaseHeading>
            <BaseButton
              size="sm"
              variant="muted"
              :loading="loading"
              @click="refreshServices"
            >
              <Icon name="ph:arrow-clockwise-duotone" class="size-4" />
            </BaseButton>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="service in services"
              :key="service.name"
              class="flex items-center justify-between p-3 rounded-lg border border-muted-200 dark:border-muted-800"
            >
              <div class="flex items-center gap-3">
                <div
                  class="flex size-10 items-center justify-center rounded-full"
                  :class="{
                    'bg-green-100 dark:bg-green-900/20': service.status === 'operational',
                    'bg-yellow-100 dark:bg-yellow-900/20': service.status === 'degraded',
                    'bg-red-100 dark:bg-red-900/20': service.status === 'down'
                  }"
                >
                  <Icon
                    name="ph:check-circle-duotone"
                    class="size-5"
                    :class="{
                      'text-green-500': service.status === 'operational',
                      'text-yellow-500': service.status === 'degraded',
                      'text-red-500': service.status === 'down'
                    }"
                  />
                </div>
                <div>
                  <BaseParagraph size="sm" weight="medium" class="text-muted-900 dark:text-white">
                    {{ service.name }}
                  </BaseParagraph>
                  <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
                    {{ service.metric }}
                  </BaseParagraph>
                </div>
              </div>
              <BaseTag
                :color="service.status === 'operational' ? 'success' : service.status === 'degraded' ? 'warning' : 'danger'"
                variant="muted"
                size="sm"
              >
                {{ service.status }}
              </BaseTag>
            </div>
          </div>
        </BaseCard>

        <!-- Performance Chart -->
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>Performance Metrics</span>
            </BaseHeading>
            <BaseText size="sm">
              <BaseLink to="#" class="not-hover:text-muted-400">
                View details
              </BaseLink>
            </BaseText>
          </div>
          <div class="h-64">
            <LazyAddonApexcharts
              type="area"
              :height="256"
              :series="[
                {
                  name: 'API Response Time',
                  data: performanceMetrics.apiResponseTimeHistory,
                  color: '#3B82F6'
                },
                {
                  name: 'DB Query Time',
                  data: performanceMetrics.dbQueryTimeHistory,
                  color: '#10B981'
                },
                {
                  name: 'Error Rate',
                  data: performanceMetrics.errorRateHistory.map(v => v * 100),
                  color: '#F59E0B'
                }
              ]"
              :options="{
                chart: {
                  toolbar: { show: false },
                  background: 'transparent'
                },
                xaxis: {
                  categories: performanceMetrics.timeLabels,
                  labels: { style: { colors: '#64748B' } }
                },
                yaxis: {
                  labels: { style: { colors: '#64748B' } }
                },
                grid: { borderColor: '#E2E8F0' },
                stroke: { curve: 'smooth', width: 2 },
                fill: { type: 'gradient', opacity: 0.1 },
                legend: { position: 'top' }
              }"
            />
          </div>
        </BaseCard>
      </div>

      <!-- Right Column -->
      <div class="col-span-12 lg:col-span-4">
        <div class="space-y-4">
          <!-- Resource Usage -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <div class="mb-6 flex items-center justify-between">
              <BaseHeading
                as="h3"
                size="md"
                weight="semibold"
                lead="tight"
                class="text-muted-900 dark:text-white"
              >
                <span>Resource Usage</span>
              </BaseHeading>
              <BaseButton
                size="sm"
                variant="muted"
                :loading="loading"
                @click="refreshResources"
              >
                <Icon name="ph:arrow-clockwise-duotone" class="size-4" />
              </BaseButton>
            </div>
            <div class="space-y-6">
              <!-- CPU Usage -->
              <div>
                <div class="mb-2 flex items-center justify-between">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                    CPU Usage
                  </BaseParagraph>
                  <BaseParagraph size="sm" weight="medium" class="text-muted-900 dark:text-white">
                    {{ resources.cpu.percentage }}%
                  </BaseParagraph>
                </div>
                <BaseProgress
                  :model-value="resources.cpu.percentage"
                  :max="100"
                  variant="primary"
                  size="xs"
                />
                <BaseParagraph size="xs" class="mt-1 text-muted-500 dark:text-muted-400">
                  {{ resources.cpu.cores }} cores, Load: {{ resources.cpu.load }}
                </BaseParagraph>
              </div>

              <!-- Memory Usage -->
              <div>
                <div class="mb-2 flex items-center justify-between">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                    Memory Usage
                  </BaseParagraph>
                  <BaseParagraph size="sm" weight="medium" class="text-muted-900 dark:text-white">
                    {{ resources.memory.percentage }}%
                  </BaseParagraph>
                </div>
                <BaseProgress
                  :model-value="resources.memory.percentage"
                  :max="100"
                  variant="primary"
                  size="xs"
                />
                <BaseParagraph size="xs" class="mt-1 text-muted-500 dark:text-muted-400">
                  {{ formatBytes(resources.memory.used) }} / {{ formatBytes(resources.memory.total) }}
                </BaseParagraph>
              </div>

              <!-- Disk Usage -->
              <div>
                <div class="mb-2 flex items-center justify-between">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                    Disk Usage
                  </BaseParagraph>
                  <BaseParagraph size="sm" weight="medium" class="text-muted-900 dark:text-white">
                    {{ resources.disk.percentage }}%
                  </BaseParagraph>
                </div>
                <BaseProgress
                  :model-value="resources.disk.percentage"
                  :max="100"
                  variant="primary"
                  size="xs"
                />
                <BaseParagraph size="xs" class="mt-1 text-muted-500 dark:text-muted-400">
                  {{ formatBytes(resources.disk.used) }} / {{ formatBytes(resources.disk.total) }}
                </BaseParagraph>
              </div>

              <!-- Network -->
              <div>
                <div class="mb-2 flex items-center justify-between">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                    Network I/O
                  </BaseParagraph>
                </div>
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                      Download
                    </BaseParagraph>
                    <BaseParagraph size="xs" weight="medium" class="text-muted-900 dark:text-white">
                      {{ formatBytes(resources.network.download) }}/s
                    </BaseParagraph>
                  </div>
                  <BaseProgress
                    :model-value="resources.network.downloadPercentage"
                    :max="100"
                    variant="primary"
                    size="xs"
                  />
                  <div class="flex items-center justify-between">
                    <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                      Upload
                    </BaseParagraph>
                    <BaseParagraph size="xs" weight="medium" class="text-muted-900 dark:text-white">
                      {{ formatBytes(resources.network.upload) }}/s
                    </BaseParagraph>
                  </div>
                  <BaseProgress
                    :model-value="resources.network.uploadPercentage"
                    :max="100"
                    variant="primary"
                    size="xs"
                  />
                </div>
              </div>
            </div>
          </BaseCard>

          <!-- Database Health -->
          <BaseCard rounded="md" class="p-4 md:p-6">
            <div class="mb-6 flex items-center justify-between">
              <BaseHeading
                as="h3"
                size="md"
                weight="semibold"
                lead="tight"
                class="text-muted-900 dark:text-white"
              >
                <span>Database Health</span>
              </BaseHeading>
            </div>
            <div class="space-y-4">
              <!-- Connection Pool -->
              <div class="text-center">
                <BaseHeading as="h4" size="xl" weight="bold" class="text-primary-500">
                  {{ database.connectionPool.active }}/{{ database.connectionPool.max }}
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Active Connections
                </BaseParagraph>
              </div>

              <!-- Cache Hit Rate -->
              <div class="text-center">
                <BaseHeading as="h4" size="xl" weight="bold" class="text-green-500">
                  {{ database.queryCacheHitRate }}%
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Cache Hit Rate
                </BaseParagraph>
              </div>

              <!-- Query Performance -->
              <div class="text-center">
                <BaseHeading as="h4" size="xl" weight="bold" class="text-orange-500">
                  {{ database.avgQueryTime }}ms
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Avg Query Time
                </BaseParagraph>
              </div>

              <!-- Database Size -->
              <div class="rounded-lg bg-muted-50 p-3 dark:bg-muted-900/50">
                <div class="flex items-center justify-between">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                    Database Size
                  </BaseParagraph>
                  <BaseParagraph size="sm" weight="medium" class="text-muted-900 dark:text-white">
                    {{ formatBytes(database.size) }}
                  </BaseParagraph>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>

    <!-- Performance Metrics and Database Health -->
    <div class="grid grid-cols-12 gap-6 mb-6">
      <!-- Performance Metrics -->
      <div class="col-span-12 lg:col-span-8">
        <PerformanceMetricsCard
          title="Performance Metrics"
          :metrics="performanceMetrics"
          :loading="loading"
          :last-updated="lastUpdated"
          @refresh="refreshPerformanceMetrics"
        />
      </div>

      <!-- Database Health -->
      <div class="col-span-12 lg:col-span-4">
        <BaseCard class="h-full">
          <div class="p-4 border-b border-muted-200 dark:border-muted-700">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Database Health
            </BaseHeading>
          </div>

          <div class="p-4">
            <div v-if="loading" class="flex justify-center py-4">
              <BaseButtonIcon shape="rounded" color="primary" loading />
            </div>

            <div v-else class="space-y-4">
              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Connection Pool
                </BaseText>
                <div class="flex items-center">
                  <BaseText
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100"
                  >
                    {{ database.connectionPool.active }} /
                    {{ database.connectionPool.max }}
                  </BaseText>
                  <BaseTag
                    :color="
                      database.connectionPool.active /
                        database.connectionPool.max >
                      0.8
                        ? 'warning'
                        : 'success'
                    "
                    flavor="pastel"
                    size="sm"
                    class="ml-2"
                  >
                    {{
                      Math.round(
                        (database.connectionPool.active /
                          database.connectionPool.max) *
                          100
                      )
                    }}%
                  </BaseTag>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Query Cache Hit Rate
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ database.queryCacheHitRate }}%
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Avg. Query Time
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ database.avgQueryTime }}ms
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Slow Queries
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ database.slowQueries }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Failed Queries
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ database.failedQueries }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Database Size
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ formatBytes(database.size) }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                  Last Backup
                </BaseText>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-muted-100"
                >
                  {{ formatDate(database.lastBackup) }}
                </BaseText>
              </div>
            </div>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <BaseButton
              color="primary"
              class="w-full"
              @click="navigateTo('/core/settings/database')"
            >
              <Icon name="ph:database-duotone" class="h-4 w-4 mr-1" />
              Database Management
            </BaseButton>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- System Alerts -->
    <BaseCard>
      <div
        class="p-4 border-b border-muted-200 dark:border-muted-700 flex justify-between items-center"
      >
        <BaseHeading
          as="h3"
          size="sm"
          weight="medium"
          class="text-muted-900 dark:text-white"
        >
          System Alerts
        </BaseHeading>
        <BaseButton
          color="primary"
          flavor="link"
          size="sm"
          @click="navigateTo('/core/monitoring/alerts')"
        >
          View All Alerts
        </BaseButton>
      </div>

      <div class="p-4">
        <div v-if="loading" class="flex justify-center py-4">
          <BaseButtonIcon shape="rounded" color="primary" loading />
        </div>

        <div v-else-if="alerts.length === 0" class="text-center py-6">
          <Icon
            name="ph:check-circle-duotone"
            class="h-12 w-12 text-success-500 mx-auto mb-3"
          />
          <BaseText class="text-muted-500 dark:text-muted-400">
            No active alerts at this time
          </BaseText>
        </div>

        <div v-else>
          <div class="overflow-x-auto">
            <table class="w-full text-left">
              <thead>
                <tr class="border-b border-muted-200 dark:border-muted-700">
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Severity
                  </th>
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Alert
                  </th>
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Source
                  </th>
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Time
                  </th>
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Status
                  </th>
                  <th
                    class="p-3 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(alert, index) in alerts"
                  :key="index"
                  class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
                >
                  <td class="p-3">
                    <BaseTag
                      :color="getSeverityColor(alert.severity)"
                      flavor="pastel"
                    >
                      {{ alert.severity }}
                    </BaseTag>
                  </td>
                  <td class="p-3">
                    <div>
                      <BaseText
                        weight="medium"
                        class="text-muted-900 dark:text-white"
                      >
                        {{ alert.title }}
                      </BaseText>
                      <BaseText
                        size="xs"
                        class="text-muted-500 dark:text-muted-400"
                      >
                        {{ alert.message }}
                      </BaseText>
                    </div>
                  </td>
                  <td class="p-3">
                    <BaseText class="text-muted-500 dark:text-muted-400">
                      {{ alert.source }}
                    </BaseText>
                  </td>
                  <td class="p-3">
                    <BaseText class="text-muted-500 dark:text-muted-400">
                      {{ formatTime(alert.timestamp) }}
                    </BaseText>
                  </td>
                  <td class="p-3">
                    <BaseTag
                      :color="getStatusColor(alert.status)"
                      flavor="pastel"
                      size="sm"
                    >
                      {{ alert.status }}
                    </BaseTag>
                  </td>
                  <td class="p-3">
                    <div class="flex gap-2">
                      <BaseButton
                        color="primary"
                        flavor="link"
                        size="sm"
                        @click="acknowledgeAlert(alert)"
                      >
                        Acknowledge
                      </BaseButton>
                      <BaseButton
                        color="danger"
                        flavor="link"
                        size="sm"
                        @click="resolveAlert(alert)"
                      >
                        Resolve
                      </BaseButton>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </BaseCard>
  </BaseLayerPage>
</template>
