<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useApi } from "@/composables/useApi";
import { useNuiToasts } from "@/composables/useNuiToasts";

// Define page meta
definePageMeta({
  title: "System Health",
  layout: "default",
});

// Composables
const api = useApi();
const toasts = useNuiToasts();

// Reactive data
const isLoading = ref(true);
const autoRefresh = ref(true);
const refreshInterval = ref<NodeJS.Timeout | null>(null);
const lastUpdated = ref(new Date());

// Health data from API
const healthData = ref<any>(null);
const historyData = ref<any>(null);

// Computed properties
const overallHealthStatus = computed(() => {
  if (!healthData.value) return { status: "unknown", color: "muted" };

  const health = healthData.value.overallHealth;
  if (health >= 95) return { status: "excellent", color: "success" };
  if (health >= 85) return { status: "good", color: "success" };
  if (health >= 70) return { status: "fair", color: "warning" };
  return { status: "poor", color: "danger" };
});

const formatBytes = (bytes: number) => {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  if (bytes === 0) return "0 Bytes";
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
};

const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}d ${hours}h ${minutes}m`;
};

// Methods
const fetchHealthData = async () => {
  isLoading.value = true;
  try {
    const [healthResponse, historyResponse] = await Promise.all([
      api.get("/core/health"),
      api.get("/core/health/history?period=24h"),
    ]);

    healthData.value = healthResponse;
    historyData.value = historyResponse;
    lastUpdated.value = new Date();
  } catch (error) {
    console.error("Failed to fetch health data:", error);
    toasts.add({
      title: "Error",
      description: "Failed to fetch health data",
      icon: "ph:warning-duotone",
      duration: 5000,
    });
  } finally {
    isLoading.value = false;
  }
};

const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    refreshInterval.value = setInterval(fetchHealthData, 30000); // Refresh every 30 seconds
  } else {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value);
      refreshInterval.value = null;
    }
  }
};

const refreshData = () => {
  fetchHealthData();
};

// Lifecycle
onMounted(() => {
  fetchHealthData();
  if (autoRefresh.value) {
    toggleAutoRefresh();
  }
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div
      class="mb-6 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center"
    >
      <div>
        <BaseHeading
          as="h1"
          size="2xl"
          weight="bold"
          class="text-muted-900 dark:text-white"
        >
          System Health
        </BaseHeading>
        <BaseParagraph class="text-muted-600 dark:text-muted-400">
          Monitor system performance and health metrics
        </BaseParagraph>
      </div>
      <div class="flex items-center gap-3">
        <BaseButton
          size="sm"
          variant="muted"
          :loading="isLoading"
          @click="refreshData"
        >
          <Icon name="ph:arrow-clockwise-duotone" class="size-4" />
          <span>Refresh</span>
        </BaseButton>
        <BaseSwitch
          v-model="autoRefresh"
          label="Auto-refresh"
          sublabel="Update every 30s"
          color="primary"
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading && !healthData" class="grid grid-cols-12 gap-4">
      <div
        v-for="i in 4"
        :key="i"
        class="col-span-12 md:col-span-6 lg:col-span-3"
      >
        <BaseCard class="p-6">
          <BasePlaceload class="h-16 w-full rounded-lg" />
        </BaseCard>
      </div>
    </div>

    <!-- Health Overview KPIs -->
    <div v-else-if="healthData" class="grid grid-cols-12 gap-4">
      <!-- Overall Health Score -->
      <div class="col-span-12 md:col-span-6 lg:col-span-3">
        <BaseCard
          variant="none"
          rounded="md"
          class="from-primary-500 to-purple-500 relative flex items-center justify-center bg-gradient-to-br p-6 shadow-xl"
        >
          <div class="relative z-20 text-center text-white">
            <BaseHeading as="h3" size="3xl" weight="bold" class="text-white">
              {{ healthData.overallHealth }}%
            </BaseHeading>
            <BaseParagraph size="sm" class="text-white/80">
              Overall Health
            </BaseParagraph>
            <BaseTag
              :color="overallHealthStatus.color"
              variant="solid"
              size="sm"
              class="mt-2"
            >
              {{ overallHealthStatus.status }}
            </BaseTag>
          </div>
          <div class="absolute bottom-2 end-2 z-10">
            <Icon name="ph:heartbeat-duotone" class="size-8 text-white/30" />
          </div>
        </BaseCard>
      </div>

      <!-- Active Users -->
      <div class="col-span-12 md:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <BaseHeading
                as="h3"
                size="2xl"
                weight="semibold"
                class="text-muted-900 dark:text-white"
              >
                {{ healthData.application?.users?.active || 0 }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Active Users
              </BaseParagraph>
            </div>
            <div
              class="flex size-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20"
            >
              <Icon name="ph:users-duotone" class="size-6 text-blue-500" />
            </div>
          </div>
          <div class="mt-4">
            <BaseProgress
              :value="
                (healthData.application?.users?.active /
                  healthData.application?.users?.total) *
                100
              "
              color="blue"
              size="xs"
            />
          </div>
        </BaseCard>
      </div>

      <!-- API Response Time -->
      <div class="col-span-12 md:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <BaseHeading
                as="h3"
                size="2xl"
                weight="semibold"
                class="text-muted-900 dark:text-white"
              >
                {{ healthData.api?.averageResponseTime || 0 }}ms
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Response Time
              </BaseParagraph>
            </div>
            <div
              class="flex size-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20"
            >
              <Icon name="ph:lightning-duotone" class="size-6 text-green-500" />
            </div>
          </div>
          <div class="mt-4">
            <BaseProgress
              :value="
                Math.max(0, 100 - healthData.api?.averageResponseTime / 10)
              "
              color="green"
              size="xs"
            />
          </div>
        </BaseCard>
      </div>

      <!-- Memory Usage -->
      <div class="col-span-12 md:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <BaseHeading
                as="h3"
                size="2xl"
                weight="semibold"
                class="text-muted-900 dark:text-white"
              >
                {{ healthData.system?.memory?.usage?.toFixed(1) || 0 }}%
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Memory Usage
              </BaseParagraph>
            </div>
            <div
              class="flex size-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20"
            >
              <Icon name="ph:cpu-duotone" class="size-6 text-yellow-500" />
            </div>
          </div>
          <div class="mt-4">
            <BaseProgress
              :value="healthData.system?.memory?.usage || 0"
              :color="
                healthData.system?.memory?.usage > 80 ? 'danger' : 'warning'
              "
              size="xs"
            />
          </div>
        </BaseCard>
      </div>

      <!-- System Performance Chart -->
      <div class="col-span-12 lg:col-span-8">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>System Performance</span>
            </BaseHeading>
            <BaseText size="sm">
              <BaseLink to="#" class="not-hover:text-muted-400">
                View details
              </BaseLink>
            </BaseText>
          </div>
          <div v-if="historyData?.history" class="h-64">
            <LazyAddonApexcharts
              type="area"
              :height="256"
              :series="[
                {
                  name: 'System Health',
                  data: historyData.history.map((h: any) => h.systemHealth),
                  color: '#10B981'
                },
                {
                  name: 'Memory Usage',
                  data: historyData.history.map((h: any) => h.memoryUsage),
                  color: '#F59E0B'
                },
                {
                  name: 'CPU Usage',
                  data: historyData.history.map((h: any) => h.cpuUsage),
                  color: '#3B82F6'
                }
              ]"
              :options="{
                chart: {
                  toolbar: { show: false },
                  background: 'transparent'
                },
                xaxis: {
                  categories: historyData.history.map((h: any) => new Date(h.timestamp).toLocaleTimeString()),
                  labels: { style: { colors: '#64748B' } }
                },
                yaxis: {
                  labels: { style: { colors: '#64748B' } }
                },
                grid: { borderColor: '#E2E8F0' },
                stroke: { curve: 'smooth', width: 2 },
                fill: { type: 'gradient', opacity: 0.1 },
                legend: { position: 'top' }
              }"
            />
          </div>
        </BaseCard>
      </div>

      <!-- Database Health -->
      <div class="col-span-12 lg:col-span-4">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>Database Health</span>
            </BaseHeading>
          </div>
          <div class="space-y-4">
            <!-- Connection Pool -->
            <div>
              <div class="mb-2 flex items-center justify-between">
                <BaseParagraph
                  size="sm"
                  class="text-muted-600 dark:text-muted-400"
                >
                  Connection Pool
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="font-medium text-muted-900 dark:text-white"
                >
                  {{ healthData.database?.connections?.active || 0 }}/{{
                    healthData.database?.connections?.total || 0
                  }}
                </BaseParagraph>
              </div>
              <BaseProgress
                :value="
                  ((healthData.database?.connections?.active || 0) /
                    (healthData.database?.connections?.total || 1)) *
                  100
                "
                color="blue"
                size="xs"
              />
            </div>

            <!-- Cache Hit Ratio -->
            <div>
              <div class="mb-2 flex items-center justify-between">
                <BaseParagraph
                  size="sm"
                  class="text-muted-600 dark:text-muted-400"
                >
                  Cache Hit Ratio
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="font-medium text-muted-900 dark:text-white"
                >
                  {{
                    healthData.database?.performance?.cacheHitRatio?.toFixed(
                      1
                    ) || 0
                  }}%
                </BaseParagraph>
              </div>
              <BaseProgress
                :value="healthData.database?.performance?.cacheHitRatio || 0"
                color="green"
                size="xs"
              />
            </div>

            <!-- Query Performance -->
            <div>
              <div class="mb-2 flex items-center justify-between">
                <BaseParagraph
                  size="sm"
                  class="text-muted-600 dark:text-muted-400"
                >
                  Avg Query Time
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="font-medium text-muted-900 dark:text-white"
                >
                  {{ healthData.database?.performance?.avgQueryTime || 0 }}ms
                </BaseParagraph>
              </div>
              <BaseProgress
                :value="
                  Math.max(
                    0,
                    100 - healthData.database?.performance?.avgQueryTime / 10
                  )
                "
                :color="
                  (healthData.database?.performance?.avgQueryTime || 0) > 500
                    ? 'danger'
                    : 'success'
                "
                size="xs"
              />
            </div>

            <!-- Database Size -->
            <div class="rounded-lg bg-muted-50 p-3 dark:bg-muted-900/50">
              <div class="flex items-center justify-between">
                <BaseParagraph
                  size="sm"
                  class="text-muted-600 dark:text-muted-400"
                >
                  Database Size
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="font-medium text-muted-900 dark:text-white"
                >
                  {{ healthData.database?.size || "Unknown" }}
                </BaseParagraph>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Application Metrics -->
      <div class="col-span-12 md:col-span-6">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>Application Metrics</span>
            </BaseHeading>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <!-- Active Projects -->
            <div class="text-center">
              <BaseHeading
                as="h4"
                size="xl"
                weight="bold"
                class="text-blue-500"
              >
                {{ healthData.application?.projects?.active || 0 }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Active Projects
              </BaseParagraph>
            </div>

            <!-- Pending Tasks -->
            <div class="text-center">
              <BaseHeading
                as="h4"
                size="xl"
                weight="bold"
                class="text-yellow-500"
              >
                {{ healthData.application?.tasks?.pending || 0 }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Pending Tasks
              </BaseParagraph>
            </div>

            <!-- Total Users -->
            <div class="text-center">
              <BaseHeading
                as="h4"
                size="xl"
                weight="bold"
                class="text-green-500"
              >
                {{ healthData.application?.users?.total || 0 }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Total Users
              </BaseParagraph>
            </div>

            <!-- Error Rate -->
            <div class="text-center">
              <BaseHeading as="h4" size="xl" weight="bold" class="text-red-500">
                {{ healthData.api?.errorRate?.toFixed(1) || 0 }}%
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                Error Rate
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- API Performance -->
      <div class="col-span-12 md:col-span-6">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>API Performance</span>
            </BaseHeading>
          </div>
          <div class="space-y-4">
            <!-- Requests per Minute -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div
                  class="flex size-10 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900/20"
                >
                  <Icon
                    name="ph:globe-duotone"
                    class="size-5 text-primary-500"
                  />
                </div>
                <div>
                  <BaseParagraph
                    size="sm"
                    class="font-medium text-muted-900 dark:text-white"
                  >
                    Requests/min
                  </BaseParagraph>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-600 dark:text-muted-400"
                  >
                    Current load
                  </BaseParagraph>
                </div>
              </div>
              <BaseHeading
                as="h4"
                size="lg"
                weight="bold"
                class="text-muted-900 dark:text-white"
              >
                {{ healthData.api?.requestsPerMinute || 0 }}
              </BaseHeading>
            </div>

            <!-- Uptime -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div
                  class="flex size-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20"
                >
                  <Icon name="ph:clock-duotone" class="size-5 text-green-500" />
                </div>
                <div>
                  <BaseParagraph
                    size="sm"
                    class="font-medium text-muted-900 dark:text-white"
                  >
                    Uptime
                  </BaseParagraph>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-600 dark:text-muted-400"
                  >
                    Service availability
                  </BaseParagraph>
                </div>
              </div>
              <BaseHeading
                as="h4"
                size="lg"
                weight="bold"
                class="text-green-500"
              >
                {{ healthData.api?.uptime?.toFixed(2) || 0 }}%
              </BaseHeading>
            </div>

            <!-- System Uptime -->
            <div class="rounded-lg bg-muted-50 p-3 dark:bg-muted-900/50">
              <div class="flex items-center justify-between">
                <BaseParagraph
                  size="sm"
                  class="text-muted-600 dark:text-muted-400"
                >
                  System Uptime
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="font-medium text-muted-900 dark:text-white"
                >
                  {{ formatUptime(healthData.system?.uptime || 0) }}
                </BaseParagraph>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
