<!--  client/.app/app/components/auth/RegistrationForm.vue -->

<template>
  <form
    method="POST"
    class="mx-auto w-full max-w-sm"
    novalidate
    @submit.prevent="onSubmit"
  >
    <BaseHeading
      as="h2"
      size="3xl"
      weight="medium"
      class="text-black dark:text-white"
    >
      {{ title }}
    </BaseHeading>
    <BaseParagraph size="sm" class="text-muted-400 dark:text-muted-300 mb-6">
      {{ subtitle }}
    </BaseParagraph>

    <div class="mb-4 space-y-3">
      <!-- First Name -->
      <Field
        name="firstName"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting"
          required
          class="nui-field"
        >
          <TairoInput
            :model-value="field.value"
            :aria-invalid="errorMessage ? 'true' : undefined"
            :disabled="isSubmitting"
            type="text"
            rounded="lg"
            size="md"
            :placeholder="t('auth.signup.first_name')"
            icon="ph:user-duotone"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>

      <!-- Last Name -->
      <Field
        name="lastName"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting"
          required
          class="nui-field"
        >
          <TairoInput
            :model-value="field.value"
            :aria-invalid="errorMessage ? 'true' : undefined"
            :disabled="isSubmitting"
            type="text"
            rounded="lg"
            size="md"
            :placeholder="t('auth.signup.last_name')"
            icon="ph:user-duotone"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>

      <!-- Mobile with Country Code -->
      <BaseField
        :state="mobileErrorMessage ? 'error' : 'idle'"
        :error="mobileErrorMessage"
        :disabled="isSubmitting"
        required
        class="nui-field"
      >
        <PhoneInput
          v-model="mobileValue"
          :disabled="isSubmitting"
          :placeholder="t('auth.signup.mobile_number')"
          name="mobile"
          :default-country="'ee'"
          :error="mobileErrorMessage"
          icon="ph:phone-duotone"
          class="dark:bg-neutral-900"
          @validate="onValidate"
          @country-changed="onCountryChanged"
        />
      </BaseField>

      <!-- Email -->
      <Field
        name="email"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting || isInvitation"
          required
          class="nui-field"
        >
          <TairoInput
            :model-value="field.value"
            :aria-invalid="errorMessage ? 'true' : undefined"
            :disabled="isSubmitting || isInvitation"
            type="email"
            rounded="lg"
            size="md"
            :placeholder="t('auth.signup.email_address')"
            icon="ph:at-duotone"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>

      <!-- Company -->
      <Field
        name="company"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting || isInvitation"
          required
          class="nui-field"
        >
          <TairoInput
            :model-value="field.value"
            :aria-invalid="errorMessage ? 'true' : undefined"
            :disabled="isSubmitting || isInvitation"
            type="text"
            rounded="lg"
            size="md"
            :placeholder="t('auth.signup.company_name')"
            icon="ph:building-duotone"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>

      <!-- Password -->
      <Field
        name="password"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          v-slot="{ inputAttrs }"
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting"
          required
          class="nui-field"
        >
          <AddonInputPassword
            ref="passwordRef"
            v-bind="inputAttrs"
            :model-value="field.value"
            :error="errorMessage"
            :disabled="isSubmitting"
            rounded="lg"
            :placeholder="t('auth.signup.password')"
            icon="ph:fingerprint-duotone"
            :touched="true"
            :user-inputs="[
              values.firstName || '',
              values.lastName || '',
              values.email || '',
            ]"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>

      <!-- Confirm Password -->
      <Field
        name="confirmPassword"
        v-slot="{ field, errorMessage, handleChange, handleBlur }"
      >
        <BaseField
          :state="errorMessage ? 'error' : 'idle'"
          :error="errorMessage"
          :disabled="isSubmitting"
          required
          class="nui-field"
        >
          <PasswordInputWithEye
            :model-value="field.value"
            :error="errorMessage"
            :disabled="isSubmitting"
            rounded="lg"
            :placeholder="t('auth.signup.confirm_password')"
            icon="ph:lock-duotone"
            class="dark:bg-neutral-900 input-with-dark-icon"
            @update:model-value="handleChange"
            @blur="handleBlur"
          />
        </BaseField>
      </Field>
    </div>

    <BaseButton
      :disabled="isSubmitting || loading"
      :loading="loading"
      type="submit"
      rounded="md"
      variant="primary"
      class="!h-11 w-full"
    >
      {{ loading ? t("auth.signup.registering_button") : submitButtonText }}
    </BaseButton>

    <p
      class="text-muted-400 mt-4 flex justify-between font-sans text-sm leading-5"
    >
      <span>{{ t("auth.signup.already_have_account") }}</span>
      <NuxtLink
        to="/auth"
        class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4"
      >
        {{ t("auth.signup.login") }}
      </NuxtLink>
    </p>
  </form>
</template>

<script setup lang="ts">
import { toTypedSchema } from "@vee-validate/zod";
import { Field, useForm, useField } from "vee-validate";
import { z } from "zod";
import { computed, watch, ref } from "vue";
import { useI18n } from "vue-i18n";
import PhoneInput from "../PhoneInput.vue";
import AddonInputPassword from "../AddonInputPassword.vue";
import PasswordInputWithEye from "../PasswordInputWithEye.vue";
import { TairoInput } from "#components";

const props = defineProps({
  role: {
    type: String,
    required: true,
  },
  prefillEmail: {
    type: String,
    default: "",
  },
  prefillCompany: {
    type: String,
    default: "",
  },
  isInvitation: {
    type: Boolean,
    default: false,
  },
  submitButtonText: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["register-success"]);
const { t } = useI18n();

// Form title and subtitle based on role
const title = computed(() => {
  switch (props.role) {
    case "CLIENT":
      return t("auth.signup.register_company");
    case "PROJECTLEADER":
      return t("auth.signup.join_as_project_lead");
    case "SALESMAN":
      return t("auth.signup.join_as_salesman");
    case "WORKER":
      return t("auth.signup.join_as_specialist");
    case "SUPPORTER":
      return t("landing.crowdfunding.supporter_modal.registration_title");
    default:
      return t("auth.signup.welcome_title");
  }
});

const subtitle = computed(() => {
  switch (props.role) {
    case "CLIENT":
      return t("auth.signup.create_account_for_company");
    case "PROJECTLEADER":
      return t("auth.signup.create_account_as_project_lead");
    case "SALESMAN":
      return t("auth.signup.create_account_as_salesman");
    case "WORKER":
      return t("auth.signup.create_account_as_specialist");
    case "SUPPORTER":
      return t("landing.crowdfunding.supporter_modal.registration_details");
    default:
      return t("auth.signup.create_account");
  }
});

const submitButtonText = computed(() => {
  // Show the button text based on role
  switch (props.role) {
    case "CLIENT":
      return t("auth.signup.register_company_button");
    case "SUPPORTER":
      return (
        props.submitButtonText ||
        t("landing.crowdfunding.supporter_modal.register_button")
      );
    default:
      return t("auth.signup.create_account_button");
  }
});

// Company field is always required

// Validation messages
const VALIDATION_TEXT = {
  EMAIL_REQUIRED: t("validation.email_required"),
  FIRST_NAME_REQUIRED: t("validation.first_name_required"),
  LAST_NAME_REQUIRED: t("validation.last_name_required"),
  MOBILE_REQUIRED: t("validation.mobile_required"),
  COMPANY_REQUIRED: t("validation.company_required"),
  PASSWORD_LENGTH: t("validation.password_length"),
  PASSWORD_MATCH: t("validation.password_match"),
};

// Create Zod schema with company field always required
const zodSchema = computed(() => {
  return z
    .object({
      firstName: z.string().min(1, VALIDATION_TEXT.FIRST_NAME_REQUIRED),
      lastName: z.string().min(1, VALIDATION_TEXT.LAST_NAME_REQUIRED),
      mobile: z.string().min(1, VALIDATION_TEXT.MOBILE_REQUIRED),
      email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
      company: z.string().min(1, VALIDATION_TEXT.COMPANY_REQUIRED),
      password: z.string().min(8, VALIDATION_TEXT.PASSWORD_LENGTH),
      confirmPassword: z.string(),
    })
    .superRefine((data: any, ctx: any) => {
      // Check password validation feedback
      if (
        passwordRef.value?.validation?.feedback?.warning ||
        passwordRef.value?.validation?.feedback?.suggestions?.length
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            passwordRef.value?.validation?.feedback?.warning ||
            passwordRef.value.validation?.feedback?.suggestions?.[0],
          path: ["password"],
        });
      }

      // Check if passwords match
      if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: VALIDATION_TEXT.PASSWORD_MATCH,
          path: ["confirmPassword"],
        });
      }
    });
});

// Convert Zod schema to VeeValidate schema
const validationSchema = computed(() => toTypedSchema(zodSchema.value));

// Initialize form with initial values if provided
const { handleSubmit, isSubmitting, setFieldError, values } = useForm({
  validationSchema: computed(() => validationSchema.value),
  initialValues: {
    email: props.prefillEmail,
    company: props.prefillCompany,
    firstName: "",
    lastName: "",
    mobile: "",
    password: "",
    confirmPassword: "",
  },
});

// Custom loading state for the button
const loading = ref(false);

// Password field ref
const passwordRef = ref<any>(null);

// Mobile field handling
const { value: mobileValue, errorMessage: mobileErrorMessage } =
  useField<string>("mobile");

function onValidate(isValid: boolean) {
  if (!isValid && mobileValue.value) {
    mobileErrorMessage.value = t("validation.invalid_mobile");
  } else {
    mobileErrorMessage.value = "";
  }
}

function onCountryChanged(countryCode: string) {
  console.log("Country changed:", countryCode);
}

watch(mobileValue, (newValue) => {
  console.log("Mobile Value Changed:", newValue);
});

// Form submission
const onSubmit = handleSubmit(async (values: any) => {
  try {
    console.log("Form submitted with values:", values);
    console.log("Current role from props:", props.role);
    console.log("isSubmitting state:", isSubmitting.value);

    // Set loading state to true
    loading.value = true;
    console.log("Setting loading state to true:", loading.value);

    // Prepare registration data
    const registrationData = {
      firstName: values.firstName,
      lastName: values.lastName,
      mobile: values.mobile,
      email: values.email,
      companyName: values.company, // Use company field to match the backend API
      password: values.password,
      role: props.role,
    };

    console.log("Prepared registration data:", registrationData);

    // Show loading state
    const toasts = useNuiToasts();

    try {
      // The isSubmitting state should be true at this point
      console.log("Before emitting event, isSubmitting:", isSubmitting.value);
      console.log("Before emitting event, loading:", loading.value);

      // Emit success event with registration data
      emit("register-success", registrationData);
      console.log("Registration success event emitted");

      // The parent component will handle the API call and state transitions
      // loading will remain true until the API call completes
    } catch (apiError: any) {
      console.error("API error during registration:", apiError);

      // Reset loading state
      loading.value = false;
      console.log(
        "API error occurred, resetting loading state:",
        loading.value
      );

      // Determine the error message to display
      let errorMessage = t("auth.signup.registration_failed");

      // Check for specific error patterns in the message
      if (apiError.message) {
        const errorMsg = apiError.message;
        console.log("API error message:", errorMsg);

        // Always use the translation function to get the localized message
        if (errorMsg.includes("company already has an owner")) {
          errorMessage = t("auth.signup.company_already_exists");
          console.log("Using translated company error message:", errorMessage);
        } else if (errorMsg.includes("already in use")) {
          errorMessage = t("auth.signup.email_already_exists");
          console.log("Using translated email error message:", errorMessage);
        } else {
          // Use the original message if it doesn't match any known patterns
          errorMessage = errorMsg;
        }
      }

      // Show error toast with the translated message
      console.log("Showing error toast with message:", errorMessage);
      toasts.add({
        title: t("common.error"),
        description: errorMessage,
        icon: "lucide:alert-triangle",
        progress: true,
        duration: 8000, // 8 seconds
      });

      // If the server returns validation errors, show them in the form
      if (apiError.response?.data?.errors) {
        for (const key in apiError.response.data.errors) {
          // Only set error for fields we know about
          if (
            [
              "firstName",
              "lastName",
              "mobile",
              "email",
              "password",
              "confirmPassword",
              "companyName",
            ].includes(key)
          ) {
            setFieldError(key as any, apiError.response.data.errors[key]);
          }
        }
      }

      // Special handling for company name error
      if (
        apiError.message &&
        apiError.message.includes("company already has an owner")
      ) {
        setFieldError("company", t("auth.signup.company_already_exists"));
      }
    }
  } catch (error: any) {
    console.error("Registration form error =>", error);
    console.log("isSubmitting state in catch block:", isSubmitting.value);

    // Reset loading state
    loading.value = false;
    console.log("Form error occurred, resetting loading state:", loading.value);

    // Determine the error message to display
    let errorMessage = t("auth.signup.registration_failed");

    // Check for specific error patterns in the message
    if (error.message) {
      const errorMsg = error.message;
      console.log("Error message:", errorMsg);

      // Always use the translation function to get the localized message
      if (errorMsg.includes("company already has an owner")) {
        errorMessage = t("auth.signup.company_already_exists");
        console.log("Using translated company error message:", errorMessage);
      } else if (errorMsg.includes("already in use")) {
        errorMessage = t("auth.signup.email_already_exists");
        console.log("Using translated email error message:", errorMessage);
      } else {
        // Use the original message if it doesn't match any known patterns
        errorMessage = errorMsg;
      }
    }

    // Show error toast with the translated message
    console.log("Showing error toast with message:", errorMessage);
    const toasts = useNuiToasts();
    toasts.add({
      title: t("common.error"),
      description: errorMessage,
      icon: "lucide:alert-triangle",
      progress: true,
      duration: 8000, // 8 seconds
    });

    // If the error has validation details, show them in the form
    if (error.response?.data?.errors) {
      for (const key in error.response.data.errors) {
        // Only set error for fields we know about
        if (
          [
            "firstName",
            "lastName",
            "mobile",
            "email",
            "password",
            "confirmPassword",
            "companyName",
          ].includes(key)
        ) {
          setFieldError(key as any, error.response.data.errors[key]);
        }
      }
    }

    // Special handling for company name error
    if (
      error.message &&
      error.message.includes("company already has an owner")
    ) {
      setFieldError("company", t("auth.signup.company_already_exists"));
    }
  }
});
</script>
