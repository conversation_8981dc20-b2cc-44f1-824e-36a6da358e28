import { Request, Response } from "express";
import { prisma } from "../../lib/prisma.js";
import bcrypt from "bcrypt";

// Using centralized prisma instance from lib/prisma.js

// Get current user with companies and roles
export const getCurrentUser = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user from database with companies and roles
    const user = await prisma.user.findUnique({
      where: {
        id: Number(userId),
      },
      include: {
        companies: {
          include: {
            company: {
              select: {
                id: true,
                name: true,
                logo: true,
                coverImage: true,
                type: true,
                status: true,
                // Basic Information
                website: true,
                email: true,
                phone: true,
                registrationNumber: true,
                vatNumber: true,
                industry: true,
                description: true,
                foundedYear: true,
                employeeCount: true,
                annualRevenue: true,
                // Address Information
                address: true,
                address2: true,
                city: true,
                postalCode: true,
                state: true,
                country: true,
                // System timestamps
                createdAt: true,
                updatedAt: true,
              },
            },
          },
        },
        userRoles: true,
      },
    });

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    // Check if user is SUPERADMIN
    const isSuperAdmin = user.userRoles.some(
      (role) => role.role === "SUPERADMIN"
    );

    // Return user profile with formatted data including all fields
    res.status(200).json({
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      birthdate: user.birthdate,
      phone: user.phone,
      avatar: user.avatar,
      coverImage: user.coverImage,
      // Address fields
      address: user.address,
      address2: user.address2,
      street: user.street,
      city: user.city,
      postalCode: user.postalCode,
      state: user.state,
      country: user.country,
      notes: user.notes,
      // Confirmation fields
      emailConfirmed: user.emailConfirmed,
      phoneConfirmed: user.phoneConfirmed,
      // Identification codes
      barcode: user.barcode,
      qrCode: user.qrCode,
      // Relations and metadata
      companies: user.companies,
      roles: user.userRoles,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isSuperAdmin,
      preferences: {
        theme: "system",
        language: "en",
        notifications: {
          enabled: true,
          flushLowPriority: false,
          marketing: false,
          partners: false,
        },
        security: {
          twoFactorEnabled: false,
        },
      },
    });
  } catch (error) {
    console.error("Error getting current user:", error);
    res.status(500).json({ error: "Failed to get current user" });
  }
};

// Get user profile
export const getUserProfile = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: {
        id: Number(userId),
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        userRoles: true, // Get roles instead of role
        avatar: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    // Return user profile
    res.status(200).json(user);
  } catch (error) {
    console.error("Error getting user profile:", error);
    res.status(500).json({ error: "Failed to get user profile" });
  }
};

// Update user profile
export const updateUserProfile = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const {
      firstName,
      lastName,
      phone,
      birthdate,
      bio,
      location,
      // Address fields
      address,
      address2,
      street,
      city,
      postalCode,
      state,
      country,
      notes,
    } = req.body;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (birthdate !== undefined)
      updateData.birthdate = birthdate ? new Date(birthdate) : null;
    if (bio !== undefined) updateData.notes = bio;
    if (location !== undefined) updateData.address = location;
    if (address !== undefined) updateData.address = address;
    if (address2 !== undefined) updateData.address2 = address2;
    if (street !== undefined) updateData.street = street;
    if (city !== undefined) updateData.city = city;
    if (postalCode !== undefined) updateData.postalCode = postalCode;
    if (state !== undefined) updateData.state = state;
    if (country !== undefined) updateData.country = country;
    if (notes !== undefined) updateData.notes = notes;

    // Update user in database
    const updatedUser = await prisma.user.update({
      where: {
        id: Number(userId),
      },
      data: updateData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        birthdate: true,
        phone: true,
        avatar: true,
        coverImage: true,
        // Address fields
        address: true,
        address2: true,
        street: true,
        city: true,
        postalCode: true,
        state: true,
        country: true,
        notes: true,
        // Confirmation fields
        emailConfirmed: true,
        phoneConfirmed: true,
        // Identification codes
        barcode: true,
        qrCode: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Return updated user profile
    res.status(200).json(updatedUser);
  } catch (error) {
    console.error("Error updating user profile:", error);
    res.status(500).json({ error: "Failed to update user profile" });
  }
};

// Update user preferences
export const updateUserPreferences = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const preferences = req.body;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // For now, we'll just return the preferences as they are
    // In a real implementation, you would store these in a database
    res.status(200).json(preferences);
  } catch (error) {
    console.error("Error updating user preferences:", error);
    res.status(500).json({ error: "Failed to update user preferences" });
  }
};

// Change user password
export const changePassword = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { currentPassword, newPassword } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: "Unauthorized",
      });
      return;
    }

    // Validate required fields
    if (!currentPassword || !newPassword) {
      res.status(400).json({
        success: false,
        error: "Current password and new password are required",
      });
      return;
    }

    // Validate new password length
    if (newPassword.length < 8) {
      res.status(400).json({
        success: false,
        error: "New password must be at least 8 characters long",
      });
      return;
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: {
        id: Number(userId),
      },
      select: {
        id: true,
        password: true,
        passwordChangedAt: true,
      },
    });

    if (!user || !user.password) {
      res.status(404).json({
        success: false,
        error: "User not found or no password set",
      });
      return;
    }

    // Check rate limiting - only allow password change once per hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    if (user.passwordChangedAt && user.passwordChangedAt > oneHourAgo) {
      const timeUntilNextChange = new Date(
        user.passwordChangedAt.getTime() + 60 * 60 * 1000
      );
      const minutesLeft = Math.ceil(
        (timeUntilNextChange.getTime() - Date.now()) / (1000 * 60)
      );

      res.status(429).json({
        success: false,
        error: `Password can only be changed once per hour. Please try again in ${minutesLeft} minutes.`,
      });
      return;
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password
    );

    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        error: "Current password is incorrect",
      });
      return;
    }

    // Check if new password is the same as current password
    const isSamePassword = await bcrypt.compare(newPassword, user.password);

    if (isSamePassword) {
      res.status(400).json({
        success: false,
        error: "New password must be different from your current password",
      });
      return;
    }

    // Hash new password
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password in database
    const now = new Date();
    await prisma.user.update({
      where: {
        id: Number(userId),
      },
      data: {
        password: hashedNewPassword,
        passwordChangedAt: now,
        updatedAt: now,
      },
    });

    // Return success response
    res.status(200).json({
      success: true,
      message: "Password changed successfully",
    });
  } catch (error) {
    console.error("Error changing password:", error);
    res.status(500).json({
      success: false,
      error: "Failed to change password",
    });
  }
};
