// client/services/speechRecognition.ts

// Define SpeechRecognition types
interface SpeechRecognitionResult {
  readonly isFinal: boolean;
  readonly [index: number]: SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  readonly transcript: string;
  readonly confidence: number;
}

interface SpeechRecognitionResultList {
  readonly length: number;
  readonly [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onresult:
    | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void)
    | null;
  onerror:
    | ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void)
    | null;
  onstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onend: ((this: SpeechRecognition, ev: Event) => void) | null;
  start(): void;
  stop(): void;
}

class SpeechRecognitionService {
  private recognition: SpeechRecognition | null = null;
  private isInitialized = false;
  private isAutoRestarting = false;

  // Check if a specific language is supported
  checkLanguageSupport(langCode: string): boolean {
    console.log(
      `SpeechRecognitionService: Checking support for language: ${langCode}`
    );

    // This is a best-effort check since browsers don't provide a direct way to check
    // We'll create a temporary recognition instance and try to set the language
    try {
      let tempRecognition: SpeechRecognition | null = null;

      if ("webkitSpeechRecognition" in window) {
        // Define the WebkitSpeechRecognition constructor
        interface WebkitSpeechRecognitionConstructor {
          new (): SpeechRecognition;
        }

        // Cast window.webkitSpeechRecognition to the constructor type
        const WebkitSpeechRecognition =
          window.webkitSpeechRecognition as unknown as WebkitSpeechRecognitionConstructor;
        tempRecognition = new WebkitSpeechRecognition();
      } else if ("SpeechRecognition" in window) {
        // Define the SpeechRecognition constructor
        interface SpeechRecognitionConstructor {
          new (): SpeechRecognition;
        }

        // Cast window.SpeechRecognition to the constructor type
        const SpeechRecognitionClass =
          window.SpeechRecognition as unknown as SpeechRecognitionConstructor;
        tempRecognition = new SpeechRecognitionClass();
      } else {
        console.log(
          "SpeechRecognitionService: Speech recognition not supported by this browser"
        );
        return false;
      }

      // At this point, tempRecognition should not be null
      if (!tempRecognition) {
        console.error(
          "SpeechRecognitionService: Failed to create recognition instance"
        );
        return false;
      }

      // Try to set the language
      tempRecognition.lang = langCode;

      // If we got here without errors, and the language was set correctly, it's probably supported
      const isSupported = tempRecognition.lang === langCode;
      console.log(
        `SpeechRecognitionService: Language ${langCode} support check result: ${isSupported}`
      );

      return isSupported;
    } catch (error) {
      console.error(
        `SpeechRecognitionService: Error checking language support for ${langCode}:`,
        error
      );
      return false;
    }
  }

  init() {
    console.log(
      "SpeechRecognitionService: init called, isInitialized:",
      this.isInitialized
    );
    if (this.isInitialized) return;

    console.log(
      "SpeechRecognitionService: Checking for webkitSpeechRecognition"
    );
    if ("webkitSpeechRecognition" in window) {
      console.log(
        "SpeechRecognitionService: webkitSpeechRecognition is available"
      );
      try {
        // Define the WebkitSpeechRecognition constructor
        interface WebkitSpeechRecognitionConstructor {
          new (): SpeechRecognition;
        }

        // Cast window.webkitSpeechRecognition to the constructor type
        const WebkitSpeechRecognition =
          window.webkitSpeechRecognition as unknown as WebkitSpeechRecognitionConstructor;
        this.recognition = new WebkitSpeechRecognition();
        console.log(
          "SpeechRecognitionService: Created recognition instance:",
          this.recognition
        );
        this.setupRecognition();
        this.isInitialized = true;
        console.log("SpeechRecognitionService: Initialization complete");
      } catch (err) {
        console.error(
          "SpeechRecognitionService: Error initializing recognition:",
          err
        );
        throw new Error(`Failed to initialize speech recognition: ${err}`);
      }
    } else {
      console.error(
        "SpeechRecognitionService: Speech recognition is not supported in this browser"
      );
      throw new Error("Speech recognition is not supported in this browser");
    }
  }

  private setupRecognition() {
    console.log("SpeechRecognitionService: setupRecognition called");
    if (!this.recognition) {
      console.error(
        "SpeechRecognitionService: Recognition is null in setupRecognition"
      );
      return;
    }

    this.recognition.continuous = true;
    this.recognition.interimResults = true;

    // Use Estonian language if specified, otherwise default to English
    const userLanguage = localStorage.getItem("preferredLanguage") || "en";
    const preferredLangCode = userLanguage === "et" ? "et-EE" : "en-US";

    // Check if the preferred language is supported
    const isPreferredSupported = this.checkLanguageSupport(preferredLangCode);

    // Use the preferred language if supported, otherwise fall back to English
    const langCode = isPreferredSupported ? preferredLangCode : "en-US";
    this.recognition.lang = langCode;

    console.log("SpeechRecognitionService: Language details:");
    console.log(`- User selected language: ${userLanguage}`);
    console.log(`- Preferred language code: ${preferredLangCode}`);
    console.log(`- Is preferred language supported: ${isPreferredSupported}`);
    console.log(`- Final language code set: ${langCode}`);
    console.log(`- Recognition language: ${this.recognition.lang}`);

    // If Estonian was requested but not supported, show a warning
    if (userLanguage === "et" && !isPreferredSupported) {
      console.warn(
        "SpeechRecognitionService: Estonian language (et-EE) is not supported by this browser. Falling back to English (en-US)."
      );
    }

    this.recognition.maxAlternatives = 3;
    console.log("SpeechRecognitionService: Recognition configured");
  }

  startListening(
    onResult: (transcript: string) => void,
    onError: (error: string) => void
  ) {
    console.log("SpeechRecognitionService: startListening called");

    // Always reinitialize to ensure we have the latest language settings
    this.isInitialized = false;

    if (!this.recognition) {
      console.log(
        "SpeechRecognitionService: Recognition not initialized, initializing..."
      );
      try {
        this.init();
        console.log("SpeechRecognitionService: Recognition initialized");
      } catch (err) {
        console.error(
          "SpeechRecognitionService: Error initializing recognition:",
          err
        );
        onError(`Failed to initialize speech recognition: ${err}`);
        return;
      }
    } else {
      // Reinitialize to pick up any language changes
      try {
        this.setupRecognition();
        console.log(
          "SpeechRecognitionService: Recognition reconfigured with new settings"
        );
      } catch (err) {
        console.error(
          "SpeechRecognitionService: Error reconfiguring recognition:",
          err
        );
      }
    }

    console.log("SpeechRecognitionService: Setting up event handlers");
    this.recognition!.onresult = (event: SpeechRecognitionEvent) => {
      console.log("SpeechRecognitionService: onresult event", event);

      // Process interim results to provide immediate feedback
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];

        if (result.isFinal) {
          // Get the transcript with highest confidence
          const transcript = result[0].transcript;
          console.log(
            "SpeechRecognitionService: Final transcript:",
            transcript,
            "Confidence:",
            result[0].confidence
          );
          onResult(transcript);
        } else {
          // Show interim results for better user feedback
          const interimTranscript = result[0].transcript;
          console.log(
            "SpeechRecognitionService: Interim transcript:",
            interimTranscript
          );
          // We could send interim results to the UI if needed
        }
      }
    };

    this.recognition!.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error("SpeechRecognitionService: onerror event", event);

      // Don't report 'no-speech' as an error to the caller
      // This is a common event that happens when the user doesn't speak
      if (event.error !== "no-speech") {
        onError(event.error);
      }
    };

    this.recognition!.onstart = () => {
      console.log("SpeechRecognitionService: Recognition started");
    };

    this.recognition!.onend = () => {
      console.log("SpeechRecognitionService: Recognition ended");

      // Only auto-restart if we haven't explicitly stopped listening
      if (!this.isAutoRestarting) {
        this.isAutoRestarting = true;

        // Automatically restart recognition after a short delay
        // This helps with continuous listening even after no-speech errors
        setTimeout(() => {
          if (this.recognition && this.isAutoRestarting) {
            try {
              console.log(
                "SpeechRecognitionService: Auto-restarting recognition"
              );
              this.recognition.start();
              this.isAutoRestarting = false;
            } catch (err) {
              console.error(
                "SpeechRecognitionService: Error auto-restarting recognition:",
                err
              );
              this.isAutoRestarting = false;
            }
          }
        }, 300);
      } else {
        console.log(
          "SpeechRecognitionService: Not auto-restarting because stopListening was called"
        );
      }
    };

    console.log("SpeechRecognitionService: Starting recognition");
    try {
      // Stop any existing recognition first to avoid "already started" error
      if (this.recognition) {
        try {
          this.recognition.stop();
          console.log(
            "SpeechRecognitionService: Stopped existing recognition before starting new one"
          );
        } catch (stopError) {
          console.log(
            "SpeechRecognitionService: No existing recognition to stop or already stopped"
          );
        }
      }

      // Small delay to ensure the previous recognition has fully stopped
      setTimeout(() => {
        try {
          this.recognition!.start();
          console.log(
            "SpeechRecognitionService: Recognition started successfully"
          );
        } catch (startError) {
          console.error(
            "SpeechRecognitionService: Error starting recognition:",
            startError
          );
          onError(`Failed to start speech recognition: ${startError}`);
        }
      }, 100);
    } catch (err) {
      console.error("SpeechRecognitionService: Error in start process:", err);
      onError(`Failed to start speech recognition: ${err}`);
    }
  }

  stopListening() {
    console.log("SpeechRecognitionService: stopListening called");
    this.isAutoRestarting = true; // Set to true to prevent auto-restart

    try {
      if (this.recognition) {
        this.recognition.stop();
        console.log("SpeechRecognitionService: Recognition stopped");
      } else {
        console.log(
          "SpeechRecognitionService: No recognition instance to stop"
        );
      }
    } catch (err) {
      console.error(
        "SpeechRecognitionService: Error stopping recognition:",
        err
      );
    }
  }
}

// Create a singleton instance
const speechRecognitionInstance = new SpeechRecognitionService();

// Export a wrapper with public methods
export const speechRecognition = {
  startListening: speechRecognitionInstance.startListening.bind(
    speechRecognitionInstance
  ),
  stopListening: speechRecognitionInstance.stopListening.bind(
    speechRecognitionInstance
  ),
  checkLanguageSupport: speechRecognitionInstance.checkLanguageSupport.bind(
    speechRecognitionInstance
  ),
};
