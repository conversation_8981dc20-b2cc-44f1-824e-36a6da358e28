<template>
  <!-- AI Assistant Button -->
  <button
    type="button"
    class="relative flex items-center justify-center px-3 py-1 bg-gradient-to-r from-primary-500 to-purple-500 text-white rounded-md font-mono text-sm transition-all duration-300 cursor-pointer"
    :class="{
      'hover:from-primary-600 hover:to-purple-600': !isWindowOpen,
      'from-primary-600 to-purple-600 shadow-lg': isWindowOpen,
      'animate-pulse': isProcessing,
    }"
    data-ai-intent="toggle-voice-assistant"
    @click="toggleAiAssistant"
  >
    <span class="font-mono tracking-widest text-white font-bold text-xs mr-1"
      >AI</span
    >

    <Icon
      :name="isListening ? 'ph:microphone-fill' : 'ph:microphone'"
      class="size-4"
      :class="{ 'animate-pulse': isListening }"
    />

    <!-- Status Indicator -->
    <span class="absolute -top-1 -right-1 flex h-2 w-2">
      <span
        v-if="isListening || isProcessing"
        class="animate-ping absolute inline-flex h-full w-full rounded-full opacity-75"
        :class="{
          'bg-green-400': isListening && !isProcessing,
          'bg-blue-400': isProcessing,
        }"
      />
      <span
        class="relative inline-flex rounded-full h-2 w-2"
        :class="{
          'bg-green-500': isListening && !isProcessing,
          'bg-blue-500': isProcessing,
          'bg-gray-400': !isListening && !isProcessing,
        }"
      />
    </span>
  </button>
</template>

<script setup lang="ts">
import { useAiAssistant } from "../composables/useAiAssistant";
import { useAiAssistantManager } from "../composables/useAiAssistantManager";

// Get AI assistant state for button indicators
const { isListening, isProcessing, stopListening, stopSpeech } =
  useAiAssistant();

// Get AI assistant manager for window control
const { isWindowOpen, toggleWindow } = useAiAssistantManager();

// Toggle AI assistant - opens/closes window and starts/stops listening
const toggleAiAssistant = () => {
  console.log(
    "AiAssistantButton: toggleAiAssistant called, window open:",
    isWindowOpen.value
  );

  if (isWindowOpen.value) {
    // Stop all AI processes and close window
    stopListening();
    stopSpeech();
    toggleWindow(); // This will close the window
  } else {
    // Open window (window will handle starting listening)
    toggleWindow(); // This will open the window and start listening
  }
};
</script>
