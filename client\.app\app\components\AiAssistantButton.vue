<template>
  <div class="z-50">
    <!-- AI Assistant Button -->
    <button
      type="button"
      class="relative flex items-center justify-center px-3 py-1 bg-gradient-to-r from-primary-500 to-purple-500 text-white rounded-md font-mono text-sm transition-all duration-300 cursor-pointer"
      :class="{
        'hover:from-primary-600 hover:to-purple-600': !showToast,
        'from-primary-600 to-purple-600 shadow-lg': showToast,
        'animate-pulse': isProcessing,
      }"
      data-ai-intent="toggle-voice-assistant"
      @click="toggleListening"
    >
      <span class="font-mono tracking-widest text-white font-bold text-xs mr-1"
        >AI</span
      >

      <Icon
        :name="isListening ? 'ph:microphone-fill' : 'ph:microphone'"
        class="size-4"
        :class="{ 'animate-pulse': isListening }"
      />

      <!-- Status Indicator -->
      <span class="absolute -top-1 -right-1 flex h-2 w-2">
        <span
          v-if="isListening || isProcessing"
          class="animate-ping absolute inline-flex h-full w-full rounded-full opacity-75"
          :class="{
            'bg-green-400': isListening && !isProcessing,
            'bg-blue-400': isProcessing,
          }"
        />
        <span
          class="relative inline-flex rounded-full h-2 w-2"
          :class="{
            'bg-green-500': isListening && !isProcessing,
            'bg-blue-500': isProcessing,
            'bg-gray-400': !isListening && !isProcessing,
          }"
        />
      </span>
    </button>

    <!-- AI Assistant Window -->
    <div
      v-if="showToast"
      ref="toastRef"
      class="fixed bottom-4 right-4 bg-white dark:bg-neutral-900 rounded-lg shadow-lg p-4 w-80 z-50 border border-neutral-400 cursor-move"
      @mousedown="startDrag"
      @touchstart="startDrag"
    >
      <!-- Header -->
      <div class="flex items-center justify-between mb-3">
        <h4
          class="text-sm font-medium text-primary-600 dark:text-primary-400 flex items-center"
        >
          <Icon name="ph:microphone-fill" class="h-4 w-4 mr-1" />
          {{ $t("ai.assistant") }}
        </h4>
        <div class="flex items-center gap-2">
          <div v-if="isListening" class="flex items-center">
            <span class="text-xs text-primary-500 mr-1">{{
              $t("ai.listening")
            }}</span>
            <span class="relative flex h-3 w-3">
              <span
                class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"
              />
              <span
                class="relative inline-flex rounded-full h-3 w-3 bg-primary-500"
              />
            </span>
          </div>

          <!-- Speaker Toggle Button -->
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="toggleSpeaker"
            :class="{
              'text-primary-500': globalSpeakerEnabled,
              'text-muted-400': !globalSpeakerEnabled,
            }"
          >
            <Icon
              :name="
                globalSpeakerEnabled ? 'ph:speaker-high' : 'ph:speaker-slash'
              "
              class="h-4 w-4"
            />
          </BaseButton>

          <!-- Close Button -->
          <BaseButton
            size="icon-sm"
            variant="ghost"
            @click="closeToast"
            class="text-muted-400 hover:text-muted-600 dark:text-muted-500 dark:hover:text-muted-300"
          >
            <Icon name="ph:x" class="h-4 w-4" />
          </BaseButton>
        </div>
      </div>

      <!-- Voice Transcript -->
      <div
        v-if="transcript"
        class="bg-gray-50 dark:bg-gray-700 p-2 rounded-md mb-3"
      >
        <p class="text-sm text-gray-600 dark:text-gray-300">{{ transcript }}</p>
      </div>

      <!-- Text Input Area -->
      <div class="mb-3">
        <BaseTextarea
          v-model="textInput"
          :placeholder="$t('ai.type_message')"
          autogrow
          :max-height="150"
          class="w-full"
          @keydown.enter.exact="handleTextSubmit"
        />
        <div class="flex justify-end mt-2">
          <BaseButton
            size="sm"
            variant="primary"
            :disabled="!textInput.trim() || isProcessing"
            :loading="isProcessing"
            @click="handleTextSubmit"
          >
            <Icon name="ph:paper-plane-tilt" class="h-4 w-4 mr-1" />
            {{ $t("ai.send") }}
          </BaseButton>
        </div>
      </div>

      <!-- AI Response -->
      <div
        v-if="aiResponse"
        class="bg-primary-50 dark:bg-primary-900/30 p-3 rounded-md"
      >
        <p class="text-sm font-medium text-gray-700 dark:text-gray-200">
          {{ aiResponse }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAiAssistant } from "../composables/useAiAssistant";
import { ref, onUnmounted, computed, nextTick, watch } from "vue";
import { useUserStore } from "../../stores/useUserStore";

const {
  isListening,
  isProcessing,
  transcript,
  aiResponse,
  startListening,
  stopListening,
  stopSpeech,
  isSpeaking,
  processCommand,
  globalSpeakerEnabled,
  setSpeakerEnabled,
} = useAiAssistant();

// Get i18n and user store
const { t } = useI18n();
const userStore = useUserStore();

// State for toast visibility and position
const showToast = ref(true);
const toastRef = ref<HTMLElement | null>(null);
let isDragging = false;
const dragOffset = { x: 0, y: 0 };

// Text input functionality
const textInput = ref("");

// Get language from user store instead of localStorage
const currentLanguage = computed(() => {
  return userStore.preferences?.language || "en";
});

// Watch for changes in listening state to keep component in sync
watch(isListening, (newValue) => {
  console.log("AiAssistantButton: isListening changed to:", newValue);
  // If listening stops unexpectedly (e.g., due to error), but window is still open,
  // we should keep the window open but show that listening has stopped
  if (!newValue && showToast.value) {
    console.log(
      "AiAssistantButton: Listening stopped but window is still open"
    );
  }
});

// Toggle listening state with proper cleanup
const toggleListening = () => {
  console.log("AiAssistantButton: toggleListening called, current states:", {
    showToast: showToast.value,
    isListening: isListening.value,
    isProcessing: isProcessing.value,
  });
  if (showToast.value) {
    console.log("AiAssistantButton: Stopping all AI processes");
    // Stop all AI processes
    stopListening();
    stopSpeech();
    showToast.value = false;
  } else {
    console.log("AiAssistantButton: Starting listening");
    showToast.value = true; // Show toast when starting to listen
    startListening();
  }
};

// Toggle speaker on/off
const toggleSpeaker = () => {
  setSpeakerEnabled(!globalSpeakerEnabled.value);
  console.log("Speaker enabled:", globalSpeakerEnabled.value);
};

// Handle text input submission
const handleTextSubmit = async (event?: KeyboardEvent) => {
  if (event) {
    event.preventDefault();
  }

  if (!textInput.value.trim() || isProcessing.value) {
    return;
  }

  const message = textInput.value.trim();
  textInput.value = "";

  try {
    await processCommand(message);
  } catch (error) {
    console.error("Error processing text command:", error);
  }
};

// Auto-adjust textarea height (handled by autogrow prop)

// Close toast - stops all AI processes
const closeToast = () => {
  console.log(
    "AiAssistantButton: Closing AI window and stopping all processes"
  );
  stopListening();
  stopSpeech();
  showToast.value = false;
};

// Drag functionality
const startDrag = (event: MouseEvent | TouchEvent) => {
  if (event.target && (event.target as HTMLElement).closest("button")) {
    return; // Don't start drag if clicking on a button
  }

  isDragging = true;
  const toast = toastRef.value;
  if (!toast) return;

  // Get current position
  const rect = toast.getBoundingClientRect();

  // Calculate offset
  if (event instanceof MouseEvent) {
    dragOffset.x = event.clientX - rect.left;
    dragOffset.y = event.clientY - rect.top;
  } else {
    dragOffset.x = event.touches[0].clientX - rect.left;
    dragOffset.y = event.touches[0].clientY - rect.top;
  }

  // Add event listeners for drag and end
  document.addEventListener("mousemove", onDrag);
  document.addEventListener("touchmove", onDrag);
  document.addEventListener("mouseup", endDrag);
  document.addEventListener("touchend", endDrag);
};

const onDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging) return;

  const toast = toastRef.value;
  if (!toast) return;

  let clientX, clientY;

  if (event instanceof MouseEvent) {
    clientX = event.clientX;
    clientY = event.clientY;
  } else {
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  }

  // Calculate new position
  const x = clientX - dragOffset.x;
  const y = clientY - dragOffset.y;

  // Apply new position
  toast.style.position = "fixed";
  toast.style.left = `${x}px`;
  toast.style.top = `${y}px`;
  toast.style.bottom = "auto";
  toast.style.right = "auto";
};

const endDrag = () => {
  isDragging = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("touchmove", onDrag);
  document.removeEventListener("mouseup", endDrag);
  document.removeEventListener("touchend", endDrag);
};

// Clean up event listeners
onUnmounted(() => {
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("touchmove", onDrag);
  document.removeEventListener("mouseup", endDrag);
  document.removeEventListener("touchend", endDrag);
});
</script>
