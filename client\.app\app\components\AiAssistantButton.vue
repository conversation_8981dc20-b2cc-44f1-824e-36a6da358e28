<template>
  <div class="z-50">
    <!-- AI Assistant But<PERSON> -->
    <BaseButton
      :variant="isListening ? 'primary' : 'muted'"
      class="rounded-full p-2 relative flex items-center gap-1"
      data-ai-intent="toggle-voice-assistant"
      @click="toggleListening"
      @mousedown="console.log('AiAssistantButton: mousedown event')"
    >
      <span
        class="font-mono text-lg font-bold"
        :class="isListening ? 'text-white' : 'text-primary-500'"
        style="letter-spacing: 1px; text-shadow: 0 0 5px currentColor"
        >AI</span
      >
      <Icon
        :name="isListening ? 'ph:microphone-fill' : 'ph:microphone'"
        class="size-6"
        :class="{ 'animate-pulse': isListening }"
      />

      <!-- Processing Indicator -->
      <div v-if="isProcessing" class="absolute -top-1 -right-1 size-3">
        <span
          class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"
        />
        <span class="relative inline-flex rounded-full size-3 bg-primary-500" />
      </div>
    </BaseButton>

    <!-- Feedback Toast -->
    <div
      v-if="isListening || (showToast && transcript)"
      ref="toastRef"
      class="fixed bottom-20 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-sm z-50 border border-primary-100 dark:border-primary-800 cursor-move"
      @mousedown="startDrag"
      @touchstart="startDrag"
    >
      <div class="flex items-center justify-between mb-2">
        <h4 class="text-sm font-medium text-primary-600 dark:text-primary-400 flex items-center">
          <Icon name="ph:microphone-fill" class="h-4 w-4 mr-1" />
          AI Assistant
        </h4>
        <div class="flex items-center gap-2">
          <div v-if="isListening" class="flex items-center">
            <span class="text-xs text-primary-500 mr-1">Listening</span>
            <span class="relative flex h-3 w-3">
              <span
                class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"
              />
              <span class="relative inline-flex rounded-full h-3 w-3 bg-primary-500" />
            </span>
          </div>
          <select
            v-model="selectedLanguage"
            class="text-xs bg-transparent border border-muted-200 dark:border-muted-700 rounded px-1 py-0.5 mr-1"
            @change="changeLanguage"
          >
            <option value="en">EN</option>
            <option value="et">ET</option>
          </select>
          <button
            class="text-muted-400 hover:text-muted-600 dark:text-muted-500 dark:hover:text-muted-300 transition-colors"
            @click="closeToast"
          >
            <Icon name="ph:x" class="h-4 w-4" />
          </button>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-700 p-2 rounded-md">
        <p class="text-sm text-gray-600 dark:text-gray-300">{{ transcript }}</p>
      </div>
      <div
        v-if="warningMessage"
        class="mt-2 bg-yellow-50 dark:bg-yellow-900/30 p-2 rounded-md border border-yellow-200 dark:border-yellow-800"
      >
        <p class="text-xs text-yellow-700 dark:text-yellow-300 flex items-center">
          <Icon name="ph:warning" class="h-3 w-3 mr-1 text-yellow-500" />
          {{ warningMessage }}
        </p>
      </div>
      <div v-if="aiResponse" class="mt-3 bg-primary-50 dark:bg-primary-900/30 p-2 rounded-md">
        <p class="text-sm font-medium text-gray-700 dark:text-gray-200">{{ aiResponse }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAiAssistant } from '../composables/useAiAssistant'
import { ref, onUnmounted } from 'vue'

const { isListening, isProcessing, transcript, aiResponse, startListening, stopListening } =
  useAiAssistant()

// State for toast visibility and position
const showToast = ref(true)
const toastRef = ref<HTMLElement | null>(null)
let isDragging = false
const dragOffset = { x: 0, y: 0 }

// Language selection
const selectedLanguage = ref(localStorage.getItem('preferredLanguage') || 'en')
const warningMessage = ref('')

// Change language
const changeLanguage = () => {
  localStorage.setItem('preferredLanguage', selectedLanguage.value)
  console.log(`AiAssistantButton: Language changed to: ${selectedLanguage.value}`)

  // Check if Estonian is supported when selecting it
  if (selectedLanguage.value === 'et') {
    // Import the speechRecognition service to check language support
    import('../services/speechRecognition').then(({ speechRecognition }) => {
      const isSupported = speechRecognition.checkLanguageSupport('et-EE')
      if (!isSupported) {
        // Show a warning message
        warningMessage.value =
          'Estonian speech recognition may not be fully supported in this browser. Recognition might default to English.'
        setTimeout(() => {
          warningMessage.value = ''
        }, 5000)
      }
    })
  } else {
    warningMessage.value = ''
  }

  // Force restart of speech recognition if currently listening
  if (isListening.value) {
    console.log('AiAssistantButton: Restarting speech recognition with new language')
    stopListening()
    setTimeout(() => {
      startListening()
    }, 300)
  }
}

// Toggle listening state
const toggleListening = () => {
  console.log('AiAssistantButton: toggleListening called, current state:', isListening.value)
  if (isListening.value) {
    console.log('AiAssistantButton: Stopping listening')
    stopListening()
  } else {
    console.log('AiAssistantButton: Starting listening')
    showToast.value = true // Show toast when starting to listen
    startListening()
  }
}

// Close toast
const closeToast = () => {
  showToast.value = false
}

// Drag functionality
const startDrag = (event: MouseEvent | TouchEvent) => {
  if (event.target && (event.target as HTMLElement).closest('button')) {
    return // Don't start drag if clicking on a button
  }

  isDragging = true
  const toast = toastRef.value
  if (!toast) return

  // Get current position
  const rect = toast.getBoundingClientRect()

  // Calculate offset
  if (event instanceof MouseEvent) {
    dragOffset.x = event.clientX - rect.left
    dragOffset.y = event.clientY - rect.top
  } else {
    dragOffset.x = event.touches[0].clientX - rect.left
    dragOffset.y = event.touches[0].clientY - rect.top
  }

  // Add event listeners for drag and end
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('touchmove', onDrag)
  document.addEventListener('mouseup', endDrag)
  document.addEventListener('touchend', endDrag)
}

const onDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging) return

  const toast = toastRef.value
  if (!toast) return

  let clientX, clientY

  if (event instanceof MouseEvent) {
    clientX = event.clientX
    clientY = event.clientY
  } else {
    clientX = event.touches[0].clientX
    clientY = event.touches[0].clientY
  }

  // Calculate new position
  const x = clientX - dragOffset.x
  const y = clientY - dragOffset.y

  // Apply new position
  toast.style.position = 'fixed'
  toast.style.left = `${x}px`
  toast.style.top = `${y}px`
  toast.style.bottom = 'auto'
  toast.style.right = 'auto'
}

const endDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('mouseup', endDrag)
  document.removeEventListener('touchend', endDrag)
}

// Clean up event listeners
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onDrag)
  document.removeEventListener('mouseup', endDrag)
  document.removeEventListener('touchend', endDrag)
})
</script>
