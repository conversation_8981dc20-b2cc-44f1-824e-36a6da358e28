"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  extends: [
  // Extend from the Tairo layer
  "../tairo"],


  modules: [
    // Add any core-specific modules here
  ],

  css: [
  // Add any core-specific CSS here
  "~/assets/custom.css"],


  // Remove i18n configuration from the Core module
  // We'll handle translations in the main app
  // Components are now handled in layer.ts
  app: {
    head: {
      title: "Core Module"
    }
  },

  compatibilityDate: "2025-05-25"
}); /* v9-0a70102c4ecd2ff6 */
