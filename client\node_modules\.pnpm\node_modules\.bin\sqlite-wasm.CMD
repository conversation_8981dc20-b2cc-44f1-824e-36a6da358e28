@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\sqlite-wasm\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\sqlite-wasm\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\sqlite-wasm\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\sqlite-wasm\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules\@sqlite.org\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\@sqlite.org+sqlite-wasm@3.49.1-build2\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@sqlite.org\sqlite-wasm\bin\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@sqlite.org\sqlite-wasm\bin\index.js" %*
)
