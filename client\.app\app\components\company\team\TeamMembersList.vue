<template>
  <div>
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <BasePlaceholderPage
        title="Loading team members"
        subtitle="Please wait while we load the team members..."
      >
        <template #image>
          <div class="flex justify-center">
            <BaseProgress :value="50" size="xl" :indeterminate="true" color="primary" />
          </div>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="flex justify-center py-8">
      <BasePlaceholderPage title="Error loading team members" :subtitle="error">
        <template #image>
          <img
            class="block dark:hidden"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-error.svg"
            alt="Error illustration"
          />
          <img
            class="hidden dark:block"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-error-dark.svg"
            alt="Error illustration"
          />
        </template>
        <template #action>
          <BaseButton color="primary" @click="$emit('refresh')"> Try Again </BaseButton>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Empty state -->
    <div v-else-if="members.length === 0" class="flex justify-center py-8">
      <BasePlaceholderPage
        title="No team members yet"
        subtitle="Invite team members to collaborate on your company projects."
      >
        <template #image>
          <img
            class="block dark:hidden"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-team.svg"
            alt="Team illustration"
          />
          <img
            class="hidden dark:block"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-team-dark.svg"
            alt="Team illustration"
          />
        </template>
        <template #action>
          <BaseButton v-if="canManageTeam" color="primary" @click="$emit('invite')">
            <Icon name="ph:user-plus-duotone" class="h-4 w-4 mr-2" />
            Invite Member
          </BaseButton>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Team members list -->
    <div v-else class="space-y-3">
      <TransitionGroup
        enter-active-class="transform-gpu"
        enter-from-class="opacity-0 -translate-x-full"
        enter-to-class="opacity-100 translate-x-0"
        leave-active-class="absolute transform-gpu"
        leave-from-class="opacity-100 translate-x-0"
        leave-to-class="opacity-0 -translate-x-full"
      >
        <TeamMemberRow
          v-for="member in members"
          :key="member.id"
          :member="member"
          :can-edit="canEditMember(member)"
          :can-remove="canRemoveMember(member)"
          @edit="$emit('edit', member)"
          @remove="$emit('remove', member)"
        />
      </TransitionGroup>
    </div>
  </div>
</template>

<script setup lang="ts">
import TeamMemberRow from './TeamMemberRow.vue'

const props = defineProps({
  members: {
    type: Array,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  },
  canManageTeam: {
    type: Boolean,
    default: false,
  },
  currentUserId: {
    type: [Number, String],
    default: null,
  },
})

defineEmits(['refresh', 'invite', 'edit', 'remove'])

// Helper functions
const canEditMember = (member) => {
  // Can't edit yourself or owners
  return props.canManageTeam && member.userId !== props.currentUserId && member.role !== 'OWNER'
}

const canRemoveMember = (member) => {
  // Can't remove yourself or owners
  return props.canManageTeam && member.userId !== props.currentUserId && member.role !== 'OWNER'
}
</script>
