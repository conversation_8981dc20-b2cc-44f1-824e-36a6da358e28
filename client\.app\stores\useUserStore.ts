// client/.app/app/stores/useUserStore.ts
import { defineStore } from "pinia";
import { useApi } from "~/composables/useApi";
import { apiClient } from "~/utils/api/client";
import { useAuthStore } from "./useAuthStore";
import { useNuiToasts } from "#imports";

// Helper functions for toast notifications
const showSuccessToast = (message: string) => {
  const toasts = useNuiToasts();
  toasts.add({
    title: "Success",
    description: message,
    icon: "lucide:check-circle",
    progress: true,
    duration: 8000, // 8 seconds
  });
};

const showErrorToast = (message: string) => {
  const toasts = useNuiToasts();
  toasts.add({
    title: "Error",
    description: message,
    icon: "lucide:alert-triangle",
    progress: true,
    duration: 8000, // 8 seconds
  });
};

// User company interface
export interface UserCompany {
  id: string;
  companyId: string;
  userId: number;
  role: string;
  status: string;
  company: {
    id: string;
    name: string;
    logo?: string;
    coverImage?: string;
    type?: string;
    status?: string;
    // Basic Information
    website?: string;
    email?: string;
    phone?: string;
    registrationNumber?: string;
    vatNumber?: string;
    industry?: string;
    description?: string;
    foundedYear?: number;
    employeeCount?: number;
    annualRevenue?: number;
    // Identification codes
    barcode?: string;
    qrCode?: string;
    // Address Information
    address?: string;
    address2?: string;
    city?: string;
    postalCode?: string;
    state?: string;
    country?: string;
    // System timestamps
    createdAt?: string;
    updatedAt?: string;
  };
}

// User role interface
export interface UserRole {
  id: number;
  userId: number;
  role: string;
}

// User preferences interface
export interface UserPreferences {
  theme: "light" | "dark" | "system";
  language: string;
  sidebar: {
    isSubsidebarCollapsed: boolean;
  };
  notifications: {
    enabled: boolean;
    flushLowPriority: boolean;
    marketing: boolean;
    partners: boolean;
    security: {
      session: boolean;
      password: boolean;
    };
    activity: {
      incoming: boolean;
      outgoing: boolean;
      failed: boolean;
      uncashed: boolean;
      payments: boolean;
    };
  };
  security: {
    twoFactorEnabled: boolean;
    phoneNumber?: string;
  };
}

// User subscription interface
export interface UserSubscription {
  id: number;
  planId: number;
  status: string;
  startDate: string;
  endDate?: string;
  trialEndDate?: string;
  plan: {
    id: number;
    name: string;
    price: number;
    features: string[];
  };
}

// User profile interface
export interface UserProfile {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  birthdate?: string;
  phone?: string;
  avatar?: string;
  coverImage?: string;
  address?: string;
  address2?: string;
  street?: string;
  city?: string;
  postalCode?: string;
  state?: string;
  country?: string;
  notes?: string;
  emailConfirmed: boolean;
  phoneConfirmed: boolean;
  barcode?: string;
  qrCode?: string;
  googleId?: string;
  referredByUserId?: number;
  companies: UserCompany[];
  roles: UserRole[];
  subscriptions?: UserSubscription[];
  createdAt: string;
  updatedAt: string;
  isSuperAdmin: boolean;
}

// Login payload interface
interface LoginPayload {
  email: string;
  password: string;
  trustDevice: boolean;
}

// Register payload interface
interface RegisterPayload {
  firstName: string;
  lastName: string;
  mobile: string;
  email: string;
  companyName: string;
  password: string;
  role?: string;
  trustDevice?: boolean;
}

// Profile update payload interface
interface ProfileUpdatePayload {
  firstName?: string;
  lastName?: string;
  phone?: string;
  birthdate?: string;
  address?: string;
  address2?: string;
  street?: string;
  city?: string;
  postalCode?: string;
  state?: string;
  country?: string;
  notes?: string;
}

// Password change payload interface
interface PasswordChangePayload {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// @ts-ignore - Ignore TypeScript errors for now
export const useUserStore = defineStore("user", {
  state: () => ({
    user: null as UserProfile | null,
    preferences: {
      theme: "system",
      language: "en",
      sidebar: {
        isSubsidebarCollapsed: false,
      },
      notifications: {
        enabled: true,
        flushLowPriority: false,
        marketing: false,
        partners: false,
        security: {
          session: true,
          password: true,
        },
        activity: {
          incoming: true,
          outgoing: false,
          failed: false,
          uncashed: false,
          payments: true,
        },
      },
      security: {
        twoFactorEnabled: false,
        phoneNumber: "",
      },
    } as UserPreferences,
    isLoading: false,
    error: null as string | null,
    isAuthenticated: false,
  }),

  getters: {
    // User information getters
    fullName: (state) => {
      if (!state.user) return "";
      return `${state.user.firstName} ${state.user.lastName}`.trim();
    },
    initials: (state) => {
      if (!state.user) return "";
      return `${state.user.firstName.charAt(0)}${state.user.lastName.charAt(
        0
      )}`.toUpperCase();
    },
    userCompanies: (state) => {
      if (!state.user) return [];
      return state.user.companies;
    },
    primaryCompany: (state) => {
      if (!state.user?.companies || state.user.companies.length === 0)
        return null;
      return state.user.companies[0];
    },
    primaryCompanyId: (state) => {
      if (!state.user?.companies || state.user.companies.length === 0)
        return null;
      return state.user.companies[0].companyId;
    },
    userRoles: (state) => {
      if (!state.user) return [];
      return state.user.roles.map((role) => role.role);
    },

    // Role-based getters
    isSuperAdmin: (state) => {
      if (!state.user) return false;
      return state.user.isSuperAdmin;
    },
    isAdmin: (state) => {
      if (!state.user) return false;
      return state.user.roles.some((role) => role.role === "ADMIN");
    },
    isProjectLeader: (state) => {
      if (!state.user) return false;
      return state.user.roles.some((role) => role.role === "PROJECTLEADER");
    },
    isSalesman: (state) => {
      if (!state.user) return false;
      return state.user.roles.some((role) => role.role === "SALESMAN");
    },
    isWorker: (state) => {
      if (!state.user) return false;
      return state.user.roles.some((role) => role.role === "WORKER");
    },
    isClient: (state) => {
      if (!state.user) return false;
      return state.user.roles.some((role) => role.role === "CLIENT");
    },

    // Subscription getters
    hasActiveSubscription: (state) => {
      if (!state.user?.subscriptions || state.user.subscriptions.length === 0)
        return false;
      return state.user.subscriptions.some((sub) => sub.status === "ACTIVE");
    },
    activeSubscription: (state) => {
      if (!state.user?.subscriptions || state.user.subscriptions.length === 0)
        return null;
      return state.user.subscriptions.find((sub) => sub.status === "ACTIVE");
    },

    // Preferences getters
    currentTheme: (state) => state.preferences.theme,
    currentLanguage: (state) => state.preferences.language,
  },

  actions: {
    /**
     * Fetch the current user's profile and preferences
     */
    async fetchUser() {
      try {
        this.isLoading = true;
        this.error = null;

        const authStore = useAuthStore();
        if (!authStore.isLoggedIn) {
          this.isAuthenticated = false;
          return;
        }

        // Fetch user profile from API
        const api = useApi();
        const userData = await api.get("/users/me");

        if (userData) {
          this.user = userData as UserProfile;
          this.isAuthenticated = true;

          // Initialize preferences if they don't exist
          if ((userData as any).preferences) {
            this.preferences = (userData as any).preferences;
          }

          // Migrate preferences to ensure all required properties exist
          if (!this.preferences.sidebar) {
            this.preferences.sidebar = {
              isSubsidebarCollapsed: false,
            };
          }

          console.log("User profile loaded:", this.user);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
        this.error = "Failed to fetch user data";
        this.isAuthenticated = false;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Update the user's profile information
     */
    async updateProfile(profileData: ProfileUpdatePayload) {
      try {
        this.isLoading = true;
        this.error = null;

        console.log("Updating profile with data:", profileData);

        const api = useApi();
        const updatedData = await api.put("/users/profile", profileData);

        if (updatedData) {
          console.log("Profile update response:", updatedData);

          // Update only the fields that were changed
          this.user = { ...this.user, ...updatedData } as UserProfile;

          // Fetch the full user profile to ensure we have the latest data
          await (this as any).fetchUser();

          // Show success toast
          showSuccessToast("Profile updated successfully");
        }

        return updatedData;
      } catch (error) {
        console.error("Error updating profile:", error);
        this.error = "Failed to update profile";

        // Show error toast
        showErrorToast("Failed to update profile");

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Update the user's preferences
     * @param preferences - The preferences to update
     */
    async updatePreferences(preferences: Partial<UserPreferences>) {
      try {
        // Start updating preferences

        this.isLoading = true;
        this.error = null;

        // Update local preferences immediately for better UX
        this.preferences = { ...this.preferences, ...preferences };
        // Local preferences updated immediately

        let apiResponse = null;
        try {
          // Try to make API call to persist preferences
          const api = useApi();
          const response = await api.put("/users/preferences", preferences);

          if (response) {
            // Update with server response data
            this.preferences = { ...this.preferences, ...response };
            apiResponse = response;
            // Preferences updated with server data

            // Show success toast
            showSuccessToast("Preferences updated successfully");
          }
        } catch (error) {
          // API call failed, but preferences were updated locally
        }

        return apiResponse;
      } catch (error) {
        console.error("Error updating preferences:", error);
        this.error = "Failed to update preferences";

        // Show error toast
        showErrorToast("Failed to update preferences");

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Initialize user language from preferences
     * This is a placeholder method that doesn't do anything anymore
     * The language initialization is now handled directly in the layout component
     */
    initializeLanguage() {
      // This is now a no-op - language initialization is handled in the layout component
    },

    /**
     * Change the user's password
     */
    async changePassword(payload: PasswordChangePayload) {
      try {
        this.isLoading = true;
        this.error = null;

        const api = useApi();
        const response = await api.put("/users/change-password", {
          currentPassword: payload.currentPassword,
          newPassword: payload.newPassword,
        });

        // Check if the API call failed (useApi returns null on error)
        if (response === null) {
          // Extract error information from the useApi error
          let errorMessage = "Failed to change password";
          let statusCode = 500;

          if (api.error.value) {
            const apiError = api.error.value;

            // Get status code from the error code
            if (apiError.code && apiError.code !== "UNKNOWN") {
              statusCode = parseInt(apiError.code);
            }

            // Check if the error details contain the backend response
            if (apiError.details?.error) {
              errorMessage = apiError.details.error;
            } else if (apiError.message) {
              errorMessage = apiError.message;
            }
          }

          this.error = errorMessage;

          return {
            success: false,
            message: errorMessage,
            statusCode: statusCode,
            error: api.error.value,
          };
        }

        // Success case
        return {
          success: true,
          message: "Password changed successfully",
          data: response,
        };
      } catch (error: any) {
        console.error("Error changing password:", error);

        // Extract error message from the response
        let errorMessage = "Failed to change password";
        let statusCode = 500;

        if (error?.response?.status) {
          statusCode = error.response.status;
        }

        if (error?.response?.data?.error) {
          // Use the specific error message from the backend
          errorMessage = error.response.data.error;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        this.error = errorMessage;

        // Return error information instead of throwing
        return {
          success: false,
          message: errorMessage,
          statusCode: statusCode,
          error: error,
        };
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Upload a profile avatar
     */
    async uploadAvatar(file: File) {
      try {
        this.isLoading = true;
        this.error = null;

        const formData = new FormData();
        formData.append("file", file);

        // Use apiClient directly in store (stores don't have component lifecycle)
        const response = await apiClient.postFormData(
          "/uploads/avatar",
          formData
        );

        if (response && this.user) {
          this.user.avatar = (response as any).url;

          // Show success toast
          showSuccessToast("Avatar uploaded successfully");
        }

        return response;
      } catch (error) {
        console.error("Error uploading avatar:", error);
        this.error = "Failed to upload avatar";

        // Show error toast
        showErrorToast("Failed to upload avatar");

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Upload a cover image
     */
    async uploadCoverImage(file: File) {
      try {
        this.isLoading = true;
        this.error = null;

        const formData = new FormData();
        formData.append("file", file);

        // Debug: Log FormData contents
        console.log("📁 FormData being sent:", formData);
        console.log("📁 File details:", {
          name: file.name,
          size: file.size,
          type: file.type,
        });

        // Check FormData entries
        for (let [key, value] of formData.entries()) {
          console.log("📁 FormData entry:", key, value);
        }

        // Use apiClient directly in store (stores don't have component lifecycle)
        const response = await apiClient.postFormData(
          "/uploads/cover",
          formData
        );

        if (response && this.user) {
          this.user.coverImage = (response as any).url;

          // Show success toast
          showSuccessToast("Cover image uploaded successfully");
        }

        return response;
      } catch (error) {
        console.error("Error uploading cover image:", error);
        this.error = "Failed to upload cover image";

        // Show error toast
        showErrorToast("Failed to upload cover image");

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Upload a document
     */
    async uploadDocument(file: File, type: string = "general") {
      try {
        this.isLoading = true;
        this.error = null;

        const formData = new FormData();
        formData.append("file", file);

        // Use apiClient directly in store (stores don't have component lifecycle)
        const response = await apiClient.postFormData(
          `/uploads/document/${type}`,
          formData
        );

        if (response) {
          // Show success toast
          showSuccessToast("Document uploaded successfully");
        }

        return response;
      } catch (error) {
        console.error("Error uploading document:", error);
        this.error = "Failed to upload document";

        // Show error toast
        showErrorToast("Failed to upload document");

        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Login user
     */
    async login(payload: LoginPayload) {
      try {
        this.isLoading = true;
        this.error = null;

        // POST /api/v1/auth/login
        const api = useApi();
        const res = await api.post("/auth/login", payload);
        const accessToken = (res as any)?.accessToken;

        if (!accessToken) {
          throw new Error("No token returned from login endpoint");
        }

        // Save token in localStorage or sessionStorage based on trustDevice
        if (payload.trustDevice) {
          localStorage.setItem("authToken", accessToken);
        } else {
          sessionStorage.setItem("authToken", accessToken);
        }

        // Update the AuthStore
        const authStore = useAuthStore();
        authStore.setAuth(accessToken);

        // Fetch user data
        await (this as any).fetchUser();

        return true; // Indicate success
      } catch (error) {
        console.error("Login error =>", error);
        this.error = "Failed to login";
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Register a new user
     */
    async register(payload: RegisterPayload, trustDevice: boolean = false) {
      try {
        this.isLoading = true;
        this.error = null;

        // POST /api/v1/auth/register
        const api = useApi();
        const res = await api.post("/auth/register", payload);
        console.log("Register API response:", res);

        // Check if response has data
        if (!res) {
          throw new Error("No response data from register endpoint");
        }

        // Extract data from response
        const accessToken = (res as any)?.accessToken;
        const userId = (res as any)?.userId;

        if (!accessToken) {
          throw new Error("No token returned from register endpoint");
        }

        if (trustDevice) {
          localStorage.setItem("authToken", accessToken);
        } else {
          sessionStorage.setItem("authToken", accessToken);
        }

        const authStore = useAuthStore();
        authStore.setAuth(accessToken);

        // Fetch user data
        await (this as any).fetchUser();

        // Return success status, userId, and role
        return {
          success: true,
          userId,
          role: (res as any)?.role, // Include the role from the response
        };
      } catch (error) {
        console.error("Register error =>", error);
        this.error = "Failed to register";
        throw error; // Let the component handle toaster / error messages
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Logout user
     */
    logout() {
      // Clear user data
      this.user = null;
      this.isAuthenticated = false;
      this.error = null;

      // Reset preferences to defaults
      this.preferences = {
        theme: "system",
        language: "en",
        sidebar: {
          isSubsidebarCollapsed: false,
          userManuallyCollapsed: false,
        },
        notifications: {
          enabled: true,
          flushLowPriority: false,
          marketing: false,
          partners: false,
          security: {
            session: true,
            password: true,
          },
          activity: {
            incoming: true,
            outgoing: false,
            failed: false,
            uncashed: false,
            payments: true,
          },
        },
        security: {
          twoFactorEnabled: false,
          phoneNumber: "",
        },
      };

      // Clear auth token
      localStorage.removeItem("authToken");
      sessionStorage.removeItem("authToken");

      // Clear auth store
      const authStore = useAuthStore();
      authStore.clearAuth();
    },
  },
  // @ts-ignore - Pinia persistence plugin types
  persist: {
    paths: ["preferences"],
  },
});
