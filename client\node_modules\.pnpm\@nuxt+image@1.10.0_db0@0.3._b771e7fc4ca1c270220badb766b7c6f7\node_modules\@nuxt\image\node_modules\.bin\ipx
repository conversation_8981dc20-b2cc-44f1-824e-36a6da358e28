#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules/ipx/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules/ipx/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules/ipx/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules/ipx/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/ipx@2.1.0_db0@0.3.2_better-sqlite3@11.10.0__ioredis@5.6.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../ipx/bin/ipx.mjs" "$@"
else
  exec node  "$basedir/../../../../ipx/bin/ipx.mjs" "$@"
fi
