// Generated by nuxt-site-config

declare module 'nitropack' {
  interface NitroRouteRules {
    site?: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigInput
  }
  interface NitroRouteConfig {
    site?: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfig
  }
  interface NitroRuntimeHooks {
    'site-config:init': (ctx: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').HookSiteConfigInitContext) => void | Promise<void>
  }
}

declare module 'h3' {
  interface H3EventContext {
    siteConfig: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigStack
    siteConfigNitroOrigin: string
  }
}

declare module '@nuxt/schema' {
  interface Nuxt {
    _siteConfig?: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigStack
  }
}
declare module 'nuxt/app' {
  interface NuxtApp {
     $nuxtSiteConfig: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigStack
  }
}
declare module '#app' {
  interface NuxtApp {
    $nuxtSiteConfig: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigStack
  }
}
declare global {
  interface Window {
    __NUXT_SITE_CONFIG__: import('../../../node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/runtime/types').SiteConfigResolved
  }
}

export {}
