@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules\detective-amd\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules\detective-amd\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules\detective-amd\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules\detective-amd\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detective-amd@6.0.1\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\detective-amd\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\detective-amd\bin\cli.js" %*
)
