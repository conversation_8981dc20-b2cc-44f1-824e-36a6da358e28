// client/services/speechService.ts

import { useAiStore } from "../../stores/useAiStore";

export class SpeechService {
  // Don't initialize the store at instantiation time
  private currentAudioSource: AudioBufferSourceNode | null = null;
  private currentAudioContext: AudioContext | null = null;

  // Get the store lazily when needed
  private getAiStore() {
    try {
      return useAiStore();
    } catch (error) {
      console.error("Failed to get AI store:", error);
      throw new Error("AI store not available");
    }
  }

  async textToSpeech(text: string, language?: string): Promise<ArrayBuffer> {
    try {
      console.log(
        `speechService: Converting text to speech: "${text}" in language: ${language}`
      );

      // Use the API directly with binary response handling
      const { useApi } = await import("../composables/useApi");
      const api = useApi();

      const response = await api.postBinary("/ai/text-to-speech", {
        text,
        language: language || "en",
      });

      console.log("speechService: TTS API response type:", typeof response);
      console.log(
        "speechService: TTS API response length:",
        response?.byteLength
      );

      if (response instanceof ArrayBuffer && response.byteLength > 0) {
        console.log(
          "speechService: Successfully received ArrayBuffer, length:",
          response.byteLength
        );
        return response;
      }

      console.error("speechService: Invalid or empty response:", response);
      throw new Error("Invalid or empty response from TTS service");
    } catch (error) {
      console.error("speechService: Error in textToSpeech:", error);
      throw error;
    }
  }

  // Stop current audio playback
  stopAudio() {
    if (this.currentAudioSource) {
      try {
        this.currentAudioSource.stop();
        this.currentAudioSource.disconnect();
      } catch (error) {
        console.error("Error stopping audio:", error);
      }
      this.currentAudioSource = null;
    }

    // Close audio context to free up resources
    if (this.currentAudioContext) {
      try {
        if (this.currentAudioContext.state !== "closed") {
          this.currentAudioContext.close();
        }
      } catch (error) {
        console.error("Error closing audio context:", error);
      }
      this.currentAudioContext = null;
    }
  }

  async playAudio(audioBuffer: ArrayBuffer): Promise<void> {
    console.log(
      "speechService: playAudio called with buffer length:",
      audioBuffer.byteLength
    );

    // Stop any currently playing audio
    this.stopAudio();

    // Create new audio context and source
    const audioContext = new AudioContext();

    // Resume context if suspended (required by some browsers)
    if (audioContext.state === "suspended") {
      await audioContext.resume();
      console.log("speechService: AudioContext resumed");
    }

    // Store references for potential interruption
    this.currentAudioContext = audioContext;

    // Decode and play audio
    try {
      console.log("speechService: Decoding audio data...");
      const decodedAudio = await audioContext.decodeAudioData(
        audioBuffer.slice(0)
      );
      console.log(
        "speechService: Audio decoded successfully, duration:",
        decodedAudio.duration,
        "seconds"
      );

      const audioBufferSource = audioContext.createBufferSource();
      this.currentAudioSource = audioBufferSource;

      audioBufferSource.buffer = decodedAudio;
      audioBufferSource.connect(audioContext.destination);

      // Return a promise that resolves when audio finishes
      return new Promise((resolve, reject) => {
        audioBufferSource.onended = () => {
          console.log("speechService: Audio playback ended");
          this.currentAudioSource = null;
          resolve();
        };

        audioBufferSource.onerror = (error) => {
          console.error("speechService: Audio playback error:", error);
          this.currentAudioSource = null;
          reject(error);
        };

        console.log("speechService: Starting audio playback...");
        audioBufferSource.start(0);
      });
    } catch (error) {
      console.error("speechService: Error playing audio:", error);
      this.currentAudioSource = null;
      this.currentAudioContext = null;
      throw error;
    }
  }
}

export const speechService = new SpeechService();
