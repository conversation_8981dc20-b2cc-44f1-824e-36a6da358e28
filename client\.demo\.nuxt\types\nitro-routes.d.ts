// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/accounts': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/accounts/index').default>>>>
    }
    '/api/accounts/linked': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/accounts/linked').default>>>>
    }
    '/api/accounts/rules': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/accounts/rules').default>>>>
    }
    '/api/cards': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/cards').default>>>>
    }
    '/api/company/billing': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/billing/index').default>>>>
    }
    '/api/company/candidates': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/candidates/index').default>>>>
    }
    '/api/company/documents': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/documents/index').default>>>>
    }
    '/api/company/invoice': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/invoice/index').default>>>>
    }
    '/api/company/members': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/members/index').default>>>>
    }
    '/api/company/projects': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/projects/index').default>>>>
    }
    '/api/company/promotion': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/promotion/index').default>>>>
    }
    '/api/company/status': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/status/index').default>>>>
    }
    '/api/company/tasks': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/tasks/index').default>>>>
    }
    '/api/company/team': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/company/team/index').default>>>>
    }
    '/api/contacts': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/contacts').default>>>>
    }
    '/api/courses': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/courses').default>>>>
    }
    '/api/freelancers': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/freelancers').default>>>>
    }
    '/api/geojson/locations': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/geojson/locations').default>>>>
    }
    '/api/hello': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/hello').default>>>>
    }
    '/api/invest': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/invest').default>>>>
    }
    '/api/jobs': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/jobs').default>>>>
    }
    '/api/members': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/members').default>>>>
    }
    '/api/messaging/:id': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/messaging/[id]').default>>>>
    }
    '/api/messaging': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/messaging/index').default>>>>
    }
    '/api/offers': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/offers').default>>>>
    }
    '/api/payments/incoming': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/payments/incoming').default>>>>
    }
    '/api/payments/outgoing': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/payments/outgoing').default>>>>
    }
    '/api/payments/recipients': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/payments/recipients').default>>>>
    }
    '/api/posts': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/posts').default>>>>
    }
    '/api/products': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/products').default>>>>
    }
    '/api/profile': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/profile/index').default>>>>
    }
    '/api/profile/notifications': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/profile/notifications/index').default>>>>
    }
    '/api/projects/details': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/projects/details/index').default>>>>
    }
    '/api/projects': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/projects/index').default>>>>
    }
    '/api/projects/invite': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/projects/invite/index').default>>>>
    }
    '/api/projects/tasks': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/projects/tasks/index').default>>>>
    }
    '/api/recipes': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/recipes').default>>>>
    }
    '/api/rentals': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/rentals').default>>>>
    }
    '/api/search': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/search').default>>>>
    }
    '/api/transactions': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/transactions').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/api/component-meta': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/dist/runtime/server/api/component-meta.get').default>>>>
    }
    '/api/component-meta.json': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/dist/runtime/server/api/component-meta.json.get').default>>>>
    }
    '/api/component-meta/:component?': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-component-meta@0.10.1_magicast@0.3.5/node_modules/nuxt-component-meta/dist/runtime/server/api/component-meta-component.get').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
    '/__nuxt_content/:collection/sql_dump': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/presets/node/database-handler').default>>>>
    }
    '/__nuxt_content/:collection/query': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/api/query.post').default>>>>
    }
    '/_ipx/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/ipx').default>>>>
    }
  }
}
export {}