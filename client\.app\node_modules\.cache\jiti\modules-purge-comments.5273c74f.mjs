"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _kit = await jitiImport("@nuxt/kit");
var _magicString = _interopRequireDefault(await jitiImport("magic-string"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };}

/**
 * This module removes HTML comments from Vue files.
 *
 * Comments are rendered in the DOM, which can cause issues with some transitions and
 * can cause hydration issues, so we remove them.
 */var _default = exports.default =
(0, _kit.defineNuxtModule)({
  meta: {
    name: 'purge-comments'
  },
  setup(options, nuxt) {
    (0, _kit.addVitePlugin)({
      name: 'purge-comments',
      enforce: 'pre',
      transform: (code, id) => {
        if (!id.endsWith('.vue') || !code.includes('<!--')) {
          return;
        }

        const s = new _magicString.default(code);
        s.replace(/<!--.*?-->/g, '');

        if (s.hasChanged()) {
          return {
            code: s.toString(),
            map:
            nuxt.options.sourcemap &&
            s.generateMap({ source: id, includeContent: true })
          };
        }
      }
    });
  }
}); /* v9-3e0db1f7681912d3 */
