#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules/mime/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules/mime/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules/mime/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules/mime/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/mime@4.0.7/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mime/bin/cli.js" "$@"
else
  exec node  "$basedir/../mime/bin/cli.js" "$@"
fi
