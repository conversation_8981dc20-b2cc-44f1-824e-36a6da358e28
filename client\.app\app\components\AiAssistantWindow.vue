<template>
  <!-- AI Assistant Window - Universal positioning -->
  <div
    v-if="showWindow"
    ref="windowRef"
    class="fixed bg-white dark:bg-neutral-900 rounded-lg shadow-lg p-4 w-80 z-50 border border-neutral-400 cursor-move dark:border-neutral-500"
    :style="windowStyle"
    @mousedown="startDrag"
    @touchstart="startDrag"
  >
    <!-- Header -->
    <div class="flex items-center justify-between mb-3">
      <h4
        class="text-sm font-medium text-primary-600 dark:text-primary-400 flex items-center"
      >
        <Icon name="ph:microphone-fill" class="h-4 w-4 mr-1" />
        {{ $t("ai.assistant") }}
      </h4>
      <div class="flex items-center gap-2">
        <div v-if="isListening" class="flex items-center">
          <span class="text-xs text-primary-500 mr-1">{{
            $t("ai.listening")
          }}</span>
          <span class="relative flex h-3 w-3">
            <span
              class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-400 opacity-75"
            />
            <span
              class="relative inline-flex rounded-full h-3 w-3 bg-primary-500"
            />
          </span>
        </div>

        <!-- Speaker Toggle Button -->
        <BaseButton
          size="icon-sm"
          variant="ghost"
          @click="toggleSpeaker"
          :class="{
            'text-primary-500': globalSpeakerEnabled,
            'text-muted-400': !globalSpeakerEnabled,
          }"
        >
          <Icon
            :name="
              globalSpeakerEnabled ? 'ph:speaker-high' : 'ph:speaker-slash'
            "
            class="h-4 w-4"
          />
        </BaseButton>

        <!-- Close Button -->
        <BaseButton
          size="icon-sm"
          variant="ghost"
          @click="closeWindow"
          class="text-muted-400 hover:text-muted-600 dark:text-muted-500 dark:hover:text-muted-300"
        >
          <Icon name="ph:x" class="h-4 w-4" />
        </BaseButton>
      </div>
    </div>

    <!-- Voice Transcript -->
    <div
      v-if="transcript"
      class="bg-gray-50 dark:bg-gray-700 p-2 rounded-md mb-3"
    >
      <p class="text-sm text-gray-600 dark:text-gray-300">{{ transcript }}</p>
    </div>

    <!-- Text Input Area -->
    <div class="mb-3">
      <BaseTextarea
        v-model="textInput"
        :placeholder="$t('ai.type_message')"
        autogrow
        :max-height="150"
        class="w-full"
        @keydown.enter.exact="handleTextSubmit"
      />
      <div class="flex justify-end mt-2">
        <BaseButton
          size="sm"
          variant="primary"
          :disabled="!textInput.trim() || isProcessing"
          :loading="isProcessing"
          @click="handleTextSubmit"
        >
          <Icon name="ph:paper-plane-tilt" class="h-4 w-4 mr-1" />
          {{ $t("ai.send") }}
        </BaseButton>
      </div>
    </div>

    <!-- AI Response -->
    <div
      v-if="aiResponse"
      class="bg-primary-50 dark:bg-primary-900/30 p-3 rounded-md"
    >
      <p class="text-sm font-medium text-gray-700 dark:text-gray-200">
        {{ aiResponse }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from "vue";
import { useAiAssistant } from "../composables/useAiAssistant";
import { useAiAssistantManager } from "../composables/useAiAssistantManager";

const {
  isListening,
  isProcessing,
  transcript,
  aiResponse,
  startListening,
  stopListening,
  stopSpeech,
  processCommand,
  globalSpeakerEnabled,
  setSpeakerEnabled,
} = useAiAssistant();

// Get AI assistant manager
const { registerWindow } = useAiAssistantManager();

// Get i18n
const { t } = useI18n();

// Window visibility and positioning
const showWindow = ref(false);
const windowRef = ref<HTMLElement | null>(null);
const windowPosition = ref({ x: 0, y: 0 });

// Text input functionality
const textInput = ref("");

// Dragging functionality
let isDragging = false;
const dragOffset = { x: 0, y: 0 };

// Computed style for window positioning
const windowStyle = computed(() => {
  if (windowPosition.value.x === 0 && windowPosition.value.y === 0) {
    // Default position: bottom-right with margins
    return {
      bottom: "16px",
      right: "16px",
    };
  }
  return {
    left: `${windowPosition.value.x}px`,
    top: `${windowPosition.value.y}px`,
  };
});

// Toggle speaker on/off
const toggleSpeaker = () => {
  setSpeakerEnabled(!globalSpeakerEnabled.value);
  console.log("Speaker enabled:", globalSpeakerEnabled.value);
};

// Handle text input submission
const handleTextSubmit = async (event?: KeyboardEvent) => {
  if (event) {
    event.preventDefault();
  }

  if (!textInput.value.trim() || isProcessing.value) {
    return;
  }

  const message = textInput.value.trim();
  textInput.value = "";

  try {
    await processCommand(message);
  } catch (error) {
    console.error("Error processing text command:", error);
  }
};

// Close window - stops all AI processes
const closeWindow = () => {
  console.log(
    "AiAssistantWindow: Closing AI window and stopping all processes"
  );
  stopListening();
  stopSpeech();
  showWindow.value = false;
  console.log(
    "AiAssistantWindow: Window closed, showWindow:",
    showWindow.value
  );
};

// Register this component with the manager on mount
onMounted(() => {
  registerWindow({
    openWindow,
    closeWindow,
    isWindowOpen,
  });
});

// Dragging functionality
const startDrag = (event: MouseEvent | TouchEvent) => {
  if (!windowRef.value) return;

  isDragging = true;
  const rect = windowRef.value.getBoundingClientRect();

  if (event instanceof MouseEvent) {
    dragOffset.x = event.clientX - rect.left;
    dragOffset.y = event.clientY - rect.top;
  } else {
    const touch = event.touches[0];
    dragOffset.x = touch.clientX - rect.left;
    dragOffset.y = touch.clientY - rect.top;
  }

  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("mouseup", stopDrag);
  document.addEventListener("touchmove", handleDrag);
  document.addEventListener("touchend", stopDrag);
};

const handleDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging) return;

  let clientX: number, clientY: number;

  if (event instanceof MouseEvent) {
    clientX = event.clientX;
    clientY = event.clientY;
  } else {
    const touch = event.touches[0];
    clientX = touch.clientX;
    clientY = touch.clientY;
  }

  windowPosition.value = {
    x: Math.max(0, Math.min(window.innerWidth - 320, clientX - dragOffset.x)),
    y: Math.max(0, Math.min(window.innerHeight - 200, clientY - dragOffset.y)),
  };
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("mouseup", stopDrag);
  document.removeEventListener("touchmove", handleDrag);
  document.removeEventListener("touchend", stopDrag);
};

// Expose methods for external control
const openWindow = () => {
  console.log("AiAssistantWindow: Opening window and starting listening");
  showWindow.value = true;
  console.log("AiAssistantWindow: showWindow set to:", showWindow.value);
  // Start listening when window opens
  nextTick(() => {
    console.log("AiAssistantWindow: Starting listening in nextTick");
    startListening();
  });
};

const isWindowOpen = computed(() => showWindow.value);

// Expose the control methods
defineExpose({
  openWindow,
  closeWindow,
  isWindowOpen,
});
</script>
