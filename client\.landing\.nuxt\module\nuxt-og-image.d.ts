// Generated by nuxt-og-image

declare module 'nitropack' {
  interface NitroRouteRules {
    ogImage?: false | import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/types').OgImageOptions & Record<string, any>
  }
  interface NitroRouteConfig {
    ogImage?: false | import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/types').OgImageOptions & Record<string, any>
  }
  interface NitroRuntimeHooks {
    'nuxt-og-image:context': (ctx: import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/types').OgImageRenderEventContext) => void | Promise<void>
    'nuxt-og-image:satori:vnodes': (vnodes: import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/types').VNode, ctx: import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/types').OgImageRenderEventContext) => void | Promise<void>
  }
}

declare module '#og-image/components' {
  export interface OgImageComponents {
    '': typeof import('../..')['default']
    'BrandedLogo': typeof import('../..')['default']
    'Frame': typeof import('../..')['default']
    'Nuxt': typeof import('../..')['default']
    'NuxtSeo': typeof import('../..')['default']
    'Pergel': typeof import('../..')['default']
    'SimpleBlog': typeof import('../..')['default']
    'UnJs': typeof import('../..')['default']
    'Wave': typeof import('../..')['default']
    'WithEmoji': typeof import('../..')['default']
  }
}
declare module '#og-image/unocss-config' {
  export type theme = any
}

export {}
