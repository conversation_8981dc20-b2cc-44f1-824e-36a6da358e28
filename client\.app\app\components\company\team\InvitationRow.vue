<template>
  <div
    class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 bg-white dark:bg-muted-800 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
  >
    <!-- Invitation Info -->
    <div class="flex items-center mb-3 sm:mb-0">
      <BaseAvatar src="../../../../../../../public/img/avatars/cute-astronout.svg" size="lg" />
      <div class="flex flex-col ml-3">
        <h3 class="font-medium text-muted-900 dark:text-white">
          {{ invitation.email }}
        </h3>
        <p class="text-xs text-muted-400">Invited {{ formatTimeAgo(invitation.createdAt) }}</p>
      </div>
    </div>

    <!-- Invitation Details -->
    <div class="flex flex-wrap items-center gap-4 w-full sm:w-auto">
      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1">Role</span>
        <BaseTag :color="getRoleColor(invitation.role)" flavor="pastel" condensed>
          {{ formatRole(invitation.role) }}
        </BaseTag>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1">Status</span>
        <BaseTag color="warning" flavor="pastel" condensed> Pending </BaseTag>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1">Expires</span>
        <span class="text-sm text-muted-500 dark:text-muted-400">
          {{ formatDate(invitation.expiresAt) }}
        </span>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1">Actions</span>
        <div class="flex items-center space-x-2">
          <BaseButton
            color="primary"
            size="sm"
            @click="$emit('resend', invitation)"
            :disabled="isResending"
          >
            <Icon v-if="!isResending" name="ph:arrow-clockwise-duotone" class="h-4 w-4 mr-1" />
            <BaseSpinner v-else size="xs" class="mr-1" />
            Resend
          </BaseButton>
          <BaseButton
            color="danger"
            size="sm"
            @click="$emit('cancel', invitation)"
            :disabled="isCanceling"
          >
            <Icon v-if="!isCanceling" name="ph:trash-duotone" class="h-4 w-4 mr-1" />
            <BaseSpinner v-else size="xs" class="mr-1" />
            Cancel
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format, formatDistanceToNow } from 'date-fns'

const props = defineProps({
  invitation: {
    type: Object,
    required: true,
  },
  isResending: {
    type: Boolean,
    default: false,
  },
  isCanceling: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['resend', 'cancel'])

// Helper functions
const formatRole = (role: string): string => {
  switch (role) {
    case 'ADMIN':
      return 'Admin'
    case 'PROJECTLEADER':
      return 'Project Leader'
    case 'SALESMAN':
      return 'Salesman'
    case 'WORKER':
      return 'Worker'
    case 'CLIENT':
      return 'Client'
    case 'OWNER':
      return 'Owner'
    default:
      return role || 'Unknown'
  }
}

const getRoleColor = (role: string): string => {
  switch (role) {
    case 'ADMIN':
      return 'info'
    case 'PROJECTLEADER':
      return 'warning'
    case 'SALESMAN':
      return 'purple'
    case 'WORKER':
      return 'success'
    case 'CLIENT':
      return 'yellow'
    case 'OWNER':
      return 'primary'
    default:
      return 'muted'
  }
}

const formatDate = (dateString: string): string => {
  try {
    return format(new Date(dateString), 'MMM d, yyyy')
  } catch (e) {
    return dateString || 'Unknown'
  }
}

const formatTimeAgo = (dateString: string): string => {
  try {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  } catch (e) {
    return dateString || 'Unknown'
  }
}
</script>
