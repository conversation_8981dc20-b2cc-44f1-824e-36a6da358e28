#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/prebuild-install@7.1.3/node_modules/prebuild-install/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/prebuild-install@7.1.3/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/prebuild-install@7.1.3/node_modules/prebuild-install/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/prebuild-install@7.1.3/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../prebuild-install/bin.js" "$@"
else
  exec node  "$basedir/../prebuild-install/bin.js" "$@"
fi
