
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'TairoCheckAnimated': typeof import("../components/TairoCheckAnimated.vue")['default']
    'TairoCheckboxAnimated': typeof import("../components/TairoCheckboxAnimated.vue")['default']
    'TairoCheckboxCardIcon': typeof import("../components/TairoCheckboxCardIcon.vue")['default']
    'TairoContentWrapper': typeof import("../components/TairoContentWrapper.vue")['default']
    'TairoContentWrapperTabbed': typeof import("../components/TairoContentWrapperTabbed.vue")['default']
    'TairoError': typeof import("../components/TairoError.vue")['default']
    'TairoFlexTable': typeof import("../components/TairoFlexTable.vue")['default']
    'TairoFlexTableCell': typeof import("../components/TairoFlexTableCell.vue")['default']
    'TairoFlexTableHeading': typeof import("../components/TairoFlexTableHeading.vue")['default']
    'TairoFlexTableRow': typeof import("../components/TairoFlexTableRow.vue")['default']
    'TairoFormGroup': typeof import("../components/TairoFormGroup.vue")['default']
    'TairoFormSave': typeof import("../components/TairoFormSave.vue")['default']
    'TairoFullscreenDropfile': typeof import("../components/TairoFullscreenDropfile.vue")['default']
    'TairoImageZoom': typeof import("../components/TairoImageZoom.vue")['default']
    'TairoInput': typeof import("../components/TairoInput.vue")['default']
    'TairoInputFileHeadless': typeof import("../components/TairoInputFileHeadless.vue")['default']
    'TairoMobileDrawer': typeof import("../components/TairoMobileDrawer.vue")['default']
    'TairoPanels': typeof import("../components/TairoPanels.vue")['default']
    'TairoRadioCard': typeof import("../components/TairoRadioCard.vue")['default']
    'TairoSelect': typeof import("../components/TairoSelect.vue")['default']
    'TairoSelectItem': typeof import("../components/TairoSelectItem.vue")['default']
    'TairoTable': typeof import("../components/TairoTable.vue")['default']
    'TairoTableCell': typeof import("../components/TairoTableCell.vue")['default']
    'TairoTableHeading': typeof import("../components/TairoTableHeading.vue")['default']
    'TairoTableRow': typeof import("../components/TairoTableRow.vue")['default']
    'TairoWelcome': typeof import("../components/TairoWelcome.vue")['default']
    'TairoCollapseBackdrop': typeof import("../components/tairo-collapse/TairoCollapseBackdrop.vue")['default']
    'TairoCollapseCollapsible': typeof import("../components/tairo-collapse/TairoCollapseCollapsible.vue")['default']
    'TairoCollapseCollapsibleLink': typeof import("../components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']
    'TairoCollapseCollapsibleTrigger': typeof import("../components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']
    'TairoCollapseContent': typeof import("../components/tairo-collapse/TairoCollapseContent.vue")['default']
    'TairoCollapseLayout': typeof import("../components/tairo-collapse/TairoCollapseLayout.vue")['default']
    'TairoCollapseSidebar': typeof import("../components/tairo-collapse/TairoCollapseSidebar.vue")['default']
    'TairoCollapseSidebarClose': typeof import("../components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']
    'TairoCollapseSidebarHeader': typeof import("../components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']
    'TairoCollapseSidebarLink': typeof import("../components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']
    'TairoCollapseSidebarLinks': typeof import("../components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']
    'TairoMenu': typeof import("../components/tairo-menu/TairoMenu.vue")['default']
    'TairoMenuContent': typeof import("../components/tairo-menu/TairoMenuContent.vue")['default']
    'TairoMenuIndicator': typeof import("../components/tairo-menu/TairoMenuIndicator.vue")['default']
    'TairoMenuItem': typeof import("../components/tairo-menu/TairoMenuItem.vue")['default']
    'TairoMenuLink': typeof import("../components/tairo-menu/TairoMenuLink.vue")['default']
    'TairoMenuLinkTab': typeof import("../components/tairo-menu/TairoMenuLinkTab.vue")['default']
    'TairoMenuList': typeof import("../components/tairo-menu/TairoMenuList.vue")['default']
    'TairoMenuListItems': typeof import("../components/tairo-menu/TairoMenuListItems.vue")['default']
    'TairoMenuTrigger': typeof import("../components/tairo-menu/TairoMenuTrigger.vue")['default']
    'TairoMenuViewport': typeof import("../components/tairo-menu/TairoMenuViewport.vue")['default']
    'TairoSidebar': typeof import("../components/tairo-sidebar/TairoSidebar.vue")['default']
    'TairoSidebarBackdrop': typeof import("../components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']
    'TairoSidebarContent': typeof import("../components/tairo-sidebar/TairoSidebarContent.vue")['default']
    'TairoSidebarLayout': typeof import("../components/tairo-sidebar/TairoSidebarLayout.vue")['default']
    'TairoSidebarLink': typeof import("../components/tairo-sidebar/TairoSidebarLink.vue")['default']
    'TairoSidebarLinks': typeof import("../components/tairo-sidebar/TairoSidebarLinks.vue")['default']
    'TairoSidebarNav': typeof import("../components/tairo-sidebar/TairoSidebarNav.vue")['default']
    'TairoSidebarSubsidebar': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']
    'TairoSidebarSubsidebarCollapsible': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']
    'TairoSidebarSubsidebarCollapsibleLink': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']
    'TairoSidebarSubsidebarCollapsibleTrigger': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']
    'TairoSidebarSubsidebarContent': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']
    'TairoSidebarSubsidebarHeader': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']
    'TairoSidebarSubsidebarLink': typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']
    'TairoSidebarTrigger': typeof import("../components/tairo-sidebar/TairoSidebarTrigger.vue")['default']
    'TairoSidenavBackdrop': typeof import("../components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']
    'TairoSidenavCollapsible': typeof import("../components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']
    'TairoSidenavCollapsibleLink': typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']
    'TairoSidenavCollapsibleTrigger': typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']
    'TairoSidenavContent': typeof import("../components/tairo-sidenav/TairoSidenavContent.vue")['default']
    'TairoSidenavLayout': typeof import("../components/tairo-sidenav/TairoSidenavLayout.vue")['default']
    'TairoSidenavSidebar': typeof import("../components/tairo-sidenav/TairoSidenavSidebar.vue")['default']
    'TairoSidenavSidebarDivider': typeof import("../components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']
    'TairoSidenavSidebarHeader': typeof import("../components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']
    'TairoSidenavSidebarLink': typeof import("../components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']
    'TairoSidenavSidebarLinks': typeof import("../components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']
    'TairoTopnavContent': typeof import("../components/tairo-topnav/TairoTopnavContent.vue")['default']
    'TairoTopnavHeader': typeof import("../components/tairo-topnav/TairoTopnavHeader.vue")['default']
    'TairoTopnavLayout': typeof import("../components/tairo-topnav/TairoTopnavLayout.vue")['default']
    'TairoTopnavNavbar': typeof import("../components/tairo-topnav/TairoTopnavNavbar.vue")['default']
    'BaseAccordion': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']
    'BaseAccordionItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']
    'BaseAutocomplete': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']
    'BaseAutocompleteGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']
    'BaseAutocompleteItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']
    'BaseAutocompleteLabel': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']
    'BaseAutocompleteSeparator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']
    'BaseAvatar': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']
    'BaseAvatarGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']
    'BaseBreadcrumb': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']
    'BaseButton': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']
    'BaseCard': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']
    'BaseCheckbox': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']
    'BaseCheckboxGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']
    'BaseChip': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']
    'BaseDropdown': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']
    'BaseDropdownArrow': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']
    'BaseDropdownCheckbox': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']
    'BaseDropdownItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']
    'BaseDropdownLabel': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']
    'BaseDropdownRadioGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']
    'BaseDropdownRadioItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']
    'BaseDropdownSeparator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']
    'BaseDropdownSub': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']
    'BaseField': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']
    'BaseHeading': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']
    'BaseIconBox': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']
    'BaseInput': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']
    'BaseInputFile': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']
    'BaseInputNumber': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']
    'BaseKbd': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']
    'BaseLink': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']
    'BaseList': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']
    'BaseListItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']
    'BaseMessage': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']
    'BasePagination': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']
    'BasePaginationButtonFirst': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']
    'BasePaginationButtonLast': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']
    'BasePaginationButtonNext': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']
    'BasePaginationButtonPrev': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']
    'BasePaginationItems': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']
    'BaseParagraph': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']
    'BasePlaceholderPage': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']
    'BasePlaceload': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']
    'BasePopover': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']
    'BasePrimitiveField': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']
    'BasePrimitiveFieldController': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']
    'BasePrimitiveFieldDescription': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']
    'BasePrimitiveFieldError': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']
    'BasePrimitiveFieldErrorIndicator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']
    'BasePrimitiveFieldLabel': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']
    'BasePrimitiveFieldLoadingIndicator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']
    'BasePrimitiveFieldRequiredIndicator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']
    'BasePrimitiveFieldSuccessIndicator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']
    'BaseProgress': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']
    'BaseProgressCircle': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']
    'BaseProse': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']
    'BaseProviders': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']
    'BaseRadio': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']
    'BaseRadioGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']
    'BaseSelect': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']
    'BaseSelectGroup': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']
    'BaseSelectItem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']
    'BaseSelectLabel': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']
    'BaseSelectSeparator': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']
    'BaseSlider': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']
    'BaseSnack': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']
    'BaseSwitchBall': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']
    'BaseSwitchThin': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']
    'BaseTabs': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']
    'BaseTabsContent': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']
    'BaseTabsTrigger': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']
    'BaseTag': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']
    'BaseText': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']
    'BaseTextarea': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']
    'BaseThemeSwitch': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']
    'BaseThemeSystem': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']
    'BaseThemeToggle': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']
    'BaseToast': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']
    'BaseToastProvider': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']
    'BaseTooltip': typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']
    'NuxtWelcome': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtRouteAnnouncer': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'ColorScheme': typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'Icon': typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtPage': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page-placeholder")['default']
    'NoScript': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyTairoCheckAnimated': LazyComponent<typeof import("../components/TairoCheckAnimated.vue")['default']>
    'LazyTairoCheckboxAnimated': LazyComponent<typeof import("../components/TairoCheckboxAnimated.vue")['default']>
    'LazyTairoCheckboxCardIcon': LazyComponent<typeof import("../components/TairoCheckboxCardIcon.vue")['default']>
    'LazyTairoContentWrapper': LazyComponent<typeof import("../components/TairoContentWrapper.vue")['default']>
    'LazyTairoContentWrapperTabbed': LazyComponent<typeof import("../components/TairoContentWrapperTabbed.vue")['default']>
    'LazyTairoError': LazyComponent<typeof import("../components/TairoError.vue")['default']>
    'LazyTairoFlexTable': LazyComponent<typeof import("../components/TairoFlexTable.vue")['default']>
    'LazyTairoFlexTableCell': LazyComponent<typeof import("../components/TairoFlexTableCell.vue")['default']>
    'LazyTairoFlexTableHeading': LazyComponent<typeof import("../components/TairoFlexTableHeading.vue")['default']>
    'LazyTairoFlexTableRow': LazyComponent<typeof import("../components/TairoFlexTableRow.vue")['default']>
    'LazyTairoFormGroup': LazyComponent<typeof import("../components/TairoFormGroup.vue")['default']>
    'LazyTairoFormSave': LazyComponent<typeof import("../components/TairoFormSave.vue")['default']>
    'LazyTairoFullscreenDropfile': LazyComponent<typeof import("../components/TairoFullscreenDropfile.vue")['default']>
    'LazyTairoImageZoom': LazyComponent<typeof import("../components/TairoImageZoom.vue")['default']>
    'LazyTairoInput': LazyComponent<typeof import("../components/TairoInput.vue")['default']>
    'LazyTairoInputFileHeadless': LazyComponent<typeof import("../components/TairoInputFileHeadless.vue")['default']>
    'LazyTairoMobileDrawer': LazyComponent<typeof import("../components/TairoMobileDrawer.vue")['default']>
    'LazyTairoPanels': LazyComponent<typeof import("../components/TairoPanels.vue")['default']>
    'LazyTairoRadioCard': LazyComponent<typeof import("../components/TairoRadioCard.vue")['default']>
    'LazyTairoSelect': LazyComponent<typeof import("../components/TairoSelect.vue")['default']>
    'LazyTairoSelectItem': LazyComponent<typeof import("../components/TairoSelectItem.vue")['default']>
    'LazyTairoTable': LazyComponent<typeof import("../components/TairoTable.vue")['default']>
    'LazyTairoTableCell': LazyComponent<typeof import("../components/TairoTableCell.vue")['default']>
    'LazyTairoTableHeading': LazyComponent<typeof import("../components/TairoTableHeading.vue")['default']>
    'LazyTairoTableRow': LazyComponent<typeof import("../components/TairoTableRow.vue")['default']>
    'LazyTairoWelcome': LazyComponent<typeof import("../components/TairoWelcome.vue")['default']>
    'LazyTairoCollapseBackdrop': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseBackdrop.vue")['default']>
    'LazyTairoCollapseCollapsible': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsible.vue")['default']>
    'LazyTairoCollapseCollapsibleLink': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']>
    'LazyTairoCollapseCollapsibleTrigger': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']>
    'LazyTairoCollapseContent': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseContent.vue")['default']>
    'LazyTairoCollapseLayout': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseLayout.vue")['default']>
    'LazyTairoCollapseSidebar': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebar.vue")['default']>
    'LazyTairoCollapseSidebarClose': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']>
    'LazyTairoCollapseSidebarHeader': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']>
    'LazyTairoCollapseSidebarLink': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']>
    'LazyTairoCollapseSidebarLinks': LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']>
    'LazyTairoMenu': LazyComponent<typeof import("../components/tairo-menu/TairoMenu.vue")['default']>
    'LazyTairoMenuContent': LazyComponent<typeof import("../components/tairo-menu/TairoMenuContent.vue")['default']>
    'LazyTairoMenuIndicator': LazyComponent<typeof import("../components/tairo-menu/TairoMenuIndicator.vue")['default']>
    'LazyTairoMenuItem': LazyComponent<typeof import("../components/tairo-menu/TairoMenuItem.vue")['default']>
    'LazyTairoMenuLink': LazyComponent<typeof import("../components/tairo-menu/TairoMenuLink.vue")['default']>
    'LazyTairoMenuLinkTab': LazyComponent<typeof import("../components/tairo-menu/TairoMenuLinkTab.vue")['default']>
    'LazyTairoMenuList': LazyComponent<typeof import("../components/tairo-menu/TairoMenuList.vue")['default']>
    'LazyTairoMenuListItems': LazyComponent<typeof import("../components/tairo-menu/TairoMenuListItems.vue")['default']>
    'LazyTairoMenuTrigger': LazyComponent<typeof import("../components/tairo-menu/TairoMenuTrigger.vue")['default']>
    'LazyTairoMenuViewport': LazyComponent<typeof import("../components/tairo-menu/TairoMenuViewport.vue")['default']>
    'LazyTairoSidebar': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebar.vue")['default']>
    'LazyTairoSidebarBackdrop': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']>
    'LazyTairoSidebarContent': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarContent.vue")['default']>
    'LazyTairoSidebarLayout': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLayout.vue")['default']>
    'LazyTairoSidebarLink': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLink.vue")['default']>
    'LazyTairoSidebarLinks': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLinks.vue")['default']>
    'LazyTairoSidebarNav': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarNav.vue")['default']>
    'LazyTairoSidebarSubsidebar': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsible': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsibleLink': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']>
    'LazyTairoSidebarSubsidebarCollapsibleTrigger': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']>
    'LazyTairoSidebarSubsidebarContent': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']>
    'LazyTairoSidebarSubsidebarHeader': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']>
    'LazyTairoSidebarSubsidebarLink': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']>
    'LazyTairoSidebarTrigger': LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarTrigger.vue")['default']>
    'LazyTairoSidenavBackdrop': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']>
    'LazyTairoSidenavCollapsible': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']>
    'LazyTairoSidenavCollapsibleLink': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']>
    'LazyTairoSidenavCollapsibleTrigger': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']>
    'LazyTairoSidenavContent': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavContent.vue")['default']>
    'LazyTairoSidenavLayout': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavLayout.vue")['default']>
    'LazyTairoSidenavSidebar': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebar.vue")['default']>
    'LazyTairoSidenavSidebarDivider': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']>
    'LazyTairoSidenavSidebarHeader': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']>
    'LazyTairoSidenavSidebarLink': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']>
    'LazyTairoSidenavSidebarLinks': LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']>
    'LazyTairoTopnavContent': LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavContent.vue")['default']>
    'LazyTairoTopnavHeader': LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavHeader.vue")['default']>
    'LazyTairoTopnavLayout': LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavLayout.vue")['default']>
    'LazyTairoTopnavNavbar': LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavNavbar.vue")['default']>
    'LazyBaseAccordion': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']>
    'LazyBaseAccordionItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']>
    'LazyBaseAutocomplete': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']>
    'LazyBaseAutocompleteGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']>
    'LazyBaseAutocompleteItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']>
    'LazyBaseAutocompleteLabel': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']>
    'LazyBaseAutocompleteSeparator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']>
    'LazyBaseAvatar': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']>
    'LazyBaseAvatarGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']>
    'LazyBaseBreadcrumb': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']>
    'LazyBaseButton': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']>
    'LazyBaseCard': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']>
    'LazyBaseCheckbox': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']>
    'LazyBaseCheckboxGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']>
    'LazyBaseChip': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']>
    'LazyBaseDropdown': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']>
    'LazyBaseDropdownArrow': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']>
    'LazyBaseDropdownCheckbox': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']>
    'LazyBaseDropdownItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']>
    'LazyBaseDropdownLabel': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']>
    'LazyBaseDropdownRadioGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']>
    'LazyBaseDropdownRadioItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']>
    'LazyBaseDropdownSeparator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']>
    'LazyBaseDropdownSub': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']>
    'LazyBaseField': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']>
    'LazyBaseHeading': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']>
    'LazyBaseIconBox': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']>
    'LazyBaseInput': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']>
    'LazyBaseInputFile': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']>
    'LazyBaseInputNumber': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']>
    'LazyBaseKbd': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']>
    'LazyBaseLink': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']>
    'LazyBaseList': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']>
    'LazyBaseListItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']>
    'LazyBaseMessage': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']>
    'LazyBasePagination': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']>
    'LazyBasePaginationButtonFirst': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']>
    'LazyBasePaginationButtonLast': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']>
    'LazyBasePaginationButtonNext': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']>
    'LazyBasePaginationButtonPrev': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']>
    'LazyBasePaginationItems': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']>
    'LazyBaseParagraph': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']>
    'LazyBasePlaceholderPage': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']>
    'LazyBasePlaceload': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']>
    'LazyBasePopover': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']>
    'LazyBasePrimitiveField': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']>
    'LazyBasePrimitiveFieldController': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']>
    'LazyBasePrimitiveFieldDescription': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']>
    'LazyBasePrimitiveFieldError': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']>
    'LazyBasePrimitiveFieldErrorIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']>
    'LazyBasePrimitiveFieldLabel': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']>
    'LazyBasePrimitiveFieldLoadingIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']>
    'LazyBasePrimitiveFieldRequiredIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']>
    'LazyBasePrimitiveFieldSuccessIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']>
    'LazyBaseProgress': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']>
    'LazyBaseProgressCircle': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']>
    'LazyBaseProse': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']>
    'LazyBaseProviders': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']>
    'LazyBaseRadio': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']>
    'LazyBaseRadioGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']>
    'LazyBaseSelect': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']>
    'LazyBaseSelectGroup': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']>
    'LazyBaseSelectItem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']>
    'LazyBaseSelectLabel': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']>
    'LazyBaseSelectSeparator': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']>
    'LazyBaseSlider': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']>
    'LazyBaseSnack': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']>
    'LazyBaseSwitchBall': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']>
    'LazyBaseSwitchThin': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']>
    'LazyBaseTabs': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']>
    'LazyBaseTabsContent': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']>
    'LazyBaseTabsTrigger': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']>
    'LazyBaseTag': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']>
    'LazyBaseText': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']>
    'LazyBaseTextarea': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']>
    'LazyBaseThemeSwitch': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']>
    'LazyBaseThemeSystem': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']>
    'LazyBaseThemeToggle': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']>
    'LazyBaseToast': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']>
    'LazyBaseToastProvider': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']>
    'LazyBaseTooltip': LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyColorScheme': LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page-placeholder")['default']>
    'LazyNoScript': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const TairoCheckAnimated: typeof import("../components/TairoCheckAnimated.vue")['default']
export const TairoCheckboxAnimated: typeof import("../components/TairoCheckboxAnimated.vue")['default']
export const TairoCheckboxCardIcon: typeof import("../components/TairoCheckboxCardIcon.vue")['default']
export const TairoContentWrapper: typeof import("../components/TairoContentWrapper.vue")['default']
export const TairoContentWrapperTabbed: typeof import("../components/TairoContentWrapperTabbed.vue")['default']
export const TairoError: typeof import("../components/TairoError.vue")['default']
export const TairoFlexTable: typeof import("../components/TairoFlexTable.vue")['default']
export const TairoFlexTableCell: typeof import("../components/TairoFlexTableCell.vue")['default']
export const TairoFlexTableHeading: typeof import("../components/TairoFlexTableHeading.vue")['default']
export const TairoFlexTableRow: typeof import("../components/TairoFlexTableRow.vue")['default']
export const TairoFormGroup: typeof import("../components/TairoFormGroup.vue")['default']
export const TairoFormSave: typeof import("../components/TairoFormSave.vue")['default']
export const TairoFullscreenDropfile: typeof import("../components/TairoFullscreenDropfile.vue")['default']
export const TairoImageZoom: typeof import("../components/TairoImageZoom.vue")['default']
export const TairoInput: typeof import("../components/TairoInput.vue")['default']
export const TairoInputFileHeadless: typeof import("../components/TairoInputFileHeadless.vue")['default']
export const TairoMobileDrawer: typeof import("../components/TairoMobileDrawer.vue")['default']
export const TairoPanels: typeof import("../components/TairoPanels.vue")['default']
export const TairoRadioCard: typeof import("../components/TairoRadioCard.vue")['default']
export const TairoSelect: typeof import("../components/TairoSelect.vue")['default']
export const TairoSelectItem: typeof import("../components/TairoSelectItem.vue")['default']
export const TairoTable: typeof import("../components/TairoTable.vue")['default']
export const TairoTableCell: typeof import("../components/TairoTableCell.vue")['default']
export const TairoTableHeading: typeof import("../components/TairoTableHeading.vue")['default']
export const TairoTableRow: typeof import("../components/TairoTableRow.vue")['default']
export const TairoWelcome: typeof import("../components/TairoWelcome.vue")['default']
export const TairoCollapseBackdrop: typeof import("../components/tairo-collapse/TairoCollapseBackdrop.vue")['default']
export const TairoCollapseCollapsible: typeof import("../components/tairo-collapse/TairoCollapseCollapsible.vue")['default']
export const TairoCollapseCollapsibleLink: typeof import("../components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']
export const TairoCollapseCollapsibleTrigger: typeof import("../components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']
export const TairoCollapseContent: typeof import("../components/tairo-collapse/TairoCollapseContent.vue")['default']
export const TairoCollapseLayout: typeof import("../components/tairo-collapse/TairoCollapseLayout.vue")['default']
export const TairoCollapseSidebar: typeof import("../components/tairo-collapse/TairoCollapseSidebar.vue")['default']
export const TairoCollapseSidebarClose: typeof import("../components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']
export const TairoCollapseSidebarHeader: typeof import("../components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']
export const TairoCollapseSidebarLink: typeof import("../components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']
export const TairoCollapseSidebarLinks: typeof import("../components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']
export const TairoMenu: typeof import("../components/tairo-menu/TairoMenu.vue")['default']
export const TairoMenuContent: typeof import("../components/tairo-menu/TairoMenuContent.vue")['default']
export const TairoMenuIndicator: typeof import("../components/tairo-menu/TairoMenuIndicator.vue")['default']
export const TairoMenuItem: typeof import("../components/tairo-menu/TairoMenuItem.vue")['default']
export const TairoMenuLink: typeof import("../components/tairo-menu/TairoMenuLink.vue")['default']
export const TairoMenuLinkTab: typeof import("../components/tairo-menu/TairoMenuLinkTab.vue")['default']
export const TairoMenuList: typeof import("../components/tairo-menu/TairoMenuList.vue")['default']
export const TairoMenuListItems: typeof import("../components/tairo-menu/TairoMenuListItems.vue")['default']
export const TairoMenuTrigger: typeof import("../components/tairo-menu/TairoMenuTrigger.vue")['default']
export const TairoMenuViewport: typeof import("../components/tairo-menu/TairoMenuViewport.vue")['default']
export const TairoSidebar: typeof import("../components/tairo-sidebar/TairoSidebar.vue")['default']
export const TairoSidebarBackdrop: typeof import("../components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']
export const TairoSidebarContent: typeof import("../components/tairo-sidebar/TairoSidebarContent.vue")['default']
export const TairoSidebarLayout: typeof import("../components/tairo-sidebar/TairoSidebarLayout.vue")['default']
export const TairoSidebarLink: typeof import("../components/tairo-sidebar/TairoSidebarLink.vue")['default']
export const TairoSidebarLinks: typeof import("../components/tairo-sidebar/TairoSidebarLinks.vue")['default']
export const TairoSidebarNav: typeof import("../components/tairo-sidebar/TairoSidebarNav.vue")['default']
export const TairoSidebarSubsidebar: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']
export const TairoSidebarSubsidebarCollapsible: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']
export const TairoSidebarSubsidebarCollapsibleLink: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']
export const TairoSidebarSubsidebarCollapsibleTrigger: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']
export const TairoSidebarSubsidebarContent: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']
export const TairoSidebarSubsidebarHeader: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']
export const TairoSidebarSubsidebarLink: typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']
export const TairoSidebarTrigger: typeof import("../components/tairo-sidebar/TairoSidebarTrigger.vue")['default']
export const TairoSidenavBackdrop: typeof import("../components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']
export const TairoSidenavCollapsible: typeof import("../components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']
export const TairoSidenavCollapsibleLink: typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']
export const TairoSidenavCollapsibleTrigger: typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']
export const TairoSidenavContent: typeof import("../components/tairo-sidenav/TairoSidenavContent.vue")['default']
export const TairoSidenavLayout: typeof import("../components/tairo-sidenav/TairoSidenavLayout.vue")['default']
export const TairoSidenavSidebar: typeof import("../components/tairo-sidenav/TairoSidenavSidebar.vue")['default']
export const TairoSidenavSidebarDivider: typeof import("../components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']
export const TairoSidenavSidebarHeader: typeof import("../components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']
export const TairoSidenavSidebarLink: typeof import("../components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']
export const TairoSidenavSidebarLinks: typeof import("../components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']
export const TairoTopnavContent: typeof import("../components/tairo-topnav/TairoTopnavContent.vue")['default']
export const TairoTopnavHeader: typeof import("../components/tairo-topnav/TairoTopnavHeader.vue")['default']
export const TairoTopnavLayout: typeof import("../components/tairo-topnav/TairoTopnavLayout.vue")['default']
export const TairoTopnavNavbar: typeof import("../components/tairo-topnav/TairoTopnavNavbar.vue")['default']
export const BaseAccordion: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']
export const BaseAccordionItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']
export const BaseAutocomplete: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']
export const BaseAutocompleteGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']
export const BaseAutocompleteItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']
export const BaseAutocompleteLabel: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']
export const BaseAutocompleteSeparator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']
export const BaseAvatar: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']
export const BaseAvatarGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']
export const BaseBreadcrumb: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']
export const BaseButton: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']
export const BaseCard: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']
export const BaseCheckbox: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']
export const BaseCheckboxGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']
export const BaseChip: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']
export const BaseDropdown: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']
export const BaseDropdownArrow: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']
export const BaseDropdownCheckbox: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']
export const BaseDropdownItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']
export const BaseDropdownLabel: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']
export const BaseDropdownRadioGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']
export const BaseDropdownRadioItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']
export const BaseDropdownSeparator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']
export const BaseDropdownSub: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']
export const BaseField: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']
export const BaseHeading: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']
export const BaseIconBox: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']
export const BaseInput: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']
export const BaseInputFile: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']
export const BaseInputNumber: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']
export const BaseKbd: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']
export const BaseLink: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']
export const BaseList: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']
export const BaseListItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']
export const BaseMessage: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']
export const BasePagination: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']
export const BasePaginationButtonFirst: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']
export const BasePaginationButtonLast: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']
export const BasePaginationButtonNext: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']
export const BasePaginationButtonPrev: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']
export const BasePaginationItems: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']
export const BaseParagraph: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']
export const BasePlaceholderPage: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']
export const BasePlaceload: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']
export const BasePopover: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']
export const BasePrimitiveField: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']
export const BasePrimitiveFieldController: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']
export const BasePrimitiveFieldDescription: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']
export const BasePrimitiveFieldError: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']
export const BasePrimitiveFieldErrorIndicator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']
export const BasePrimitiveFieldLabel: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']
export const BasePrimitiveFieldLoadingIndicator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']
export const BasePrimitiveFieldRequiredIndicator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']
export const BasePrimitiveFieldSuccessIndicator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']
export const BaseProgress: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']
export const BaseProgressCircle: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']
export const BaseProse: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']
export const BaseProviders: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']
export const BaseRadio: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']
export const BaseRadioGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']
export const BaseSelect: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']
export const BaseSelectGroup: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']
export const BaseSelectItem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']
export const BaseSelectLabel: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']
export const BaseSelectSeparator: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']
export const BaseSlider: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']
export const BaseSnack: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']
export const BaseSwitchBall: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']
export const BaseSwitchThin: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']
export const BaseTabs: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']
export const BaseTabsContent: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']
export const BaseTabsTrigger: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']
export const BaseTag: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']
export const BaseText: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']
export const BaseTextarea: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']
export const BaseThemeSwitch: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']
export const BaseThemeSystem: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']
export const BaseThemeToggle: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']
export const BaseToast: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']
export const BaseToastProvider: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']
export const BaseTooltip: typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']
export const NuxtWelcome: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtRouteAnnouncer: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const ColorScheme: typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const Icon: typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtPage: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page-placeholder")['default']
export const NoScript: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyTairoCheckAnimated: LazyComponent<typeof import("../components/TairoCheckAnimated.vue")['default']>
export const LazyTairoCheckboxAnimated: LazyComponent<typeof import("../components/TairoCheckboxAnimated.vue")['default']>
export const LazyTairoCheckboxCardIcon: LazyComponent<typeof import("../components/TairoCheckboxCardIcon.vue")['default']>
export const LazyTairoContentWrapper: LazyComponent<typeof import("../components/TairoContentWrapper.vue")['default']>
export const LazyTairoContentWrapperTabbed: LazyComponent<typeof import("../components/TairoContentWrapperTabbed.vue")['default']>
export const LazyTairoError: LazyComponent<typeof import("../components/TairoError.vue")['default']>
export const LazyTairoFlexTable: LazyComponent<typeof import("../components/TairoFlexTable.vue")['default']>
export const LazyTairoFlexTableCell: LazyComponent<typeof import("../components/TairoFlexTableCell.vue")['default']>
export const LazyTairoFlexTableHeading: LazyComponent<typeof import("../components/TairoFlexTableHeading.vue")['default']>
export const LazyTairoFlexTableRow: LazyComponent<typeof import("../components/TairoFlexTableRow.vue")['default']>
export const LazyTairoFormGroup: LazyComponent<typeof import("../components/TairoFormGroup.vue")['default']>
export const LazyTairoFormSave: LazyComponent<typeof import("../components/TairoFormSave.vue")['default']>
export const LazyTairoFullscreenDropfile: LazyComponent<typeof import("../components/TairoFullscreenDropfile.vue")['default']>
export const LazyTairoImageZoom: LazyComponent<typeof import("../components/TairoImageZoom.vue")['default']>
export const LazyTairoInput: LazyComponent<typeof import("../components/TairoInput.vue")['default']>
export const LazyTairoInputFileHeadless: LazyComponent<typeof import("../components/TairoInputFileHeadless.vue")['default']>
export const LazyTairoMobileDrawer: LazyComponent<typeof import("../components/TairoMobileDrawer.vue")['default']>
export const LazyTairoPanels: LazyComponent<typeof import("../components/TairoPanels.vue")['default']>
export const LazyTairoRadioCard: LazyComponent<typeof import("../components/TairoRadioCard.vue")['default']>
export const LazyTairoSelect: LazyComponent<typeof import("../components/TairoSelect.vue")['default']>
export const LazyTairoSelectItem: LazyComponent<typeof import("../components/TairoSelectItem.vue")['default']>
export const LazyTairoTable: LazyComponent<typeof import("../components/TairoTable.vue")['default']>
export const LazyTairoTableCell: LazyComponent<typeof import("../components/TairoTableCell.vue")['default']>
export const LazyTairoTableHeading: LazyComponent<typeof import("../components/TairoTableHeading.vue")['default']>
export const LazyTairoTableRow: LazyComponent<typeof import("../components/TairoTableRow.vue")['default']>
export const LazyTairoWelcome: LazyComponent<typeof import("../components/TairoWelcome.vue")['default']>
export const LazyTairoCollapseBackdrop: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseBackdrop.vue")['default']>
export const LazyTairoCollapseCollapsible: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsible.vue")['default']>
export const LazyTairoCollapseCollapsibleLink: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsibleLink.vue")['default']>
export const LazyTairoCollapseCollapsibleTrigger: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue")['default']>
export const LazyTairoCollapseContent: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseContent.vue")['default']>
export const LazyTairoCollapseLayout: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseLayout.vue")['default']>
export const LazyTairoCollapseSidebar: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebar.vue")['default']>
export const LazyTairoCollapseSidebarClose: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarClose.vue")['default']>
export const LazyTairoCollapseSidebarHeader: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarHeader.vue")['default']>
export const LazyTairoCollapseSidebarLink: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarLink.vue")['default']>
export const LazyTairoCollapseSidebarLinks: LazyComponent<typeof import("../components/tairo-collapse/TairoCollapseSidebarLinks.vue")['default']>
export const LazyTairoMenu: LazyComponent<typeof import("../components/tairo-menu/TairoMenu.vue")['default']>
export const LazyTairoMenuContent: LazyComponent<typeof import("../components/tairo-menu/TairoMenuContent.vue")['default']>
export const LazyTairoMenuIndicator: LazyComponent<typeof import("../components/tairo-menu/TairoMenuIndicator.vue")['default']>
export const LazyTairoMenuItem: LazyComponent<typeof import("../components/tairo-menu/TairoMenuItem.vue")['default']>
export const LazyTairoMenuLink: LazyComponent<typeof import("../components/tairo-menu/TairoMenuLink.vue")['default']>
export const LazyTairoMenuLinkTab: LazyComponent<typeof import("../components/tairo-menu/TairoMenuLinkTab.vue")['default']>
export const LazyTairoMenuList: LazyComponent<typeof import("../components/tairo-menu/TairoMenuList.vue")['default']>
export const LazyTairoMenuListItems: LazyComponent<typeof import("../components/tairo-menu/TairoMenuListItems.vue")['default']>
export const LazyTairoMenuTrigger: LazyComponent<typeof import("../components/tairo-menu/TairoMenuTrigger.vue")['default']>
export const LazyTairoMenuViewport: LazyComponent<typeof import("../components/tairo-menu/TairoMenuViewport.vue")['default']>
export const LazyTairoSidebar: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebar.vue")['default']>
export const LazyTairoSidebarBackdrop: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarBackdrop.vue")['default']>
export const LazyTairoSidebarContent: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarContent.vue")['default']>
export const LazyTairoSidebarLayout: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLayout.vue")['default']>
export const LazyTairoSidebarLink: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLink.vue")['default']>
export const LazyTairoSidebarLinks: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarLinks.vue")['default']>
export const LazyTairoSidebarNav: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarNav.vue")['default']>
export const LazyTairoSidebarSubsidebar: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebar.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsible: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsibleLink: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue")['default']>
export const LazyTairoSidebarSubsidebarCollapsibleTrigger: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue")['default']>
export const LazyTairoSidebarSubsidebarContent: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarContent.vue")['default']>
export const LazyTairoSidebarSubsidebarHeader: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue")['default']>
export const LazyTairoSidebarSubsidebarLink: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarSubsidebarLink.vue")['default']>
export const LazyTairoSidebarTrigger: LazyComponent<typeof import("../components/tairo-sidebar/TairoSidebarTrigger.vue")['default']>
export const LazyTairoSidenavBackdrop: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavBackdrop.vue")['default']>
export const LazyTairoSidenavCollapsible: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsible.vue")['default']>
export const LazyTairoSidenavCollapsibleLink: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleLink.vue")['default']>
export const LazyTairoSidenavCollapsibleTrigger: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue")['default']>
export const LazyTairoSidenavContent: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavContent.vue")['default']>
export const LazyTairoSidenavLayout: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavLayout.vue")['default']>
export const LazyTairoSidenavSidebar: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebar.vue")['default']>
export const LazyTairoSidenavSidebarDivider: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarDivider.vue")['default']>
export const LazyTairoSidenavSidebarHeader: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarHeader.vue")['default']>
export const LazyTairoSidenavSidebarLink: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarLink.vue")['default']>
export const LazyTairoSidenavSidebarLinks: LazyComponent<typeof import("../components/tairo-sidenav/TairoSidenavSidebarLinks.vue")['default']>
export const LazyTairoTopnavContent: LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavContent.vue")['default']>
export const LazyTairoTopnavHeader: LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavHeader.vue")['default']>
export const LazyTairoTopnavLayout: LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavLayout.vue")['default']>
export const LazyTairoTopnavNavbar: LazyComponent<typeof import("../components/tairo-topnav/TairoTopnavNavbar.vue")['default']>
export const LazyBaseAccordion: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue")['default']>
export const LazyBaseAccordionItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue")['default']>
export const LazyBaseAutocomplete: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue")['default']>
export const LazyBaseAutocompleteGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue")['default']>
export const LazyBaseAutocompleteItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue")['default']>
export const LazyBaseAutocompleteLabel: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue")['default']>
export const LazyBaseAutocompleteSeparator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue")['default']>
export const LazyBaseAvatar: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue")['default']>
export const LazyBaseAvatarGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue")['default']>
export const LazyBaseBreadcrumb: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue")['default']>
export const LazyBaseButton: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue")['default']>
export const LazyBaseCard: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue")['default']>
export const LazyBaseCheckbox: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue")['default']>
export const LazyBaseCheckboxGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue")['default']>
export const LazyBaseChip: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue")['default']>
export const LazyBaseDropdown: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue")['default']>
export const LazyBaseDropdownArrow: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue")['default']>
export const LazyBaseDropdownCheckbox: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue")['default']>
export const LazyBaseDropdownItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue")['default']>
export const LazyBaseDropdownLabel: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue")['default']>
export const LazyBaseDropdownRadioGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue")['default']>
export const LazyBaseDropdownRadioItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue")['default']>
export const LazyBaseDropdownSeparator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue")['default']>
export const LazyBaseDropdownSub: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue")['default']>
export const LazyBaseField: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue")['default']>
export const LazyBaseHeading: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue")['default']>
export const LazyBaseIconBox: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue")['default']>
export const LazyBaseInput: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue")['default']>
export const LazyBaseInputFile: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue")['default']>
export const LazyBaseInputNumber: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue")['default']>
export const LazyBaseKbd: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue")['default']>
export const LazyBaseLink: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue")['default']>
export const LazyBaseList: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue")['default']>
export const LazyBaseListItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue")['default']>
export const LazyBaseMessage: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue")['default']>
export const LazyBasePagination: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Pagination.vue")['default']>
export const LazyBasePaginationButtonFirst: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue")['default']>
export const LazyBasePaginationButtonLast: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue")['default']>
export const LazyBasePaginationButtonNext: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue")['default']>
export const LazyBasePaginationButtonPrev: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue")['default']>
export const LazyBasePaginationItems: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue")['default']>
export const LazyBaseParagraph: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue")['default']>
export const LazyBasePlaceholderPage: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue")['default']>
export const LazyBasePlaceload: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue")['default']>
export const LazyBasePopover: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue")['default']>
export const LazyBasePrimitiveField: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue")['default']>
export const LazyBasePrimitiveFieldController: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue")['default']>
export const LazyBasePrimitiveFieldDescription: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue")['default']>
export const LazyBasePrimitiveFieldError: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue")['default']>
export const LazyBasePrimitiveFieldErrorIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue")['default']>
export const LazyBasePrimitiveFieldLabel: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue")['default']>
export const LazyBasePrimitiveFieldLoadingIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue")['default']>
export const LazyBasePrimitiveFieldRequiredIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue")['default']>
export const LazyBasePrimitiveFieldSuccessIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue")['default']>
export const LazyBaseProgress: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue")['default']>
export const LazyBaseProgressCircle: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue")['default']>
export const LazyBaseProse: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue")['default']>
export const LazyBaseProviders: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue")['default']>
export const LazyBaseRadio: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue")['default']>
export const LazyBaseRadioGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue")['default']>
export const LazyBaseSelect: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue")['default']>
export const LazyBaseSelectGroup: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue")['default']>
export const LazyBaseSelectItem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue")['default']>
export const LazyBaseSelectLabel: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue")['default']>
export const LazyBaseSelectSeparator: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue")['default']>
export const LazyBaseSlider: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue")['default']>
export const LazyBaseSnack: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue")['default']>
export const LazyBaseSwitchBall: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue")['default']>
export const LazyBaseSwitchThin: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue")['default']>
export const LazyBaseTabs: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue")['default']>
export const LazyBaseTabsContent: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue")['default']>
export const LazyBaseTabsTrigger: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue")['default']>
export const LazyBaseTag: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue")['default']>
export const LazyBaseText: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue")['default']>
export const LazyBaseTextarea: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue")['default']>
export const LazyBaseThemeSwitch: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue")['default']>
export const LazyBaseThemeSystem: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue")['default']>
export const LazyBaseThemeToggle: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue")['default']>
export const LazyBaseToast: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue")['default']>
export const LazyBaseToastProvider: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue")['default']>
export const LazyBaseTooltip: LazyComponent<typeof import("../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyColorScheme: LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page-placeholder")['default']>
export const LazyNoScript: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
