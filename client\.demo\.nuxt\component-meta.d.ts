import type { ComponentData } from 'nuxt-component-meta'
export type NuxtComponentMetaNames = 'CodeGroup' | 'CodeTimeline' | 'CodeTimelineItem' | 'DocButton' | 'DocChecklist' | 'DocComponentDemo' | 'DocComponentList' | 'DocComponentMeta' | 'DocCustomizerButton' | 'DocGrid' | 'DocGridIcon' | 'DocImage' | 'DocInfo' | 'DocLinker' | 'DocMessage' | 'DocOverview' | 'DocOverviewLayers' | 'DocStacks' | 'DocTag' | 'AddonApexcharts' | 'AddonCollapseTransition' | 'AddonDatepicker' | 'AddonInputPassword' | 'AddonInputPhone' | 'AddonLightweightCharts' | 'AddonMapboxLocationPicker' | 'CodeGroupHeader' | 'ComponentMetaCode' | 'DemoAccountMenu' | 'DemoActionText' | 'DemoActivityTable' | 'DemoAppLayoutSwitcher' | 'DemoAppSearch' | 'DemoAppSearchResult' | 'DemoAuthorsListCompact' | 'DemoCalendarEvent' | 'DemoCalendarEventPending' | 'DemoCardFilters' | 'DemoCommentListCompact' | 'DemoCompanyOverview' | 'DemoCreditCard' | 'DemoCreditCardReal' | 'DemoCreditCardSmall' | 'DemoDatepicker' | 'DemoDaysSquare' | 'DemoFileListTabbed' | 'DemoFlexTableCell' | 'DemoFlexTableRow' | 'DemoFlexTableStart' | 'DemoFlexTableWrapper' | 'DemoFollowersCompact' | 'DemoIconLinks' | 'DemoIconText' | 'DemoIconsSquare' | 'DemoImageLinks' | 'DemoInboxMessage' | 'DemoInfoBadges' | 'DemoInfoImage' | 'DemoLeagueListCompact' | 'DemoLinkArrow' | 'DemoMapMarker' | 'DemoMenuIconList' | 'DemoNavigationTop' | 'DemoNotificationsCompact' | 'DemoOfferCollapse' | 'DemoPanelAccount' | 'DemoPanelActivity' | 'DemoPanelCard' | 'DemoPanelInvest' | 'DemoPanelLanguage' | 'DemoPanelSearch' | 'DemoPanelTask' | 'DemoPendingTickets' | 'DemoPicture' | 'DemoPlaceholderCompact' | 'DemoPlaceholderMinimal' | 'DemoPopularCryptos' | 'DemoProductCompact' | 'DemoProgressCircle' | 'DemoProjectListCompact' | 'DemoSearchCompact' | 'DemoShoppingCartCompact' | 'DemoSocialLinks' | 'DemoStarterSwitcher' | 'DemoSubsidebarMessaging' | 'DemoTabbedContent' | 'DemoTagListCompact' | 'DemoTeamListCompact' | 'DemoTeamSearchCompact' | 'DemoTimelineCompact' | 'DemoTodoListCompact' | 'DemoTodoListTabbed' | 'DemoToolbar' | 'DemoToolbarTopnav' | 'DemoTopicListCompact' | 'DemoTransactionsFilters' | 'DemoTransactionsListPlaceload' | 'DemoTrendingSkills' | 'DemoUserList' | 'DemoVcardRight' | 'DemoVideoCompact' | 'DemoWidgetAccountBalance' | 'DemoWidgetFeatures' | 'DemoWidgetInvest' | 'DemoWidgetMoneyIn' | 'DemoWidgetMoneyOut' | 'DemoWidgetTransactionCompact' | 'DemoWidgetTransactionSummary' | 'DemoWidgetWelcome' | 'DemoWizardButtons' | 'DemoWizardNavigation' | 'DemoWizardStepTitle' | 'DemoWorkspaceDropdown' | 'DocLayoutSection' | 'DocSurround' | 'DocToc' | 'TairoLogo' | 'TairoLogoText' | 'VectorChartStockOne' | 'VectorChartStockThree' | 'VectorChartStockTwo' | 'VectorIllustrationBankFront' | 'VectorIllustrationCalendar' | 'VectorIllustrationCreditCard' | 'VectorIllustrationManWondering' | 'VectorIllustrationTransaction' | 'VectorLogoVisa' | 'DemoChartArea' | 'DemoChartAreaBalance' | 'DemoChartAreaBtcPrice' | 'DemoChartAreaCondition' | 'DemoChartAreaCustomers' | 'DemoChartAreaExpenses' | 'DemoChartAreaIncomeHistory' | 'DemoChartAreaInterviews' | 'DemoChartAreaMulti' | 'DemoChartAreaProgress' | 'DemoChartAreaSparkSalesFour' | 'DemoChartAreaSparkSalesOne' | 'DemoChartAreaSparkSalesThree' | 'DemoChartAreaSparkSalesTwo' | 'DemoChartAreaStats' | 'DemoChartAreaStockPrice' | 'DemoChartAreaTaskCompletion' | 'DemoChartBar' | 'DemoChartBarHorizontal' | 'DemoChartBarHorizontalMulti' | 'DemoChartBarMulti' | 'DemoChartBarMultiIncome' | 'DemoChartBarOrders' | 'DemoChartBarOxygen' | 'DemoChartBarProfit' | 'DemoChartBarRange' | 'DemoChartBarSalesProfit' | 'DemoChartBarSocialChannels' | 'DemoChartBarStacked' | 'DemoChartBarTeamEfficiency' | 'DemoChartBubble' | 'DemoChartDonut' | 'DemoChartDonutExpenses' | 'DemoChartLine' | 'DemoChartLineMulti' | 'DemoChartLineMultiAlt' | 'DemoChartLineRevenue' | 'DemoChartLineSparkFour' | 'DemoChartLineSparkOne' | 'DemoChartLineSparkThree' | 'DemoChartLineSparkTwo' | 'DemoChartLineStep' | 'DemoChartPie' | 'DemoChartRadar' | 'DemoChartRadial' | 'DemoChartRadialEvolution' | 'DemoChartRadialGauge' | 'DemoChartRadialGaugeAlt' | 'DemoChartRadialGoal' | 'DemoChartRadialGrowth' | 'DemoChartRadialMulti' | 'DemoChartRadialPopularity' | 'DemoChartRadialSalesRevenue' | 'DemoChartRadialSmallOne' | 'DemoChartRadialSmallThree' | 'DemoChartRadialSmallTwo' | 'DemoChartScatter' | 'DemoChartScatterEnergy' | 'DemoChartTimeline' | 'LandingBenefits' | 'LandingContent' | 'LandingCta' | 'LandingCustomizer' | 'LandingDemoLink' | 'LandingDemos' | 'LandingFeatures' | 'LandingFeaturesTile' | 'LandingFooter' | 'LandingHero' | 'LandingHeroMockup' | 'LandingLayers' | 'LandingLayersBox' | 'LandingLayout' | 'LandingLayouts' | 'LandingMobileNav' | 'LandingNavbar' | 'ProseA' | 'ProseBlockquote' | 'ProseCode' | 'ProseCodeInline' | 'ProseEm' | 'ProseH1' | 'ProseH2' | 'ProseH3' | 'ProseH5' | 'ProseH6' | 'ProseHr' | 'ProseImg' | 'ProseLi' | 'ProseOl' | 'ProseP' | 'ProsePre' | 'ProseScript' | 'ProseStrong' | 'ProseTable' | 'ProseTbody' | 'ProseTd' | 'ProseTh' | 'ProseThead' | 'ProseTr' | 'ProseUl' | 'ProseH4' | 'TairoCheckAnimated' | 'TairoCheckboxAnimated' | 'TairoCheckboxCardIcon' | 'TairoContentWrapper' | 'TairoContentWrapperTabbed' | 'TairoError' | 'TairoFlexTable' | 'TairoFlexTableCell' | 'TairoFlexTableHeading' | 'TairoFlexTableRow' | 'TairoFormGroup' | 'TairoFormSave' | 'TairoFullscreenDropfile' | 'TairoImageZoom' | 'TairoInput' | 'TairoInputFileHeadless' | 'TairoMobileDrawer' | 'TairoPanels' | 'TairoRadioCard' | 'TairoSelect' | 'TairoSelectItem' | 'TairoTable' | 'TairoTableCell' | 'TairoTableHeading' | 'TairoTableRow' | 'TairoWelcome' | 'TairoCollapseBackdrop' | 'TairoCollapseCollapsible' | 'TairoCollapseCollapsibleLink' | 'TairoCollapseCollapsibleTrigger' | 'TairoCollapseContent' | 'TairoCollapseLayout' | 'TairoCollapseSidebar' | 'TairoCollapseSidebarClose' | 'TairoCollapseSidebarHeader' | 'TairoCollapseSidebarLink' | 'TairoCollapseSidebarLinks' | 'TairoMenu' | 'TairoMenuContent' | 'TairoMenuIndicator' | 'TairoMenuItem' | 'TairoMenuLink' | 'TairoMenuLinkTab' | 'TairoMenuList' | 'TairoMenuListItems' | 'TairoMenuTrigger' | 'TairoMenuViewport' | 'TairoSidebar' | 'TairoSidebarBackdrop' | 'TairoSidebarContent' | 'TairoSidebarLayout' | 'TairoSidebarLink' | 'TairoSidebarLinks' | 'TairoSidebarNav' | 'TairoSidebarSubsidebar' | 'TairoSidebarSubsidebarCollapsible' | 'TairoSidebarSubsidebarCollapsibleLink' | 'TairoSidebarSubsidebarCollapsibleTrigger' | 'TairoSidebarSubsidebarContent' | 'TairoSidebarSubsidebarHeader' | 'TairoSidebarSubsidebarLink' | 'TairoSidebarTrigger' | 'TairoSidenavBackdrop' | 'TairoSidenavCollapsible' | 'TairoSidenavCollapsibleLink' | 'TairoSidenavCollapsibleTrigger' | 'TairoSidenavContent' | 'TairoSidenavLayout' | 'TairoSidenavSidebar' | 'TairoSidenavSidebarDivider' | 'TairoSidenavSidebarHeader' | 'TairoSidenavSidebarLink' | 'TairoSidenavSidebarLinks' | 'TairoTopnavContent' | 'TairoTopnavHeader' | 'TairoTopnavLayout' | 'TairoTopnavNavbar' | 'ExamplesAddonsDatepicker' | 'ExamplesAddonsMapbox' | 'ExamplesApexchartsBase' | 'ExamplesFlexTableCurved' | 'ExamplesFlexTableRounded' | 'ExamplesFlexTableSmooth' | 'ExamplesFlexTableStraight' | 'ExamplesInputPasswordBase' | 'ExamplesInputPasswordDisabled' | 'ExamplesInputPasswordLocale' | 'ExamplesInputPasswordUserInput' | 'ExamplesInputPasswordValidation' | 'ExamplesInputPhoneBase' | 'ExamplesInputPhoneCountry' | 'ExamplesInputPhoneDisabled' | 'ExamplesInputPhoneFormat' | 'ExamplesInputPhoneShape' | 'ExamplesInputPhoneSize' | 'ExamplesInputPhoneValidation' | 'ExamplesLightweightChartsBase' | 'ExamplesPanelActivity' | 'ExamplesPanelLanguage' | 'ExamplesPanelSearch' | 'ExamplesPanelTask' | 'ExamplesTableCurved' | 'ExamplesTableMediaCurved' | 'ExamplesTableMediaRounded' | 'ExamplesTableMediaSmooth' | 'ExamplesTableMediaStraight' | 'ExamplesTableRounded' | 'ExamplesTableSmooth' | 'ExamplesTableStraight' | 'ExamplesTairoCheckAnimated' | 'ExamplesTairoCheckboxAnimated' | 'ExamplesTairoCheckboxCardIcon' | 'ExamplesTairoCircularMenu' | 'ExamplesTairoError' | 'ExamplesTairoFormGroup' | 'ExamplesTairoFormSave' | 'ExamplesTairoInput' | 'ExamplesTairoLogo' | 'ExamplesTairoLogotext' | 'ExamplesTairoMenuComplete' | 'ExamplesTairoMenu' | 'ExamplesTairoMobileDrawer' | 'ExamplesTairoRadioCard' | 'ExamplesTairoSelect' | 'ExamplesTairoValidation' | 'BaseAccordion' | 'BaseAccordionItem' | 'BaseAutocomplete' | 'BaseAutocompleteGroup' | 'BaseAutocompleteItem' | 'BaseAutocompleteLabel' | 'BaseAutocompleteSeparator' | 'BaseAvatar' | 'BaseAvatarGroup' | 'BaseBreadcrumb' | 'BaseButton' | 'BaseCard' | 'BaseCheckbox' | 'BaseCheckboxGroup' | 'BaseChip' | 'BaseDropdown' | 'BaseDropdownArrow' | 'BaseDropdownCheckbox' | 'BaseDropdownItem' | 'BaseDropdownLabel' | 'BaseDropdownRadioGroup' | 'BaseDropdownRadioItem' | 'BaseDropdownSeparator' | 'BaseDropdownSub' | 'BaseField' | 'BaseHeading' | 'BaseIconBox' | 'BaseInput' | 'BaseInputFile' | 'BaseInputNumber' | 'BaseKbd' | 'BaseLink' | 'BaseList' | 'BaseListItem' | 'BaseMessage' | 'BasePagination' | 'BasePaginationButtonFirst' | 'BasePaginationButtonLast' | 'BasePaginationButtonNext' | 'BasePaginationButtonPrev' | 'BasePaginationItems' | 'BaseParagraph' | 'BasePlaceholderPage' | 'BasePlaceload' | 'BasePopover' | 'BasePrimitiveField' | 'BasePrimitiveFieldController' | 'BasePrimitiveFieldDescription' | 'BasePrimitiveFieldError' | 'BasePrimitiveFieldErrorIndicator' | 'BasePrimitiveFieldLabel' | 'BasePrimitiveFieldLoadingIndicator' | 'BasePrimitiveFieldRequiredIndicator' | 'BasePrimitiveFieldSuccessIndicator' | 'BaseProgress' | 'BaseProgressCircle' | 'BaseProse' | 'BaseProviders' | 'BaseRadio' | 'BaseRadioGroup' | 'BaseSelect' | 'BaseSelectGroup' | 'BaseSelectItem' | 'BaseSelectLabel' | 'BaseSelectSeparator' | 'BaseSlider' | 'BaseSnack' | 'BaseSwitchBall' | 'BaseSwitchThin' | 'BaseTabs' | 'BaseTabsContent' | 'BaseTabsTrigger' | 'BaseTag' | 'BaseText' | 'BaseTextarea' | 'BaseThemeSwitch' | 'BaseThemeSystem' | 'BaseThemeToggle' | 'BaseToast' | 'BaseToastProvider' | 'BaseTooltip' | 'NuxtWelcome' | 'NuxtLayout' | 'NuxtErrorBoundary' | 'ClientOnly' | 'DevOnly' | 'ServerPlaceholder' | 'NuxtLink' | 'NuxtLoadingIndicator' | 'NuxtRouteAnnouncer' | 'NuxtImg' | 'NuxtPicture' | 'AccordionContent' | 'AccordionHeader' | 'AccordionItem' | 'AccordionRoot' | 'AccordionTrigger' | 'AlertDialogRoot' | 'AlertDialogTrigger' | 'AlertDialogPortal' | 'AlertDialogContent' | 'AlertDialogOverlay' | 'AlertDialogCancel' | 'AlertDialogTitle' | 'AlertDialogDescription' | 'AlertDialogAction' | 'AspectRatio' | 'AvatarRoot' | 'AvatarFallback' | 'AvatarImage' | 'CalendarRoot' | 'CalendarHeader' | 'CalendarHeading' | 'CalendarGrid' | 'CalendarCell' | 'CalendarHeadCell' | 'CalendarNext' | 'CalendarPrev' | 'CalendarGridHead' | 'CalendarGridBody' | 'CalendarGridRow' | 'CalendarCellTrigger' | 'CheckboxGroupRoot' | 'CheckboxRoot' | 'CheckboxIndicator' | 'CollapsibleRoot' | 'CollapsibleTrigger' | 'CollapsibleContent' | 'ComboboxRoot' | 'ComboboxInput' | 'ComboboxAnchor' | 'ComboboxEmpty' | 'ComboboxTrigger' | 'ComboboxCancel' | 'ComboboxGroup' | 'ComboboxLabel' | 'ComboboxContent' | 'ComboboxViewport' | 'ComboboxVirtualizer' | 'ComboboxItem' | 'ComboboxItemIndicator' | 'ComboboxSeparator' | 'ComboboxArrow' | 'ComboboxPortal' | 'ContextMenuRoot' | 'ContextMenuTrigger' | 'ContextMenuPortal' | 'ContextMenuContent' | 'ContextMenuArrow' | 'ContextMenuItem' | 'ContextMenuGroup' | 'ContextMenuSeparator' | 'ContextMenuCheckboxItem' | 'ContextMenuItemIndicator' | 'ContextMenuLabel' | 'ContextMenuRadioGroup' | 'ContextMenuRadioItem' | 'ContextMenuSub' | 'ContextMenuSubContent' | 'ContextMenuSubTrigger' | 'DateFieldRoot' | 'DateFieldInput' | 'DatePickerRoot' | 'DatePickerHeader' | 'DatePickerHeading' | 'DatePickerGrid' | 'DatePickerCell' | 'DatePickerHeadCell' | 'DatePickerNext' | 'DatePickerPrev' | 'DatePickerGridHead' | 'DatePickerGridBody' | 'DatePickerGridRow' | 'DatePickerCellTrigger' | 'DatePickerInput' | 'DatePickerCalendar' | 'DatePickerField' | 'DatePickerAnchor' | 'DatePickerArrow' | 'DatePickerClose' | 'DatePickerTrigger' | 'DatePickerContent' | 'DateRangePickerRoot' | 'DateRangePickerHeader' | 'DateRangePickerHeading' | 'DateRangePickerGrid' | 'DateRangePickerCell' | 'DateRangePickerHeadCell' | 'DateRangePickerNext' | 'DateRangePickerPrev' | 'DateRangePickerGridHead' | 'DateRangePickerGridBody' | 'DateRangePickerGridRow' | 'DateRangePickerCellTrigger' | 'DateRangePickerInput' | 'DateRangePickerCalendar' | 'DateRangePickerField' | 'DateRangePickerAnchor' | 'DateRangePickerArrow' | 'DateRangePickerClose' | 'DateRangePickerTrigger' | 'DateRangePickerContent' | 'DateRangeFieldRoot' | 'DateRangeFieldInput' | 'DialogRoot' | 'DialogTrigger' | 'DialogPortal' | 'DialogContent' | 'DialogOverlay' | 'DialogClose' | 'DialogTitle' | 'DialogDescription' | 'DropdownMenuRoot' | 'DropdownMenuTrigger' | 'DropdownMenuPortal' | 'DropdownMenuContent' | 'DropdownMenuArrow' | 'DropdownMenuItem' | 'DropdownMenuGroup' | 'DropdownMenuSeparator' | 'DropdownMenuCheckboxItem' | 'DropdownMenuItemIndicator' | 'DropdownMenuLabel' | 'DropdownMenuRadioGroup' | 'DropdownMenuRadioItem' | 'DropdownMenuSub' | 'DropdownMenuSubContent' | 'DropdownMenuSubTrigger' | 'EditableRoot' | 'EditableArea' | 'EditableInput' | 'EditablePreview' | 'EditableSubmitTrigger' | 'EditableCancelTrigger' | 'EditableEditTrigger' | 'HoverCardRoot' | 'HoverCardTrigger' | 'HoverCardPortal' | 'HoverCardContent' | 'HoverCardArrow' | 'Label' | 'ListboxRoot' | 'ListboxContent' | 'ListboxFilter' | 'ListboxItem' | 'ListboxItemIndicator' | 'ListboxVirtualizer' | 'ListboxGroup' | 'ListboxGroupLabel' | 'MenubarRoot' | 'MenubarTrigger' | 'MenubarPortal' | 'MenubarContent' | 'MenubarArrow' | 'MenubarItem' | 'MenubarGroup' | 'MenubarSeparator' | 'MenubarCheckboxItem' | 'MenubarItemIndicator' | 'MenubarLabel' | 'MenubarRadioGroup' | 'MenubarRadioItem' | 'MenubarSub' | 'MenubarSubContent' | 'MenubarSubTrigger' | 'MenubarMenu' | 'NavigationMenuRoot' | 'NavigationMenuContent' | 'NavigationMenuIndicator' | 'NavigationMenuItem' | 'NavigationMenuLink' | 'NavigationMenuList' | 'NavigationMenuSub' | 'NavigationMenuTrigger' | 'NavigationMenuViewport' | 'NumberFieldRoot' | 'NumberFieldInput' | 'NumberFieldIncrement' | 'NumberFieldDecrement' | 'PaginationRoot' | 'PaginationEllipsis' | 'PaginationFirst' | 'PaginationLast' | 'PaginationList' | 'PaginationListItem' | 'PaginationNext' | 'PaginationPrev' | 'PinInputRoot' | 'PinInputInput' | 'PopoverRoot' | 'PopoverTrigger' | 'PopoverPortal' | 'PopoverContent' | 'PopoverArrow' | 'PopoverClose' | 'PopoverAnchor' | 'ProgressRoot' | 'ProgressIndicator' | 'RadioGroupRoot' | 'RadioGroupItem' | 'RadioGroupIndicator' | 'RangeCalendarRoot' | 'RangeCalendarHeader' | 'RangeCalendarHeading' | 'RangeCalendarGrid' | 'RangeCalendarCell' | 'RangeCalendarHeadCell' | 'RangeCalendarNext' | 'RangeCalendarPrev' | 'RangeCalendarGridHead' | 'RangeCalendarGridBody' | 'RangeCalendarGridRow' | 'RangeCalendarCellTrigger' | 'ScrollAreaRoot' | 'ScrollAreaViewport' | 'ScrollAreaScrollbar' | 'ScrollAreaThumb' | 'ScrollAreaCorner' | 'SelectRoot' | 'SelectTrigger' | 'SelectPortal' | 'SelectContent' | 'SelectArrow' | 'SelectSeparator' | 'SelectItemIndicator' | 'SelectLabel' | 'SelectGroup' | 'SelectItem' | 'SelectItemText' | 'SelectViewport' | 'SelectScrollUpButton' | 'SelectScrollDownButton' | 'SelectValue' | 'SelectIcon' | 'Separator' | 'SliderRoot' | 'SliderThumb' | 'SliderTrack' | 'SliderRange' | 'SplitterGroup' | 'SplitterPanel' | 'SplitterResizeHandle' | 'StepperRoot' | 'StepperItem' | 'StepperTrigger' | 'StepperDescription' | 'StepperTitle' | 'StepperIndicator' | 'StepperSeparator' | 'SwitchRoot' | 'SwitchThumb' | 'TabsRoot' | 'TabsList' | 'TabsContent' | 'TabsTrigger' | 'TabsIndicator' | 'TagsInputRoot' | 'TagsInputInput' | 'TagsInputItem' | 'TagsInputItemText' | 'TagsInputItemDelete' | 'TagsInputClear' | 'TimeFieldInput' | 'TimeFieldRoot' | 'ToastProvider' | 'ToastRoot' | 'ToastPortal' | 'ToastAction' | 'ToastClose' | 'ToastViewport' | 'ToastTitle' | 'ToastDescription' | 'Toggle' | 'ToggleGroupRoot' | 'ToggleGroupItem' | 'ToolbarRoot' | 'ToolbarButton' | 'ToolbarLink' | 'ToolbarToggleGroup' | 'ToolbarToggleItem' | 'ToolbarSeparator' | 'TooltipRoot' | 'TooltipTrigger' | 'TooltipContent' | 'TooltipArrow' | 'TooltipPortal' | 'TooltipProvider' | 'TreeRoot' | 'TreeItem' | 'TreeVirtualizer' | 'Viewport' | 'ConfigProvider' | 'FocusScope' | 'RovingFocusGroup' | 'RovingFocusItem' | 'Presence' | 'Primitive' | 'Slot' | 'VisuallyHidden'
export type NuxtComponentMeta = Record<NuxtComponentMetaNames, ComponentData>
declare const components: NuxtComponentMeta
export { components as default, components }