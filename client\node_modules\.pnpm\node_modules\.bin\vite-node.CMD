@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\vite-node@3.2.0_@types+node_7574904a704188d9c29e1a2f7ebfd06f\node_modules\vite-node\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\vite-node@3.2.0_@types+node_7574904a704188d9c29e1a2f7ebfd06f\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\vite-node@3.2.0_@types+node_7574904a704188d9c29e1a2f7ebfd06f\node_modules\vite-node\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\vite-node@3.2.0_@types+node_7574904a704188d9c29e1a2f7ebfd06f\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vite-node\vite-node.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vite-node\vite-node.mjs" %*
)
