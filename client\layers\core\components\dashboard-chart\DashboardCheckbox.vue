<script setup lang="ts">
defineProps<{
  checked?: boolean;
}>();
</script>

<template>
  <div class="relative inline-flex items-center">
    <div
      class="h-5 w-5 rounded border border-muted-300 dark:border-muted-700 flex items-center justify-center"
      :class="[
        checked 
          ? 'bg-primary-500 border-primary-500 dark:bg-primary-500 dark:border-primary-500' 
          : 'bg-white dark:bg-muted-800'
      ]"
    >
      <Icon
        v-if="checked"
        name="lucide:check"
        class="size-3.5 text-white"
      />
    </div>
  </div>
</template>
