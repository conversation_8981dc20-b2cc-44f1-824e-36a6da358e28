import express from "express";
import { auth, wrapController } from "../utils/route-helpers.js";
import {
  getAllUsers,
  createUser,
  updateUserRoles,
} from "../controllers/user.controller.js";
import {
  getCurrentUser,
  getUserProfile,
  updateUserProfile,
  updateUserPreferences,
  changePassword,
} from "../controllers/user/profile.controller.js";

const router = express.Router();

// Get all users (protected route)
router.get("/", auth, wrapController(getAllUsers));

// Create new user
router.post("/", auth, wrapController(createUser));

// Update user roles
router.put("/roles", auth, wrapController(updateUserRoles));

// Get current user
router.get("/me", auth, wrapController(getCurrentUser));

// Get user profile
router.get("/profile", auth, wrapController(getUserProfile));

// Update user profile
router.put("/profile", auth, wrapController(updateUserProfile));

// Update user preferences
router.put("/preferences", auth, wrapController(updateUserPreferences));

// Change user password
router.put("/change-password", auth, wrapController(changePassword));

export default router;
