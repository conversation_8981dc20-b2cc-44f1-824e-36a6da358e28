"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _content = await jitiImport("@nuxt/content");

const schema = _content.z.object({
  components: _content.z.string().array(),
  toc: _content.z.boolean(),
  icon: _content.z.object({
    src: _content.z.string(),
    srcDark: _content.z.string().optional()
  })
});var _default = exports.default =

(0, _content.defineContentConfig)({
  collections: {
    docs: (0, _content.defineCollection)({
      source: 'documentation/**',
      type: 'page',
      schema
    })
  }
}); /* v9-b5cbc8ae73d9d012 */
