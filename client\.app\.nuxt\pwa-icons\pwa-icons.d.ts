// Generated by @vite-pwa/nuxt
import type { AppleSplashScreenLink, FaviconLink, HtmlLink, IconAsset } from '@vite-pwa/assets-generator/api'

export interface PWAAssetIconImage {
  width?: number
  height?: number
  key: string
  src: string
}
export type P<PERSON><PERSON>setIcon<T extends HtmlLink> = Omit<IconAsset<T>, 'buffer'> & {
  asImage: PWAAssetIconImage
}
export interface PWAIcons {
  transparent: Record<string, PWAAssetIcon<HtmlLink>>
  maskable: Record<string, PWAAssetIcon<HtmlLink>>
  favicon: Record<string, PWAAssetIcon<FaviconLink>>
  apple: Record<string, PWAAssetIcon<HtmlLink>>
  appleSplashScreen: Record<string, PWAAssetIcon<AppleSplashScreenLink>>
}

declare module '#app' {
  interface NuxtApp {
    $pwaIcons?: PWAIcons
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $pwaIcons?: PWAIcons
  }
}

export {}
