// client/.app/app/composables/useAiAssistant.ts

import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useAiStore } from "../../stores/useAiStore";
import { speechService } from "../services/speechService";
import { speechRecognition } from "../services/speechRecognition";
import { useRouteExtractor } from "../../composables/useRouteExtractor";
import { useSidebar } from "./sidebar";
import { useTailwindBreakpoints } from "./tailwind"; // Fix: Import from local composables

// Use ref for state management instead of Nuxt's useState
// We'll create our own useState function for compatibility
const useState = <T>(_key: string, defaultValue: () => T) => {
  return ref<T>(defaultValue());
};

// Mock useAppConfig for compatibility
const useAppConfig = () => {
  return {
    tairo: {
      sidebar: {
        navigation: {
          items: [
            // Add some mock items for testing
            { title: "Dashboard", activePath: "/dashboard" },
            { title: "Core", activePath: "/core" },
            { title: "Budget", activePath: "/budget" },
          ],
        },
      },
    },
  };
};

// Type definitions
interface NavigationItem {
  title: string;
  path: string;
  icon?: string;
  children?: NavigationItem[];
  exact?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface ModuleNavigation {
  title: string;
  path: string;
  icon: string;
  children: NavigationItem[];
  dashboardPath?: string;
}

interface UIAction {
  type: string;
  label: string;
  description: string;
}

interface PageContext {
  path: string;
  module: string;
  elements: Record<string, HTMLElement>;
  actions: Record<string, UIAction>;
  navigation: {
    main: NavigationItem[];
    modules: Record<string, NavigationItem[]>;
  };
  currentLocation: {
    path: string;
    module: string;
    breadcrumb: string[];
  };
}

interface CommandContext {
  currentPath: string;
  currentModule: string;
  availableIntents: string[];
  currentLocation: {
    path: string;
    module: string;
    breadcrumb: string[];
  };
  navigation: {
    mainMenu: Record<string, unknown>[];
    moduleMenus: Record<string, Record<string, unknown>[]>;
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface CommandResponse {
  action: string;
  message: string;
  parameters?: Record<string, unknown>;
}

// Speech Recognition is now handled by the speechRecognition service

// Add processingTimeout to Window interface
declare global {
  interface Window {
    processingTimeout?: ReturnType<typeof setTimeout>;
  }
}

export function useAiAssistant() {
  const router = useRouter();
  const route = useRoute();
  const appConfig = useAppConfig();
  const aiStore = useAiStore();
  const { currentName, isOpen } = useSidebar();
  const { extractAllRoutes, getRouteHierarchy } = useRouteExtractor();

  // Add state management for navigation
  // These are used by the navigation system but not directly in this composable
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const selectedMenuItem = useState<string | null>(
    "topnav-selected-menu-item",
    () => null
  );
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const iconNavSelectedMenuItem = useState<string | null>(
    "iconnav-selected-menu-item",
    () => null
  );

  const isListening = ref(false);
  const transcript = ref("");
  const aiResponse = ref("");
  const isProcessing = ref(false);
  const error = ref<string | null>(null);

  let customActions: Record<
    string,
    {
      description: string;
      action: (params: Record<string, unknown>) => Promise<void>;
    }
  > = {};

  // Track if we're currently speaking
  const isSpeaking = ref(false);
  const speechQueue = ref<
    (string | { text: string; language?: string; speakerEnabled?: boolean })[]
  >([]);

  // Global speaker setting
  const globalSpeakerEnabled = ref(true);

  // Flag to track if we should process the speech queue
  const processingQueue = ref(false);

  // Process the next item in the speech queue
  const processNextInQueue = async (): Promise<void> => {
    // If we're already processing the queue or there's nothing in the queue, return
    if (processingQueue.value || speechQueue.value.length === 0) {
      return;
    }

    // Set the processing flag
    processingQueue.value = true;

    try {
      // Get the next item from the queue
      const nextItem = speechQueue.value.shift();
      if (!nextItem) {
        processingQueue.value = false;
        return;
      }

      // Process the item
      if (typeof nextItem === "string") {
        // Handle legacy string items in queue
        await speakResponse(nextItem, true); // Use priority to bypass queue
      } else {
        // Handle new object format with text, language, and speakerEnabled
        await speakResponse(
          nextItem.text,
          true,
          nextItem.language,
          nextItem.speakerEnabled
        ); // Use priority to bypass queue
      }
    } catch (error) {
      console.error("Error processing speech queue:", error);
    } finally {
      // Clear the processing flag
      processingQueue.value = false;

      // Check if there are more items in the queue
      if (speechQueue.value.length > 0) {
        // Process the next item after a short delay
        setTimeout(() => {
          processNextInQueue();
        }, 100);
      }
    }
  };

  const speakResponse = async (
    text: string,
    priority = false,
    language?: string,
    speakerEnabled?: boolean
  ): Promise<void> => {
    try {
      // Check if speaker is enabled (use global setting if not specified)
      const shouldSpeak =
        speakerEnabled !== undefined
          ? speakerEnabled
          : globalSpeakerEnabled.value;

      if (!shouldSpeak) {
        console.log("useAiAssistant: Speaker disabled, skipping speech");
        return;
      }

      // If priority, clear queue and stop current speech
      if (priority) {
        // Don't clear the queue, just stop current speech
        // This allows priority items to be processed immediately while preserving the queue
        if (isSpeaking.value) {
          stopSpeech();
        }
      } else {
        // If not priority and already speaking, add to queue and return
        if (isSpeaking.value) {
          // Store the language with the text in the queue
          speechQueue.value.push({ text, language, speakerEnabled });
          console.log(
            `useAiAssistant: Added to speech queue, queue length: ${speechQueue.value.length}`
          );
          return;
        }
      }

      // At this point, we're either priority or not speaking
      isSpeaking.value = true;

      // Get user's preferred language if not specified
      if (!language) {
        // Use i18n locale from the store
        const { locale } = useI18n();
        language = locale.value || "en";
      }

      console.log(`useAiAssistant: Speaking in language: ${language}`);

      // Get the AI store safely
      let audioBuffer: ArrayBuffer;
      try {
        // Use the speechService to get the audio buffer with language
        audioBuffer = await speechService.textToSpeech(text, language);
      } catch (error) {
        console.error("Error getting audio buffer:", error);
        isSpeaking.value = false;

        // Try to process the next item in the queue
        processNextInQueue();
        return;
      }

      try {
        // Play the audio
        await speechService.playAudio(audioBuffer);
      } catch (error) {
        console.error("Error playing audio:", error);
      } finally {
        // After speech completes, always set speaking to false
        isSpeaking.value = false;

        // Process next item in queue if any
        processNextInQueue();
      }
    } catch (err) {
      console.error("Text-to-speech error:", err);
      isSpeaking.value = false;
    }
  };

  const findModuleByPath = (path: string): string | null => {
    const normalizedPath = path.toLowerCase();
    const allRoutes = extractAllRoutes();

    // Find the route that matches the path
    for (const route of allRoutes) {
      if (normalizedPath.startsWith(route.path.toLowerCase())) {
        return route.module;
      }
    }
    return null;
  };

  const getBreadcrumb = (path: string): string[] => {
    const breadcrumb: string[] = [];
    const pathParts = path.split("/").filter(Boolean);

    let currentPath = "";
    for (const part of pathParts) {
      currentPath += `/${part}`;
      const module = findModuleByPath(currentPath);
      if (module) {
        breadcrumb.push(module);
      }
    }

    return breadcrumb;
  };

  const getCurrentPageContext = async (): Promise<PageContext> => {
    const currentPath = route.path;
    const currentModule = findModuleByPath(currentPath) || "";

    // Get all available UI elements on the current page
    const elements: Record<string, HTMLElement> = {};
    document.querySelectorAll("[data-action]").forEach((element) => {
      const action = element.getAttribute("data-action");
      if (action) {
        elements[action] = element as HTMLElement;
      }
    });

    // Get all registered custom actions
    const actions: Record<string, UIAction> = {};
    Object.entries(customActions).forEach(([key, value]) => {
      actions[key] = {
        type: "custom",
        label: key,
        description: value.description,
      };
    });

    // Build navigation structure from extracted routes
    const routeHierarchy = getRouteHierarchy();

    // Convert extracted routes to NavigationItem format
    const mainNavigation: NavigationItem[] = [];
    const moduleNavigations: Record<string, NavigationItem[]> = {};

    // Group routes by module and create navigation structure
    Object.entries(routeHierarchy).forEach(([module, routes]) => {
      const moduleRoutes = routes.map((route) => ({
        title: route.title,
        path: route.path,
        icon: "ph:circle-duotone", // Default icon, could be enhanced
        children: [],
      }));

      moduleNavigations[module] = moduleRoutes;

      // Add module to main navigation if it has routes
      if (moduleRoutes.length > 0) {
        mainNavigation.push({
          title: module.charAt(0).toUpperCase() + module.slice(1),
          path: `/${module}`,
          icon: "ph:squares-four-duotone", // Default module icon
          children: moduleRoutes,
        });
      }
    });

    return {
      path: currentPath,
      module: currentModule,
      elements,
      actions,
      navigation: {
        main: mainNavigation,
        modules: moduleNavigations,
      },
      currentLocation: {
        path: currentPath,
        module: currentModule,
        breadcrumb: getBreadcrumb(currentPath),
      },
    };
  };

  const executeAction = async (
    intent: string,
    value?: string
  ): Promise<boolean> => {
    const context = await getCurrentPageContext();
    const element = context.elements[intent];

    if (element) {
      if (element instanceof HTMLInputElement) {
        element.value = value || "";
        element.dispatchEvent(new Event("input"));
      } else if (element instanceof HTMLSelectElement) {
        element.value = value || "";
        element.dispatchEvent(new Event("change"));
      } else {
        element.click();
      }
      return true;
    }

    if (customActions[intent]) {
      await customActions[intent].action(value ? { value } : {});
      return true;
    }

    return false;
  };

  const updateNavigationState = (path: string) => {
    // Find the matching sidebar item from app config
    const sidebarItems = appConfig.tairo?.sidebar?.navigation?.items || [];
    const matchingSidebarItem = sidebarItems.find(
      (item: { activePath?: string; title: string }) =>
        item.activePath && path.startsWith(item.activePath)
    );

    if (matchingSidebarItem) {
      // Update the sidebar state using the same mechanism as the native sidebar
      currentName.value = matchingSidebarItem.title;

      // Only open sidebar on larger screens, following the same logic as in sidebar.ts
      if (typeof window === "undefined") {
        isOpen.value = Boolean(currentName.value);
      } else {
        const isXl = useTailwindBreakpoints().xl.value;
        isOpen.value = Boolean(currentName.value) && isXl;
      }
    }
  };

  const navigateToRoute = async (target: string) => {
    await router.push(target);
    updateNavigationState(target);
    await speakResponse("We've arrived. What would you like to do here?");
  };

  const processCommand = async (command: string): Promise<void> => {
    // If the user is speaking again, cancel any ongoing processing
    if (isListening.value) {
      try {
        const aiStore = useAiStore();
        if (aiStore.isProcessing) {
          console.log(
            "useAiAssistant: User interrupted, canceling previous command"
          );
          aiStore.cancelProcessing();

          // Clear any ongoing speech
          stopSpeech();

          // Clear the speech queue
          speechQueue.value = [];
        }
      } catch (err) {
        console.error("useAiAssistant: Error canceling previous command:", err);
      }
    }

    try {
      isProcessing.value = true;
      const context = await getCurrentPageContext();

      // Convert NavigationItem[] to Record<string, unknown>[] to match the store's expected type
      const mainMenuAsRecord = context.navigation.main.map((item) => {
        return { ...item } as Record<string, unknown>;
      });

      // Convert modules to Record<string, Record<string, unknown>[]>
      const moduleMenusAsRecord: Record<string, Record<string, unknown>[]> = {};
      Object.entries(context.navigation.modules).forEach(([key, value]) => {
        moduleMenusAsRecord[key] = value.map(
          (item) => ({ ...item } as Record<string, unknown>)
        );
      });

      const commandContext: CommandContext = {
        currentPath: context.path,
        currentModule: context.module,
        availableIntents: [...Object.keys(context.actions)],
        currentLocation: context.currentLocation,
        navigation: {
          mainMenu: mainMenuAsRecord,
          moduleMenus: moduleMenusAsRecord,
        },
      };

      const response = await aiStore.processCommand(command, commandContext);

      if (response && response.message) {
        aiResponse.value = response.message;

        if (response.action && response.action.startsWith("navigate:")) {
          const target = response.action.split(":")[1];
          if (target) {
            await navigateToRoute(target);
          }
        } else if (response.action && response.action.startsWith("ui:")) {
          const [, intent, value] = response.action.split(":");
          if (intent && value) {
            await executeAction(intent, value);
          }
          await speakResponse(response.message);
        } else if (response.action === "explain") {
          await speakResponse(response.message);
        }
      } else {
        // Handle case where response is null or doesn't have expected structure
        aiResponse.value =
          "Sorry, I couldn't process that command. Please try again.";
        await speakResponse(aiResponse.value);
      }
    } catch (err) {
      console.error("useAiAssistant: Error processing command:", err);
      error.value = "Failed to process command";
      aiResponse.value =
        "Sorry, I encountered an error processing your request. Please try again.";
      await speakResponse(aiResponse.value);
    } finally {
      isProcessing.value = false;
    }
  };

  // We're now using the speechRecognition service instead of implementing speech recognition here

  // Check if speech recognition is supported
  const isSpeechRecognitionSupported = (): boolean => {
    return (
      typeof window !== "undefined" &&
      ("webkitSpeechRecognition" in window || "SpeechRecognition" in window)
    );
  };

  const startListening = (): void => {
    console.log("useAiAssistant: startListening called");

    // Check if speech recognition is supported
    if (!isSpeechRecognitionSupported()) {
      console.error(
        "useAiAssistant: Speech recognition is not supported in this browser"
      );
      error.value = "Speech recognition is not supported in this browser.";
      transcript.value =
        "Sorry, your browser does not support speech recognition. Try using Chrome or Edge.";
      return;
    }

    isListening.value = true;
    error.value = null;
    transcript.value = "";

    try {
      // Create a debounced version of processCommand to avoid processing too many commands
      const debouncedProcessCommand = (result: string) => {
        // Clear any existing timeout
        if (window.processingTimeout) {
          clearTimeout(window.processingTimeout);
        }

        // Set a new timeout
        window.processingTimeout = setTimeout(() => {
          // Only process commands that are at least 2 characters long
          if (result && result.trim().length > 2) {
            processCommand(result);
          }
        }, 1000); // Wait 1 second after the user stops speaking
      };

      // Use the speechRecognition service instead of the built-in implementation
      speechRecognition.startListening(
        (result: string) => {
          console.log("useAiAssistant: Got transcript:", result);
          transcript.value = result;

          // Use the debounced version to process the command
          debouncedProcessCommand(result);
        },
        (err: string) => {
          console.error("useAiAssistant: Speech recognition error:", err);
          error.value = `Error occurred in recognition: ${err}`;
          isListening.value = false;
        }
      );
      console.log("useAiAssistant: Recognition started via service");

      // Show a temporary message to indicate that the AI is listening
      transcript.value = "Listening... Say something.";
      setTimeout(() => {
        if (transcript.value === "Listening... Say something.") {
          transcript.value = "Waiting for your voice...";
        }
      }, 3000);
    } catch (err) {
      console.error("useAiAssistant: Error starting recognition:", err);
      error.value = `Failed to start speech recognition: ${err}`;
      isListening.value = false;
    }
  };

  const stopListening = (): void => {
    console.log("useAiAssistant: stopListening called");

    // Check if speech recognition is supported
    if (!isSpeechRecognitionSupported()) {
      console.error(
        "useAiAssistant: Speech recognition is not supported in this browser"
      );
      isListening.value = false;
      return;
    }

    try {
      // Use the speechRecognition service instead of the built-in implementation
      speechRecognition.stopListening();
      console.log("useAiAssistant: Recognition stopped via service");
    } catch (err) {
      console.error("useAiAssistant: Error stopping recognition:", err);
    }

    // Clear any existing timeout to prevent auto-restart
    if (window.processingTimeout) {
      clearTimeout(window.processingTimeout);
      window.processingTimeout = undefined;
    }

    // Stop any ongoing speech
    stopSpeech();

    // Clear the speech queue
    speechQueue.value = [];

    // Cancel any ongoing API requests
    // This requires the AI store to expose a method to cancel requests
    try {
      const aiStore = useAiStore();
      if (aiStore.isProcessing) {
        aiStore.cancelProcessing();
      }
    } catch (err) {
      console.error("useAiAssistant: Error canceling API requests:", err);
    }

    // Set listening state to false
    isListening.value = false;

    // Don't automatically clear transcript and response - let the user control this
    // The window should only close when explicitly closed by the user
  };

  // Stop speech
  const stopSpeech = (): void => {
    if (isSpeaking.value) {
      // Clear the queue
      speechQueue.value = [];

      // Stop current speech using the speechService
      try {
        speechService.stopAudio();
      } catch (error) {
        console.error("Error stopping speech:", error);
      }

      // Update state
      isSpeaking.value = false;
    }
  };

  const registerActions = (
    actions: Record<
      string,
      {
        description: string;
        action: (params: Record<string, unknown>) => Promise<void>;
      }
    >
  ): void => {
    customActions = { ...customActions, ...actions };
  };

  // Function to set speaker enabled state
  const setSpeakerEnabled = (enabled: boolean): void => {
    globalSpeakerEnabled.value = enabled;
    if (!enabled && isSpeaking.value) {
      stopSpeech();
    }
  };

  return {
    isListening,
    isProcessing,
    transcript,
    aiResponse,
    error,
    startListening,
    stopListening,
    stopSpeech,
    processCommand,
    registerActions,
    updateNavigationState,
    isSpeaking,
    speakResponse, // Expose the speakResponse method for external use
    globalSpeakerEnabled, // Expose the speaker setting
    setSpeakerEnabled, // Expose the setter function
  };
}
