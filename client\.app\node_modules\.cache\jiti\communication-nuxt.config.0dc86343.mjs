"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _default = exports.default = defineNuxtConfig({
  compatibilityDate: "2025-03-05",
  future: {
    compatibilityVersion: 4
  },
  devtools: { enabled: false },
  ssr: false,
  extends: ["../tairo"],

  modules: [
  "@shuriken-ui/nuxt",
  "reka-ui/nuxt",
  "@vueuse/nuxt",
  "@pinia/nuxt",
  "pinia-plugin-persistedstate/nuxt",
  "@nuxtjs/i18n"],


  css: ["~/assets/main.css"],

  i18n: {
    locales: [
    {
      code: "en",
      dir: "ltr",
      language: "en-US",
      file: "en-US.yaml",
      name: "English"
    },
    {
      code: "et",
      dir: "ltr",
      language: "et-EE",
      file: "et-EE.yaml",
      name: "<PERSON>est<PERSON>"
    }],

    lazy: true,
    langDir: "./locales",
    defaultLocale: "en",
    strategy: "no_prefix",
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: "i18n_redirected",
      redirectOn: "root"
    },
    experimental: {
      generatedLocaleFilePathFormat: "off"
    },
    bundle: {
      optimizeTranslationDirective: false
    }
  },

  vite: {
    define: {
      // Enable / disable Options API support. Disabling this will result in smaller bundles,
      // but may affect compatibility with 3rd party libraries if they rely on Options API.
      __VUE_OPTIONS_API__: false
    },
    // Defining the optimizeDeps.include option prebuilds the dependencies, this avoid
    // some reloads when navigating between pages during development.
    // It's also useful to track them usage.
    optimizeDeps: {
      include: [
      // utilities
      "lodash-es",
      // vue ecosystem
      "vue",
      "vue-router"]

    }
  }
}); /* v9-0aa97e4b499ae24d */
