"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _default = exports.default = defineNuxtConfig({
  compatibilityDate: "2025-03-05",
  future: {
    compatibilityVersion: 4
  },
  devtools: { enabled: false },
  ssr: false,
  extends: ["../tairo"],

  modules: ["@shuriken-ui/nuxt", "reka-ui/nuxt", "@vueuse/nuxt", "@pinia/nuxt"],
  css: [
  // Add any core-specific CSS here
  "~/assets/custom.css"],


  vite: {
    define: {
      // Enable / disable Options API support. Disabling this will result in smaller bundles,
      // but may affect compatibility with 3rd party libraries if they rely on Options API.
      __VUE_OPTIONS_API__: false
    },
    // Defining the optimizeDeps.include option prebuilds the dependencies, this avoid
    // some reloads when navigating between pages during development.
    // It's also useful to track them usage.
    optimizeDeps: {
      include: [
      // form validation
      "@vee-validate/zod",
      "vee-validate",
      "zod",
      // date utilities
      "date-fns",
      "date-fns/locale",
      // charts
      "chart.js"]

    }
  }
}); /* v9-0be30eb23389a2cd */
