<!-- client/.app/app/pages/users/company/edit.vue -->

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div class="mb-8 mt-8">
      <div class="flex items-center gap-4 mb-4">
        <BaseButton
          variant="muted"
          color="muted"
          size="sm"
          @click="$router.push('/users/company')"
        >
          <Icon name="solar:arrow-left-linear" class="h-4 w-4 mr-1" />
          {{ $t("common.back") }}
        </BaseButton>
      </div>
      <BaseHeading
        as="h1"
        size="2xl"
        weight="medium"
        class="text-muted-800 dark:text-white"
      >
        {{ $t("company.edit_company") }}
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
        {{ $t("company.edit_company_desc") }}
      </BaseParagraph>
    </div>

    <!-- Content -->
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-3">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center gap-2">
            <h4
              class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
            >
              {{ $t("company.edit_sections") }}
            </h4>
          </div>
          <ul class="space-y-1 font-sans text-sm">
            <li>
              <button
                @click="activeTab = 'general'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'general'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:buildings-linear" class="size-5" />
                <span>{{ $t("company.general_info") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'address'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'address'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:map-point-linear" class="size-5" />
                <span>{{ $t("company.address_info") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'team'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'team'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:users-group-rounded-linear" class="size-5" />
                <span>{{ $t("company.team_members") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'documents'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'documents'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:document-text-linear" class="size-5" />
                <span>{{ $t("company.company_documents") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'settings'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'settings'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:settings-linear" class="size-5" />
                <span>{{ $t("company.company_settings_tab") }}</span>
              </button>
            </li>
          </ul>
        </BaseCard>
      </div>
      <div class="col-span-12 lg:col-span-9">
        <!-- General Information Tab -->
        <div v-show="activeTab === 'general'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <form @submit.prevent="saveGeneralInfo">
              <div class="grid gap-8 pt-6 md:grid-cols-12">
                <!-- Column -->
                <div class="md:col-span-5">
                  <div class="w-full max-w-xs">
                    <BaseHeading
                      as="h3"
                      size="md"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100 mb-1"
                    >
                      {{ $t("company.company_info") }}
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ $t("company.company_info_desc") }}
                    </BaseParagraph>
                  </div>
                </div>
                <!-- Column -->
                <div class="md:col-span-7">
                  <div class="space-y-6">
                    <BaseInput
                      v-model="generalForm.name"
                      :label="$t('company.company_name')"
                      placeholder="Enter company name"
                      required
                    />
                    <BaseInput
                      v-model="generalForm.registrationNumber"
                      :label="$t('company.registration_number')"
                      placeholder="Enter registration number"
                    />
                    <BaseInput
                      v-model="generalForm.vatNumber"
                      :label="$t('company.vat_number')"
                      placeholder="Enter VAT number"
                    />
                    <BaseInput
                      v-model="generalForm.industry"
                      :label="$t('company.industry')"
                      placeholder="Enter industry"
                    />
                    <BaseTextarea
                      v-model="generalForm.description"
                      :label="$t('company.description')"
                      placeholder="Enter company description"
                      rows="4"
                    />
                  </div>
                </div>
              </div>
              <div class="flex justify-end pt-6">
                <BaseButton
                  type="submit"
                  variant="primary"
                  color="primary"
                  :loading="isLoading"
                >
                  <Icon name="solar:check-circle-linear" class="h-4 w-4 mr-2" />
                  {{ $t("common.save_changes") }}
                </BaseButton>
              </div>
            </form>
          </BaseCard>
        </div>

        <!-- Address Information Tab -->
        <div v-show="activeTab === 'address'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <form @submit.prevent="saveAddressInfo">
              <div class="grid gap-8 pt-6 md:grid-cols-12">
                <!-- Column -->
                <div class="md:col-span-5">
                  <div class="w-full max-w-xs">
                    <BaseHeading
                      as="h3"
                      size="md"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100 mb-1"
                    >
                      {{ $t("company.address_info") }}
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ $t("company.address_info_desc") }}
                    </BaseParagraph>
                  </div>
                </div>
                <!-- Column -->
                <div class="md:col-span-7">
                  <div class="space-y-6">
                    <BaseInput
                      v-model="addressForm.address"
                      :label="$t('company.address_line_1')"
                      placeholder="Enter address line 1"
                    />
                    <BaseInput
                      v-model="addressForm.address2"
                      :label="$t('company.address_line_2')"
                      placeholder="Enter address line 2"
                    />
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <BaseInput
                        v-model="addressForm.city"
                        :label="$t('company.city')"
                        placeholder="Enter city"
                      />
                      <BaseInput
                        v-model="addressForm.postalCode"
                        :label="$t('company.postal_code')"
                        placeholder="Enter postal code"
                      />
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <BaseInput
                        v-model="addressForm.state"
                        :label="$t('company.state_province')"
                        placeholder="Enter state/province"
                      />
                      <BaseInput
                        v-model="addressForm.country"
                        :label="$t('company.country')"
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex justify-end pt-6">
                <BaseButton
                  type="submit"
                  variant="primary"
                  color="primary"
                  :loading="isLoading"
                >
                  <Icon name="solar:check-circle-linear" class="h-4 w-4 mr-2" />
                  {{ $t("common.save_changes") }}
                </BaseButton>
              </div>
            </form>
          </BaseCard>
        </div>

        <!-- Team Members Tab -->
        <div v-show="activeTab === 'team'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:users-group-rounded-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.team_management") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.team_management_desc") }}
              </BaseParagraph>
              <BaseButton variant="primary" color="primary" disabled>
                <Icon name="solar:user-plus-linear" class="h-4 w-4 mr-2" />
                {{ $t("common.coming_soon") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Documents Tab -->
        <div v-show="activeTab === 'documents'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:document-text-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.document_management") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.document_management_desc") }}
              </BaseParagraph>
              <BaseButton variant="primary" color="primary" disabled>
                <Icon name="solar:document-add-linear" class="h-4 w-4 mr-2" />
                {{ $t("common.coming_soon") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>

        <!-- Settings Tab -->
        <div v-show="activeTab === 'settings'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="text-center py-12">
              <Icon
                name="solar:settings-linear"
                class="h-16 w-16 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                class="text-muted-800 dark:text-muted-100 mb-2"
              >
                {{ $t("company.company_settings") }}
              </BaseHeading>
              <BaseParagraph
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-6"
              >
                {{ $t("company.company_settings_desc") }}
              </BaseParagraph>
              <BaseButton variant="primary" color="primary" disabled>
                <Icon name="solar:settings-linear" class="h-4 w-4 mr-2" />
                {{ $t("common.coming_soon") }}
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { useUserStore } from "../../../stores/useUserStore";
import { useApi } from "../../../composables/useApi";

// Initialize stores and composables
const userStore = useUserStore();
const toaster = useNuiToasts();
const api = useApi();
const route = useRoute();

// State
const activeTab = ref("general");
const isLoading = ref(false);

// Forms
const generalForm = reactive({
  name: "",
  registrationNumber: "",
  vatNumber: "",
  industry: "",
  description: "",
});

const addressForm = reactive({
  address: "",
  address2: "",
  city: "",
  postalCode: "",
  state: "",
  country: "",
});

// Computed properties
const company = computed(() => userStore.primaryCompany?.company);

// Methods
const prefillForms = () => {
  if (company.value) {
    // Prefill general form
    generalForm.name = company.value.name || "";
    generalForm.registrationNumber = company.value.registrationNumber || "";
    generalForm.vatNumber = company.value.vatNumber || "";
    generalForm.industry = company.value.industry || "";
    generalForm.description = company.value.description || "";

    // Prefill address form
    addressForm.address = company.value.address || "";
    addressForm.address2 = company.value.address2 || "";
    addressForm.city = company.value.city || "";
    addressForm.postalCode = company.value.postalCode || "";
    addressForm.state = company.value.state || "";
    addressForm.country = company.value.country || "";
  }
};

const saveGeneralInfo = async () => {
  if (!company.value?.id) return;

  isLoading.value = true;
  try {
    await api.put(`/companies/${company.value.id}`, generalForm);

    toaster.add({
      title: "Success",
      description: "Company information updated successfully",
      icon: "solar:check-circle-linear",
      progress: true,
    });

    // Refresh user data
    await userStore.fetchUser();
  } catch (error) {
    toaster.add({
      title: "Error",
      description: "Failed to update company information",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isLoading.value = false;
  }
};

const saveAddressInfo = async () => {
  if (!company.value?.id) return;

  isLoading.value = true;
  try {
    await api.put(`/companies/${company.value.id}`, addressForm);

    toaster.add({
      title: "Success",
      description: "Company address updated successfully",
      icon: "solar:check-circle-linear",
      progress: true,
    });

    // Refresh user data
    await userStore.fetchUser();
  } catch (error) {
    toaster.add({
      title: "Error",
      description: "Failed to update company address",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isLoading.value = false;
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser();
  }

  // Check for tab parameter in URL
  const tab = route.query.tab as string;
  if (
    tab &&
    ["general", "address", "team", "documents", "settings"].includes(tab)
  ) {
    activeTab.value = tab;
  }

  // Prefill forms with existing data
  prefillForms();

  // Redirect if user doesn't have a company
  if (!userStore.primaryCompany) {
    toaster.add({
      title: "Error",
      description: "You are not associated with any company",
      icon: "solar:close-circle-linear",
      progress: true,
    });
    await navigateTo("/users/company");
  }
});
</script>
