{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@vee-validate+zod@4.15.0_vu_fa4b8e4a9c8853c63a022da4f9080e7c/node_modules/@vee-validate/zod/dist/vee-validate-zod.mjs"], "sourcesContent": ["/**\n  * vee-validate v4.15.0\n  * (c) 2024 <PERSON><PERSON><PERSON><PERSON>\n  * @license MIT\n  */\nimport { ZodObject, ZodDefault, ZodFirstPartyTypeKind } from 'zod';\nimport { isNotNestedPath, cleanupNonNestedPath } from 'vee-validate';\n\nconst isObject = (obj) => obj !== null && !!obj && typeof obj === 'object' && !Array.isArray(obj);\nfunction isIndex(value) {\n    return Number(value) >= 0;\n}\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n// Reference: https://github.com/lodash/lodash/blob/master/isPlainObject.js\nfunction isPlainObject(value) {\n    if (!isObjectLike(value) || getTag(value) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(value) === null) {\n        return true;\n    }\n    let proto = value;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(value) === proto;\n}\nfunction merge(target, source) {\n    Object.keys(source).forEach(key => {\n        if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n            if (!target[key]) {\n                target[key] = {};\n            }\n            merge(target[key], source[key]);\n            return;\n        }\n        target[key] = source[key];\n    });\n    return target;\n}\n/**\n * Constructs a path with dot paths for arrays to use brackets to be compatible with vee-validate path syntax\n */\nfunction normalizeFormPath(path) {\n    const pathArr = path.split('.');\n    if (!pathArr.length) {\n        return '';\n    }\n    let fullPath = String(pathArr[0]);\n    for (let i = 1; i < pathArr.length; i++) {\n        if (isIndex(pathArr[i])) {\n            fullPath += `[${pathArr[i]}]`;\n            continue;\n        }\n        fullPath += `.${pathArr[i]}`;\n    }\n    return fullPath;\n}\n\n/**\n * Transforms a Zod object schema to Yup's schema\n */\nfunction toTypedSchema(zodSchema, opts) {\n    const schema = {\n        __type: 'VVTypedSchema',\n        async parse(value) {\n            const result = await zodSchema.safeParseAsync(value, opts);\n            if (result.success) {\n                return {\n                    value: result.data,\n                    errors: [],\n                };\n            }\n            const errors = {};\n            processIssues(result.error.issues, errors);\n            return {\n                errors: Object.values(errors),\n            };\n        },\n        cast(values) {\n            try {\n                return zodSchema.parse(values);\n            }\n            catch (_a) {\n                // Zod does not support \"casting\" or not validating a value, so next best thing is getting the defaults and merging them with the provided values.\n                const defaults = getDefaults(zodSchema);\n                if (isObject(defaults) && isObject(values)) {\n                    return merge(defaults, values);\n                }\n                return values;\n            }\n        },\n        describe(path) {\n            try {\n                if (!path) {\n                    return {\n                        required: !zodSchema.isOptional(),\n                        exists: true,\n                    };\n                }\n                const description = getSchemaForPath(path, zodSchema);\n                if (!description) {\n                    return {\n                        required: false,\n                        exists: false,\n                    };\n                }\n                return {\n                    required: !description.isOptional(),\n                    exists: true,\n                };\n            }\n            catch (_a) {\n                if ((process.env.NODE_ENV !== 'production')) {\n                    // eslint-disable-next-line no-console\n                    console.warn(`Failed to describe path ${path} on the schema, returning a default description.`);\n                }\n                return {\n                    required: false,\n                    exists: false,\n                };\n            }\n        },\n    };\n    return schema;\n}\nfunction processIssues(issues, errors) {\n    issues.forEach(issue => {\n        const path = normalizeFormPath(issue.path.join('.'));\n        if (issue.code === 'invalid_union') {\n            processIssues(issue.unionErrors.flatMap(ue => ue.issues), errors);\n            if (!path) {\n                return;\n            }\n        }\n        if (!errors[path]) {\n            errors[path] = { errors: [], path };\n        }\n        errors[path].errors.push(issue.message);\n    });\n}\n// Zod does not support extracting default values so the next best thing is manually extracting them.\n// https://github.com/colinhacks/zod/issues/1944#issuecomment-1406566175\nfunction getDefaults(schema) {\n    if (!(schema instanceof ZodObject)) {\n        return undefined;\n    }\n    return Object.fromEntries(Object.entries(schema.shape).map(([key, value]) => {\n        if (value instanceof ZodDefault) {\n            return [key, value._def.defaultValue()];\n        }\n        if (value instanceof ZodObject) {\n            return [key, getDefaults(value)];\n        }\n        return [key, undefined];\n    }));\n}\n/**\n * @deprecated use toTypedSchema instead.\n */\nconst toFieldValidator = toTypedSchema;\n/**\n * @deprecated use toTypedSchema instead.\n */\nconst toFormValidator = toTypedSchema;\nfunction getSchemaForPath(path, schema) {\n    if (!isObjectSchema(schema)) {\n        return null;\n    }\n    if (isNotNestedPath(path)) {\n        return schema.shape[cleanupNonNestedPath(path)];\n    }\n    const paths = (path || '').split(/\\.|\\[(\\d+)\\]/).filter(Boolean);\n    let currentSchema = schema;\n    for (let i = 0; i <= paths.length; i++) {\n        const p = paths[i];\n        if (!p || !currentSchema) {\n            return currentSchema;\n        }\n        if (isObjectSchema(currentSchema)) {\n            currentSchema = currentSchema.shape[p] || null;\n            continue;\n        }\n        if (isIndex(p) && isArraySchema(currentSchema)) {\n            currentSchema = currentSchema._def.type;\n        }\n    }\n    return null;\n}\nfunction getDefType(schema) {\n    return schema._def.typeName;\n}\nfunction isArraySchema(schema) {\n    return getDefType(schema) === ZodFirstPartyTypeKind.ZodArray;\n}\nfunction isObjectSchema(schema) {\n    return getDefType(schema) === ZodFirstPartyTypeKind.ZodObject;\n}\n\nexport { toFieldValidator, toFormValidator, toTypedSchema };\n"], "mappings": ";;;;;;;;;;;;AAQA,IAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,CAAC,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG;AAChG,SAAS,QAAQ,OAAO;AACpB,SAAO,OAAO,KAAK,KAAK;AAC5B;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,OAAO,OAAO;AACnB,MAAI,SAAS,MAAM;AACf,WAAO,UAAU,SAAY,uBAAuB;AAAA,EACxD;AACA,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK;AAC/C;AAEA,SAAS,cAAc,OAAO;AAC1B,MAAI,CAAC,aAAa,KAAK,KAAK,OAAO,KAAK,MAAM,mBAAmB;AAC7D,WAAO;AAAA,EACX;AACA,MAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACvC;AACA,SAAO,OAAO,eAAe,KAAK,MAAM;AAC5C;AACA,SAAS,MAAM,QAAQ,QAAQ;AAC3B,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AAC/B,QAAI,cAAc,OAAO,GAAG,CAAC,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC1D,UAAI,CAAC,OAAO,GAAG,GAAG;AACd,eAAO,GAAG,IAAI,CAAC;AAAA,MACnB;AACA,YAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAC9B;AAAA,IACJ;AACA,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC5B,CAAC;AACD,SAAO;AACX;AAIA,SAAS,kBAAkB,MAAM;AAC7B,QAAM,UAAU,KAAK,MAAM,GAAG;AAC9B,MAAI,CAAC,QAAQ,QAAQ;AACjB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,OAAO,QAAQ,CAAC,CAAC;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,QAAQ,QAAQ,CAAC,CAAC,GAAG;AACrB,kBAAY,IAAI,QAAQ,CAAC,CAAC;AAC1B;AAAA,IACJ;AACA,gBAAY,IAAI,QAAQ,CAAC,CAAC;AAAA,EAC9B;AACA,SAAO;AACX;AAKA,SAAS,cAAc,WAAW,MAAM;AACpC,QAAM,SAAS;AAAA,IACX,QAAQ;AAAA,IACR,MAAM,MAAM,OAAO;AACf,YAAM,SAAS,MAAM,UAAU,eAAe,OAAO,IAAI;AACzD,UAAI,OAAO,SAAS;AAChB,eAAO;AAAA,UACH,OAAO,OAAO;AAAA,UACd,QAAQ,CAAC;AAAA,QACb;AAAA,MACJ;AACA,YAAM,SAAS,CAAC;AAChB,oBAAc,OAAO,MAAM,QAAQ,MAAM;AACzC,aAAO;AAAA,QACH,QAAQ,OAAO,OAAO,MAAM;AAAA,MAChC;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,UAAI;AACA,eAAO,UAAU,MAAM,MAAM;AAAA,MACjC,SACO,IAAI;AAEP,cAAM,WAAW,YAAY,SAAS;AACtC,YAAI,SAAS,QAAQ,KAAK,SAAS,MAAM,GAAG;AACxC,iBAAO,MAAM,UAAU,MAAM;AAAA,QACjC;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,SAAS,MAAM;AACX,UAAI;AACA,YAAI,CAAC,MAAM;AACP,iBAAO;AAAA,YACH,UAAU,CAAC,UAAU,WAAW;AAAA,YAChC,QAAQ;AAAA,UACZ;AAAA,QACJ;AACA,cAAM,cAAc,iBAAiB,MAAM,SAAS;AACpD,YAAI,CAAC,aAAa;AACd,iBAAO;AAAA,YACH,UAAU;AAAA,YACV,QAAQ;AAAA,UACZ;AAAA,QACJ;AACA,eAAO;AAAA,UACH,UAAU,CAAC,YAAY,WAAW;AAAA,UAClC,QAAQ;AAAA,QACZ;AAAA,MACJ,SACO,IAAI;AACP,YAAK,MAAwC;AAEzC,kBAAQ,KAAK,2BAA2B,IAAI,kDAAkD;AAAA,QAClG;AACA,eAAO;AAAA,UACH,UAAU;AAAA,UACV,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,QAAQ;AACnC,SAAO,QAAQ,WAAS;AACpB,UAAM,OAAO,kBAAkB,MAAM,KAAK,KAAK,GAAG,CAAC;AACnD,QAAI,MAAM,SAAS,iBAAiB;AAChC,oBAAc,MAAM,YAAY,QAAQ,QAAM,GAAG,MAAM,GAAG,MAAM;AAChE,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAO,IAAI,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK;AAAA,IACtC;AACA,WAAO,IAAI,EAAE,OAAO,KAAK,MAAM,OAAO;AAAA,EAC1C,CAAC;AACL;AAGA,SAAS,YAAY,QAAQ;AACzB,MAAI,EAAE,kBAAkB,YAAY;AAChC,WAAO;AAAA,EACX;AACA,SAAO,OAAO,YAAY,OAAO,QAAQ,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACzE,QAAI,iBAAiB,YAAY;AAC7B,aAAO,CAAC,KAAK,MAAM,KAAK,aAAa,CAAC;AAAA,IAC1C;AACA,QAAI,iBAAiB,WAAW;AAC5B,aAAO,CAAC,KAAK,YAAY,KAAK,CAAC;AAAA,IACnC;AACA,WAAO,CAAC,KAAK,MAAS;AAAA,EAC1B,CAAC,CAAC;AACN;AAIA,IAAM,mBAAmB;AAIzB,IAAM,kBAAkB;AACxB,SAAS,iBAAiB,MAAM,QAAQ;AACpC,MAAI,CAAC,eAAe,MAAM,GAAG;AACzB,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,IAAI,GAAG;AACvB,WAAO,OAAO,MAAM,qBAAqB,IAAI,CAAC;AAAA,EAClD;AACA,QAAM,SAAS,QAAQ,IAAI,MAAM,cAAc,EAAE,OAAO,OAAO;AAC/D,MAAI,gBAAgB;AACpB,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACpC,UAAM,IAAI,MAAM,CAAC;AACjB,QAAI,CAAC,KAAK,CAAC,eAAe;AACtB,aAAO;AAAA,IACX;AACA,QAAI,eAAe,aAAa,GAAG;AAC/B,sBAAgB,cAAc,MAAM,CAAC,KAAK;AAC1C;AAAA,IACJ;AACA,QAAI,QAAQ,CAAC,KAAK,cAAc,aAAa,GAAG;AAC5C,sBAAgB,cAAc,KAAK;AAAA,IACvC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,QAAQ;AACxB,SAAO,OAAO,KAAK;AACvB;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,WAAW,MAAM,MAAM,sBAAsB;AACxD;AACA,SAAS,eAAe,QAAQ;AAC5B,SAAO,WAAW,MAAM,MAAM,sBAAsB;AACxD;", "names": []}