// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/core/runtime/nitro/handlers/renderer').default>>>>
    }
    '/__og-image__/font/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/server/routes/font').default>>>>
    }
    '/__og-image__/image/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/server/routes/image').default>>>>
    }
    '/__og-image__/static/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/nuxt-og-image@5.1.4_@unhead_b60bb409377c28c3a06359efdc0fccc6/node_modules/nuxt-og-image/dist/runtime/server/routes/image').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
    '/_ipx/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/ipx').default>>>>
    }
  }
}
export {}