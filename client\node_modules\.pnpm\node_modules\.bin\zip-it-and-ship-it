#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/zip-it-and-ship-it/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/zip-it-and-ship-it/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@netlify/zip-it-and-ship-it/bin.js" "$@"
else
  exec node  "$basedir/../@netlify/zip-it-and-ship-it/bin.js" "$@"
fi
