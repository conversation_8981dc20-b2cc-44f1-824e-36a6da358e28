#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules/module-definition/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules/module-definition/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules/module-definition/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules/module-definition/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/module-definition@6.0.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../module-definition/bin/cli.js" "$@"
else
  exec node  "$basedir/../module-definition/bin/cli.js" "$@"
fi
