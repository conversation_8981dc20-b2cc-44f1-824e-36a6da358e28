{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/helper.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/data/dateSplits.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/data/const.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/date/matching.ts", "../../../../../../node_modules/.pnpm/fastest-levenshtein@1.0.16/node_modules/fastest-levenshtein/esm/mod.js", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/levenshtein.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/data/l33tTable.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/data/translationKeys.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/matching/unmunger/TrieNode.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/matching/unmunger/l33tTableToTrieNode.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/Options.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/matching/reverse.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/matching/unmunger/getCleanPasswords.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/matching/l33t.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/regex/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/scoring/utils.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/bruteforce/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/date/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/scoring/uppercase.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/variants/scoring/l33t.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/regex/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/repeat/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/sequence/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/spatial/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/separator/scoring.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/scoring/estimate.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/scoring/index.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/repeat/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/sequence/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/spatial/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/separator/matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/Matching.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/TimeEstimates.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/bruteforce/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/date/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/dictionary/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/regex/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/repeat/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/sequence/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/spatial/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/matcher/separator/feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/Feedback.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/debounce.ts", "../../../../../../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/src/index.ts"], "sourcesContent": ["export const empty = (obj) => Object.keys(obj).length === 0;\nexport const extend = (listToExtend, list) => \n// eslint-disable-next-line prefer-spread\nlistToExtend.push.apply(listToExtend, list);\nexport const translate = (string, chrMap) => {\n    let newString = string;\n    Object.entries(chrMap).forEach(([key, value]) => {\n        const escapedKey = key.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(escapedKey, 'g');\n        newString = newString.replace(regex, value);\n    });\n    return newString;\n};\n// mod implementation that works for negative numbers\nexport const mod = (n, m) => ((n % m) + m) % m;\n// sort on i primary, j secondary\nexport const sorted = (matches) => matches.sort((m1, m2) => m1.i - m2.i || m1.j - m2.j);\nexport const buildRankedDictionary = (orderedList) => {\n    const result = {};\n    let counter = 1; // rank starts at 1, not 0\n    orderedList.forEach((word) => {\n        result[word] = counter;\n        counter += 1;\n    });\n    return result;\n};\n//# sourceMappingURL=helper.js.map", "export default {\n    4: [\n        // for length-4 strings, eg 1191 or 9111, two ways to split:\n        [1, 2],\n        [2, 3], // 91 1 1\n    ],\n    5: [\n        [1, 3],\n        [2, 3],\n        //  [2, 3], // 91 1 11    <- duplicate previous one\n        [2, 4], // 91 11 1    <- New and must be added as bug fix\n    ],\n    6: [\n        [1, 2],\n        [2, 4],\n        [4, 5], // 1991 1 1\n    ],\n    //  1111991\n    7: [\n        [1, 3],\n        [2, 3],\n        [4, 5],\n        [4, 6], // 1991 11 1\n    ],\n    8: [\n        [2, 4],\n        [4, 6], // 1991 11 11\n    ],\n};\n//# sourceMappingURL=dateSplits.js.map", "import dateSplits from './dateSplits';\nexport const DATE_MAX_YEAR = 2050;\nexport const DATE_MIN_YEAR = 1000;\nexport const DATE_SPLITS = dateSplits;\nexport const BRUTEFORCE_CARDINALITY = 10;\nexport const MIN_GUESSES_BEFORE_GROWING_SEQUENCE = 10000;\nexport const MIN_SUBMATCH_GUESSES_SINGLE_CHAR = 10;\nexport const MIN_SUBMATCH_GUESSES_MULTI_CHAR = 50;\nexport const MIN_YEAR_SPACE = 20;\n// \\xbf-\\xdf is a range for almost all special uppercase letter like Ä and so on\nexport const START_UPPER = /^[A-Z\\xbf-\\xdf][^A-Z\\xbf-\\xdf]+$/;\nexport const END_UPPER = /^[^A-Z\\xbf-\\xdf]+[A-Z\\xbf-\\xdf]$/;\n// \\xdf-\\xff is a range for almost all special lowercase letter like ä and so on\nexport const ALL_UPPER = /^[A-Z\\xbf-\\xdf]+$/;\nexport const ALL_UPPER_INVERTED = /^[^a-z\\xdf-\\xff]+$/;\nexport const ALL_LOWER = /^[a-z\\xdf-\\xff]+$/;\nexport const ALL_LOWER_INVERTED = /^[^A-Z\\xbf-\\xdf]+$/;\nexport const ONE_LOWER = /[a-z\\xdf-\\xff]/;\nexport const ONE_UPPER = /[A-Z\\xbf-\\xdf]/;\nexport const ALPHA_INVERTED = /[^A-Za-z\\xbf-\\xdf]/gi;\nexport const ALL_DIGIT = /^\\d+$/;\nexport const REFERENCE_YEAR = new Date().getFullYear();\nexport const REGEXEN = { recentYear: /19\\d\\d|200\\d|201\\d|202\\d/g };\n/* Separators */\nexport const SEPERATOR_CHARS = [\n    ' ',\n    ',',\n    ';',\n    ':',\n    '|',\n    '/',\n    '\\\\',\n    '_',\n    '.',\n    '-',\n];\nexport const SEPERATOR_CHAR_COUNT = SEPERATOR_CHARS.length;\n//# sourceMappingURL=const.js.map", "import { DATE_MAX_YEAR, DATE_MIN_YEAR, DATE_SPLITS, REFERENCE_YEAR, } from '../../data/const';\nimport { sorted } from '../../helper';\n/*\n * -------------------------------------------------------------------------------\n *  date matching ----------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchDate {\n    /*\n     * a \"date\" is recognized as:\n     *   any 3-tuple that starts or ends with a 2- or 4-digit year,\n     *   with 2 or 0 separator chars (1.1.91 or 1191),\n     *   maybe zero-padded (01-01-91 vs 1-1-91),\n     *   a month between 1 and 12,\n     *   a day between 1 and 31.\n     *\n     * note: this isn't true date parsing in that \"feb 31st\" is allowed,\n     * this doesn't check for leap years, etc.\n     *\n     * recipe:\n     * start with regex to find maybe-dates, then attempt to map the integers\n     * onto month-day-year to filter the maybe-dates into dates.\n     * finally, remove matches that are substrings of other matches to reduce noise.\n     *\n     * note: instead of using a lazy or greedy regex to find many dates over the full string,\n     * this uses a ^...$ regex against every substring of the password -- less performant but leads\n     * to every possible date match.\n     */\n    match({ password }) {\n        const matches = [\n            ...this.getMatchesWithoutSeparator(password),\n            ...this.getMatchesWithSeparator(password),\n        ];\n        const filteredMatches = this.filterNoise(matches);\n        return sorted(filteredMatches);\n    }\n    getMatchesWithSeparator(password) {\n        const matches = [];\n        const maybeDateWithSeparator = /^(\\d{1,4})([\\s/\\\\_.-])(\\d{1,2})\\2(\\d{1,4})$/;\n        // # dates with separators are between length 6 '1/1/91' and 10 '11/11/1991'\n        for (let i = 0; i <= Math.abs(password.length - 6); i += 1) {\n            for (let j = i + 5; j <= i + 9; j += 1) {\n                if (j >= password.length) {\n                    break;\n                }\n                const token = password.slice(i, +j + 1 || 9e9);\n                const regexMatch = maybeDateWithSeparator.exec(token);\n                if (regexMatch != null) {\n                    const dmy = this.mapIntegersToDayMonthYear([\n                        parseInt(regexMatch[1], 10),\n                        parseInt(regexMatch[3], 10),\n                        parseInt(regexMatch[4], 10),\n                    ]);\n                    if (dmy != null) {\n                        matches.push({\n                            pattern: 'date',\n                            token,\n                            i,\n                            j,\n                            separator: regexMatch[2],\n                            year: dmy.year,\n                            month: dmy.month,\n                            day: dmy.day,\n                        });\n                    }\n                }\n            }\n        }\n        return matches;\n    }\n    // eslint-disable-next-line max-statements\n    getMatchesWithoutSeparator(password) {\n        const matches = [];\n        const maybeDateNoSeparator = /^\\d{4,8}$/;\n        const metric = (candidate) => Math.abs(candidate.year - REFERENCE_YEAR);\n        // # dates without separators are between length 4 '1191' and 8 '11111991'\n        for (let i = 0; i <= Math.abs(password.length - 4); i += 1) {\n            for (let j = i + 3; j <= i + 7; j += 1) {\n                if (j >= password.length) {\n                    break;\n                }\n                const token = password.slice(i, +j + 1 || 9e9);\n                if (maybeDateNoSeparator.exec(token)) {\n                    const candidates = [];\n                    const index = token.length;\n                    const splittedDates = DATE_SPLITS[index];\n                    splittedDates.forEach(([k, l]) => {\n                        const dmy = this.mapIntegersToDayMonthYear([\n                            parseInt(token.slice(0, k), 10),\n                            parseInt(token.slice(k, l), 10),\n                            parseInt(token.slice(l), 10),\n                        ]);\n                        if (dmy != null) {\n                            candidates.push(dmy);\n                        }\n                    });\n                    if (candidates.length > 0) {\n                        /*\n                         * at this point: different possible dmy mappings for the same i,j substring.\n                         * match the candidate date that likely takes the fewest guesses: a year closest\n                         * to 2000.\n                         * (scoring.REFERENCE_YEAR).\n                         *\n                         * ie, considering '111504', prefer 11-15-04 to 1-1-1504\n                         * (interpreting '04' as 2004)\n                         */\n                        let bestCandidate = candidates[0];\n                        let minDistance = metric(candidates[0]);\n                        candidates.slice(1).forEach((candidate) => {\n                            const distance = metric(candidate);\n                            if (distance < minDistance) {\n                                bestCandidate = candidate;\n                                minDistance = distance;\n                            }\n                        });\n                        matches.push({\n                            pattern: 'date',\n                            token,\n                            i,\n                            j,\n                            separator: '',\n                            year: bestCandidate.year,\n                            month: bestCandidate.month,\n                            day: bestCandidate.day,\n                        });\n                    }\n                }\n            }\n        }\n        return matches;\n    }\n    /*\n     * matches now contains all valid date strings in a way that is tricky to capture\n     * with regexes only. while thorough, it will contain some unintuitive noise:\n     *\n     * '2015_06_04', in addition to matching 2015_06_04, will also contain\n     * 5(!) other date matches: 15_06_04, 5_06_04, ..., even 2015 (matched as 5/1/2020)\n     *\n     * to reduce noise, remove date matches that are strict substrings of others\n     */\n    filterNoise(matches) {\n        return matches.filter((match) => {\n            let isSubmatch = false;\n            const matchesLength = matches.length;\n            for (let o = 0; o < matchesLength; o += 1) {\n                const otherMatch = matches[o];\n                if (match !== otherMatch) {\n                    if (otherMatch.i <= match.i && otherMatch.j >= match.j) {\n                        isSubmatch = true;\n                        break;\n                    }\n                }\n            }\n            return !isSubmatch;\n        });\n    }\n    /*\n     * given a 3-tuple, discard if:\n     *   middle int is over 31 (for all dmy formats, years are never allowed in the middle)\n     *   middle int is zero\n     *   any int is over the max allowable year\n     *   any int is over two digits but under the min allowable year\n     *   2 integers are over 31, the max allowable day\n     *   2 integers are zero\n     *   all integers are over 12, the max allowable month\n     */\n    // eslint-disable-next-line complexity, max-statements\n    mapIntegersToDayMonthYear(integers) {\n        if (integers[1] > 31 || integers[1] <= 0) {\n            return null;\n        }\n        let over12 = 0;\n        let over31 = 0;\n        let under1 = 0;\n        for (let o = 0, len1 = integers.length; o < len1; o += 1) {\n            const int = integers[o];\n            if ((int > 99 && int < DATE_MIN_YEAR) || int > DATE_MAX_YEAR) {\n                return null;\n            }\n            if (int > 31) {\n                over31 += 1;\n            }\n            if (int > 12) {\n                over12 += 1;\n            }\n            if (int <= 0) {\n                under1 += 1;\n            }\n        }\n        if (over31 >= 2 || over12 === 3 || under1 >= 2) {\n            return null;\n        }\n        return this.getDayMonth(integers);\n    }\n    // eslint-disable-next-line max-statements\n    getDayMonth(integers) {\n        // first look for a four digit year: yyyy + daymonth or daymonth + yyyy\n        const possibleYearSplits = [\n            [integers[2], integers.slice(0, 2)],\n            [integers[0], integers.slice(1, 3)], // year first\n        ];\n        const possibleYearSplitsLength = possibleYearSplits.length;\n        for (let j = 0; j < possibleYearSplitsLength; j += 1) {\n            const [y, rest] = possibleYearSplits[j];\n            if (DATE_MIN_YEAR <= y && y <= DATE_MAX_YEAR) {\n                const dm = this.mapIntegersToDayMonth(rest);\n                if (dm != null) {\n                    return {\n                        year: y,\n                        month: dm.month,\n                        day: dm.day,\n                    };\n                }\n                /*\n                 * for a candidate that includes a four-digit year,\n                 * when the remaining integers don't match to a day and month,\n                 * it is not a date.\n                 */\n                return null;\n            }\n        }\n        // given no four-digit year, two digit years are the most flexible int to match, so\n        // try to parse a day-month out of integers[0..1] or integers[1..0]\n        for (let k = 0; k < possibleYearSplitsLength; k += 1) {\n            const [y, rest] = possibleYearSplits[k];\n            const dm = this.mapIntegersToDayMonth(rest);\n            if (dm != null) {\n                return {\n                    year: this.twoToFourDigitYear(y),\n                    month: dm.month,\n                    day: dm.day,\n                };\n            }\n        }\n        return null;\n    }\n    mapIntegersToDayMonth(integers) {\n        const temp = [integers, integers.slice().reverse()];\n        for (let i = 0; i < temp.length; i += 1) {\n            const data = temp[i];\n            const day = data[0];\n            const month = data[1];\n            if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {\n                return {\n                    day,\n                    month,\n                };\n            }\n        }\n        return null;\n    }\n    twoToFourDigitYear(year) {\n        if (year > 99) {\n            return year;\n        }\n        if (year > 50) {\n            // 87 -> 1987\n            return year + 1900;\n        }\n        // 15 -> 2015\n        return year + 2000;\n    }\n}\nexport default MatchDate;\n//# sourceMappingURL=matching.js.map", "const peq = new Uint32Array(0x10000);\nconst myers_32 = (a, b) => {\n    const n = a.length;\n    const m = b.length;\n    const lst = 1 << (n - 1);\n    let pv = -1;\n    let mv = 0;\n    let sc = n;\n    let i = n;\n    while (i--) {\n        peq[a.charCodeAt(i)] |= 1 << i;\n    }\n    for (i = 0; i < m; i++) {\n        let eq = peq[b.charCodeAt(i)];\n        const xv = eq | mv;\n        eq |= ((eq & pv) + pv) ^ pv;\n        mv |= ~(eq | pv);\n        pv &= eq;\n        if (mv & lst) {\n            sc++;\n        }\n        if (pv & lst) {\n            sc--;\n        }\n        mv = (mv << 1) | 1;\n        pv = (pv << 1) | ~(xv | mv);\n        mv &= xv;\n    }\n    i = n;\n    while (i--) {\n        peq[a.charCodeAt(i)] = 0;\n    }\n    return sc;\n};\nconst myers_x = (b, a) => {\n    const n = a.length;\n    const m = b.length;\n    const mhc = [];\n    const phc = [];\n    const hsize = Math.ceil(n / 32);\n    const vsize = Math.ceil(m / 32);\n    for (let i = 0; i < hsize; i++) {\n        phc[i] = -1;\n        mhc[i] = 0;\n    }\n    let j = 0;\n    for (; j < vsize - 1; j++) {\n        let mv = 0;\n        let pv = -1;\n        const start = j * 32;\n        const vlen = Math.min(32, m) + start;\n        for (let k = start; k < vlen; k++) {\n            peq[b.charCodeAt(k)] |= 1 << k;\n        }\n        for (let i = 0; i < n; i++) {\n            const eq = peq[a.charCodeAt(i)];\n            const pb = (phc[(i / 32) | 0] >>> i) & 1;\n            const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n            const xv = eq | mv;\n            const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n            let ph = mv | ~(xh | pv);\n            let mh = pv & xh;\n            if ((ph >>> 31) ^ pb) {\n                phc[(i / 32) | 0] ^= 1 << i;\n            }\n            if ((mh >>> 31) ^ mb) {\n                mhc[(i / 32) | 0] ^= 1 << i;\n            }\n            ph = (ph << 1) | pb;\n            mh = (mh << 1) | mb;\n            pv = mh | ~(xv | ph);\n            mv = ph & xv;\n        }\n        for (let k = start; k < vlen; k++) {\n            peq[b.charCodeAt(k)] = 0;\n        }\n    }\n    let mv = 0;\n    let pv = -1;\n    const start = j * 32;\n    const vlen = Math.min(32, m - start) + start;\n    for (let k = start; k < vlen; k++) {\n        peq[b.charCodeAt(k)] |= 1 << k;\n    }\n    let score = m;\n    for (let i = 0; i < n; i++) {\n        const eq = peq[a.charCodeAt(i)];\n        const pb = (phc[(i / 32) | 0] >>> i) & 1;\n        const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n        const xv = eq | mv;\n        const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n        let ph = mv | ~(xh | pv);\n        let mh = pv & xh;\n        score += (ph >>> (m - 1)) & 1;\n        score -= (mh >>> (m - 1)) & 1;\n        if ((ph >>> 31) ^ pb) {\n            phc[(i / 32) | 0] ^= 1 << i;\n        }\n        if ((mh >>> 31) ^ mb) {\n            mhc[(i / 32) | 0] ^= 1 << i;\n        }\n        ph = (ph << 1) | pb;\n        mh = (mh << 1) | mb;\n        pv = mh | ~(xv | ph);\n        mv = ph & xv;\n    }\n    for (let k = start; k < vlen; k++) {\n        peq[b.charCodeAt(k)] = 0;\n    }\n    return score;\n};\nconst distance = (a, b) => {\n    if (a.length < b.length) {\n        const tmp = b;\n        b = a;\n        a = tmp;\n    }\n    if (b.length === 0) {\n        return a.length;\n    }\n    if (a.length <= 32) {\n        return myers_32(a, b);\n    }\n    return myers_x(a, b);\n};\nconst closest = (str, arr) => {\n    let min_distance = Infinity;\n    let min_index = 0;\n    for (let i = 0; i < arr.length; i++) {\n        const dist = distance(str, arr[i]);\n        if (dist < min_distance) {\n            min_distance = dist;\n            min_index = i;\n        }\n    }\n    return arr[min_index];\n};\nexport { closest, distance };\n", "import { distance } from 'fastest-levenshtein';\nconst getUsedThreshold = (password, entry, threshold) => {\n    const isPasswordToShort = password.length <= entry.length;\n    const isThresholdLongerThanPassword = password.length <= threshold;\n    const shouldUsePasswordLength = isPasswordToShort || isThresholdLongerThanPassword;\n    // if password is too small use the password length divided by 4 while the threshold needs to be at least 1\n    return shouldUsePasswordLength ? Math.ceil(password.length / 4) : threshold;\n};\nconst findLevenshteinDistance = (password, rankedDictionary, threshold) => {\n    let foundDistance = 0;\n    const found = Object.keys(rankedDictionary).find((entry) => {\n        const usedThreshold = getUsedThreshold(password, entry, threshold);\n        if (Math.abs(password.length - entry.length) > usedThreshold) {\n            return false;\n        }\n        const foundEntryDistance = distance(password, entry);\n        const isInThreshold = foundEntryDistance <= usedThreshold;\n        if (isInThreshold) {\n            foundDistance = foundEntryDistance;\n        }\n        return isInThreshold;\n    });\n    if (found) {\n        return {\n            levenshteinDistance: foundDistance,\n            levenshteinDistanceEntry: found,\n        };\n    }\n    return {};\n};\nexport default findLevenshteinDistance;\n//# sourceMappingURL=levenshtein.js.map", "export default {\n    a: ['4', '@'],\n    b: ['8'],\n    c: ['(', '{', '[', '<'],\n    d: ['6', '|)'],\n    e: ['3'],\n    f: ['#'],\n    g: ['6', '9', '&'],\n    h: ['#', '|-|'],\n    i: ['1', '!', '|'],\n    k: ['<', '|<'],\n    l: ['!', '1', '|', '7'],\n    m: ['^^', 'nn', '2n', '/\\\\\\\\/\\\\\\\\'],\n    n: ['//'],\n    o: ['0', '()'],\n    q: ['9'],\n    u: ['|_|'],\n    s: ['$', '5'],\n    t: ['+', '7'],\n    v: ['<', '>', '/'],\n    w: ['^/', 'uu', 'vv', '2u', '2v', '\\\\\\\\/\\\\\\\\/'],\n    x: ['%', '><'],\n    z: ['2'],\n};\n//# sourceMappingURL=l33tTable.js.map", "export default {\n    warnings: {\n        straightRow: 'straightRow',\n        keyPattern: 'keyPattern',\n        simpleRepeat: 'simpleRepeat',\n        extendedRepeat: 'extendedRepeat',\n        sequences: 'sequences',\n        recentYears: 'recentYears',\n        dates: 'dates',\n        topTen: 'topTen',\n        topHundred: 'topHundred',\n        common: 'common',\n        similarToCommon: 'similarToCommon',\n        wordByItself: 'wordByItself',\n        namesByThemselves: 'namesByThemselves',\n        commonNames: 'commonNames',\n        userInputs: 'userInputs',\n        pwned: 'pwned',\n    },\n    suggestions: {\n        l33t: 'l33t',\n        reverseWords: 'reverseWords',\n        allUppercase: 'allUppercase',\n        capitalization: 'capitalization',\n        dates: 'dates',\n        recentYears: 'recentYears',\n        associatedYears: 'associatedYears',\n        sequences: 'sequences',\n        repeated: 'repeated',\n        longerKeyboardPattern: 'longerKeyboardPattern',\n        anotherWord: 'anotherWord',\n        useWords: 'useWords',\n        noNeed: 'noNeed',\n        pwned: 'pwned',\n    },\n    timeEstimation: {\n        ltSecond: 'ltSecond',\n        second: 'second',\n        seconds: 'seconds',\n        minute: 'minute',\n        minutes: 'minutes',\n        hour: 'hour',\n        hours: 'hours',\n        day: 'day',\n        days: 'days',\n        month: 'month',\n        months: 'months',\n        year: 'year',\n        years: 'years',\n        centuries: 'centuries',\n    },\n};\n//# sourceMappingURL=translationKeys.js.map", "export default class TrieNode {\n    constructor(parents = []) {\n        this.parents = parents;\n        // eslint-disable-next-line no-use-before-define\n        this.children = new Map();\n    }\n    addSub(key, ...subs) {\n        const firstChar = key.charAt(0);\n        if (!this.children.has(firstChar)) {\n            this.children.set(firstChar, new TrieNode([...this.parents, firstChar]));\n        }\n        let cur = this.children.get(firstChar);\n        for (let i = 1; i < key.length; i += 1) {\n            const c = key.charAt(i);\n            if (!cur.hasChild(c)) {\n                cur.addChild(c);\n            }\n            cur = cur.getChild(c);\n        }\n        cur.subs = (cur.subs || []).concat(subs);\n        return this;\n    }\n    getChild(child) {\n        return this.children.get(child);\n    }\n    isTerminal() {\n        return !!this.subs;\n    }\n    addChild(child) {\n        if (!this.hasChild(child)) {\n            this.children.set(child, new TrieNode([...this.parents, child]));\n        }\n    }\n    hasChild(child) {\n        return this.children.has(child);\n    }\n}\n//# sourceMappingURL=TrieNode.js.map", "export default (l33tTable, triNode) => {\n    Object.entries(l33tTable).forEach(([letter, substitutions]) => {\n        substitutions.forEach((substitution) => {\n            triNode.addSub(substitution, letter);\n        });\n    });\n    return triNode;\n};\n//# sourceMappingURL=l33tTableToTrieNode.js.map", "import { buildRankedDictionary } from './helper';\nimport l33tTable from './data/l33tTable';\nimport translationKeys from './data/translationKeys';\nimport TrieNode from './matcher/dictionary/variants/matching/unmunger/TrieNode';\nimport l33tTableToTrieNode from './matcher/dictionary/variants/matching/unmunger/l33tTableToTrieNode';\nexport class Options {\n    constructor() {\n        this.matchers = {};\n        this.l33tTable = l33tTable;\n        this.trieNodeRoot = l33tTableToTrieNode(l33tTable, new TrieNode());\n        this.dictionary = {\n            userInputs: [],\n        };\n        this.rankedDictionaries = {};\n        this.rankedDictionariesMaxWordSize = {};\n        this.translations = translationKeys;\n        this.graphs = {};\n        this.useLevenshteinDistance = false;\n        this.levenshteinThreshold = 2;\n        this.l33tMaxSubstitutions = 100;\n        this.maxLength = 256;\n        this.setRankedDictionaries();\n    }\n    // eslint-disable-next-line max-statements,complexity\n    setOptions(options = {}) {\n        if (options.l33tTable) {\n            this.l33tTable = options.l33tTable;\n            this.trieNodeRoot = l33tTableToTrieNode(options.l33tTable, new TrieNode());\n        }\n        if (options.dictionary) {\n            this.dictionary = options.dictionary;\n            this.setRankedDictionaries();\n        }\n        if (options.translations) {\n            this.setTranslations(options.translations);\n        }\n        if (options.graphs) {\n            this.graphs = options.graphs;\n        }\n        if (options.useLevenshteinDistance !== undefined) {\n            this.useLevenshteinDistance = options.useLevenshteinDistance;\n        }\n        if (options.levenshteinThreshold !== undefined) {\n            this.levenshteinThreshold = options.levenshteinThreshold;\n        }\n        if (options.l33tMaxSubstitutions !== undefined) {\n            this.l33tMaxSubstitutions = options.l33tMaxSubstitutions;\n        }\n        if (options.maxLength !== undefined) {\n            this.maxLength = options.maxLength;\n        }\n    }\n    setTranslations(translations) {\n        if (this.checkCustomTranslations(translations)) {\n            this.translations = translations;\n        }\n        else {\n            throw new Error('Invalid translations object fallback to keys');\n        }\n    }\n    checkCustomTranslations(translations) {\n        let valid = true;\n        Object.keys(translationKeys).forEach((type) => {\n            if (type in translations) {\n                const translationType = type;\n                Object.keys(translationKeys[translationType]).forEach((key) => {\n                    if (!(key in translations[translationType])) {\n                        valid = false;\n                    }\n                });\n            }\n            else {\n                valid = false;\n            }\n        });\n        return valid;\n    }\n    setRankedDictionaries() {\n        const rankedDictionaries = {};\n        const rankedDictionariesMaxWorkSize = {};\n        Object.keys(this.dictionary).forEach((name) => {\n            rankedDictionaries[name] = buildRankedDictionary(this.dictionary[name]);\n            rankedDictionariesMaxWorkSize[name] =\n                this.getRankedDictionariesMaxWordSize(this.dictionary[name]);\n        });\n        this.rankedDictionaries = rankedDictionaries;\n        this.rankedDictionariesMaxWordSize = rankedDictionariesMaxWorkSize;\n    }\n    getRankedDictionariesMaxWordSize(list) {\n        const data = list.map((el) => {\n            if (typeof el !== 'string') {\n                return el.toString().length;\n            }\n            return el.length;\n        });\n        // do not use Math.max(...data) because it can result in max stack size error because every entry will be used as an argument\n        if (data.length === 0) {\n            return 0;\n        }\n        return data.reduce((a, b) => Math.max(a, b), -Infinity);\n    }\n    buildSanitizedRankedDictionary(list) {\n        const sanitizedInputs = [];\n        list.forEach((input) => {\n            const inputType = typeof input;\n            if (inputType === 'string' ||\n                inputType === 'number' ||\n                inputType === 'boolean') {\n                sanitizedInputs.push(input.toString().toLowerCase());\n            }\n        });\n        return buildRankedDictionary(sanitizedInputs);\n    }\n    extendUserInputsDictionary(dictionary) {\n        if (!this.dictionary.userInputs) {\n            this.dictionary.userInputs = [];\n        }\n        const newList = [...this.dictionary.userInputs, ...dictionary];\n        this.rankedDictionaries.userInputs =\n            this.buildSanitizedRankedDictionary(newList);\n        this.rankedDictionariesMaxWordSize.userInputs =\n            this.getRankedDictionariesMaxWordSize(newList);\n    }\n    addMatcher(name, matcher) {\n        if (this.matchers[name]) {\n            console.info(`Matcher ${name} already exists`);\n        }\n        else {\n            this.matchers[name] = matcher;\n        }\n    }\n}\nexport const zxcvbnOptions = new Options();\n//# sourceMappingURL=Options.js.map", "/*\n * -------------------------------------------------------------------------------\n *  Dictionary reverse matching --------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchReverse {\n    constructor(defaultMatch) {\n        this.defaultMatch = defaultMatch;\n    }\n    match({ password }) {\n        const passwordReversed = password.split('').reverse().join('');\n        return this.defaultMatch({\n            password: passwordReversed,\n        }).map((match) => ({\n            ...match,\n            token: match.token.split('').reverse().join(''),\n            reversed: true,\n            // map coordinates back to original string\n            i: password.length - 1 - match.j,\n            j: password.length - 1 - match.i,\n        }));\n    }\n}\nexport default MatchReverse;\n//# sourceMappingURL=reverse.js.map", "class CleanPasswords {\n    constructor({ substr, limit, trieRoot }) {\n        this.buffer = [];\n        this.finalPasswords = [];\n        this.substr = substr;\n        this.limit = limit;\n        this.trieRoot = trieRoot;\n    }\n    getAllPossibleSubsAtIndex(index) {\n        const nodes = [];\n        let cur = this.trieRoot;\n        for (let i = index; i < this.substr.length; i += 1) {\n            const character = this.substr.charAt(i);\n            cur = cur.getChild(character);\n            if (!cur) {\n                break;\n            }\n            nodes.push(cur);\n        }\n        return nodes;\n    }\n    // eslint-disable-next-line complexity,max-statements\n    helper({ onlyFullSub, isFullSub, index, subIndex, changes, lastSubLetter, consecutiveSubCount, }) {\n        if (this.finalPasswords.length >= this.limit) {\n            return;\n        }\n        if (index === this.substr.length) {\n            if (onlyFullSub === isFullSub) {\n                this.finalPasswords.push({ password: this.buffer.join(''), changes });\n            }\n            return;\n        }\n        // first, exhaust all possible substitutions at this index\n        const nodes = [...this.getAllPossibleSubsAtIndex(index)];\n        let hasSubs = false;\n        // iterate backward to get wider substitutions first\n        for (let i = index + nodes.length - 1; i >= index; i -= 1) {\n            const cur = nodes[i - index];\n            if (cur.isTerminal()) {\n                // Skip if this would be a 4th or more consecutive substitution of the same letter\n                // this should work in all language as there shouldn't be the same letter more than four times in a row\n                // So we can ignore the rest to save calculation time\n                if (lastSubLetter === cur.parents.join('') &&\n                    consecutiveSubCount >= 3) {\n                    // eslint-disable-next-line no-continue\n                    continue;\n                }\n                hasSubs = true;\n                const subs = cur.subs;\n                // eslint-disable-next-line no-restricted-syntax\n                for (const sub of subs) {\n                    this.buffer.push(sub);\n                    const newSubs = changes.concat({\n                        i: subIndex,\n                        letter: sub,\n                        substitution: cur.parents.join(''),\n                    });\n                    // recursively build the rest of the string\n                    this.helper({\n                        onlyFullSub,\n                        isFullSub,\n                        index: i + 1,\n                        subIndex: subIndex + sub.length,\n                        changes: newSubs,\n                        lastSubLetter: cur.parents.join(''),\n                        consecutiveSubCount: lastSubLetter === cur.parents.join('')\n                            ? consecutiveSubCount + 1\n                            : 1,\n                    });\n                    // backtrack by ignoring the added postfix\n                    this.buffer.pop();\n                    if (this.finalPasswords.length >= this.limit) {\n                        return;\n                    }\n                }\n            }\n        }\n        // next, generate all combos without doing a substitution at this index\n        // if a partial substitution is requested or there are no substitutions at this index\n        if (!onlyFullSub || !hasSubs) {\n            const firstChar = this.substr.charAt(index);\n            this.buffer.push(firstChar);\n            this.helper({\n                onlyFullSub,\n                isFullSub: isFullSub && !hasSubs,\n                index: index + 1,\n                subIndex: subIndex + 1,\n                changes,\n                lastSubLetter,\n                consecutiveSubCount,\n            });\n            this.buffer.pop();\n        }\n    }\n    getAll() {\n        // only full substitution\n        this.helper({\n            onlyFullSub: true,\n            isFullSub: true,\n            index: 0,\n            subIndex: 0,\n            changes: [],\n            lastSubLetter: undefined,\n            consecutiveSubCount: 0,\n        });\n        // only partial substitution\n        this.helper({\n            onlyFullSub: false,\n            isFullSub: true,\n            index: 0,\n            subIndex: 0,\n            changes: [],\n            lastSubLetter: undefined,\n            consecutiveSubCount: 0,\n        });\n        return this.finalPasswords;\n    }\n}\nconst getCleanPasswords = (password, limit, trieRoot) => {\n    const helper = new CleanPasswords({\n        substr: password,\n        limit,\n        trieRoot,\n    });\n    return helper.getAll();\n};\nexport default getCleanPasswords;\n//# sourceMappingURL=getCleanPasswords.js.map", "import { zxcvbnOptions } from '../../../../Options';\nimport getCleanPasswords from './unmunger/getCleanPasswords';\nconst getExtras = (passwordWithSubs, i, j) => {\n    const previousChanges = passwordWithSubs.changes.filter((changes) => {\n        return changes.i < i;\n    });\n    const iUnsubbed = previousChanges.reduce((value, change) => {\n        return value - change.letter.length + change.substitution.length;\n    }, i);\n    const usedChanges = passwordWithSubs.changes.filter((changes) => {\n        return changes.i >= i && changes.i <= j;\n    });\n    const jUnsubbed = usedChanges.reduce((value, change) => {\n        return value - change.letter.length + change.substitution.length;\n    }, j - i + iUnsubbed);\n    const filtered = [];\n    const subDisplay = [];\n    usedChanges.forEach((value) => {\n        const existingIndex = filtered.findIndex((t) => {\n            return t.letter === value.letter && t.substitution === value.substitution;\n        });\n        if (existingIndex < 0) {\n            filtered.push({\n                letter: value.letter,\n                substitution: value.substitution,\n            });\n            subDisplay.push(`${value.substitution} -> ${value.letter}`);\n        }\n    });\n    return {\n        i: iUnsubbed,\n        j: jUnsubbed,\n        subs: filtered,\n        subDisplay: subDisplay.join(', '),\n    };\n};\n/*\n * -------------------------------------------------------------------------------\n *  Dictionary l33t matching -----------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchL33t {\n    constructor(defaultMatch) {\n        this.defaultMatch = defaultMatch;\n    }\n    isAlreadyIncluded(matches, newMatch) {\n        return matches.some((l33tMatch) => {\n            return Object.entries(l33tMatch).every(([key, value]) => {\n                return key === 'subs' || value === newMatch[key];\n            });\n        });\n    }\n    match({ password }) {\n        const matches = [];\n        const subbedPasswords = getCleanPasswords(password, zxcvbnOptions.l33tMaxSubstitutions, zxcvbnOptions.trieNodeRoot);\n        let hasFullMatch = false;\n        let isFullSubstitution = true;\n        subbedPasswords.forEach((subbedPassword) => {\n            if (hasFullMatch) {\n                return;\n            }\n            const matchedDictionary = this.defaultMatch({\n                password: subbedPassword.password,\n                useLevenshtein: isFullSubstitution,\n            });\n            // only the first entry has a full substitution\n            isFullSubstitution = false;\n            matchedDictionary.forEach((match) => {\n                if (!hasFullMatch) {\n                    hasFullMatch = match.i === 0 && match.j === password.length - 1;\n                }\n                const extras = getExtras(subbedPassword, match.i, match.j);\n                const token = password.slice(extras.i, +extras.j + 1 || 9e9);\n                const newMatch = {\n                    ...match,\n                    l33t: true,\n                    token,\n                    ...extras,\n                };\n                const alreadyIncluded = this.isAlreadyIncluded(matches, newMatch);\n                // only return the matches that contain an actual substitution\n                if (token.toLowerCase() !== match.matchedWord && !alreadyIncluded) {\n                    matches.push(newMatch);\n                }\n            });\n        });\n        // filter single-character l33t matches to reduce noise.\n        // otherwise '1' matches 'i', '4' matches 'a', both very common English words\n        // with low dictionary rank.\n        return matches.filter((match) => match.token.length > 1);\n    }\n}\nexport default MatchL33t;\n//# sourceMappingURL=l33t.js.map", "import findLevenshteinDistance from '../../levenshtein';\nimport { sorted } from '../../helper';\nimport { zxcvbnOptions } from '../../Options';\nimport Reverse from './variants/matching/reverse';\nimport L33t from './variants/matching/l33t';\nclass MatchDictionary {\n    constructor() {\n        this.l33t = new L33t(this.defaultMatch);\n        this.reverse = new Reverse(this.defaultMatch);\n    }\n    match({ password }) {\n        const matches = [\n            ...this.defaultMatch({\n                password,\n            }),\n            ...this.reverse.match({ password }),\n            ...this.l33t.match({ password }),\n        ];\n        return sorted(matches);\n    }\n    defaultMatch({ password, useLevenshtein = true }) {\n        const matches = [];\n        const passwordLength = password.length;\n        const passwordLower = password.toLowerCase();\n        // eslint-disable-next-line complexity,max-statements\n        Object.keys(zxcvbnOptions.rankedDictionaries).forEach((dictionaryName) => {\n            const rankedDict = zxcvbnOptions.rankedDictionaries[dictionaryName];\n            const longestDictionaryWordSize = zxcvbnOptions.rankedDictionariesMaxWordSize[dictionaryName];\n            const searchWidth = Math.min(longestDictionaryWordSize, passwordLength);\n            for (let i = 0; i < passwordLength; i += 1) {\n                const searchEnd = Math.min(i + searchWidth, passwordLength);\n                for (let j = i; j < searchEnd; j += 1) {\n                    const usedPassword = passwordLower.slice(i, +j + 1 || 9e9);\n                    const isInDictionary = usedPassword in rankedDict;\n                    let foundLevenshteinDistance = {};\n                    // only use levenshtein distance on full password to minimize the performance drop\n                    // and because otherwise there would be to many false positives\n                    const isFullPassword = i === 0 && j === passwordLength - 1;\n                    if (zxcvbnOptions.useLevenshteinDistance &&\n                        isFullPassword &&\n                        !isInDictionary &&\n                        useLevenshtein) {\n                        foundLevenshteinDistance = findLevenshteinDistance(usedPassword, rankedDict, zxcvbnOptions.levenshteinThreshold);\n                    }\n                    const isLevenshteinMatch = Object.keys(foundLevenshteinDistance).length !== 0;\n                    if (isInDictionary || isLevenshteinMatch) {\n                        const usedRankPassword = isLevenshteinMatch\n                            ? foundLevenshteinDistance.levenshteinDistanceEntry\n                            : usedPassword;\n                        const rank = rankedDict[usedRankPassword];\n                        matches.push({\n                            pattern: 'dictionary',\n                            i,\n                            j,\n                            token: password.slice(i, +j + 1 || 9e9),\n                            matchedWord: usedPassword,\n                            rank,\n                            dictionaryName: dictionaryName,\n                            reversed: false,\n                            l33t: false,\n                            ...foundLevenshteinDistance,\n                        });\n                    }\n                }\n            }\n        });\n        return matches;\n    }\n}\nexport default MatchDictionary;\n//# sourceMappingURL=matching.js.map", "import { REGEXEN } from '../../data/const';\nimport { sorted } from '../../helper';\n/*\n * -------------------------------------------------------------------------------\n *  regex matching ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass MatchRegex {\n    match({ password, regexes = REGEXEN }) {\n        const matches = [];\n        Object.keys(regexes).forEach((name) => {\n            const regex = regexes[name];\n            regex.lastIndex = 0; // keeps regexMatch stateless\n            let regexMatch;\n            // eslint-disable-next-line no-cond-assign\n            while ((regexMatch = regex.exec(password))) {\n                if (regexMatch) {\n                    const token = regexMatch[0];\n                    matches.push({\n                        pattern: 'regex',\n                        token,\n                        i: regexMatch.index,\n                        j: regexMatch.index + regexMatch[0].length - 1,\n                        regexName: name,\n                        regexMatch,\n                    });\n                }\n            }\n        });\n        return sorted(matches);\n    }\n}\nexport default MatchRegex;\n//# sourceMappingURL=matching.js.map", "export default {\n    // binomial coefficients\n    // src: http://blog.plover.com/math/choose.html\n    nCk(n, k) {\n        let count = n;\n        if (k > count) {\n            return 0;\n        }\n        if (k === 0) {\n            return 1;\n        }\n        let coEff = 1;\n        for (let i = 1; i <= k; i += 1) {\n            coEff *= count;\n            coEff /= i;\n            count -= 1;\n        }\n        return coEff;\n    },\n    log10(n) {\n        if (n === 0)\n            return 0;\n        return Math.log(n) / Math.log(10); // IE doesn't support Math.log10 :(\n    },\n    log2(n) {\n        return Math.log(n) / Math.log(2);\n    },\n    factorial(num) {\n        let rval = 1;\n        for (let i = 2; i <= num; i += 1)\n            rval *= i;\n        return rval;\n    },\n};\n//# sourceMappingURL=utils.js.map", "import { BRUTEFORCE_CARDINALITY, MIN_SUBMATCH_GUESSES_SINGLE_CHAR, MIN_SUBMATCH_GUESSES_MULTI_CHAR, } from '../../data/const';\nexport default ({ token }) => {\n    let guesses = BRUTEFORCE_CARDINALITY ** token.length;\n    if (guesses === Number.POSITIVE_INFINITY) {\n        guesses = Number.MAX_VALUE;\n    }\n    let minGuesses;\n    // small detail: make bruteforce matches at minimum one guess bigger than smallest allowed\n    // submatch guesses, such that non-bruteforce submatches over the same [i..j] take precedence.\n    if (token.length === 1) {\n        minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR + 1;\n    }\n    else {\n        minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR + 1;\n    }\n    return Math.max(guesses, minGuesses);\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_YEAR_SPACE, REFERENCE_YEAR } from '../../data/const';\nexport default ({ year, separator }) => {\n    // base guesses: (year distance from REFERENCE_YEAR) * num_days * num_years\n    const yearSpace = Math.max(Math.abs(year - REFERENCE_YEAR), MIN_YEAR_SPACE);\n    let guesses = yearSpace * 365;\n    // add factor of 4 for separator selection (one of ~4 choices)\n    if (separator) {\n        guesses *= 4;\n    }\n    return guesses;\n};\n//# sourceMappingURL=scoring.js.map", "import utils from '../../../../scoring/utils';\nimport { START_UPPER, END_UPPER, ALL_UPPER_INVERTED, ALL_LOWER_INVERTED, ONE_LOWER, ONE_UPPER, ALPHA_INVERTED, } from '../../../../data/const';\nconst getVariations = (cleanedWord) => {\n    const wordArray = cleanedWord.split('');\n    const upperCaseCount = wordArray.filter((char) => char.match(ONE_UPPER)).length;\n    const lowerCaseCount = wordArray.filter((char) => char.match(ONE_LOWER)).length;\n    let variations = 0;\n    const variationLength = Math.min(upperCaseCount, lowerCaseCount);\n    for (let i = 1; i <= variationLength; i += 1) {\n        variations += utils.nCk(upperCaseCount + lowerCaseCount, i);\n    }\n    return variations;\n};\nexport default (word) => {\n    // clean words of non alpha characters to remove the reward effekt to capitalize the first letter https://github.com/dropbox/zxcvbn/issues/232\n    const cleanedWord = word.replace(ALPHA_INVERTED, '');\n    if (cleanedWord.match(ALL_LOWER_INVERTED) ||\n        cleanedWord.toLowerCase() === cleanedWord) {\n        return 1;\n    }\n    // a capitalized word is the most common capitalization scheme,\n    // so it only doubles the search space (uncapitalized + capitalized).\n    // all caps and end-capitalized are common enough too, underestimate as 2x factor to be safe.\n    const commonCases = [START_UPPER, END_UPPER, ALL_UPPER_INVERTED];\n    const commonCasesLength = commonCases.length;\n    for (let i = 0; i < commonCasesLength; i += 1) {\n        const regex = commonCases[i];\n        if (cleanedWord.match(regex)) {\n            return 2;\n        }\n    }\n    // otherwise calculate the number of ways to capitalize U+L uppercase+lowercase letters\n    // with U uppercase letters or less. or, if there's more uppercase than lower (for eg. PASSwORD),\n    // the number of ways to lowercase U+L letters with L lowercase letters or less.\n    return getVariations(cleanedWord);\n};\n//# sourceMappingURL=uppercase.js.map", "import utils from '../../../../scoring/utils';\nconst countSubstring = (string, substring) => {\n    let count = 0;\n    let pos = string.indexOf(substring);\n    while (pos >= 0) {\n        count += 1;\n        pos = string.indexOf(substring, pos + substring.length);\n    }\n    return count;\n};\nconst getCounts = ({ sub, token }) => {\n    // lower-case match.token before calculating: capitalization shouldn't affect l33t calc.\n    const tokenLower = token.toLowerCase();\n    // num of subbed chars\n    const subbedCount = countSubstring(tokenLower, sub.substitution);\n    // num of unsubbed chars\n    const unsubbedCount = countSubstring(tokenLower, sub.letter);\n    return {\n        subbedCount,\n        unsubbedCount,\n    };\n};\nexport default ({ l33t, subs, token }) => {\n    if (!l33t) {\n        return 1;\n    }\n    let variations = 1;\n    subs.forEach((sub) => {\n        const { subbedCount, unsubbedCount } = getCounts({ sub, token });\n        if (subbedCount === 0 || unsubbedCount === 0) {\n            // for this sub, password is either fully subbed (444) or fully unsubbed (aaa)\n            // treat that as doubling the space (attacker needs to try fully subbed chars in addition to\n            // unsubbed.)\n            variations *= 2;\n        }\n        else {\n            // this case is similar to capitalization:\n            // with aa44a, U = 3, S = 2, attacker needs to try unsubbed + one sub + two subs\n            const p = Math.min(unsubbedCount, subbedCount);\n            let possibilities = 0;\n            for (let i = 1; i <= p; i += 1) {\n                possibilities += utils.nCk(unsubbedCount + subbedCount, i);\n            }\n            variations *= possibilities;\n        }\n    });\n    return variations;\n};\n//# sourceMappingURL=l33t.js.map", "import uppercaseVariant from './variants/scoring/uppercase';\nimport l33tVariant from './variants/scoring/l33t';\nexport default ({ rank, reversed, l33t, subs, token, dictionaryName, }) => {\n    const baseGuesses = rank; // keep these as properties for display purposes\n    const uppercaseVariations = uppercaseVariant(token);\n    const l33tVariations = l33tVariant({ l33t, subs, token });\n    const reversedVariations = (reversed && 2) || 1;\n    let calculation;\n    if (dictionaryName === 'diceware') {\n        // diceware dictionaries are special, so we get a simple scoring of 1/2 of 6^5 (6 digits on 5 dice)\n        // to get fix entropy of ~12.9 bits for every entry https://en.wikipedia.org/wiki/Diceware#:~:text=The%20level%20of,bits\n        calculation = 6 ** 5 / 2;\n    }\n    else {\n        calculation =\n            baseGuesses * uppercaseVariations * l33tVariations * reversedVariations;\n    }\n    return {\n        baseGuesses,\n        uppercaseVariations,\n        l33tVariations,\n        calculation,\n    };\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_YEAR_SPACE, REFERENCE_YEAR } from '../../data/const';\nexport default ({ regexName, regexMatch, token, }) => {\n    const charClassBases = {\n        alphaLower: 26,\n        alphaUpper: 26,\n        alpha: 52,\n        alphanumeric: 62,\n        digits: 10,\n        symbols: 33,\n    };\n    if (regexName in charClassBases) {\n        return (charClassBases[regexName] ** token.length);\n    }\n    // TODO add more regex types for example special dates like 09.11\n    // eslint-disable-next-line default-case\n    switch (regexName) {\n        case 'recentYear':\n            // conservative estimate of year space: num years from REFERENCE_YEAR.\n            // if year is close to REFERENCE_YEAR, estimate a year space of MIN_YEAR_SPACE.\n            return Math.max(Math.abs(parseInt(regexMatch[0], 10) - REFERENCE_YEAR), MIN_YEAR_SPACE);\n    }\n    return 0;\n};\n//# sourceMappingURL=scoring.js.map", "export default ({ baseGuesses, repeatCount }) => baseGuesses * repeatCount;\n//# sourceMappingURL=scoring.js.map", "export default ({ token, ascending }) => {\n    const firstChr = token.charAt(0);\n    let baseGuesses = 0;\n    const startingPoints = ['a', 'A', 'z', 'Z', '0', '1', '9'];\n    // lower guesses for obvious starting points\n    if (startingPoints.includes(firstChr)) {\n        baseGuesses = 4;\n    }\n    else if (firstChr.match(/\\d/)) {\n        baseGuesses = 10; // digits\n    }\n    else {\n        // could give a higher base for uppercase,\n        // assigning 26 to both upper and lower sequences is more conservative.\n        baseGuesses = 26;\n    }\n    // need to try a descending sequence in addition to every ascending sequence ->\n    // 2x guesses\n    if (!ascending) {\n        baseGuesses *= 2;\n    }\n    return baseGuesses * token.length;\n};\n//# sourceMappingURL=scoring.js.map", "import utils from '../../scoring/utils';\nimport { zxcvbnOptions } from '../../Options';\nconst calcAverageDegree = (graph) => {\n    let average = 0;\n    Object.keys(graph).forEach((key) => {\n        const neighbors = graph[key];\n        average += neighbors.filter((entry) => !!entry).length;\n    });\n    average /= Object.entries(graph).length;\n    return average;\n};\nconst estimatePossiblePatterns = ({ token, graph, turns, }) => {\n    const startingPosition = Object.keys(zxcvbnOptions.graphs[graph]).length;\n    const averageDegree = calcAverageDegree(zxcvbnOptions.graphs[graph]);\n    let guesses = 0;\n    const tokenLength = token.length;\n    // # estimate the number of possible patterns w/ tokenLength or less with turns or less.\n    for (let i = 2; i <= tokenLength; i += 1) {\n        const possibleTurns = Math.min(turns, i - 1);\n        for (let j = 1; j <= possibleTurns; j += 1) {\n            guesses += utils.nCk(i - 1, j - 1) * startingPosition * averageDegree ** j;\n        }\n    }\n    return guesses;\n};\nexport default ({ graph, token, shiftedCount, turns, }) => {\n    let guesses = estimatePossiblePatterns({ token, graph, turns });\n    // add extra guesses for shifted keys. (% instead of 5, A instead of a.)\n    // math is similar to extra guesses of l33t substitutions in dictionary matches.\n    if (shiftedCount) {\n        const unShiftedCount = token.length - shiftedCount;\n        if (shiftedCount === 0 || unShiftedCount === 0) {\n            guesses *= 2;\n        }\n        else {\n            let shiftedVariations = 0;\n            for (let i = 1; i <= Math.min(shiftedCount, unShiftedCount); i += 1) {\n                shiftedVariations += utils.nCk(shiftedCount + unShiftedCount, i);\n            }\n            guesses *= shiftedVariations;\n        }\n    }\n    return Math.round(guesses);\n};\n//# sourceMappingURL=scoring.js.map", "import { SEPERATOR_CHAR_COUNT } from '../../data/const';\nexport default () => {\n    return SEPERATOR_CHAR_COUNT;\n};\n//# sourceMappingURL=scoring.js.map", "import { MIN_SUBMATCH_GUESSES_SINGLE_CHAR, MIN_SUBMATCH_GUESSES_MULTI_CHAR, } from '../data/const';\nimport utils from './utils';\nimport { zxcvbnOptions } from '../Options';\nimport bruteforceMatcher from '../matcher/bruteforce/scoring';\nimport dateMatcher from '../matcher/date/scoring';\nimport dictionaryMatcher from '../matcher/dictionary/scoring';\nimport regexMatcher from '../matcher/regex/scoring';\nimport repeatMatcher from '../matcher/repeat/scoring';\nimport sequenceMatcher from '../matcher/sequence/scoring';\nimport spatialMatcher from '../matcher/spatial/scoring';\nimport separatorMatcher from '../matcher/separator/scoring';\nconst getMinGuesses = (match, password) => {\n    let minGuesses = 1;\n    if (match.token.length < password.length) {\n        if (match.token.length === 1) {\n            minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR;\n        }\n        else {\n            minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR;\n        }\n    }\n    return minGuesses;\n};\nconst matchers = {\n    bruteforce: bruteforceMatcher,\n    date: dateMatcher,\n    dictionary: dictionaryMatcher,\n    regex: regexMatcher,\n    repeat: repeatMatcher,\n    sequence: sequenceMatcher,\n    spatial: spatialMatcher,\n    separator: separatorMatcher,\n};\nconst getScoring = (name, match) => {\n    if (matchers[name]) {\n        return matchers[name](match);\n    }\n    if (zxcvbnOptions.matchers[name] &&\n        'scoring' in zxcvbnOptions.matchers[name]) {\n        return zxcvbnOptions.matchers[name].scoring(match);\n    }\n    return 0;\n};\n// ------------------------------------------------------------------------------\n// guess estimation -- one function per match pattern ---------------------------\n// ------------------------------------------------------------------------------\n// eslint-disable-next-line complexity, max-statements\nexport default (match, password) => {\n    const extraData = {};\n    // a match's guess estimate doesn't change. cache it.\n    if ('guesses' in match && match.guesses != null) {\n        return match;\n    }\n    const minGuesses = getMinGuesses(match, password);\n    const estimationResult = getScoring(match.pattern, match);\n    let guesses = 0;\n    if (typeof estimationResult === 'number') {\n        guesses = estimationResult;\n    }\n    else if (match.pattern === 'dictionary') {\n        guesses = estimationResult.calculation;\n        extraData.baseGuesses = estimationResult.baseGuesses;\n        extraData.uppercaseVariations = estimationResult.uppercaseVariations;\n        extraData.l33tVariations = estimationResult.l33tVariations;\n    }\n    const matchGuesses = Math.max(guesses, minGuesses);\n    return {\n        ...match,\n        ...extraData,\n        guesses: matchGuesses,\n        guessesLog10: utils.log10(matchGuesses),\n    };\n};\n//# sourceMappingURL=estimate.js.map", "import utils from './utils';\nimport estimateGuesses from './estimate';\nimport { MIN_GUESSES_BEFORE_GROWING_SEQUENCE } from '../data/const';\nconst scoringHelper = {\n    password: '',\n    optimal: {},\n    excludeAdditive: false,\n    separatorRegex: undefined,\n    fillArray(size, valueType) {\n        const result = [];\n        for (let i = 0; i < size; i += 1) {\n            let value = [];\n            if (valueType === 'object') {\n                value = {};\n            }\n            result.push(value);\n        }\n        return result;\n    },\n    // helper: make bruteforce match objects spanning i to j, inclusive.\n    makeBruteforceMatch(i, j) {\n        return {\n            pattern: 'bruteforce',\n            token: this.password.slice(i, +j + 1 || 9e9),\n            i,\n            j,\n        };\n    },\n    // helper: considers whether a length-sequenceLength\n    // sequence ending at match m is better (fewer guesses)\n    // than previously encountered sequences, updating state if so.\n    update(match, sequenceLength) {\n        const k = match.j;\n        const estimatedMatch = estimateGuesses(match, this.password);\n        let pi = estimatedMatch.guesses;\n        if (sequenceLength > 1) {\n            // we're considering a length-sequenceLength sequence ending with match m:\n            // obtain the product term in the minimization function by multiplying m's guesses\n            // by the product of the length-(sequenceLength-1)\n            // sequence ending just before m, at m.i - 1.\n            pi *= this.optimal.pi[estimatedMatch.i - 1][sequenceLength - 1];\n        }\n        // calculate the minimization func\n        let g = utils.factorial(sequenceLength) * pi;\n        if (!this.excludeAdditive) {\n            g += MIN_GUESSES_BEFORE_GROWING_SEQUENCE ** (sequenceLength - 1);\n        }\n        // update state if new best.\n        // first see if any competing sequences covering this prefix,\n        // with sequenceLength or fewer matches,\n        // fare better than this sequence. if so, skip it and return.\n        let shouldSkip = false;\n        Object.keys(this.optimal.g[k]).forEach((competingPatternLength) => {\n            const competingMetricMatch = this.optimal.g[k][competingPatternLength];\n            if (parseInt(competingPatternLength, 10) <= sequenceLength) {\n                if (competingMetricMatch <= g) {\n                    shouldSkip = true;\n                }\n            }\n        });\n        if (!shouldSkip) {\n            // this sequence might be part of the final optimal sequence.\n            this.optimal.g[k][sequenceLength] = g;\n            this.optimal.m[k][sequenceLength] = estimatedMatch;\n            this.optimal.pi[k][sequenceLength] = pi;\n        }\n    },\n    // helper: evaluate bruteforce matches ending at passwordCharIndex.\n    bruteforceUpdate(passwordCharIndex) {\n        // see if a single bruteforce match spanning the passwordCharIndex-prefix is optimal.\n        let match = this.makeBruteforceMatch(0, passwordCharIndex);\n        this.update(match, 1);\n        for (let i = 1; i <= passwordCharIndex; i += 1) {\n            // generate passwordCharIndex bruteforce matches, spanning from (i=1, j=passwordCharIndex) up to (i=passwordCharIndex, j=passwordCharIndex).\n            // see if adding these new matches to any of the sequences in optimal[i-1]\n            // leads to new bests.\n            match = this.makeBruteforceMatch(i, passwordCharIndex);\n            const tmp = this.optimal.m[i - 1];\n            // eslint-disable-next-line no-loop-func\n            Object.keys(tmp).forEach((sequenceLength) => {\n                const lastMatch = tmp[sequenceLength];\n                // corner: an optimal sequence will never have two adjacent bruteforce matches.\n                // it is strictly better to have a single bruteforce match spanning the same region:\n                // same contribution to the guess product with a lower length.\n                // --> safe to skip those cases.\n                if (lastMatch.pattern !== 'bruteforce') {\n                    // try adding m to this length-sequenceLength sequence.\n                    this.update(match, parseInt(sequenceLength, 10) + 1);\n                }\n            });\n        }\n    },\n    // helper: step backwards through optimal.m starting at the end,\n    // constructing the final optimal match sequence.\n    unwind(passwordLength) {\n        const optimalMatchSequence = [];\n        let k = passwordLength - 1;\n        // find the final best sequence length and score\n        let sequenceLength = 0;\n        // eslint-disable-next-line no-loss-of-precision\n        let g = 2e308;\n        const temp = this.optimal.g[k];\n        // safety check for empty passwords\n        if (temp) {\n            Object.keys(temp).forEach((candidateSequenceLength) => {\n                const candidateMetricMatch = temp[candidateSequenceLength];\n                if (candidateMetricMatch < g) {\n                    sequenceLength = parseInt(candidateSequenceLength, 10);\n                    g = candidateMetricMatch;\n                }\n            });\n        }\n        while (k >= 0) {\n            const match = this.optimal.m[k][sequenceLength];\n            optimalMatchSequence.unshift(match);\n            k = match.i - 1;\n            sequenceLength -= 1;\n        }\n        return optimalMatchSequence;\n    },\n};\nexport default {\n    // ------------------------------------------------------------------------------\n    // search --- most guessable match sequence -------------------------------------\n    // ------------------------------------------------------------------------------\n    //\n    // takes a sequence of overlapping matches, returns the non-overlapping sequence with\n    // minimum guesses. the following is a O(l_max * (n + m)) dynamic programming algorithm\n    // for a length-n password with m candidate matches. l_max is the maximum optimal\n    // sequence length spanning each prefix of the password. In practice it rarely exceeds 5 and the\n    // search terminates rapidly.\n    //\n    // the optimal \"minimum guesses\" sequence is here defined to be the sequence that\n    // minimizes the following function:\n    //\n    //    g = sequenceLength! * Product(m.guesses for m in sequence) + D^(sequenceLength - 1)\n    //\n    // where sequenceLength is the length of the sequence.\n    //\n    // the factorial term is the number of ways to order sequenceLength patterns.\n    //\n    // the D^(sequenceLength-1) term is another length penalty, roughly capturing the idea that an\n    // attacker will try lower-length sequences first before trying length-sequenceLength sequences.\n    //\n    // for example, consider a sequence that is date-repeat-dictionary.\n    //  - an attacker would need to try other date-repeat-dictionary combinations,\n    //    hence the product term.\n    //  - an attacker would need to try repeat-date-dictionary, dictionary-repeat-date,\n    //    ..., hence the factorial term.\n    //  - an attacker would also likely try length-1 (dictionary) and length-2 (dictionary-date)\n    //    sequences before length-3. assuming at minimum D guesses per pattern type,\n    //    D^(sequenceLength-1) approximates Sum(D^i for i in [1..sequenceLength-1]\n    //\n    // ------------------------------------------------------------------------------\n    mostGuessableMatchSequence(password, matches, excludeAdditive = false) {\n        scoringHelper.password = password;\n        scoringHelper.excludeAdditive = excludeAdditive;\n        const passwordLength = password.length;\n        // partition matches into sublists according to ending index j\n        let matchesByCoordinateJ = scoringHelper.fillArray(passwordLength, 'array');\n        matches.forEach((match) => {\n            matchesByCoordinateJ[match.j].push(match);\n        });\n        // small detail: for deterministic output, sort each sublist by i.\n        matchesByCoordinateJ = matchesByCoordinateJ.map((match) => match.sort((m1, m2) => m1.i - m2.i));\n        scoringHelper.optimal = {\n            // optimal.m[k][sequenceLength] holds final match in the best length-sequenceLength\n            // match sequence covering the\n            // password prefix up to k, inclusive.\n            // if there is no length-sequenceLength sequence that scores better (fewer guesses) than\n            // a shorter match sequence spanning the same prefix,\n            // optimal.m[k][sequenceLength] is undefined.\n            m: scoringHelper.fillArray(passwordLength, 'object'),\n            // same structure as optimal.m -- holds the product term Prod(m.guesses for m in sequence).\n            // optimal.pi allows for fast (non-looping) updates to the minimization function.\n            pi: scoringHelper.fillArray(passwordLength, 'object'),\n            // same structure as optimal.m -- holds the overall metric.\n            g: scoringHelper.fillArray(passwordLength, 'object'),\n        };\n        for (let k = 0; k < passwordLength; k += 1) {\n            matchesByCoordinateJ[k].forEach((match) => {\n                if (match.i > 0) {\n                    Object.keys(scoringHelper.optimal.m[match.i - 1]).forEach((sequenceLength) => {\n                        scoringHelper.update(match, parseInt(sequenceLength, 10) + 1);\n                    });\n                }\n                else {\n                    scoringHelper.update(match, 1);\n                }\n            });\n            scoringHelper.bruteforceUpdate(k);\n        }\n        const optimalMatchSequence = scoringHelper.unwind(passwordLength);\n        const optimalSequenceLength = optimalMatchSequence.length;\n        const guesses = this.getGuesses(password, optimalSequenceLength);\n        return {\n            password,\n            guesses,\n            guessesLog10: utils.log10(guesses),\n            sequence: optimalMatchSequence,\n        };\n    },\n    getGuesses(password, optimalSequenceLength) {\n        const passwordLength = password.length;\n        let guesses = 0;\n        if (password.length === 0) {\n            guesses = 1;\n        }\n        else {\n            guesses =\n                scoringHelper.optimal.g[passwordLength - 1][optimalSequenceLength];\n        }\n        return guesses;\n    },\n};\n//# sourceMappingURL=index.js.map", "import scoring from '../../scoring';\n/*\n *-------------------------------------------------------------------------------\n * repeats (aaa, abcabcabc) ------------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchRepeat {\n    // eslint-disable-next-line max-statements\n    match({ password, omniMatch }) {\n        const matches = [];\n        let lastIndex = 0;\n        while (lastIndex < password.length) {\n            const greedyMatch = this.getGreedyMatch(password, lastIndex);\n            const lazyMatch = this.getLazyMatch(password, lastIndex);\n            if (greedyMatch == null) {\n                break;\n            }\n            const { match, baseToken } = this.setMatchToken(greedyMatch, lazyMatch);\n            if (match) {\n                const j = match.index + match[0].length - 1;\n                const baseGuesses = this.getBaseGuesses(baseToken, omniMatch);\n                matches.push(this.normalizeMatch(baseToken, j, match, baseGuesses));\n                lastIndex = j + 1;\n            }\n        }\n        const hasPromises = matches.some((match) => {\n            return match instanceof Promise;\n        });\n        if (hasPromises) {\n            return Promise.all(matches);\n        }\n        return matches;\n    }\n    // eslint-disable-next-line max-params\n    normalizeMatch(baseToken, j, match, baseGuesses) {\n        const baseMatch = {\n            pattern: 'repeat',\n            i: match.index,\n            j,\n            token: match[0],\n            baseToken,\n            baseGuesses: 0,\n            repeatCount: match[0].length / baseToken.length,\n        };\n        if (baseGuesses instanceof Promise) {\n            return baseGuesses.then((resolvedBaseGuesses) => {\n                return {\n                    ...baseMatch,\n                    baseGuesses: resolvedBaseGuesses,\n                };\n            });\n        }\n        return {\n            ...baseMatch,\n            baseGuesses,\n        };\n    }\n    getGreedyMatch(password, lastIndex) {\n        const greedy = /(.+)\\1+/g;\n        greedy.lastIndex = lastIndex;\n        return greedy.exec(password);\n    }\n    getLazyMatch(password, lastIndex) {\n        const lazy = /(.+?)\\1+/g;\n        lazy.lastIndex = lastIndex;\n        return lazy.exec(password);\n    }\n    setMatchToken(greedyMatch, lazyMatch) {\n        const lazyAnchored = /^(.+?)\\1+$/;\n        let match;\n        let baseToken = '';\n        if (lazyMatch && greedyMatch[0].length > lazyMatch[0].length) {\n            // greedy beats lazy for 'aabaab'\n            // greedy: [aabaab, aab]\n            // lazy:   [aa,     a]\n            match = greedyMatch;\n            // greedy's repeated string might itself be repeated, eg.\n            // aabaab in aabaabaabaab.\n            // run an anchored lazy match on greedy's repeated string\n            // to find the shortest repeated string\n            const temp = lazyAnchored.exec(match[0]);\n            if (temp) {\n                baseToken = temp[1];\n            }\n        }\n        else {\n            // lazy beats greedy for 'aaaaa'\n            // greedy: [aaaa,  aa]\n            // lazy:   [aaaaa, a]\n            match = lazyMatch;\n            if (match) {\n                baseToken = match[1];\n            }\n        }\n        return {\n            match,\n            baseToken,\n        };\n    }\n    getBaseGuesses(baseToken, omniMatch) {\n        const matches = omniMatch.match(baseToken);\n        if (matches instanceof Promise) {\n            return matches.then((resolvedMatches) => {\n                const baseAnalysis = scoring.mostGuessableMatchSequence(baseToken, resolvedMatches);\n                return baseAnalysis.guesses;\n            });\n        }\n        const baseAnalysis = scoring.mostGuessableMatchSequence(baseToken, matches);\n        return baseAnalysis.guesses;\n    }\n}\nexport default MatchRepeat;\n//# sourceMappingURL=matching.js.map", "import { ALL_UPPER, ALL_LOWER, ALL_DIGIT } from '../../data/const';\n/*\n *-------------------------------------------------------------------------------\n * sequences (abcdef) ------------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchSequence {\n    constructor() {\n        this.MAX_DELTA = 5;\n    }\n    // eslint-disable-next-line max-statements\n    match({ password }) {\n        /*\n         * Identifies sequences by looking for repeated differences in unicode codepoint.\n         * this allows skipping, such as 9753, and also matches some extended unicode sequences\n         * such as Greek and Cyrillic alphabets.\n         *\n         * for example, consider the input 'abcdb975zy'\n         *\n         * password: a   b   c   d   b    9   7   5   z   y\n         * index:    0   1   2   3   4    5   6   7   8   9\n         * delta:      1   1   1  -2  -41  -2  -2  69   1\n         *\n         * expected result:\n         * [(i, j, delta), ...] = [(0, 3, 1), (5, 7, -2), (8, 9, 1)]\n         */\n        const result = [];\n        if (password.length === 1) {\n            return [];\n        }\n        let i = 0;\n        let lastDelta = null;\n        const passwordLength = password.length;\n        for (let k = 1; k < passwordLength; k += 1) {\n            const delta = password.charCodeAt(k) - password.charCodeAt(k - 1);\n            if (lastDelta == null) {\n                lastDelta = delta;\n            }\n            if (delta !== lastDelta) {\n                const j = k - 1;\n                this.update({\n                    i,\n                    j,\n                    delta: lastDelta,\n                    password,\n                    result,\n                });\n                i = j;\n                lastDelta = delta;\n            }\n        }\n        this.update({\n            i,\n            j: passwordLength - 1,\n            delta: lastDelta,\n            password,\n            result,\n        });\n        return result;\n    }\n    update({ i, j, delta, password, result }) {\n        if (j - i > 1 || Math.abs(delta) === 1) {\n            const absoluteDelta = Math.abs(delta);\n            if (absoluteDelta > 0 && absoluteDelta <= this.MAX_DELTA) {\n                const token = password.slice(i, +j + 1 || 9e9);\n                const { sequenceName, sequenceSpace } = this.getSequence(token);\n                return result.push({\n                    pattern: 'sequence',\n                    i,\n                    j,\n                    token: password.slice(i, +j + 1 || 9e9),\n                    sequenceName,\n                    sequenceSpace,\n                    ascending: delta > 0,\n                });\n            }\n        }\n        return null;\n    }\n    getSequence(token) {\n        // TODO conservatively stick with roman alphabet size.\n        //  (this could be improved)\n        let sequenceName = 'unicode';\n        let sequenceSpace = 26;\n        if (ALL_LOWER.test(token)) {\n            sequenceName = 'lower';\n            sequenceSpace = 26;\n        }\n        else if (ALL_UPPER.test(token)) {\n            sequenceName = 'upper';\n            sequenceSpace = 26;\n        }\n        else if (ALL_DIGIT.test(token)) {\n            sequenceName = 'digits';\n            sequenceSpace = 10;\n        }\n        return {\n            sequenceName,\n            sequenceSpace,\n        };\n    }\n}\nexport default MatchSequence;\n//# sourceMappingURL=matching.js.map", "import { sorted, extend } from '../../helper';\nimport { zxcvbnOptions } from '../../Options';\n/*\n * ------------------------------------------------------------------------------\n * spatial match (qwerty/dvorak/keypad and so on) -----------------------------------------\n * ------------------------------------------------------------------------------\n */\nclass MatchSpatial {\n    constructor() {\n        this.SHIFTED_RX = /[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?]/;\n    }\n    match({ password }) {\n        const matches = [];\n        Object.keys(zxcvbnOptions.graphs).forEach((graphName) => {\n            const graph = zxcvbnOptions.graphs[graphName];\n            extend(matches, this.helper(password, graph, graphName));\n        });\n        return sorted(matches);\n    }\n    checkIfShifted(graphName, password, index) {\n        if (!graphName.includes('keypad') &&\n            // initial character is shifted\n            this.SHIFTED_RX.test(password.charAt(index))) {\n            return 1;\n        }\n        return 0;\n    }\n    // eslint-disable-next-line complexity, max-statements\n    helper(password, graph, graphName) {\n        let shiftedCount;\n        const matches = [];\n        let i = 0;\n        const passwordLength = password.length;\n        while (i < passwordLength - 1) {\n            let j = i + 1;\n            let lastDirection = null;\n            let turns = 0;\n            shiftedCount = this.checkIfShifted(graphName, password, i);\n            // eslint-disable-next-line no-constant-condition\n            while (true) {\n                const prevChar = password.charAt(j - 1);\n                const adjacents = graph[prevChar] || [];\n                let found = false;\n                let foundDirection = -1;\n                let curDirection = -1;\n                // consider growing pattern by one character if j hasn't gone over the edge.\n                if (j < passwordLength) {\n                    const curChar = password.charAt(j);\n                    const adjacentsLength = adjacents.length;\n                    for (let k = 0; k < adjacentsLength; k += 1) {\n                        const adjacent = adjacents[k];\n                        curDirection += 1;\n                        // eslint-disable-next-line max-depth\n                        if (adjacent) {\n                            const adjacentIndex = adjacent.indexOf(curChar);\n                            // eslint-disable-next-line max-depth\n                            if (adjacentIndex !== -1) {\n                                found = true;\n                                foundDirection = curDirection;\n                                // eslint-disable-next-line max-depth\n                                if (adjacentIndex === 1) {\n                                    // # index 1 in the adjacency means the key is shifted,\n                                    // # 0 means unshifted: A vs a, % vs 5, etc.\n                                    // # for example, 'q' is adjacent to the entry '2@'.\n                                    // # @ is shifted w/ index 1, 2 is unshifted.\n                                    shiftedCount += 1;\n                                }\n                                // eslint-disable-next-line max-depth\n                                if (lastDirection !== foundDirection) {\n                                    // # adding a turn is correct even in the initial\n                                    // case when last_direction is null:\n                                    // # every spatial pattern starts with a turn.\n                                    turns += 1;\n                                    lastDirection = foundDirection;\n                                }\n                                break;\n                            }\n                        }\n                    }\n                }\n                // if the current pattern continued, extend j and try to grow again\n                if (found) {\n                    j += 1;\n                    // otherwise push the pattern discovered so far, if any...\n                }\n                else {\n                    // don't consider length 1 or 2 chains.\n                    if (j - i > 2) {\n                        matches.push({\n                            pattern: 'spatial',\n                            i,\n                            j: j - 1,\n                            token: password.slice(i, j),\n                            graph: graphName,\n                            turns,\n                            shiftedCount,\n                        });\n                    }\n                    // ...and then start a new search for the rest of the password.\n                    i = j;\n                    break;\n                }\n            }\n        }\n        return matches;\n    }\n}\nexport default MatchSpatial;\n//# sourceMappingURL=matching.js.map", "import { SEPERATOR_CHARS } from '../../data/const';\nconst separatorRegex = new RegExp(`[${SEPERATOR_CHARS.join('')}]`);\n/*\n *-------------------------------------------------------------------------------\n * separators (any semi-repeated special character) -----------------------------\n *-------------------------------------------------------------------------------\n */\nclass MatchSeparator {\n    static getMostUsedSeparatorChar(password) {\n        const mostUsedSeperators = [\n            ...password\n                .split('')\n                .filter((c) => separatorRegex.test(c))\n                .reduce((memo, c) => {\n                const m = memo.get(c);\n                if (m) {\n                    memo.set(c, m + 1);\n                }\n                else {\n                    memo.set(c, 1);\n                }\n                return memo;\n            }, new Map())\n                .entries(),\n        ].sort(([_a, a], [_b, b]) => b - a);\n        if (!mostUsedSeperators.length)\n            return undefined;\n        const match = mostUsedSeperators[0];\n        // If the special character is only used once, don't treat it like a separator\n        if (match[1] < 2)\n            return undefined;\n        return match[0];\n    }\n    static getSeparatorRegex(separator) {\n        return new RegExp(`([^${separator}\\n])(${separator})(?!${separator})`, 'g');\n        // negative lookbehind can be added again in a few years when it is more supported by the browsers (currently 2023)\n        // https://github.com/zxcvbn-ts/zxcvbn/issues/202\n        // return new RegExp(`(?<!${separator})(${separator})(?!${separator})`, 'g')\n    }\n    // eslint-disable-next-line max-statements\n    match({ password }) {\n        const result = [];\n        if (password.length === 0)\n            return result;\n        const mostUsedSpecial = MatchSeparator.getMostUsedSeparatorChar(password);\n        if (mostUsedSpecial === undefined)\n            return result;\n        const isSeparator = MatchSeparator.getSeparatorRegex(mostUsedSpecial);\n        // eslint-disable-next-line no-restricted-syntax\n        for (const match of password.matchAll(isSeparator)) {\n            // eslint-disable-next-line no-continue\n            if (match.index === undefined)\n                continue;\n            // add one to the index because we changed the regex from negative lookbehind to something simple.\n            // this simple approach uses the first character before the separater too but we only need the index of the separater\n            // https://github.com/zxcvbn-ts/zxcvbn/issues/202\n            const i = match.index + 1;\n            result.push({\n                pattern: 'separator',\n                token: mostUsedSpecial,\n                i,\n                j: i,\n            });\n        }\n        return result;\n    }\n}\nexport default MatchSeparator;\n//# sourceMappingURL=matching.js.map", "import { extend, sorted } from './helper';\nimport dateMatcher from './matcher/date/matching';\nimport dictionaryMatcher from './matcher/dictionary/matching';\nimport regexMatcher from './matcher/regex/matching';\nimport repeatMatcher from './matcher/repeat/matching';\nimport sequenceMatcher from './matcher/sequence/matching';\nimport spatialMatcher from './matcher/spatial/matching';\nimport separatorMatcher from './matcher/separator/matching';\nimport { zxcvbnOptions } from './Options';\nclass Matching {\n    constructor() {\n        this.matchers = {\n            date: dateMatcher,\n            dictionary: dictionaryMatcher,\n            regex: regexMatcher,\n            // @ts-ignore => TODO resolve this type issue. This is because it is possible to be async\n            repeat: repeatMatcher,\n            sequence: sequenceMatcher,\n            spatial: spatialMatcher,\n            separator: separatorMatcher,\n        };\n    }\n    match(password) {\n        const matches = [];\n        const promises = [];\n        const matchers = [\n            ...Object.keys(this.matchers),\n            ...Object.keys(zxcvbnOptions.matchers),\n        ];\n        matchers.forEach((key) => {\n            if (!this.matchers[key] && !zxcvbnOptions.matchers[key]) {\n                return;\n            }\n            const Matcher = this.matchers[key]\n                ? this.matchers[key]\n                : zxcvbnOptions.matchers[key].Matching;\n            const usedMatcher = new Matcher();\n            const result = usedMatcher.match({\n                password,\n                omniMatch: this,\n            });\n            if (result instanceof Promise) {\n                result.then((response) => {\n                    extend(matches, response);\n                });\n                promises.push(result);\n            }\n            else {\n                extend(matches, result);\n            }\n        });\n        if (promises.length > 0) {\n            return new Promise((resolve, reject) => {\n                Promise.all(promises)\n                    .then(() => {\n                    resolve(sorted(matches));\n                })\n                    .catch((error) => {\n                    reject(error);\n                });\n            });\n        }\n        return sorted(matches);\n    }\n}\nexport default Matching;\n//# sourceMappingURL=Matching.js.map", "import { zxcvbnOptions } from './Options';\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst MONTH = DAY * 31;\nconst YEAR = MONTH * 12;\nconst CENTURY = YEAR * 100;\nconst times = {\n    second: SECOND,\n    minute: MINUTE,\n    hour: HOUR,\n    day: DAY,\n    month: MONTH,\n    year: YEAR,\n    century: CENTURY,\n};\n/*\n * -------------------------------------------------------------------------------\n *  Estimates time for an attacker ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass TimeEstimates {\n    translate(displayStr, value) {\n        let key = displayStr;\n        if (value !== undefined && value !== 1) {\n            key += 's';\n        }\n        const { timeEstimation } = zxcvbnOptions.translations;\n        return timeEstimation[key].replace('{base}', `${value}`);\n    }\n    estimateAttackTimes(guesses) {\n        const crackTimesSeconds = {\n            onlineThrottling100PerHour: guesses / (100 / 3600),\n            onlineNoThrottling10PerSecond: guesses / 10,\n            offlineSlowHashing1e4PerSecond: guesses / 1e4,\n            offlineFastHashing1e10PerSecond: guesses / 1e10,\n        };\n        const crackTimesDisplay = {\n            onlineThrottling100PerHour: '',\n            onlineNoThrottling10PerSecond: '',\n            offlineSlowHashing1e4PerSecond: '',\n            offlineFastHashing1e10PerSecond: '',\n        };\n        Object.keys(crackTimesSeconds).forEach((scenario) => {\n            const seconds = crackTimesSeconds[scenario];\n            crackTimesDisplay[scenario] =\n                this.displayTime(seconds);\n        });\n        return {\n            crackTimesSeconds,\n            crackTimesDisplay,\n            score: this.guessesToScore(guesses),\n        };\n    }\n    guessesToScore(guesses) {\n        const DELTA = 5;\n        if (guesses < 1e3 + DELTA) {\n            // risky password: \"too guessable\"\n            return 0;\n        }\n        if (guesses < 1e6 + DELTA) {\n            // modest protection from throttled online attacks: \"very guessable\"\n            return 1;\n        }\n        if (guesses < 1e8 + DELTA) {\n            // modest protection from unthrottled online attacks: \"somewhat guessable\"\n            return 2;\n        }\n        if (guesses < 1e10 + DELTA) {\n            // modest protection from offline attacks: \"safely unguessable\"\n            // assuming a salted, slow hash function like bcrypt, scrypt, PBKDF2, argon, etc\n            return 3;\n        }\n        // strong protection from offline attacks under same scenario: \"very unguessable\"\n        return 4;\n    }\n    displayTime(seconds) {\n        let displayStr = 'centuries';\n        let base;\n        const timeKeys = Object.keys(times);\n        const foundIndex = timeKeys.findIndex((time) => seconds < times[time]);\n        if (foundIndex > -1) {\n            displayStr = timeKeys[foundIndex - 1];\n            if (foundIndex !== 0) {\n                base = Math.round(seconds / times[displayStr]);\n            }\n            else {\n                displayStr = 'ltSecond';\n            }\n        }\n        return this.translate(displayStr, base);\n    }\n}\nexport default TimeEstimates;\n//# sourceMappingURL=TimeEstimates.js.map", "export default () => {\n    return null;\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default () => {\n    return {\n        warning: zxcvbnOptions.translations.warnings.dates,\n        suggestions: [zxcvbnOptions.translations.suggestions.dates],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nimport { ALL_UPPER_INVERTED, START_UPPER } from '../../data/const';\nconst getDictionaryWarningPassword = (match, isSoleMatch) => {\n    let warning = null;\n    if (isSoleMatch && !match.l33t && !match.reversed) {\n        if (match.rank <= 10) {\n            warning = zxcvbnOptions.translations.warnings.topTen;\n        }\n        else if (match.rank <= 100) {\n            warning = zxcvbnOptions.translations.warnings.topHundred;\n        }\n        else {\n            warning = zxcvbnOptions.translations.warnings.common;\n        }\n    }\n    else if (match.guessesLog10 <= 4) {\n        warning = zxcvbnOptions.translations.warnings.similarToCommon;\n    }\n    return warning;\n};\nconst getDictionaryWarningWikipedia = (match, isSoleMatch) => {\n    let warning = null;\n    if (isSoleMatch) {\n        warning = zxcvbnOptions.translations.warnings.wordByItself;\n    }\n    return warning;\n};\nconst getDictionaryWarningNames = (match, isSoleMatch) => {\n    if (isSoleMatch) {\n        return zxcvbnOptions.translations.warnings.namesByThemselves;\n    }\n    return zxcvbnOptions.translations.warnings.commonNames;\n};\nconst getDictionaryWarning = (match, isSoleMatch) => {\n    let warning = null;\n    const dictName = match.dictionaryName;\n    const isAName = dictName === 'lastnames' || dictName.toLowerCase().includes('firstnames');\n    if (dictName === 'passwords') {\n        warning = getDictionaryWarningPassword(match, isSoleMatch);\n    }\n    else if (dictName.includes('wikipedia')) {\n        warning = getDictionaryWarningWikipedia(match, isSoleMatch);\n    }\n    else if (isAName) {\n        warning = getDictionaryWarningNames(match, isSoleMatch);\n    }\n    else if (dictName === 'userInputs') {\n        warning = zxcvbnOptions.translations.warnings.userInputs;\n    }\n    return warning;\n};\nexport default (match, isSoleMatch) => {\n    const warning = getDictionaryWarning(match, isSoleMatch);\n    const suggestions = [];\n    const word = match.token;\n    if (word.match(START_UPPER)) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.capitalization);\n    }\n    else if (word.match(ALL_UPPER_INVERTED) && word.toLowerCase() !== word) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.allUppercase);\n    }\n    if (match.reversed && match.token.length >= 4) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.reverseWords);\n    }\n    if (match.l33t) {\n        suggestions.push(zxcvbnOptions.translations.suggestions.l33t);\n    }\n    return {\n        warning,\n        suggestions,\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    if (match.regexName === 'recentYear') {\n        return {\n            warning: zxcvbnOptions.translations.warnings.recentYears,\n            suggestions: [\n                zxcvbnOptions.translations.suggestions.recentYears,\n                zxcvbnOptions.translations.suggestions.associatedYears,\n            ],\n        };\n    }\n    return {\n        warning: null,\n        suggestions: [],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    let warning = zxcvbnOptions.translations.warnings.extendedRepeat;\n    if (match.baseToken.length === 1) {\n        warning = zxcvbnOptions.translations.warnings.simpleRepeat;\n    }\n    return {\n        warning,\n        suggestions: [zxcvbnOptions.translations.suggestions.repeated],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default () => {\n    return {\n        warning: zxcvbnOptions.translations.warnings.sequences,\n        suggestions: [zxcvbnOptions.translations.suggestions.sequences],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from '../../Options';\nexport default (match) => {\n    let warning = zxcvbnOptions.translations.warnings.keyPattern;\n    if (match.turns === 1) {\n        warning = zxcvbnOptions.translations.warnings.straightRow;\n    }\n    return {\n        warning,\n        suggestions: [zxcvbnOptions.translations.suggestions.longerKeyboardPattern],\n    };\n};\n//# sourceMappingURL=feedback.js.map", "export default () => {\n    // no suggestions\n    return null;\n};\n//# sourceMappingURL=feedback.js.map", "import { zxcvbnOptions } from './Options';\nimport bruteforceMatcher from './matcher/bruteforce/feedback';\nimport dateMatcher from './matcher/date/feedback';\nimport dictionaryMatcher from './matcher/dictionary/feedback';\nimport regexMatcher from './matcher/regex/feedback';\nimport repeatMatcher from './matcher/repeat/feedback';\nimport sequenceMatcher from './matcher/sequence/feedback';\nimport spatialMatcher from './matcher/spatial/feedback';\nimport separatorMatcher from './matcher/separator/feedback';\nconst defaultFeedback = {\n    warning: null,\n    suggestions: [],\n};\n/*\n * -------------------------------------------------------------------------------\n *  Generate feedback ---------------------------------------------------------------\n * -------------------------------------------------------------------------------\n */\nclass Feedback {\n    constructor() {\n        this.matchers = {\n            bruteforce: bruteforceMatcher,\n            date: dateMatcher,\n            dictionary: dictionaryMatcher,\n            regex: regexMatcher,\n            repeat: repeatMatcher,\n            sequence: sequenceMatcher,\n            spatial: spatialMatcher,\n            separator: separatorMatcher,\n        };\n        this.defaultFeedback = {\n            warning: null,\n            suggestions: [],\n        };\n        this.setDefaultSuggestions();\n    }\n    setDefaultSuggestions() {\n        this.defaultFeedback.suggestions.push(zxcvbnOptions.translations.suggestions.useWords, zxcvbnOptions.translations.suggestions.noNeed);\n    }\n    getFeedback(score, sequence) {\n        if (sequence.length === 0) {\n            return this.defaultFeedback;\n        }\n        if (score > 2) {\n            return defaultFeedback;\n        }\n        const extraFeedback = zxcvbnOptions.translations.suggestions.anotherWord;\n        const longestMatch = this.getLongestMatch(sequence);\n        let feedback = this.getMatchFeedback(longestMatch, sequence.length === 1);\n        if (feedback !== null && feedback !== undefined) {\n            feedback.suggestions.unshift(extraFeedback);\n        }\n        else {\n            feedback = {\n                warning: null,\n                suggestions: [extraFeedback],\n            };\n        }\n        return feedback;\n    }\n    getLongestMatch(sequence) {\n        let longestMatch = sequence[0];\n        const slicedSequence = sequence.slice(1);\n        slicedSequence.forEach((match) => {\n            if (match.token.length > longestMatch.token.length) {\n                longestMatch = match;\n            }\n        });\n        return longestMatch;\n    }\n    getMatchFeedback(match, isSoleMatch) {\n        if (this.matchers[match.pattern]) {\n            return this.matchers[match.pattern](match, isSoleMatch);\n        }\n        if (zxcvbnOptions.matchers[match.pattern] &&\n            'feedback' in zxcvbnOptions.matchers[match.pattern]) {\n            return zxcvbnOptions.matchers[match.pattern].feedback(match, isSoleMatch);\n        }\n        return defaultFeedback;\n    }\n}\nexport default Feedback;\n//# sourceMappingURL=Feedback.js.map", "/**\n * @link https://davidwalsh.name/javascript-debounce-function\n * @param func needs to implement a function which is debounced\n * @param wait how long do you want to wait till the previous declared function is executed\n * @param isImmediate defines if you want to execute the function on the first execution or the last execution inside the time window. `true` for first and `false` for last.\n */\nexport default (func, wait, isImmediate) => {\n    let timeout;\n    return function debounce(...args) {\n        const context = this;\n        const later = () => {\n            timeout = undefined;\n            if (!isImmediate) {\n                func.apply(context, args);\n            }\n        };\n        const shouldCallNow = isImmediate && !timeout;\n        if (timeout !== undefined) {\n            clearTimeout(timeout);\n        }\n        timeout = setTimeout(later, wait);\n        if (shouldCallNow) {\n            return func.apply(context, args);\n        }\n        return undefined;\n    };\n};\n//# sourceMappingURL=debounce.js.map", "import Matching from './Matching';\nimport scoring from './scoring';\nimport TimeEstimates from './TimeEstimates';\nimport Feedback from './Feedback';\nimport { zxcvbnOptions, Options } from './Options';\nimport debounce from './debounce';\nconst time = () => new Date().getTime();\nconst createReturnValue = (resolvedMatches, password, start) => {\n    const feedback = new Feedback();\n    const timeEstimates = new TimeEstimates();\n    const matchSequence = scoring.mostGuessableMatchSequence(password, resolvedMatches);\n    const calcTime = time() - start;\n    const attackTimes = timeEstimates.estimateAttackTimes(matchSequence.guesses);\n    return {\n        calcTime,\n        ...matchSequence,\n        ...attackTimes,\n        feedback: feedback.getFeedback(attackTimes.score, matchSequence.sequence),\n    };\n};\nconst main = (password, userInputs) => {\n    if (userInputs) {\n        zxcvbnOptions.extendUserInputsDictionary(userInputs);\n    }\n    const matching = new Matching();\n    return matching.match(password);\n};\nexport const zxcvbn = (password, userInputs) => {\n    const start = time();\n    const matches = main(password, userInputs);\n    if (matches instanceof Promise) {\n        throw new Error('You are using a Promised matcher, please use `zxcvbnAsync` for it.');\n    }\n    return createReturnValue(matches, password, start);\n};\nexport const zxcvbnAsync = async (password, userInputs) => {\n    const usedPassword = password.substring(0, zxcvbnOptions.maxLength);\n    const start = time();\n    const matches = await main(usedPassword, userInputs);\n    return createReturnValue(matches, usedPassword, start);\n};\nexport * from './types';\nexport { zxcvbnOptions, Options, debounce };\n//# sourceMappingURL=index.js.map"], "mappings": ";;;IACaA,SAASA,CAACC,cAAcC;;EAErCD,aAAaE,KAAKC,MAAMH,cAAcC,IAAI;;AAa7BG,IAAAA,SAAUC,aAAYA,QAAQC,KAAK,CAACC,IAAIC,OAAOD,GAAGE,IAAID,GAAGC,KAAKF,GAAGG,IAAIF,GAAGE,CAAC;AACzEC,IAAAA,wBAAyBC,iBAAgB;AAClD,QAAMC,SAAS,CAAA;AACf,MAAIC,UAAU;AACdF,cAAYG,QAASC,UAAS;AAC1BH,WAAOG,IAAI,IAAIF;AACfA,eAAW;EACf,CAAC;AACD,SAAOD;AACX;;;ACzBA,IAAA,aAAe;EACX,GAAG;;IAEC,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;;;EAET,GAAG;IACC,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;;IAEL,CAAC,GAAG,CAAC;;;EAET,GAAG;IACC,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;;;;EAGT,GAAG;IACC,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;;;EAET,GAAG;IACC,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;;EAAG;AAEhB;;;AC3BO,IAAMI,gBAAgB;AACtB,IAAMC,gBAAgB;AACtB,IAAMC,cAAcC;AACpB,IAAMC,yBAAyB;AAC/B,IAAMC,sCAAsC;AAC5C,IAAMC,mCAAmC;AACzC,IAAMC,kCAAkC;AACxC,IAAMC,iBAAiB;AAEvB,IAAMC,cAAc;AACpB,IAAMC,YAAY;AAElB,IAAMC,YAAY;AAClB,IAAMC,qBAAqB;AAC3B,IAAMC,YAAY;AAClB,IAAMC,qBAAqB;AAC3B,IAAMC,YAAY;AAClB,IAAMC,YAAY;AAClB,IAAMC,iBAAiB;AACvB,IAAMC,YAAY;AAClB,IAAMC,kBAAiB,oBAAIC,KAAI,GAAGC,YAAW;AAC7C,IAAMC,UAAU;EAAEC,YAAY;AAA4B;AAE1D,IAAMC,kBAAkB,CAC3B,KACA,KACA,KACA,KACA,KACA,KACA,MACA,KACA,KACA,GAAG;AAEMC,IAAAA,uBAAuBD,gBAAgBE;;;AC7BpD,IAAMC,YAAN,MAAgB;;;;;;;;;;;;;;;;;;;;;EAqBZC,MAAM;IAAEC;EAAS,GAAG;AAChB,UAAMC,UAAU,CACZ,GAAG,KAAKC,2BAA2BF,QAAQ,GAC3C,GAAG,KAAKG,wBAAwBH,QAAQ,CAAC;AAE7C,UAAMI,kBAAkB,KAAKC,YAAYJ,OAAO;AAChD,WAAOK,OAAOF,eAAe;EACjC;EACAD,wBAAwBH,UAAU;AAC9B,UAAMC,UAAU,CAAA;AAChB,UAAMM,yBAAyB;AAE/B,aAASC,IAAI,GAAGA,KAAKC,KAAKC,IAAIV,SAASW,SAAS,CAAC,GAAGH,KAAK,GAAG;AACxD,eAASI,IAAIJ,IAAI,GAAGI,KAAKJ,IAAI,GAAGI,KAAK,GAAG;AACpC,YAAIA,KAAKZ,SAASW,QAAQ;AACtB;QACJ;AACA,cAAME,QAAQb,SAASc,MAAMN,GAAG,CAACI,IAAI,KAAK,GAAG;AAC7C,cAAMG,aAAaR,uBAAuBS,KAAKH,KAAK;AACpD,YAAIE,cAAc,MAAM;AACpB,gBAAME,MAAM,KAAKC,0BAA0B,CACvCC,SAASJ,WAAW,CAAC,GAAG,EAAE,GAC1BI,SAASJ,WAAW,CAAC,GAAG,EAAE,GAC1BI,SAASJ,WAAW,CAAC,GAAG,EAAE,CAAC,CAC9B;AACD,cAAIE,OAAO,MAAM;AACbhB,oBAAQmB,KAAK;cACTC,SAAS;cACTR;cACAL;cACAI;cACAU,WAAWP,WAAW,CAAC;cACvBQ,MAAMN,IAAIM;cACVC,OAAOP,IAAIO;cACXC,KAAKR,IAAIQ;YACb,CAAC;UACL;QACJ;MACJ;IACJ;AACA,WAAOxB;EACX;;EAEAC,2BAA2BF,UAAU;AACjC,UAAMC,UAAU,CAAA;AAChB,UAAMyB,uBAAuB;AAC7B,UAAMC,SAAUC,eAAcnB,KAAKC,IAAIkB,UAAUL,OAAOM,cAAc;AAEtE,aAASrB,IAAI,GAAGA,KAAKC,KAAKC,IAAIV,SAASW,SAAS,CAAC,GAAGH,KAAK,GAAG;AACxD,eAASI,IAAIJ,IAAI,GAAGI,KAAKJ,IAAI,GAAGI,KAAK,GAAG;AACpC,YAAIA,KAAKZ,SAASW,QAAQ;AACtB;QACJ;AACA,cAAME,QAAQb,SAASc,MAAMN,GAAG,CAACI,IAAI,KAAK,GAAG;AAC7C,YAAIc,qBAAqBV,KAAKH,KAAK,GAAG;AAClC,gBAAMiB,aAAa,CAAA;AACnB,gBAAMC,QAAQlB,MAAMF;AACpB,gBAAMqB,gBAAgBC,YAAYF,KAAK;AACvCC,wBAAcE,QAAQ,CAAC,CAACC,GAAGC,CAAC,MAAM;AAC9B,kBAAMnB,MAAM,KAAKC,0BAA0B,CACvCC,SAASN,MAAMC,MAAM,GAAGqB,CAAC,GAAG,EAAE,GAC9BhB,SAASN,MAAMC,MAAMqB,GAAGC,CAAC,GAAG,EAAE,GAC9BjB,SAASN,MAAMC,MAAMsB,CAAC,GAAG,EAAE,CAAC,CAC/B;AACD,gBAAInB,OAAO,MAAM;AACba,yBAAWV,KAAKH,GAAG;YACvB;UACJ,CAAC;AACD,cAAIa,WAAWnB,SAAS,GAAG;AAUvB,gBAAI0B,gBAAgBP,WAAW,CAAC;AAChC,gBAAIQ,cAAcX,OAAOG,WAAW,CAAC,CAAC;AACtCA,uBAAWhB,MAAM,CAAC,EAAEoB,QAASN,eAAc;AACvC,oBAAMW,YAAWZ,OAAOC,SAAS;AACjC,kBAAIW,YAAWD,aAAa;AACxBD,gCAAgBT;AAChBU,8BAAcC;cAClB;YACJ,CAAC;AACDtC,oBAAQmB,KAAK;cACTC,SAAS;cACTR;cACAL;cACAI;cACAU,WAAW;cACXC,MAAMc,cAAcd;cACpBC,OAAOa,cAAcb;cACrBC,KAAKY,cAAcZ;YACvB,CAAC;UACL;QACJ;MACJ;IACJ;AACA,WAAOxB;EACX;;;;;;;;;;EAUAI,YAAYJ,SAAS;AACjB,WAAOA,QAAQuC,OAAQzC,WAAU;AAC7B,UAAI0C,aAAa;AACjB,YAAMC,gBAAgBzC,QAAQU;AAC9B,eAASgC,IAAI,GAAGA,IAAID,eAAeC,KAAK,GAAG;AACvC,cAAMC,aAAa3C,QAAQ0C,CAAC;AAC5B,YAAI5C,UAAU6C,YAAY;AACtB,cAAIA,WAAWpC,KAAKT,MAAMS,KAAKoC,WAAWhC,KAAKb,MAAMa,GAAG;AACpD6B,yBAAa;AACb;UACJ;QACJ;MACJ;AACA,aAAO,CAACA;IACZ,CAAC;EACL;;;;;;;;;;;;EAYAvB,0BAA0B2B,UAAU;AAChC,QAAIA,SAAS,CAAC,IAAI,MAAMA,SAAS,CAAC,KAAK,GAAG;AACtC,aAAO;IACX;AACA,QAAIC,SAAS;AACb,QAAIC,SAAS;AACb,QAAIC,SAAS;AACb,aAASL,IAAI,GAAGM,OAAOJ,SAASlC,QAAQgC,IAAIM,MAAMN,KAAK,GAAG;AACtD,YAAMO,MAAML,SAASF,CAAC;AACtB,UAAKO,MAAM,MAAMA,MAAMC,iBAAkBD,MAAME,eAAe;AAC1D,eAAO;MACX;AACA,UAAIF,MAAM,IAAI;AACVH,kBAAU;MACd;AACA,UAAIG,MAAM,IAAI;AACVJ,kBAAU;MACd;AACA,UAAII,OAAO,GAAG;AACVF,kBAAU;MACd;IACJ;AACA,QAAID,UAAU,KAAKD,WAAW,KAAKE,UAAU,GAAG;AAC5C,aAAO;IACX;AACA,WAAO,KAAKK,YAAYR,QAAQ;EACpC;;EAEAQ,YAAYR,UAAU;AAElB,UAAMS,qBAAqB;MACvB,CAACT,SAAS,CAAC,GAAGA,SAAS/B,MAAM,GAAG,CAAC,CAAC;MAClC,CAAC+B,SAAS,CAAC,GAAGA,SAAS/B,MAAM,GAAG,CAAC,CAAC;;;AAEtC,UAAMyC,2BAA2BD,mBAAmB3C;AACpD,aAASC,IAAI,GAAGA,IAAI2C,0BAA0B3C,KAAK,GAAG;AAClD,YAAM,CAAC4C,GAAGC,IAAI,IAAIH,mBAAmB1C,CAAC;AACtC,UAAIuC,iBAAiBK,KAAKA,KAAKJ,eAAe;AAC1C,cAAMM,KAAK,KAAKC,sBAAsBF,IAAI;AAC1C,YAAIC,MAAM,MAAM;AACZ,iBAAO;YACHnC,MAAMiC;YACNhC,OAAOkC,GAAGlC;YACVC,KAAKiC,GAAGjC;;QAEhB;AAMA,eAAO;MACX;IACJ;AAGA,aAASU,IAAI,GAAGA,IAAIoB,0BAA0BpB,KAAK,GAAG;AAClD,YAAM,CAACqB,GAAGC,IAAI,IAAIH,mBAAmBnB,CAAC;AACtC,YAAMuB,KAAK,KAAKC,sBAAsBF,IAAI;AAC1C,UAAIC,MAAM,MAAM;AACZ,eAAO;UACHnC,MAAM,KAAKqC,mBAAmBJ,CAAC;UAC/BhC,OAAOkC,GAAGlC;UACVC,KAAKiC,GAAGjC;;MAEhB;IACJ;AACA,WAAO;EACX;EACAkC,sBAAsBd,UAAU;AAC5B,UAAMgB,OAAO,CAAChB,UAAUA,SAAS/B,MAAK,EAAGgD,QAAO,CAAE;AAClD,aAAStD,IAAI,GAAGA,IAAIqD,KAAKlD,QAAQH,KAAK,GAAG;AACrC,YAAMuD,OAAOF,KAAKrD,CAAC;AACnB,YAAMiB,MAAMsC,KAAK,CAAC;AAClB,YAAMvC,QAAQuC,KAAK,CAAC;AACpB,UAAItC,OAAO,KAAKA,OAAO,MAAMD,SAAS,KAAKA,SAAS,IAAI;AACpD,eAAO;UACHC;UACAD;;MAER;IACJ;AACA,WAAO;EACX;EACAoC,mBAAmBrC,MAAM;AACrB,QAAIA,OAAO,IAAI;AACX,aAAOA;IACX;AACA,QAAIA,OAAO,IAAI;AAEX,aAAOA,OAAO;IAClB;AAEA,WAAOA,OAAO;EAClB;AACJ;;;ACtQA,IAAM,MAAM,IAAI,YAAY,KAAO;AACnC,IAAM,WAAW,CAAC,GAAG,MAAM;AACvB,QAAM,IAAI,EAAE;AACZ,QAAM,IAAI,EAAE;AACZ,QAAM,MAAM,KAAM,IAAI;AACtB,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI;AACR,SAAO,KAAK;AACR,QAAI,EAAE,WAAW,CAAC,CAAC,KAAK,KAAK;AAAA,EACjC;AACA,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,QAAI,KAAK,IAAI,EAAE,WAAW,CAAC,CAAC;AAC5B,UAAM,KAAK,KAAK;AAChB,WAAQ,KAAK,MAAM,KAAM;AACzB,UAAM,EAAE,KAAK;AACb,UAAM;AACN,QAAI,KAAK,KAAK;AACV;AAAA,IACJ;AACA,QAAI,KAAK,KAAK;AACV;AAAA,IACJ;AACA,SAAM,MAAM,IAAK;AACjB,SAAM,MAAM,IAAK,EAAE,KAAK;AACxB,UAAM;AAAA,EACV;AACA,MAAI;AACJ,SAAO,KAAK;AACR,QAAI,EAAE,WAAW,CAAC,CAAC,IAAI;AAAA,EAC3B;AACA,SAAO;AACX;AACA,IAAM,UAAU,CAAC,GAAG,MAAM;AACtB,QAAM,IAAI,EAAE;AACZ,QAAM,IAAI,EAAE;AACZ,QAAM,MAAM,CAAC;AACb,QAAM,MAAM,CAAC;AACb,QAAM,QAAQ,KAAK,KAAK,IAAI,EAAE;AAC9B,QAAM,QAAQ,KAAK,KAAK,IAAI,EAAE;AAC9B,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AAAA,EACb;AACA,MAAI,IAAI;AACR,SAAO,IAAI,QAAQ,GAAG,KAAK;AACvB,QAAIyC,MAAK;AACT,QAAIC,MAAK;AACT,UAAMC,SAAQ,IAAI;AAClB,UAAMC,QAAO,KAAK,IAAI,IAAI,CAAC,IAAID;AAC/B,aAAS,IAAIA,QAAO,IAAIC,OAAM,KAAK;AAC/B,UAAI,EAAE,WAAW,CAAC,CAAC,KAAK,KAAK;AAAA,IACjC;AACA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,KAAK,IAAI,EAAE,WAAW,CAAC,CAAC;AAC9B,YAAM,KAAM,IAAK,IAAI,KAAM,CAAC,MAAM,IAAK;AACvC,YAAM,KAAM,IAAK,IAAI,KAAM,CAAC,MAAM,IAAK;AACvC,YAAM,KAAK,KAAKH;AAChB,YAAM,OAAS,KAAK,MAAMC,OAAMA,MAAMA,MAAM,KAAK;AACjD,UAAI,KAAKD,MAAK,EAAE,KAAKC;AACrB,UAAI,KAAKA,MAAK;AACd,UAAK,OAAO,KAAM,IAAI;AAClB,YAAK,IAAI,KAAM,CAAC,KAAK,KAAK;AAAA,MAC9B;AACA,UAAK,OAAO,KAAM,IAAI;AAClB,YAAK,IAAI,KAAM,CAAC,KAAK,KAAK;AAAA,MAC9B;AACA,WAAM,MAAM,IAAK;AACjB,WAAM,MAAM,IAAK;AACjB,MAAAA,MAAK,KAAK,EAAE,KAAK;AACjB,MAAAD,MAAK,KAAK;AAAA,IACd;AACA,aAAS,IAAIE,QAAO,IAAIC,OAAM,KAAK;AAC/B,UAAI,EAAE,WAAW,CAAC,CAAC,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,MAAI,KAAK;AACT,MAAI,KAAK;AACT,QAAM,QAAQ,IAAI;AAClB,QAAM,OAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;AACvC,WAAS,IAAI,OAAO,IAAI,MAAM,KAAK;AAC/B,QAAI,EAAE,WAAW,CAAC,CAAC,KAAK,KAAK;AAAA,EACjC;AACA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAM,KAAK,IAAI,EAAE,WAAW,CAAC,CAAC;AAC9B,UAAM,KAAM,IAAK,IAAI,KAAM,CAAC,MAAM,IAAK;AACvC,UAAM,KAAM,IAAK,IAAI,KAAM,CAAC,MAAM,IAAK;AACvC,UAAM,KAAK,KAAK;AAChB,UAAM,OAAS,KAAK,MAAM,MAAM,KAAM,KAAM,KAAK;AACjD,QAAI,KAAK,KAAK,EAAE,KAAK;AACrB,QAAI,KAAK,KAAK;AACd,aAAU,OAAQ,IAAI,IAAM;AAC5B,aAAU,OAAQ,IAAI,IAAM;AAC5B,QAAK,OAAO,KAAM,IAAI;AAClB,UAAK,IAAI,KAAM,CAAC,KAAK,KAAK;AAAA,IAC9B;AACA,QAAK,OAAO,KAAM,IAAI;AAClB,UAAK,IAAI,KAAM,CAAC,KAAK,KAAK;AAAA,IAC9B;AACA,SAAM,MAAM,IAAK;AACjB,SAAM,MAAM,IAAK;AACjB,SAAK,KAAK,EAAE,KAAK;AACjB,SAAK,KAAK;AAAA,EACd;AACA,WAAS,IAAI,OAAO,IAAI,MAAM,KAAK;AAC/B,QAAI,EAAE,WAAW,CAAC,CAAC,IAAI;AAAA,EAC3B;AACA,SAAO;AACX;AACA,IAAM,WAAW,CAAC,GAAG,MAAM;AACvB,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,UAAM,MAAM;AACZ,QAAI;AACJ,QAAI;AAAA,EACR;AACA,MAAI,EAAE,WAAW,GAAG;AAChB,WAAO,EAAE;AAAA,EACb;AACA,MAAI,EAAE,UAAU,IAAI;AAChB,WAAO,SAAS,GAAG,CAAC;AAAA,EACxB;AACA,SAAO,QAAQ,GAAG,CAAC;AACvB;;;AC3HA,IAAMC,mBAAmBA,CAACC,UAAUC,OAAOC,cAAc;AACrD,QAAMC,oBAAoBH,SAASI,UAAUH,MAAMG;AACnD,QAAMC,gCAAgCL,SAASI,UAAUF;AACzD,QAAMI,0BAA0BH,qBAAqBE;AAErD,SAAOC,0BAA0BC,KAAKC,KAAKR,SAASI,SAAS,CAAC,IAAIF;AACtE;AACMO,IAAAA,0BAA0BA,CAACT,UAAUU,kBAAkBR,cAAc;AACvE,MAAIS,gBAAgB;AACpB,QAAMC,QAAQC,OAAOC,KAAKJ,gBAAgB,EAAEK,KAAMd,WAAU;AACxD,UAAMe,gBAAgBjB,iBAAiBC,UAAUC,OAAOC,SAAS;AACjE,QAAIK,KAAKU,IAAIjB,SAASI,SAASH,MAAMG,MAAM,IAAIY,eAAe;AAC1D,aAAO;IACX;AACA,UAAME,qBAAqBC,SAASnB,UAAUC,KAAK;AACnD,UAAMmB,gBAAgBF,sBAAsBF;AAC5C,QAAII,eAAe;AACfT,sBAAgBO;IACpB;AACA,WAAOE;EACX,CAAC;AACD,MAAIR,OAAO;AACP,WAAO;MACHS,qBAAqBV;MACrBW,0BAA0BV;;EAElC;AACA,SAAO,CAAA;AACX;;;AC7BA,IAAA,YAAe;EACXW,GAAG,CAAC,KAAK,GAAG;EACZC,GAAG,CAAC,GAAG;EACPC,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG;EACtBC,GAAG,CAAC,KAAK,IAAI;EACbC,GAAG,CAAC,GAAG;EACPC,GAAG,CAAC,GAAG;EACPC,GAAG,CAAC,KAAK,KAAK,GAAG;EACjBC,GAAG,CAAC,KAAK,KAAK;EACdC,GAAG,CAAC,KAAK,KAAK,GAAG;EACjBC,GAAG,CAAC,KAAK,IAAI;EACbC,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG;EACtBC,GAAG,CAAC,MAAM,MAAM,MAAM,YAAY;EAClCC,GAAG,CAAC,IAAI;EACRC,GAAG,CAAC,KAAK,IAAI;EACbC,GAAG,CAAC,GAAG;EACPC,GAAG,CAAC,KAAK;EACTC,GAAG,CAAC,KAAK,GAAG;EACZC,GAAG,CAAC,KAAK,GAAG;EACZC,GAAG,CAAC,KAAK,KAAK,GAAG;EACjBC,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,YAAY;EAC9CC,GAAG,CAAC,KAAK,IAAI;EACbC,GAAG,CAAC,GAAG;AACX;;;ACvBA,IAAA,kBAAe;EACXC,UAAU;IACNC,aAAa;IACbC,YAAY;IACZC,cAAc;IACdC,gBAAgB;IAChBC,WAAW;IACXC,aAAa;IACbC,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC,QAAQ;IACRC,iBAAiB;IACjBC,cAAc;IACdC,mBAAmB;IACnBC,aAAa;IACbC,YAAY;IACZC,OAAO;;EAEXC,aAAa;IACTC,MAAM;IACNC,cAAc;IACdC,cAAc;IACdC,gBAAgB;IAChBd,OAAO;IACPD,aAAa;IACbgB,iBAAiB;IACjBjB,WAAW;IACXkB,UAAU;IACVC,uBAAuB;IACvBC,aAAa;IACbC,UAAU;IACVC,QAAQ;IACRX,OAAO;;EAEXY,gBAAgB;IACZC,UAAU;IACVC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,MAAM;IACNC,OAAO;IACPC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,OAAO;IACPC,WAAW;EACf;AACJ;;;ACnDe,IAAMC,WAAN,MAAMA,UAAS;EAC1BC,YAAYC,UAAU,CAAA,GAAI;AACtB,SAAKA,UAAUA;AAEf,SAAKC,WAAW,oBAAIC,IAAG;EAC3B;EACAC,OAAOC,QAAQC,MAAM;AACjB,UAAMC,YAAYF,IAAIG,OAAO,CAAC;AAC9B,QAAI,CAAC,KAAKN,SAASO,IAAIF,SAAS,GAAG;AAC/B,WAAKL,SAASQ,IAAIH,WAAW,IAAIR,UAAS,CAAC,GAAG,KAAKE,SAASM,SAAS,CAAC,CAAC;IAC3E;AACA,QAAII,MAAM,KAAKT,SAASU,IAAIL,SAAS;AACrC,aAASM,IAAI,GAAGA,IAAIR,IAAIS,QAAQD,KAAK,GAAG;AACpC,YAAME,IAAIV,IAAIG,OAAOK,CAAC;AACtB,UAAI,CAACF,IAAIK,SAASD,CAAC,GAAG;AAClBJ,YAAIM,SAASF,CAAC;MAClB;AACAJ,YAAMA,IAAIO,SAASH,CAAC;IACxB;AACAJ,QAAIL,QAAQK,IAAIL,QAAQ,CAAA,GAAIa,OAAOb,IAAI;AACvC,WAAO;EACX;EACAY,SAASE,OAAO;AACZ,WAAO,KAAKlB,SAASU,IAAIQ,KAAK;EAClC;EACAC,aAAa;AACT,WAAO,CAAC,CAAC,KAAKf;EAClB;EACAW,SAASG,OAAO;AACZ,QAAI,CAAC,KAAKJ,SAASI,KAAK,GAAG;AACvB,WAAKlB,SAASQ,IAAIU,OAAO,IAAIrB,UAAS,CAAC,GAAG,KAAKE,SAASmB,KAAK,CAAC,CAAC;IACnE;EACJ;EACAJ,SAASI,OAAO;AACZ,WAAO,KAAKlB,SAASO,IAAIW,KAAK;EAClC;AACJ;;;ACpCA,IAAA,sBAAe,CAACE,YAAWC,YAAY;AACnCC,SAAOC,QAAQH,UAAS,EAAEI,QAAQ,CAAC,CAACC,QAAQC,aAAa,MAAM;AAC3DA,kBAAcF,QAASG,kBAAiB;AACpCN,cAAQO,OAAOD,cAAcF,MAAM;IACvC,CAAC;EACL,CAAC;AACD,SAAOJ;AACX;;;ACFO,IAAMQ,UAAN,MAAc;EACjBC,cAAc;AACV,SAAKC,WAAW,CAAA;AAChB,SAAKC,YAAYA;AACjB,SAAKC,eAAeC,oBAAoBF,WAAW,IAAIG,SAAQ,CAAE;AACjE,SAAKC,aAAa;MACdC,YAAY,CAAA;;AAEhB,SAAKC,qBAAqB,CAAA;AAC1B,SAAKC,gCAAgC,CAAA;AACrC,SAAKC,eAAeC;AACpB,SAAKC,SAAS,CAAA;AACd,SAAKC,yBAAyB;AAC9B,SAAKC,uBAAuB;AAC5B,SAAKC,uBAAuB;AAC5B,SAAKC,YAAY;AACjB,SAAKC,sBAAqB;EAC9B;;EAEAC,WAAWC,UAAU,CAAA,GAAI;AACrB,QAAIA,QAAQjB,WAAW;AACnB,WAAKA,YAAYiB,QAAQjB;AACzB,WAAKC,eAAeC,oBAAoBe,QAAQjB,WAAW,IAAIG,SAAQ,CAAE;IAC7E;AACA,QAAIc,QAAQb,YAAY;AACpB,WAAKA,aAAaa,QAAQb;AAC1B,WAAKW,sBAAqB;IAC9B;AACA,QAAIE,QAAQT,cAAc;AACtB,WAAKU,gBAAgBD,QAAQT,YAAY;IAC7C;AACA,QAAIS,QAAQP,QAAQ;AAChB,WAAKA,SAASO,QAAQP;IAC1B;AACA,QAAIO,QAAQN,2BAA2BQ,QAAW;AAC9C,WAAKR,yBAAyBM,QAAQN;IAC1C;AACA,QAAIM,QAAQL,yBAAyBO,QAAW;AAC5C,WAAKP,uBAAuBK,QAAQL;IACxC;AACA,QAAIK,QAAQJ,yBAAyBM,QAAW;AAC5C,WAAKN,uBAAuBI,QAAQJ;IACxC;AACA,QAAII,QAAQH,cAAcK,QAAW;AACjC,WAAKL,YAAYG,QAAQH;IAC7B;EACJ;EACAI,gBAAgBV,cAAc;AAC1B,QAAI,KAAKY,wBAAwBZ,YAAY,GAAG;AAC5C,WAAKA,eAAeA;IACxB,OACK;AACD,YAAM,IAAIa,MAAM,8CAA8C;IAClE;EACJ;EACAD,wBAAwBZ,cAAc;AAClC,QAAIc,QAAQ;AACZC,WAAOC,KAAKf,eAAe,EAAEgB,QAASC,UAAS;AAC3C,UAAIA,QAAQlB,cAAc;AACtB,cAAMmB,kBAAkBD;AACxBH,eAAOC,KAAKf,gBAAgBkB,eAAe,CAAC,EAAEF,QAASG,SAAQ;AAC3D,cAAI,EAAEA,OAAOpB,aAAamB,eAAe,IAAI;AACzCL,oBAAQ;UACZ;QACJ,CAAC;MACL,OACK;AACDA,gBAAQ;MACZ;IACJ,CAAC;AACD,WAAOA;EACX;EACAP,wBAAwB;AACpB,UAAMT,qBAAqB,CAAA;AAC3B,UAAMuB,gCAAgC,CAAA;AACtCN,WAAOC,KAAK,KAAKpB,UAAU,EAAEqB,QAASK,UAAS;AAC3CxB,yBAAmBwB,IAAI,IAAIC,sBAAsB,KAAK3B,WAAW0B,IAAI,CAAC;AACtED,oCAA8BC,IAAI,IAC9B,KAAKE,iCAAiC,KAAK5B,WAAW0B,IAAI,CAAC;IACnE,CAAC;AACD,SAAKxB,qBAAqBA;AAC1B,SAAKC,gCAAgCsB;EACzC;EACAG,iCAAiCC,MAAM;AACnC,UAAMC,OAAOD,KAAKE,IAAKC,QAAO;AAC1B,UAAI,OAAOA,OAAO,UAAU;AACxB,eAAOA,GAAGC,SAAQ,EAAGC;MACzB;AACA,aAAOF,GAAGE;IACd,CAAC;AAED,QAAIJ,KAAKI,WAAW,GAAG;AACnB,aAAO;IACX;AACA,WAAOJ,KAAKK,OAAO,CAACC,GAAGC,MAAMC,KAAKC,IAAIH,GAAGC,CAAC,GAAG,SAAS;EAC1D;EACAG,+BAA+BX,MAAM;AACjC,UAAMY,kBAAkB,CAAA;AACxBZ,SAAKR,QAASqB,WAAU;AACpB,YAAMC,YAAY,OAAOD;AACzB,UAAIC,cAAc,YACdA,cAAc,YACdA,cAAc,WAAW;AACzBF,wBAAgBG,KAAKF,MAAMT,SAAQ,EAAGY,YAAW,CAAE;MACvD;IACJ,CAAC;AACD,WAAOlB,sBAAsBc,eAAe;EAChD;EACAK,2BAA2B9C,YAAY;AACnC,QAAI,CAAC,KAAKA,WAAWC,YAAY;AAC7B,WAAKD,WAAWC,aAAa,CAAA;IACjC;AACA,UAAM8C,UAAU,CAAC,GAAG,KAAK/C,WAAWC,YAAY,GAAGD,UAAU;AAC7D,SAAKE,mBAAmBD,aACpB,KAAKuC,+BAA+BO,OAAO;AAC/C,SAAK5C,8BAA8BF,aAC/B,KAAK2B,iCAAiCmB,OAAO;EACrD;EACAC,WAAWtB,MAAMuB,SAAS;AACtB,QAAI,KAAKtD,SAAS+B,IAAI,GAAG;AACrBwB,cAAQC,KAAM,WAAUzB,IAAK,iBAAgB;IACjD,OACK;AACD,WAAK/B,SAAS+B,IAAI,IAAIuB;IAC1B;EACJ;AACJ;IACaG,gBAAgB,IAAI3D,QAAO;;;AC/HxC,IAAM4D,eAAN,MAAmB;EACfC,YAAYC,cAAc;AACtB,SAAKA,eAAeA;EACxB;EACAC,MAAM;IAAEC;EAAS,GAAG;AAChB,UAAMC,mBAAmBD,SAASE,MAAM,EAAE,EAAEC,QAAO,EAAGC,KAAK,EAAE;AAC7D,WAAO,KAAKN,aAAa;MACrBE,UAAUC;IACd,CAAC,EAAEI,IAAKN,YAAW;MACf,GAAGA;MACHO,OAAOP,MAAMO,MAAMJ,MAAM,EAAE,EAAEC,QAAO,EAAGC,KAAK,EAAE;MAC9CG,UAAU;;MAEVC,GAAGR,SAASS,SAAS,IAAIV,MAAMW;MAC/BA,GAAGV,SAASS,SAAS,IAAIV,MAAMS;IACnC,EAAE;EACN;AACJ;;;ACtBA,IAAMG,iBAAN,MAAqB;EACjBC,YAAY;IAAEC;IAAQC;IAAOC;EAAS,GAAG;AACrC,SAAKC,SAAS,CAAA;AACd,SAAKC,iBAAiB,CAAA;AACtB,SAAKJ,SAASA;AACd,SAAKC,QAAQA;AACb,SAAKC,WAAWA;EACpB;EACAG,0BAA0BC,OAAO;AAC7B,UAAMC,QAAQ,CAAA;AACd,QAAIC,MAAM,KAAKN;AACf,aAASO,IAAIH,OAAOG,IAAI,KAAKT,OAAOU,QAAQD,KAAK,GAAG;AAChD,YAAME,YAAY,KAAKX,OAAOY,OAAOH,CAAC;AACtCD,YAAMA,IAAIK,SAASF,SAAS;AAC5B,UAAI,CAACH,KAAK;AACN;MACJ;AACAD,YAAMO,KAAKN,GAAG;IAClB;AACA,WAAOD;EACX;;EAEAQ,OAAO;IAAEC;IAAaC;IAAWX;IAAOY;IAAUC;IAASC;IAAeC;EAAqB,GAAG;AAC9F,QAAI,KAAKjB,eAAeM,UAAU,KAAKT,OAAO;AAC1C;IACJ;AACA,QAAIK,UAAU,KAAKN,OAAOU,QAAQ;AAC9B,UAAIM,gBAAgBC,WAAW;AAC3B,aAAKb,eAAeU,KAAK;UAAEQ,UAAU,KAAKnB,OAAOoB,KAAK,EAAE;UAAGJ;QAAQ,CAAC;MACxE;AACA;IACJ;AAEA,UAAMZ,QAAQ,CAAC,GAAG,KAAKF,0BAA0BC,KAAK,CAAC;AACvD,QAAIkB,UAAU;AAEd,aAASf,IAAIH,QAAQC,MAAMG,SAAS,GAAGD,KAAKH,OAAOG,KAAK,GAAG;AACvD,YAAMD,MAAMD,MAAME,IAAIH,KAAK;AAC3B,UAAIE,IAAIiB,WAAU,GAAI;AAIlB,YAAIL,kBAAkBZ,IAAIkB,QAAQH,KAAK,EAAE,KACrCF,uBAAuB,GAAG;AAE1B;QACJ;AACAG,kBAAU;AACV,cAAMG,OAAOnB,IAAImB;AAEjB,mBAAWC,OAAOD,MAAM;AACpB,eAAKxB,OAAOW,KAAKc,GAAG;AACpB,gBAAMC,UAAUV,QAAQW,OAAO;YAC3BrB,GAAGS;YACHa,QAAQH;YACRI,cAAcxB,IAAIkB,QAAQH,KAAK,EAAE;UACrC,CAAC;AAED,eAAKR,OAAO;YACRC;YACAC;YACAX,OAAOG,IAAI;YACXS,UAAUA,WAAWU,IAAIlB;YACzBS,SAASU;YACTT,eAAeZ,IAAIkB,QAAQH,KAAK,EAAE;YAClCF,qBAAqBD,kBAAkBZ,IAAIkB,QAAQH,KAAK,EAAE,IACpDF,sBAAsB,IACtB;UACV,CAAC;AAED,eAAKlB,OAAO8B,IAAG;AACf,cAAI,KAAK7B,eAAeM,UAAU,KAAKT,OAAO;AAC1C;UACJ;QACJ;MACJ;IACJ;AAGA,QAAI,CAACe,eAAe,CAACQ,SAAS;AAC1B,YAAMU,YAAY,KAAKlC,OAAOY,OAAON,KAAK;AAC1C,WAAKH,OAAOW,KAAKoB,SAAS;AAC1B,WAAKnB,OAAO;QACRC;QACAC,WAAWA,aAAa,CAACO;QACzBlB,OAAOA,QAAQ;QACfY,UAAUA,WAAW;QACrBC;QACAC;QACAC;MACJ,CAAC;AACD,WAAKlB,OAAO8B,IAAG;IACnB;EACJ;EACAE,SAAS;AAEL,SAAKpB,OAAO;MACRC,aAAa;MACbC,WAAW;MACXX,OAAO;MACPY,UAAU;MACVC,SAAS,CAAA;MACTC,eAAegB;MACff,qBAAqB;IACzB,CAAC;AAED,SAAKN,OAAO;MACRC,aAAa;MACbC,WAAW;MACXX,OAAO;MACPY,UAAU;MACVC,SAAS,CAAA;MACTC,eAAegB;MACff,qBAAqB;IACzB,CAAC;AACD,WAAO,KAAKjB;EAChB;AACJ;AACMiC,IAAAA,oBAAoBA,CAACf,UAAUrB,OAAOC,aAAa;AACrD,QAAMa,SAAS,IAAIjB,eAAe;IAC9BE,QAAQsB;IACRrB;IACAC;EACJ,CAAC;AACD,SAAOa,OAAOoB,OAAM;AACxB;;;AC3HA,IAAMG,YAAYA,CAACC,kBAAkBC,GAAGC,MAAM;AAC1C,QAAMC,kBAAkBH,iBAAiBI,QAAQC,OAAQD,aAAY;AACjE,WAAOA,QAAQH,IAAIA;EACvB,CAAC;AACD,QAAMK,YAAYH,gBAAgBI,OAAO,CAACC,OAAOC,WAAW;AACxD,WAAOD,QAAQC,OAAOC,OAAOC,SAASF,OAAOG,aAAaD;KAC3DV,CAAC;AACJ,QAAMY,cAAcb,iBAAiBI,QAAQC,OAAQD,aAAY;AAC7D,WAAOA,QAAQH,KAAKA,KAAKG,QAAQH,KAAKC;EAC1C,CAAC;AACD,QAAMY,YAAYD,YAAYN,OAAO,CAACC,OAAOC,WAAW;AACpD,WAAOD,QAAQC,OAAOC,OAAOC,SAASF,OAAOG,aAAaD;EAC9D,GAAGT,IAAID,IAAIK,SAAS;AACpB,QAAMS,WAAW,CAAA;AACjB,QAAMC,aAAa,CAAA;AACnBH,cAAYI,QAAST,WAAU;AAC3B,UAAMU,gBAAgBH,SAASI,UAAWC,OAAM;AAC5C,aAAOA,EAAEV,WAAWF,MAAME,UAAUU,EAAER,iBAAiBJ,MAAMI;IACjE,CAAC;AACD,QAAIM,gBAAgB,GAAG;AACnBH,eAASM,KAAK;QACVX,QAAQF,MAAME;QACdE,cAAcJ,MAAMI;MACxB,CAAC;AACDI,iBAAWK,KAAM,GAAEb,MAAMI,YAAa,OAAMJ,MAAME,MAAO,EAAC;IAC9D;EACJ,CAAC;AACD,SAAO;IACHT,GAAGK;IACHJ,GAAGY;IACHQ,MAAMP;IACNC,YAAYA,WAAWO,KAAK,IAAI;;AAExC;AAMA,IAAMC,YAAN,MAAgB;EACZC,YAAYC,cAAc;AACtB,SAAKA,eAAeA;EACxB;EACAC,kBAAkBC,SAASC,UAAU;AACjC,WAAOD,QAAQE,KAAMC,eAAc;AAC/B,aAAOC,OAAOC,QAAQF,SAAS,EAAEG,MAAM,CAAC,CAACC,KAAK3B,KAAK,MAAM;AACrD,eAAO2B,QAAQ,UAAU3B,UAAUqB,SAASM,GAAG;MACnD,CAAC;IACL,CAAC;EACL;EACAC,MAAM;IAAEC;EAAS,GAAG;AAChB,UAAMT,UAAU,CAAA;AAChB,UAAMU,kBAAkBC,kBAAkBF,UAAUG,cAAcC,sBAAsBD,cAAcE,YAAY;AAClH,QAAIC,eAAe;AACnB,QAAIC,qBAAqB;AACzBN,oBAAgBrB,QAAS4B,oBAAmB;AACxC,UAAIF,cAAc;AACd;MACJ;AACA,YAAMG,oBAAoB,KAAKpB,aAAa;QACxCW,UAAUQ,eAAeR;QACzBU,gBAAgBH;MACpB,CAAC;AAEDA,2BAAqB;AACrBE,wBAAkB7B,QAASmB,WAAU;AACjC,YAAI,CAACO,cAAc;AACfA,yBAAeP,MAAMnC,MAAM,KAAKmC,MAAMlC,MAAMmC,SAAS1B,SAAS;QAClE;AACA,cAAMqC,SAASjD,UAAU8C,gBAAgBT,MAAMnC,GAAGmC,MAAMlC,CAAC;AACzD,cAAM+C,QAAQZ,SAASa,MAAMF,OAAO/C,GAAG,CAAC+C,OAAO9C,IAAI,KAAK,GAAG;AAC3D,cAAM2B,WAAW;UACb,GAAGO;UACHe,MAAM;UACNF;UACA,GAAGD;;AAEP,cAAMI,kBAAkB,KAAKzB,kBAAkBC,SAASC,QAAQ;AAEhE,YAAIoB,MAAMI,YAAW,MAAOjB,MAAMkB,eAAe,CAACF,iBAAiB;AAC/DxB,kBAAQP,KAAKQ,QAAQ;QACzB;MACJ,CAAC;IACL,CAAC;AAID,WAAOD,QAAQvB,OAAQ+B,WAAUA,MAAMa,MAAMtC,SAAS,CAAC;EAC3D;AACJ;;;ACtFA,IAAM4C,kBAAN,MAAsB;EAClBC,cAAc;AACV,SAAKC,OAAO,IAAIC,UAAK,KAAKC,YAAY;AACtC,SAAKC,UAAU,IAAIC,aAAQ,KAAKF,YAAY;EAChD;EACAG,MAAM;IAAEC;EAAS,GAAG;AAChB,UAAMC,UAAU,CACZ,GAAG,KAAKL,aAAa;MACjBI;KACH,GACD,GAAG,KAAKH,QAAQE,MAAM;MAAEC;KAAU,GAClC,GAAG,KAAKN,KAAKK,MAAM;MAAEC;IAAS,CAAC,CAAC;AAEpC,WAAOE,OAAOD,OAAO;EACzB;EACAL,aAAa;IAAEI;IAAUG,iBAAiB;EAAK,GAAG;AAC9C,UAAMF,UAAU,CAAA;AAChB,UAAMG,iBAAiBJ,SAASK;AAChC,UAAMC,gBAAgBN,SAASO,YAAW;AAE1CC,WAAOC,KAAKC,cAAcC,kBAAkB,EAAEC,QAASC,oBAAmB;AACtE,YAAMC,aAAaJ,cAAcC,mBAAmBE,cAAc;AAClE,YAAME,4BAA4BL,cAAcM,8BAA8BH,cAAc;AAC5F,YAAMI,cAAcC,KAAKC,IAAIJ,2BAA2BX,cAAc;AACtE,eAASgB,IAAI,GAAGA,IAAIhB,gBAAgBgB,KAAK,GAAG;AACxC,cAAMC,YAAYH,KAAKC,IAAIC,IAAIH,aAAab,cAAc;AAC1D,iBAASkB,IAAIF,GAAGE,IAAID,WAAWC,KAAK,GAAG;AACnC,gBAAMC,eAAejB,cAAckB,MAAMJ,GAAG,CAACE,IAAI,KAAK,GAAG;AACzD,gBAAMG,iBAAiBF,gBAAgBT;AACvC,cAAIY,2BAA2B,CAAA;AAG/B,gBAAMC,iBAAiBP,MAAM,KAAKE,MAAMlB,iBAAiB;AACzD,cAAIM,cAAckB,0BACdD,kBACA,CAACF,kBACDtB,gBAAgB;AAChBuB,uCAA2BG,wBAAwBN,cAAcT,YAAYJ,cAAcoB,oBAAoB;UACnH;AACA,gBAAMC,qBAAqBvB,OAAOC,KAAKiB,wBAAwB,EAAErB,WAAW;AAC5E,cAAIoB,kBAAkBM,oBAAoB;AACtC,kBAAMC,mBAAmBD,qBACnBL,yBAAyBO,2BACzBV;AACN,kBAAMW,OAAOpB,WAAWkB,gBAAgB;AACxC/B,oBAAQkC,KAAK;cACTC,SAAS;cACThB;cACAE;cACAe,OAAOrC,SAASwB,MAAMJ,GAAG,CAACE,IAAI,KAAK,GAAG;cACtCgB,aAAaf;cACbW;cACArB;cACA0B,UAAU;cACV7C,MAAM;cACN,GAAGgC;YACP,CAAC;UACL;QACJ;MACJ;IACJ,CAAC;AACD,WAAOzB;EACX;AACJ;;;AC7DA,IAAMuC,aAAN,MAAiB;EACbC,MAAM;IAAEC;IAAUC,UAAUC;EAAQ,GAAG;AACnC,UAAMC,UAAU,CAAA;AAChBC,WAAOC,KAAKJ,OAAO,EAAEK,QAASC,UAAS;AACnC,YAAMC,QAAQP,QAAQM,IAAI;AAC1BC,YAAMC,YAAY;AAClB,UAAIC;AAEJ,aAAQA,aAAaF,MAAMG,KAAKX,QAAQ,GAAI;AACxC,YAAIU,YAAY;AACZ,gBAAME,QAAQF,WAAW,CAAC;AAC1BP,kBAAQU,KAAK;YACTC,SAAS;YACTF;YACAG,GAAGL,WAAWM;YACdC,GAAGP,WAAWM,QAAQN,WAAW,CAAC,EAAEQ,SAAS;YAC7CC,WAAWZ;YACXG;UACJ,CAAC;QACL;MACJ;IACJ,CAAC;AACD,WAAOU,OAAOjB,OAAO;EACzB;AACJ;;;AC/BA,IAAA,QAAe;;;EAGXkB,IAAIC,GAAGC,GAAG;AACN,QAAIC,QAAQF;AACZ,QAAIC,IAAIC,OAAO;AACX,aAAO;IACX;AACA,QAAID,MAAM,GAAG;AACT,aAAO;IACX;AACA,QAAIE,QAAQ;AACZ,aAASC,IAAI,GAAGA,KAAKH,GAAGG,KAAK,GAAG;AAC5BD,eAASD;AACTC,eAASC;AACTF,eAAS;IACb;AACA,WAAOC;;EAEXE,MAAML,GAAG;AACL,QAAIA,MAAM,EACN,QAAO;AACX,WAAOM,KAAKC,IAAIP,CAAC,IAAIM,KAAKC,IAAI,EAAE;;EAEpCC,KAAKR,GAAG;AACJ,WAAOM,KAAKC,IAAIP,CAAC,IAAIM,KAAKC,IAAI,CAAC;;EAEnCE,UAAUC,KAAK;AACX,QAAIC,OAAO;AACX,aAASP,IAAI,GAAGA,KAAKM,KAAKN,KAAK,EAC3BO,SAAQP;AACZ,WAAOO;EACX;AACJ;;;AChCA,IAAA,oBAAe,CAAC;EAAEC;AAAM,MAAM;AAC1B,MAAIC,UAAUC,0BAA0BF,MAAMG;AAC9C,MAAIF,YAAYG,OAAOC,mBAAmB;AACtCJ,cAAUG,OAAOE;EACrB;AACA,MAAIC;AAGJ,MAAIP,MAAMG,WAAW,GAAG;AACpBI,iBAAaC,mCAAmC;EACpD,OACK;AACDD,iBAAaE,kCAAkC;EACnD;AACA,SAAOC,KAAKC,IAAIV,SAASM,UAAU;AACvC;;;ACfA,IAAA,cAAe,CAAC;EAAEK;EAAMC;AAAU,MAAM;AAEpC,QAAMC,YAAYC,KAAKC,IAAID,KAAKE,IAAIL,OAAOM,cAAc,GAAGC,cAAc;AAC1E,MAAIC,UAAUN,YAAY;AAE1B,MAAID,WAAW;AACXO,eAAW;EACf;AACA,SAAOA;AACX;;;ACRA,IAAMC,gBAAiBC,iBAAgB;AACnC,QAAMC,YAAYD,YAAYE,MAAM,EAAE;AACtC,QAAMC,iBAAiBF,UAAUG,OAAQC,UAASA,KAAKC,MAAMC,SAAS,CAAC,EAAEC;AACzE,QAAMC,iBAAiBR,UAAUG,OAAQC,UAASA,KAAKC,MAAMI,SAAS,CAAC,EAAEF;AACzE,MAAIG,aAAa;AACjB,QAAMC,kBAAkBC,KAAKC,IAAIX,gBAAgBM,cAAc;AAC/D,WAASM,IAAI,GAAGA,KAAKH,iBAAiBG,KAAK,GAAG;AAC1CJ,kBAAcK,MAAMC,IAAId,iBAAiBM,gBAAgBM,CAAC;EAC9D;AACA,SAAOJ;AACX;AACA,IAAA,mBAAgBO,UAAS;AAErB,QAAMlB,cAAckB,KAAKC,QAAQC,gBAAgB,EAAE;AACnD,MAAIpB,YAAYM,MAAMe,kBAAkB,KACpCrB,YAAYsB,YAAW,MAAOtB,aAAa;AAC3C,WAAO;EACX;AAIA,QAAMuB,cAAc,CAACC,aAAaC,WAAWC,kBAAkB;AAC/D,QAAMC,oBAAoBJ,YAAYf;AACtC,WAASO,IAAI,GAAGA,IAAIY,mBAAmBZ,KAAK,GAAG;AAC3C,UAAMa,QAAQL,YAAYR,CAAC;AAC3B,QAAIf,YAAYM,MAAMsB,KAAK,GAAG;AAC1B,aAAO;IACX;EACJ;AAIA,SAAO7B,cAAcC,WAAW;AACpC;;;AClCA,IAAM6B,iBAAiBA,CAACC,QAAQC,cAAc;AAC1C,MAAIC,QAAQ;AACZ,MAAIC,MAAMH,OAAOI,QAAQH,SAAS;AAClC,SAAOE,OAAO,GAAG;AACbD,aAAS;AACTC,UAAMH,OAAOI,QAAQH,WAAWE,MAAMF,UAAUI,MAAM;EAC1D;AACA,SAAOH;AACX;AACA,IAAMI,YAAYA,CAAC;EAAEC;EAAKC;AAAM,MAAM;AAElC,QAAMC,aAAaD,MAAME,YAAW;AAEpC,QAAMC,cAAcZ,eAAeU,YAAYF,IAAIK,YAAY;AAE/D,QAAMC,gBAAgBd,eAAeU,YAAYF,IAAIO,MAAM;AAC3D,SAAO;IACHH;IACAE;;AAER;AACA,IAAA,cAAe,CAAC;EAAEE;EAAMC;EAAMR;AAAM,MAAM;AACtC,MAAI,CAACO,MAAM;AACP,WAAO;EACX;AACA,MAAIE,aAAa;AACjBD,OAAKE,QAASX,SAAQ;AAClB,UAAM;MAAEI;MAAaE;QAAkBP,UAAU;MAAEC;MAAKC;IAAM,CAAC;AAC/D,QAAIG,gBAAgB,KAAKE,kBAAkB,GAAG;AAI1CI,oBAAc;IAClB,OACK;AAGD,YAAME,IAAIC,KAAKC,IAAIR,eAAeF,WAAW;AAC7C,UAAIW,gBAAgB;AACpB,eAASC,IAAI,GAAGA,KAAKJ,GAAGI,KAAK,GAAG;AAC5BD,yBAAiBE,MAAMC,IAAIZ,gBAAgBF,aAAaY,CAAC;MAC7D;AACAN,oBAAcK;IAClB;EACJ,CAAC;AACD,SAAOL;AACX;;;AC7CA,IAAA,oBAAe,CAAC;EAAES;EAAMC;EAAUC;EAAMC;EAAMC;EAAOC;AAAgB,MAAM;AACvE,QAAMC,cAAcN;AACpB,QAAMO,sBAAsBC,iBAAiBJ,KAAK;AAClD,QAAMK,iBAAiBC,YAAY;IAAER;IAAMC;IAAMC;EAAM,CAAC;AACxD,QAAMO,qBAAsBV,YAAY,KAAM;AAC9C,MAAIW;AACJ,MAAIP,mBAAmB,YAAY;AAG/BO,kBAAc,KAAK,IAAI;EAC3B,OACK;AACDA,kBACIN,cAAcC,sBAAsBE,iBAAiBE;EAC7D;AACA,SAAO;IACHL;IACAC;IACAE;IACAG;;AAER;;;ACtBA,IAAA,eAAe,CAAC;EAAEC;EAAWC;EAAYC;AAAO,MAAM;AAClD,QAAMC,iBAAiB;IACnBC,YAAY;IACZC,YAAY;IACZC,OAAO;IACPC,cAAc;IACdC,QAAQ;IACRC,SAAS;;AAEb,MAAIT,aAAaG,gBAAgB;AAC7B,WAAQA,eAAeH,SAAS,KAAKE,MAAMQ;EAC/C;AAGA,UAAQV,WAAS;IACb,KAAK;AAGD,aAAOW,KAAKC,IAAID,KAAKE,IAAIC,SAASb,WAAW,CAAC,GAAG,EAAE,IAAIc,cAAc,GAAGC,cAAc;EAC9F;AACA,SAAO;AACX;;;ACtBA,IAAA,gBAAe,CAAC;EAAEC;EAAaC;AAAY,MAAMD,cAAcC;;;ACA/D,IAAA,kBAAe,CAAC;EAAEC;EAAOC;AAAU,MAAM;AACrC,QAAMC,WAAWF,MAAMG,OAAO,CAAC;AAC/B,MAAIC,cAAc;AAClB,QAAMC,iBAAiB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEzD,MAAIA,eAAeC,SAASJ,QAAQ,GAAG;AACnCE,kBAAc;aAETF,SAASK,MAAM,IAAI,GAAG;AAC3BH,kBAAc;EAClB,OACK;AAGDA,kBAAc;EAClB;AAGA,MAAI,CAACH,WAAW;AACZG,mBAAe;EACnB;AACA,SAAOA,cAAcJ,MAAMQ;AAC/B;;;ACpBA,IAAMC,oBAAqBC,WAAU;AACjC,MAAIC,UAAU;AACdC,SAAOC,KAAKH,KAAK,EAAEI,QAASC,SAAQ;AAChC,UAAMC,YAAYN,MAAMK,GAAG;AAC3BJ,eAAWK,UAAUC,OAAQC,WAAU,CAAC,CAACA,KAAK,EAAEC;EACpD,CAAC;AACDR,aAAWC,OAAOQ,QAAQV,KAAK,EAAES;AACjC,SAAOR;AACX;AACA,IAAMU,2BAA2BA,CAAC;EAAEC;EAAOZ;EAAOa;AAAO,MAAM;AAC3D,QAAMC,mBAAmBZ,OAAOC,KAAKY,cAAcC,OAAOhB,KAAK,CAAC,EAAES;AAClE,QAAMQ,gBAAgBlB,kBAAkBgB,cAAcC,OAAOhB,KAAK,CAAC;AACnE,MAAIkB,UAAU;AACd,QAAMC,cAAcP,MAAMH;AAE1B,WAASW,IAAI,GAAGA,KAAKD,aAAaC,KAAK,GAAG;AACtC,UAAMC,gBAAgBC,KAAKC,IAAIV,OAAOO,IAAI,CAAC;AAC3C,aAASI,IAAI,GAAGA,KAAKH,eAAeG,KAAK,GAAG;AACxCN,iBAAWO,MAAMC,IAAIN,IAAI,GAAGI,IAAI,CAAC,IAAIV,mBAAmBG,iBAAiBO;IAC7E;EACJ;AACA,SAAON;AACX;AACA,IAAA,iBAAe,CAAC;EAAElB;EAAOY;EAAOe;EAAcd;AAAO,MAAM;AACvD,MAAIK,UAAUP,yBAAyB;IAAEC;IAAOZ;IAAOa;EAAM,CAAC;AAG9D,MAAIc,cAAc;AACd,UAAMC,iBAAiBhB,MAAMH,SAASkB;AACtC,QAAIA,iBAAiB,KAAKC,mBAAmB,GAAG;AAC5CV,iBAAW;IACf,OACK;AACD,UAAIW,oBAAoB;AACxB,eAAST,IAAI,GAAGA,KAAKE,KAAKC,IAAII,cAAcC,cAAc,GAAGR,KAAK,GAAG;AACjES,6BAAqBJ,MAAMC,IAAIC,eAAeC,gBAAgBR,CAAC;MACnE;AACAF,iBAAWW;IACf;EACJ;AACA,SAAOP,KAAKQ,MAAMZ,OAAO;AAC7B;;;AC1CA,IAAA,mBAAe,MAAM;AACjB,SAAOa;AACX;;;ACQA,IAAMC,gBAAgBA,CAACC,OAAOC,aAAa;AACvC,MAAIC,aAAa;AACjB,MAAIF,MAAMG,MAAMC,SAASH,SAASG,QAAQ;AACtC,QAAIJ,MAAMG,MAAMC,WAAW,GAAG;AAC1BF,mBAAaG;IACjB,OACK;AACDH,mBAAaI;IACjB;EACJ;AACA,SAAOJ;AACX;AACA,IAAMK,WAAW;EACbC,YAAYC;EACZC,MAAMC;EACNC,YAAYC;EACZC,OAAOC;EACPC,QAAQC;EACRC,UAAUC;EACVC,SAASC;EACTC,WAAWC;AACf;AACA,IAAMC,aAAaA,CAACC,MAAMzB,UAAU;AAChC,MAAIO,SAASkB,IAAI,GAAG;AAChB,WAAOlB,SAASkB,IAAI,EAAEzB,KAAK;EAC/B;AACA,MAAI0B,cAAcnB,SAASkB,IAAI,KAC3B,aAAaC,cAAcnB,SAASkB,IAAI,GAAG;AAC3C,WAAOC,cAAcnB,SAASkB,IAAI,EAAEE,QAAQ3B,KAAK;EACrD;AACA,SAAO;AACX;AAKA,IAAA,kBAAe,CAACA,OAAOC,aAAa;AAChC,QAAM2B,YAAY,CAAA;AAElB,MAAI,aAAa5B,SAASA,MAAM6B,WAAW,MAAM;AAC7C,WAAO7B;EACX;AACA,QAAME,aAAaH,cAAcC,OAAOC,QAAQ;AAChD,QAAM6B,mBAAmBN,WAAWxB,MAAM+B,SAAS/B,KAAK;AACxD,MAAI6B,UAAU;AACd,MAAI,OAAOC,qBAAqB,UAAU;AACtCD,cAAUC;EACd,WACS9B,MAAM+B,YAAY,cAAc;AACrCF,cAAUC,iBAAiBE;AAC3BJ,cAAUK,cAAcH,iBAAiBG;AACzCL,cAAUM,sBAAsBJ,iBAAiBI;AACjDN,cAAUO,iBAAiBL,iBAAiBK;EAChD;AACA,QAAMC,eAAeC,KAAKC,IAAIT,SAAS3B,UAAU;AACjD,SAAO;IACH,GAAGF;IACH,GAAG4B;IACHC,SAASO;IACTG,cAAcC,MAAMC,MAAML,YAAY;;AAE9C;;;ACrEA,IAAMM,gBAAgB;EAClBC,UAAU;EACVC,SAAS,CAAA;EACTC,iBAAiB;EACjBC,gBAAgBC;EAChBC,UAAUC,MAAMC,WAAW;AACvB,UAAMC,SAAS,CAAA;AACf,aAASC,IAAI,GAAGA,IAAIH,MAAMG,KAAK,GAAG;AAC9B,UAAIC,QAAQ,CAAA;AACZ,UAAIH,cAAc,UAAU;AACxBG,gBAAQ,CAAA;MACZ;AACAF,aAAOG,KAAKD,KAAK;IACrB;AACA,WAAOF;;;EAGXI,oBAAoBH,GAAGI,GAAG;AACtB,WAAO;MACHC,SAAS;MACTC,OAAO,KAAKf,SAASgB,MAAMP,GAAG,CAACI,IAAI,KAAK,GAAG;MAC3CJ;MACAI;;;;;;EAMRI,OAAOC,OAAOC,gBAAgB;AAC1B,UAAMC,IAAIF,MAAML;AAChB,UAAMQ,iBAAiBC,gBAAgBJ,OAAO,KAAKlB,QAAQ;AAC3D,QAAIuB,KAAKF,eAAeG;AACxB,QAAIL,iBAAiB,GAAG;AAKpBI,YAAM,KAAKtB,QAAQsB,GAAGF,eAAeZ,IAAI,CAAC,EAAEU,iBAAiB,CAAC;IAClE;AAEA,QAAIM,IAAIC,MAAMC,UAAUR,cAAc,IAAII;AAC1C,QAAI,CAAC,KAAKrB,iBAAiB;AACvBuB,WAAKG,wCAAwCT,iBAAiB;IAClE;AAKA,QAAIU,aAAa;AACjBC,WAAOC,KAAK,KAAK9B,QAAQwB,EAAEL,CAAC,CAAC,EAAEY,QAASC,4BAA2B;AAC/D,YAAMC,uBAAuB,KAAKjC,QAAQwB,EAAEL,CAAC,EAAEa,sBAAsB;AACrE,UAAIE,SAASF,wBAAwB,EAAE,KAAKd,gBAAgB;AACxD,YAAIe,wBAAwBT,GAAG;AAC3BI,uBAAa;QACjB;MACJ;IACJ,CAAC;AACD,QAAI,CAACA,YAAY;AAEb,WAAK5B,QAAQwB,EAAEL,CAAC,EAAED,cAAc,IAAIM;AACpC,WAAKxB,QAAQmC,EAAEhB,CAAC,EAAED,cAAc,IAAIE;AACpC,WAAKpB,QAAQsB,GAAGH,CAAC,EAAED,cAAc,IAAII;IACzC;;;EAGJc,iBAAiBC,mBAAmB;AAEhC,QAAIpB,QAAQ,KAAKN,oBAAoB,GAAG0B,iBAAiB;AACzD,SAAKrB,OAAOC,OAAO,CAAC;AACpB,aAAST,IAAI,GAAGA,KAAK6B,mBAAmB7B,KAAK,GAAG;AAI5CS,cAAQ,KAAKN,oBAAoBH,GAAG6B,iBAAiB;AACrD,YAAMC,MAAM,KAAKtC,QAAQmC,EAAE3B,IAAI,CAAC;AAEhCqB,aAAOC,KAAKQ,GAAG,EAAEP,QAASb,oBAAmB;AACzC,cAAMqB,YAAYD,IAAIpB,cAAc;AAKpC,YAAIqB,UAAU1B,YAAY,cAAc;AAEpC,eAAKG,OAAOC,OAAOiB,SAAShB,gBAAgB,EAAE,IAAI,CAAC;QACvD;MACJ,CAAC;IACL;;;;EAIJsB,OAAOC,gBAAgB;AACnB,UAAMC,uBAAuB,CAAA;AAC7B,QAAIvB,IAAIsB,iBAAiB;AAEzB,QAAIvB,iBAAiB;AAErB,QAAIM,IAAI;AACR,UAAMmB,OAAO,KAAK3C,QAAQwB,EAAEL,CAAC;AAE7B,QAAIwB,MAAM;AACNd,aAAOC,KAAKa,IAAI,EAAEZ,QAASa,6BAA4B;AACnD,cAAMC,uBAAuBF,KAAKC,uBAAuB;AACzD,YAAIC,uBAAuBrB,GAAG;AAC1BN,2BAAiBgB,SAASU,yBAAyB,EAAE;AACrDpB,cAAIqB;QACR;MACJ,CAAC;IACL;AACA,WAAO1B,KAAK,GAAG;AACX,YAAMF,QAAQ,KAAKjB,QAAQmC,EAAEhB,CAAC,EAAED,cAAc;AAC9CwB,2BAAqBI,QAAQ7B,KAAK;AAClCE,UAAIF,MAAMT,IAAI;AACdU,wBAAkB;IACtB;AACA,WAAOwB;EACX;AACJ;AACA,IAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCXK,2BAA2BhD,UAAUiD,SAAS/C,kBAAkB,OAAO;AACnEH,kBAAcC,WAAWA;AACzBD,kBAAcG,kBAAkBA;AAChC,UAAMwC,iBAAiB1C,SAASkD;AAEhC,QAAIC,uBAAuBpD,cAAcM,UAAUqC,gBAAgB,OAAO;AAC1EO,YAAQjB,QAASd,WAAU;AACvBiC,2BAAqBjC,MAAML,CAAC,EAAEF,KAAKO,KAAK;IAC5C,CAAC;AAEDiC,2BAAuBA,qBAAqBC,IAAKlC,WAAUA,MAAMmC,KAAK,CAACC,IAAIC,OAAOD,GAAG7C,IAAI8C,GAAG9C,CAAC,CAAC;AAC9FV,kBAAcE,UAAU;;;;;;;MAOpBmC,GAAGrC,cAAcM,UAAUqC,gBAAgB,QAAQ;;;MAGnDnB,IAAIxB,cAAcM,UAAUqC,gBAAgB,QAAQ;;MAEpDjB,GAAG1B,cAAcM,UAAUqC,gBAAgB,QAAQ;;AAEvD,aAAStB,IAAI,GAAGA,IAAIsB,gBAAgBtB,KAAK,GAAG;AACxC+B,2BAAqB/B,CAAC,EAAEY,QAASd,WAAU;AACvC,YAAIA,MAAMT,IAAI,GAAG;AACbqB,iBAAOC,KAAKhC,cAAcE,QAAQmC,EAAElB,MAAMT,IAAI,CAAC,CAAC,EAAEuB,QAASb,oBAAmB;AAC1EpB,0BAAckB,OAAOC,OAAOiB,SAAShB,gBAAgB,EAAE,IAAI,CAAC;UAChE,CAAC;QACL,OACK;AACDpB,wBAAckB,OAAOC,OAAO,CAAC;QACjC;MACJ,CAAC;AACDnB,oBAAcsC,iBAAiBjB,CAAC;IACpC;AACA,UAAMuB,uBAAuB5C,cAAc0C,OAAOC,cAAc;AAChE,UAAMc,wBAAwBb,qBAAqBO;AACnD,UAAM1B,UAAU,KAAKiC,WAAWzD,UAAUwD,qBAAqB;AAC/D,WAAO;MACHxD;MACAwB;MACAkC,cAAchC,MAAMiC,MAAMnC,OAAO;MACjCoC,UAAUjB;;;EAGlBc,WAAWzD,UAAUwD,uBAAuB;AACxC,UAAMd,iBAAiB1C,SAASkD;AAChC,QAAI1B,UAAU;AACd,QAAIxB,SAASkD,WAAW,GAAG;AACvB1B,gBAAU;IACd,OACK;AACDA,gBACIzB,cAAcE,QAAQwB,EAAEiB,iBAAiB,CAAC,EAAEc,qBAAqB;IACzE;AACA,WAAOhC;EACX;AACJ;;;AChNA,IAAMqC,cAAN,MAAkB;;EAEdC,MAAM;IAAEC;IAAUC;EAAU,GAAG;AAC3B,UAAMC,UAAU,CAAA;AAChB,QAAIC,YAAY;AAChB,WAAOA,YAAYH,SAASI,QAAQ;AAChC,YAAMC,cAAc,KAAKC,eAAeN,UAAUG,SAAS;AAC3D,YAAMI,YAAY,KAAKC,aAAaR,UAAUG,SAAS;AACvD,UAAIE,eAAe,MAAM;AACrB;MACJ;AACA,YAAM;QAAEN;QAAOU;UAAc,KAAKC,cAAcL,aAAaE,SAAS;AACtE,UAAIR,OAAO;AACP,cAAMY,IAAIZ,MAAMa,QAAQb,MAAM,CAAC,EAAEK,SAAS;AAC1C,cAAMS,cAAc,KAAKC,eAAeL,WAAWR,SAAS;AAC5DC,gBAAQa,KAAK,KAAKC,eAAeP,WAAWE,GAAGZ,OAAOc,WAAW,CAAC;AAClEV,oBAAYQ,IAAI;MACpB;IACJ;AACA,UAAMM,cAAcf,QAAQgB,KAAMnB,WAAU;AACxC,aAAOA,iBAAiBoB;IAC5B,CAAC;AACD,QAAIF,aAAa;AACb,aAAOE,QAAQC,IAAIlB,OAAO;IAC9B;AACA,WAAOA;EACX;;EAEAc,eAAeP,WAAWE,GAAGZ,OAAOc,aAAa;AAC7C,UAAMQ,YAAY;MACdC,SAAS;MACTC,GAAGxB,MAAMa;MACTD;MACAa,OAAOzB,MAAM,CAAC;MACdU;MACAI,aAAa;MACbY,aAAa1B,MAAM,CAAC,EAAEK,SAASK,UAAUL;;AAE7C,QAAIS,uBAAuBM,SAAS;AAChC,aAAON,YAAYa,KAAMC,yBAAwB;AAC7C,eAAO;UACH,GAAGN;UACHR,aAAac;;MAErB,CAAC;IACL;AACA,WAAO;MACH,GAAGN;MACHR;;EAER;EACAP,eAAeN,UAAUG,WAAW;AAChC,UAAMyB,SAAS;AACfA,WAAOzB,YAAYA;AACnB,WAAOyB,OAAOC,KAAK7B,QAAQ;EAC/B;EACAQ,aAAaR,UAAUG,WAAW;AAC9B,UAAM2B,OAAO;AACbA,SAAK3B,YAAYA;AACjB,WAAO2B,KAAKD,KAAK7B,QAAQ;EAC7B;EACAU,cAAcL,aAAaE,WAAW;AAClC,UAAMwB,eAAe;AACrB,QAAIhC;AACJ,QAAIU,YAAY;AAChB,QAAIF,aAAaF,YAAY,CAAC,EAAED,SAASG,UAAU,CAAC,EAAEH,QAAQ;AAI1DL,cAAQM;AAKR,YAAM2B,OAAOD,aAAaF,KAAK9B,MAAM,CAAC,CAAC;AACvC,UAAIiC,MAAM;AACNvB,oBAAYuB,KAAK,CAAC;MACtB;IACJ,OACK;AAIDjC,cAAQQ;AACR,UAAIR,OAAO;AACPU,oBAAYV,MAAM,CAAC;MACvB;IACJ;AACA,WAAO;MACHA;MACAU;;EAER;EACAK,eAAeL,WAAWR,WAAW;AACjC,UAAMC,UAAUD,UAAUF,MAAMU,SAAS;AACzC,QAAIP,mBAAmBiB,SAAS;AAC5B,aAAOjB,QAAQwB,KAAMO,qBAAoB;AACrC,cAAMC,gBAAeC,QAAQC,2BAA2B3B,WAAWwB,eAAe;AAClF,eAAOC,cAAaG;MACxB,CAAC;IACL;AACA,UAAMH,eAAeC,QAAQC,2BAA2B3B,WAAWP,OAAO;AAC1E,WAAOgC,aAAaG;EACxB;AACJ;;;ACxGA,IAAMC,gBAAN,MAAoB;EAChBC,cAAc;AACV,SAAKC,YAAY;EACrB;;EAEAC,MAAM;IAAEC;EAAS,GAAG;AAehB,UAAMC,SAAS,CAAA;AACf,QAAID,SAASE,WAAW,GAAG;AACvB,aAAO,CAAA;IACX;AACA,QAAIC,IAAI;AACR,QAAIC,YAAY;AAChB,UAAMC,iBAAiBL,SAASE;AAChC,aAASI,IAAI,GAAGA,IAAID,gBAAgBC,KAAK,GAAG;AACxC,YAAMC,QAAQP,SAASQ,WAAWF,CAAC,IAAIN,SAASQ,WAAWF,IAAI,CAAC;AAChE,UAAIF,aAAa,MAAM;AACnBA,oBAAYG;MAChB;AACA,UAAIA,UAAUH,WAAW;AACrB,cAAMK,IAAIH,IAAI;AACd,aAAKI,OAAO;UACRP;UACAM;UACAF,OAAOH;UACPJ;UACAC;QACJ,CAAC;AACDE,YAAIM;AACJL,oBAAYG;MAChB;IACJ;AACA,SAAKG,OAAO;MACRP;MACAM,GAAGJ,iBAAiB;MACpBE,OAAOH;MACPJ;MACAC;IACJ,CAAC;AACD,WAAOA;EACX;EACAS,OAAO;IAAEP;IAAGM;IAAGF;IAAOP;IAAUC;EAAO,GAAG;AACtC,QAAIQ,IAAIN,IAAI,KAAKQ,KAAKC,IAAIL,KAAK,MAAM,GAAG;AACpC,YAAMM,gBAAgBF,KAAKC,IAAIL,KAAK;AACpC,UAAIM,gBAAgB,KAAKA,iBAAiB,KAAKf,WAAW;AACtD,cAAMgB,QAAQd,SAASe,MAAMZ,GAAG,CAACM,IAAI,KAAK,GAAG;AAC7C,cAAM;UAAEO;UAAcC;QAAc,IAAI,KAAKC,YAAYJ,KAAK;AAC9D,eAAOb,OAAOkB,KAAK;UACfC,SAAS;UACTjB;UACAM;UACAK,OAAOd,SAASe,MAAMZ,GAAG,CAACM,IAAI,KAAK,GAAG;UACtCO;UACAC;UACAI,WAAWd,QAAQ;QACvB,CAAC;MACL;IACJ;AACA,WAAO;EACX;EACAW,YAAYJ,OAAO;AAGf,QAAIE,eAAe;AACnB,QAAIC,gBAAgB;AACpB,QAAIK,UAAUC,KAAKT,KAAK,GAAG;AACvBE,qBAAe;AACfC,sBAAgB;eAEXO,UAAUD,KAAKT,KAAK,GAAG;AAC5BE,qBAAe;AACfC,sBAAgB;eAEXQ,UAAUF,KAAKT,KAAK,GAAG;AAC5BE,qBAAe;AACfC,sBAAgB;IACpB;AACA,WAAO;MACHD;MACAC;;EAER;AACJ;;;AC9FA,IAAMS,eAAN,MAAmB;EACfC,cAAc;AACV,SAAKC,aAAa;EACtB;EACAC,MAAM;IAAEC;EAAS,GAAG;AAChB,UAAMC,UAAU,CAAA;AAChBC,WAAOC,KAAKC,cAAcC,MAAM,EAAEC,QAASC,eAAc;AACrD,YAAMC,QAAQJ,cAAcC,OAAOE,SAAS;AAC5CE,aAAOR,SAAS,KAAKS,OAAOV,UAAUQ,OAAOD,SAAS,CAAC;IAC3D,CAAC;AACD,WAAOI,OAAOV,OAAO;EACzB;EACAW,eAAeL,WAAWP,UAAUa,OAAO;AACvC,QAAI,CAACN,UAAUO,SAAS,QAAQ;IAE5B,KAAKhB,WAAWiB,KAAKf,SAASgB,OAAOH,KAAK,CAAC,GAAG;AAC9C,aAAO;IACX;AACA,WAAO;EACX;;EAEAH,OAAOV,UAAUQ,OAAOD,WAAW;AAC/B,QAAIU;AACJ,UAAMhB,UAAU,CAAA;AAChB,QAAIiB,IAAI;AACR,UAAMC,iBAAiBnB,SAASoB;AAChC,WAAOF,IAAIC,iBAAiB,GAAG;AAC3B,UAAIE,IAAIH,IAAI;AACZ,UAAII,gBAAgB;AACpB,UAAIC,QAAQ;AACZN,qBAAe,KAAKL,eAAeL,WAAWP,UAAUkB,CAAC;AAEzD,aAAO,MAAM;AACT,cAAMM,WAAWxB,SAASgB,OAAOK,IAAI,CAAC;AACtC,cAAMI,YAAYjB,MAAMgB,QAAQ,KAAK,CAAA;AACrC,YAAIE,QAAQ;AACZ,YAAIC,iBAAiB;AACrB,YAAIC,eAAe;AAEnB,YAAIP,IAAIF,gBAAgB;AACpB,gBAAMU,UAAU7B,SAASgB,OAAOK,CAAC;AACjC,gBAAMS,kBAAkBL,UAAUL;AAClC,mBAASW,IAAI,GAAGA,IAAID,iBAAiBC,KAAK,GAAG;AACzC,kBAAMC,WAAWP,UAAUM,CAAC;AAC5BH,4BAAgB;AAEhB,gBAAII,UAAU;AACV,oBAAMC,gBAAgBD,SAASE,QAAQL,OAAO;AAE9C,kBAAII,kBAAkB,IAAI;AACtBP,wBAAQ;AACRC,iCAAiBC;AAEjB,oBAAIK,kBAAkB,GAAG;AAKrBhB,kCAAgB;gBACpB;AAEA,oBAAIK,kBAAkBK,gBAAgB;AAIlCJ,2BAAS;AACTD,kCAAgBK;gBACpB;AACA;cACJ;YACJ;UACJ;QACJ;AAEA,YAAID,OAAO;AACPL,eAAK;QAET,OACK;AAED,cAAIA,IAAIH,IAAI,GAAG;AACXjB,oBAAQkC,KAAK;cACTC,SAAS;cACTlB;cACAG,GAAGA,IAAI;cACPgB,OAAOrC,SAASsC,MAAMpB,GAAGG,CAAC;cAC1Bb,OAAOD;cACPgB;cACAN;YACJ,CAAC;UACL;AAEAC,cAAIG;AACJ;QACJ;MACJ;IACJ;AACA,WAAOpB;EACX;AACJ;;;ACzGA,IAAMsC,iBAAiB,IAAIC,OAAQ,IAAGC,gBAAgBC,KAAK,EAAE,CAAE,GAAE;AAMjE,IAAMC,iBAAN,MAAMA,gBAAe;EACjB,OAAOC,yBAAyBC,UAAU;AACtC,UAAMC,qBAAqB,CACvB,GAAGD,SACEE,MAAM,EAAE,EACRC,OAAQC,OAAMV,eAAeW,KAAKD,CAAC,CAAC,EACpCE,OAAO,CAACC,MAAMH,MAAM;AACrB,YAAMI,IAAID,KAAKE,IAAIL,CAAC;AACpB,UAAII,GAAG;AACHD,aAAKG,IAAIN,GAAGI,IAAI,CAAC;MACrB,OACK;AACDD,aAAKG,IAAIN,GAAG,CAAC;MACjB;AACA,aAAOG;IACX,GAAG,oBAAII,IAAG,CAAE,EACPC,QAAO,CAAE,EAChBC,KAAK,CAAC,CAACC,IAAIC,CAAC,GAAG,CAACC,IAAIC,CAAC,MAAMA,IAAIF,CAAC;AAClC,QAAI,CAACd,mBAAmBiB,OACpB,QAAOC;AACX,UAAMC,QAAQnB,mBAAmB,CAAC;AAElC,QAAImB,MAAM,CAAC,IAAI,EACX,QAAOD;AACX,WAAOC,MAAM,CAAC;EAClB;EACA,OAAOC,kBAAkBC,WAAW;AAChC,WAAO,IAAI3B,OAAQ,MAAK2B,SAAU;KAAOA,SAAU,OAAMA,SAAU,KAAI,GAAG;EAI9E;;EAEAF,MAAM;IAAEpB;EAAS,GAAG;AAChB,UAAMuB,SAAS,CAAA;AACf,QAAIvB,SAASkB,WAAW,EACpB,QAAOK;AACX,UAAMC,kBAAkB1B,gBAAeC,yBAAyBC,QAAQ;AACxE,QAAIwB,oBAAoBL,OACpB,QAAOI;AACX,UAAME,cAAc3B,gBAAeuB,kBAAkBG,eAAe;AAEpE,eAAWJ,SAASpB,SAAS0B,SAASD,WAAW,GAAG;AAEhD,UAAIL,MAAMO,UAAUR,OAChB;AAIJ,YAAMS,IAAIR,MAAMO,QAAQ;AACxBJ,aAAOM,KAAK;QACRC,SAAS;QACTC,OAAOP;QACPI;QACAI,GAAGJ;MACP,CAAC;IACL;AACA,WAAOL;EACX;AACJ;;;ACzDA,IAAMU,WAAN,MAAe;EACXC,cAAc;AACV,SAAKC,WAAW;MACZC,MAAMC;MACNC,YAAYC;MACZC,OAAOC;;MAEPC,QAAQC;MACRC,UAAUC;MACVC,SAASC;MACTC,WAAWC;;EAEnB;EACAC,MAAMC,UAAU;AACZ,UAAMC,UAAU,CAAA;AAChB,UAAMC,WAAW,CAAA;AACjB,UAAMlB,YAAW,CACb,GAAGmB,OAAOC,KAAK,KAAKpB,QAAQ,GAC5B,GAAGmB,OAAOC,KAAKC,cAAcrB,QAAQ,CAAC;AAE1CA,IAAAA,UAASsB,QAASC,SAAQ;AACtB,UAAI,CAAC,KAAKvB,SAASuB,GAAG,KAAK,CAACF,cAAcrB,SAASuB,GAAG,GAAG;AACrD;MACJ;AACA,YAAMC,UAAU,KAAKxB,SAASuB,GAAG,IAC3B,KAAKvB,SAASuB,GAAG,IACjBF,cAAcrB,SAASuB,GAAG,EAAEzB;AAClC,YAAM2B,cAAc,IAAID,QAAO;AAC/B,YAAME,SAASD,YAAYV,MAAM;QAC7BC;QACAW,WAAW;MACf,CAAC;AACD,UAAID,kBAAkBE,SAAS;AAC3BF,eAAOG,KAAMC,cAAa;AACtBC,iBAAOd,SAASa,QAAQ;QAC5B,CAAC;AACDZ,iBAASc,KAAKN,MAAM;MACxB,OACK;AACDK,eAAOd,SAASS,MAAM;MAC1B;IACJ,CAAC;AACD,QAAIR,SAASe,SAAS,GAAG;AACrB,aAAO,IAAIL,QAAQ,CAACM,SAASC,WAAW;AACpCP,gBAAQQ,IAAIlB,QAAQ,EACfW,KAAK,MAAM;AACZK,kBAAQG,OAAOpB,OAAO,CAAC;QAC3B,CAAC,EACIqB,MAAOC,WAAU;AAClBJ,iBAAOI,KAAK;QAChB,CAAC;MACL,CAAC;IACL;AACA,WAAOF,OAAOpB,OAAO;EACzB;AACJ;;;AC/DA,IAAMuB,SAAS;AACf,IAAMC,SAASD,SAAS;AACxB,IAAME,OAAOD,SAAS;AACtB,IAAME,MAAMD,OAAO;AACnB,IAAME,QAAQD,MAAM;AACpB,IAAME,OAAOD,QAAQ;AACrB,IAAME,UAAUD,OAAO;AACvB,IAAME,QAAQ;EACVC,QAAQR;EACRS,QAAQR;EACRS,MAAMR;EACNS,KAAKR;EACLS,OAAOR;EACPS,MAAMR;EACNS,SAASR;AACb;AAMA,IAAMS,gBAAN,MAAoB;EAChBC,UAAUC,YAAYC,OAAO;AACzB,QAAIC,MAAMF;AACV,QAAIC,UAAUE,UAAaF,UAAU,GAAG;AACpCC,aAAO;IACX;AACA,UAAM;MAAEE;QAAmBC,cAAcC;AACzC,WAAOF,eAAeF,GAAG,EAAEK,QAAQ,UAAW,GAAEN,KAAM,EAAC;EAC3D;EACAO,oBAAoBC,SAAS;AACzB,UAAMC,oBAAoB;MACtBC,4BAA4BF,WAAW,MAAM;MAC7CG,+BAA+BH,UAAU;MACzCI,gCAAgCJ,UAAU;MAC1CK,iCAAiCL,UAAU;;AAE/C,UAAMM,oBAAoB;MACtBJ,4BAA4B;MAC5BC,+BAA+B;MAC/BC,gCAAgC;MAChCC,iCAAiC;;AAErCE,WAAOC,KAAKP,iBAAiB,EAAEQ,QAASC,cAAa;AACjD,YAAMC,UAAUV,kBAAkBS,QAAQ;AAC1CJ,wBAAkBI,QAAQ,IACtB,KAAKE,YAAYD,OAAO;IAChC,CAAC;AACD,WAAO;MACHV;MACAK;MACAO,OAAO,KAAKC,eAAed,OAAO;;EAE1C;EACAc,eAAed,SAAS;AACpB,UAAMe,QAAQ;AACd,QAAIf,UAAU,MAAMe,OAAO;AAEvB,aAAO;IACX;AACA,QAAIf,UAAU,MAAMe,OAAO;AAEvB,aAAO;IACX;AACA,QAAIf,UAAU,MAAMe,OAAO;AAEvB,aAAO;IACX;AACA,QAAIf,UAAU,OAAOe,OAAO;AAGxB,aAAO;IACX;AAEA,WAAO;EACX;EACAH,YAAYD,SAAS;AACjB,QAAIpB,aAAa;AACjB,QAAIyB;AACJ,UAAMC,WAAWV,OAAOC,KAAK3B,KAAK;AAClC,UAAMqC,aAAaD,SAASE,UAAWC,CAAAA,UAAST,UAAU9B,MAAMuC,KAAI,CAAC;AACrE,QAAIF,aAAa,IAAI;AACjB3B,mBAAa0B,SAASC,aAAa,CAAC;AACpC,UAAIA,eAAe,GAAG;AAClBF,eAAOK,KAAKC,MAAMX,UAAU9B,MAAMU,UAAU,CAAC;MACjD,OACK;AACDA,qBAAa;MACjB;IACJ;AACA,WAAO,KAAKD,UAAUC,YAAYyB,IAAI;EAC1C;AACJ;;;AC7FA,IAAAO,qBAAe,MAAM;AACjB,SAAO;AACX;;;ACDA,IAAAC,eAAe,MAAM;AACjB,SAAO;IACHC,SAASC,cAAcC,aAAaC,SAASC;IAC7CC,aAAa,CAACJ,cAAcC,aAAaG,YAAYD,KAAK;;AAElE;;;ACJA,IAAME,+BAA+BA,CAACC,OAAOC,gBAAgB;AACzD,MAAIC,UAAU;AACd,MAAID,eAAe,CAACD,MAAMG,QAAQ,CAACH,MAAMI,UAAU;AAC/C,QAAIJ,MAAMK,QAAQ,IAAI;AAClBH,gBAAUI,cAAcC,aAAaC,SAASC;IAClD,WACST,MAAMK,QAAQ,KAAK;AACxBH,gBAAUI,cAAcC,aAAaC,SAASE;IAClD,OACK;AACDR,gBAAUI,cAAcC,aAAaC,SAASG;IAClD;EACJ,WACSX,MAAMY,gBAAgB,GAAG;AAC9BV,cAAUI,cAAcC,aAAaC,SAASK;EAClD;AACA,SAAOX;AACX;AACA,IAAMY,gCAAgCA,CAACd,OAAOC,gBAAgB;AAC1D,MAAIC,UAAU;AACd,MAAID,aAAa;AACbC,cAAUI,cAAcC,aAAaC,SAASO;EAClD;AACA,SAAOb;AACX;AACA,IAAMc,4BAA4BA,CAAChB,OAAOC,gBAAgB;AACtD,MAAIA,aAAa;AACb,WAAOK,cAAcC,aAAaC,SAASS;EAC/C;AACA,SAAOX,cAAcC,aAAaC,SAASU;AAC/C;AACA,IAAMC,uBAAuBA,CAACnB,OAAOC,gBAAgB;AACjD,MAAIC,UAAU;AACd,QAAMkB,WAAWpB,MAAMqB;AACvB,QAAMC,UAAUF,aAAa,eAAeA,SAASG,YAAW,EAAGC,SAAS,YAAY;AACxF,MAAIJ,aAAa,aAAa;AAC1BlB,cAAUH,6BAA6BC,OAAOC,WAAW;aAEpDmB,SAASI,SAAS,WAAW,GAAG;AACrCtB,cAAUY,8BAA8Bd,OAAOC,WAAW;aAErDqB,SAAS;AACdpB,cAAUc,0BAA0BhB,OAAOC,WAAW;EAC1D,WACSmB,aAAa,cAAc;AAChClB,cAAUI,cAAcC,aAAaC,SAASiB;EAClD;AACA,SAAOvB;AACX;AACA,IAAAwB,qBAAe,CAAC1B,OAAOC,gBAAgB;AACnC,QAAMC,UAAUiB,qBAAqBnB,OAAOC,WAAW;AACvD,QAAM0B,cAAc,CAAA;AACpB,QAAMC,OAAO5B,MAAM6B;AACnB,MAAID,KAAK5B,MAAM8B,WAAW,GAAG;AACzBH,gBAAYI,KAAKzB,cAAcC,aAAaoB,YAAYK,cAAc;EAC1E,WACSJ,KAAK5B,MAAMiC,kBAAkB,KAAKL,KAAKL,YAAW,MAAOK,MAAM;AACpED,gBAAYI,KAAKzB,cAAcC,aAAaoB,YAAYO,YAAY;EACxE;AACA,MAAIlC,MAAMI,YAAYJ,MAAM6B,MAAMM,UAAU,GAAG;AAC3CR,gBAAYI,KAAKzB,cAAcC,aAAaoB,YAAYS,YAAY;EACxE;AACA,MAAIpC,MAAMG,MAAM;AACZwB,gBAAYI,KAAKzB,cAAcC,aAAaoB,YAAYxB,IAAI;EAChE;AACA,SAAO;IACHD;IACAyB;;AAER;;;ACtEA,IAAAU,gBAAgBC,WAAU;AACtB,MAAIA,MAAMC,cAAc,cAAc;AAClC,WAAO;MACHC,SAASC,cAAcC,aAAaC,SAASC;MAC7CC,aAAa,CACTJ,cAAcC,aAAaG,YAAYD,aACvCH,cAAcC,aAAaG,YAAYC,eAAe;;EAGlE;AACA,SAAO;IACHN,SAAS;IACTK,aAAa,CAAA;;AAErB;;;ACdA,IAAAE,iBAAgBC,WAAU;AACtB,MAAIC,UAAUC,cAAcC,aAAaC,SAASC;AAClD,MAAIL,MAAMM,UAAUC,WAAW,GAAG;AAC9BN,cAAUC,cAAcC,aAAaC,SAASI;EAClD;AACA,SAAO;IACHP;IACAQ,aAAa,CAACP,cAAcC,aAAaM,YAAYC,QAAQ;;AAErE;;;ACTA,IAAAC,mBAAe,MAAM;AACjB,SAAO;IACHC,SAASC,cAAcC,aAAaC,SAASC;IAC7CC,aAAa,CAACJ,cAAcC,aAAaG,YAAYD,SAAS;;AAEtE;;;ACLA,IAAAE,kBAAgBC,WAAU;AACtB,MAAIC,UAAUC,cAAcC,aAAaC,SAASC;AAClD,MAAIL,MAAMM,UAAU,GAAG;AACnBL,cAAUC,cAAcC,aAAaC,SAASG;EAClD;AACA,SAAO;IACHN;IACAO,aAAa,CAACN,cAAcC,aAAaK,YAAYC,qBAAqB;;AAElF;;;ACVA,IAAAC,oBAAe,MAAM;AAEjB,SAAO;AACX;;;ACMA,IAAMC,kBAAkB;EACpBC,SAAS;EACTC,aAAa,CAAA;AACjB;AAMA,IAAMC,WAAN,MAAe;EACXC,cAAc;AACV,SAAKC,WAAW;MACZC,YAAYC;MACZC,MAAMC;MACNC,YAAYC;MACZC,OAAOC;MACPC,QAAQC;MACRC,UAAUC;MACVC,SAASC;MACTC,WAAWC;;AAEf,SAAKrB,kBAAkB;MACnBC,SAAS;MACTC,aAAa,CAAA;;AAEjB,SAAKoB,sBAAqB;EAC9B;EACAA,wBAAwB;AACpB,SAAKtB,gBAAgBE,YAAYqB,KAAKC,cAAcC,aAAavB,YAAYwB,UAAUF,cAAcC,aAAavB,YAAYyB,MAAM;EACxI;EACAC,YAAYC,OAAOb,UAAU;AACzB,QAAIA,SAASc,WAAW,GAAG;AACvB,aAAO,KAAK9B;IAChB;AACA,QAAI6B,QAAQ,GAAG;AACX,aAAO7B;IACX;AACA,UAAM+B,gBAAgBP,cAAcC,aAAavB,YAAY8B;AAC7D,UAAMC,eAAe,KAAKC,gBAAgBlB,QAAQ;AAClD,QAAImB,WAAW,KAAKC,iBAAiBH,cAAcjB,SAASc,WAAW,CAAC;AACxE,QAAIK,aAAa,QAAQA,aAAaE,QAAW;AAC7CF,eAASjC,YAAYoC,QAAQP,aAAa;IAC9C,OACK;AACDI,iBAAW;QACPlC,SAAS;QACTC,aAAa,CAAC6B,aAAa;;IAEnC;AACA,WAAOI;EACX;EACAD,gBAAgBlB,UAAU;AACtB,QAAIiB,eAAejB,SAAS,CAAC;AAC7B,UAAMuB,iBAAiBvB,SAASwB,MAAM,CAAC;AACvCD,mBAAeE,QAASC,WAAU;AAC9B,UAAIA,MAAMC,MAAMb,SAASG,aAAaU,MAAMb,QAAQ;AAChDG,uBAAeS;MACnB;IACJ,CAAC;AACD,WAAOT;EACX;EACAG,iBAAiBM,OAAOE,aAAa;AACjC,QAAI,KAAKvC,SAASqC,MAAMG,OAAO,GAAG;AAC9B,aAAO,KAAKxC,SAASqC,MAAMG,OAAO,EAAEH,OAAOE,WAAW;IAC1D;AACA,QAAIpB,cAAcnB,SAASqC,MAAMG,OAAO,KACpC,cAAcrB,cAAcnB,SAASqC,MAAMG,OAAO,GAAG;AACrD,aAAOrB,cAAcnB,SAASqC,MAAMG,OAAO,EAAEV,SAASO,OAAOE,WAAW;IAC5E;AACA,WAAO5C;EACX;AACJ;;;AC1EA,IAAA,WAAe,CAAC8C,MAAMC,MAAMC,gBAAgB;AACxC,MAAIC;AACJ,SAAO,SAASC,aAAYC,MAAM;AAC9B,UAAMC,UAAU;AAChB,UAAMC,QAAQA,MAAM;AAChBJ,gBAAUK;AACV,UAAI,CAACN,aAAa;AACdF,aAAKS,MAAMH,SAASD,IAAI;MAC5B;;AAEJ,UAAMK,gBAAgBR,eAAe,CAACC;AACtC,QAAIA,YAAYK,QAAW;AACvBG,mBAAaR,OAAO;IACxB;AACAA,cAAUS,WAAWL,OAAON,IAAI;AAChC,QAAIS,eAAe;AACf,aAAOV,KAAKS,MAAMH,SAASD,IAAI;IACnC;AACA,WAAOG;;AAEf;;;ACpBA,IAAMK,OAAOA,OAAM,oBAAIC,KAAI,GAAGC,QAAO;AACrC,IAAMC,oBAAoBA,CAACC,iBAAiBC,UAAUC,UAAU;AAC5D,QAAMC,WAAW,IAAIC,SAAQ;AAC7B,QAAMC,gBAAgB,IAAIC,cAAa;AACvC,QAAMC,gBAAgBC,QAAQC,2BAA2BR,UAAUD,eAAe;AAClF,QAAMU,WAAWd,KAAI,IAAKM;AAC1B,QAAMS,cAAcN,cAAcO,oBAAoBL,cAAcM,OAAO;AAC3E,SAAO;IACHH;IACA,GAAGH;IACH,GAAGI;IACHR,UAAUA,SAASW,YAAYH,YAAYI,OAAOR,cAAcS,QAAQ;;AAEhF;AACA,IAAMC,OAAOA,CAAChB,UAAUiB,eAAe;AACnC,MAAIA,YAAY;AACZC,kBAAcC,2BAA2BF,UAAU;EACvD;AACA,QAAMG,WAAW,IAAIC,SAAQ;AAC7B,SAAOD,SAASE,MAAMtB,QAAQ;AAClC;IACauB,SAASA,CAACvB,UAAUiB,eAAe;AAC5C,QAAMhB,QAAQN,KAAI;AAClB,QAAM6B,UAAUR,KAAKhB,UAAUiB,UAAU;AACzC,MAAIO,mBAAmBC,SAAS;AAC5B,UAAM,IAAIC,MAAM,oEAAoE;EACxF;AACA,SAAO5B,kBAAkB0B,SAASxB,UAAUC,KAAK;AACrD;AACO,IAAM0B,cAAc,OAAO3B,UAAUiB,eAAe;AACvD,QAAMW,eAAe5B,SAAS6B,UAAU,GAAGX,cAAcY,SAAS;AAClE,QAAM7B,QAAQN,KAAI;AAClB,QAAM6B,UAAU,MAAMR,KAAKY,cAAcX,UAAU;AACnD,SAAOnB,kBAAkB0B,SAASI,cAAc3B,KAAK;AACzD;", "names": ["extend", "listToExtend", "list", "push", "apply", "sorted", "matches", "sort", "m1", "m2", "i", "j", "buildRankedDictionary", "orderedList", "result", "counter", "for<PERSON>ach", "word", "DATE_MAX_YEAR", "DATE_MIN_YEAR", "DATE_SPLITS", "dateSplits", "BRUTEFORCE_CARDINALITY", "MIN_GUESSES_BEFORE_GROWING_SEQUENCE", "MIN_SUBMATCH_GUESSES_SINGLE_CHAR", "MIN_SUBMATCH_GUESSES_MULTI_CHAR", "MIN_YEAR_SPACE", "START_UPPER", "END_UPPER", "ALL_UPPER", "ALL_UPPER_INVERTED", "ALL_LOWER", "ALL_LOWER_INVERTED", "ONE_LOWER", "ONE_UPPER", "ALPHA_INVERTED", "ALL_DIGIT", "REFERENCE_YEAR", "Date", "getFullYear", "REGEXEN", "recentYear", "SEPERATOR_CHARS", "SEPERATOR_CHAR_COUNT", "length", "MatchDate", "match", "password", "matches", "getMatchesWithoutSeparator", "getMatchesWithSeparator", "filteredMatches", "filterNoise", "sorted", "maybeDateWithSeparator", "i", "Math", "abs", "length", "j", "token", "slice", "regexMatch", "exec", "dmy", "mapIntegersToDayMonthYear", "parseInt", "push", "pattern", "separator", "year", "month", "day", "maybeDateNoSeparator", "metric", "candidate", "REFERENCE_YEAR", "candidates", "index", "splittedDates", "DATE_SPLITS", "for<PERSON>ach", "k", "l", "bestCandidate", "minDistance", "distance", "filter", "isSubmatch", "matchesLength", "o", "otherMatch", "integers", "over12", "over31", "under1", "len1", "int", "DATE_MIN_YEAR", "DATE_MAX_YEAR", "getDayMonth", "possibleYearSplits", "possibleYearSplitsLength", "y", "rest", "dm", "mapIntegersToDayMonth", "twoToFourDigitYear", "temp", "reverse", "data", "mv", "pv", "start", "vlen", "getUsedT<PERSON><PERSON>old", "password", "entry", "threshold", "isPasswordToShort", "length", "isThresholdLongerThanPassword", "shouldUsePasswordLength", "Math", "ceil", "findLevenshteinDistance", "rankedDictionary", "foundDistance", "found", "Object", "keys", "find", "usedThreshold", "abs", "foundEntryDistance", "distance", "isInThreshold", "levenshteinDistance", "levenshteinDistanceEntry", "a", "b", "c", "d", "e", "f", "g", "h", "i", "k", "l", "m", "n", "o", "q", "u", "s", "t", "v", "w", "x", "z", "warnings", "straightRow", "keyPattern", "simpleRepeat", "extendedRepeat", "sequences", "recentYears", "dates", "topTen", "topHundred", "common", "similarToCommon", "wordByItself", "namesByThemselves", "commonNames", "userInputs", "pwned", "suggestions", "l33t", "reverseWords", "allUppercase", "capitalization", "associatedYears", "repeated", "longerKeyboardPattern", "anotherWord", "useWords", "no<PERSON><PERSON>", "timeEstimation", "ltSecond", "second", "seconds", "minute", "minutes", "hour", "hours", "day", "days", "month", "months", "year", "years", "centuries", "TrieNode", "constructor", "parents", "children", "Map", "addSub", "key", "subs", "firstChar", "char<PERSON>t", "has", "set", "cur", "get", "i", "length", "c", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "concat", "child", "isTerminal", "l33tTable", "triNode", "Object", "entries", "for<PERSON>ach", "letter", "substitutions", "substitution", "addSub", "Options", "constructor", "matchers", "l33tTable", "trieNodeRoot", "l33tTableToTrieNode", "TrieNode", "dictionary", "userInputs", "rankedDictionaries", "rankedDictionariesMaxWordSize", "translations", "translationKeys", "graphs", "useLevenshteinDistance", "levenshteinThreshold", "l33tMaxSubstitutions", "max<PERSON><PERSON><PERSON>", "setRankedDictionaries", "setOptions", "options", "setTranslations", "undefined", "checkCustomTranslations", "Error", "valid", "Object", "keys", "for<PERSON>ach", "type", "translationType", "key", "rankedDictionariesMaxWorkSize", "name", "buildRankedDictionary", "getRankedDictionariesMaxWordSize", "list", "data", "map", "el", "toString", "length", "reduce", "a", "b", "Math", "max", "buildSanitizedRankedDictionary", "sanitizedInputs", "input", "inputType", "push", "toLowerCase", "extendUserInputsDictionary", "newList", "addMatcher", "matcher", "console", "info", "zxcvbnOptions", "MatchReverse", "constructor", "defaultMatch", "match", "password", "passwordReversed", "split", "reverse", "join", "map", "token", "reversed", "i", "length", "j", "CleanPasswords", "constructor", "substr", "limit", "trieRoot", "buffer", "finalPasswords", "getAllPossibleSubsAtIndex", "index", "nodes", "cur", "i", "length", "character", "char<PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "push", "helper", "only<PERSON>ull<PERSON><PERSON>", "isFullSub", "subIndex", "changes", "lastSubLetter", "consecutiveSubCount", "password", "join", "hasSubs", "isTerminal", "parents", "subs", "sub", "newSubs", "concat", "letter", "substitution", "pop", "firstChar", "getAll", "undefined", "getCleanPasswords", "getExtras", "passwordWithSubs", "i", "j", "previous<PERSON><PERSON><PERSON>", "changes", "filter", "iUnsubbed", "reduce", "value", "change", "letter", "length", "substitution", "usedChanges", "jUnsubbed", "filtered", "subDisplay", "for<PERSON>ach", "existingIndex", "findIndex", "t", "push", "subs", "join", "MatchL33t", "constructor", "defaultMatch", "isAlreadyIncluded", "matches", "newMatch", "some", "l33tMatch", "Object", "entries", "every", "key", "match", "password", "subbedPasswords", "getCleanPasswords", "zxcvbnOptions", "l33tMaxSubstitutions", "trieNodeRoot", "hasFullMatch", "isFullSubstitution", "subbedPassword", "matchedDictionary", "useLevenshtein", "extras", "token", "slice", "l33t", "alreadyIncluded", "toLowerCase", "matchedWord", "MatchDictionary", "constructor", "l33t", "L33t", "defaultMatch", "reverse", "Reverse", "match", "password", "matches", "sorted", "useLevenshtein", "<PERSON><PERSON><PERSON><PERSON>", "length", "passwordLower", "toLowerCase", "Object", "keys", "zxcvbnOptions", "rankedDictionaries", "for<PERSON>ach", "dictionaryName", "rankedDict", "longestDictionaryWordSize", "rankedDictionariesMaxWordSize", "searchWidth", "Math", "min", "i", "searchEnd", "j", "usedPassword", "slice", "isInDictionary", "foundLevenshteinDistance", "isFullPassword", "useLevenshteinDistance", "findLevenshteinDistance", "levenshteinThreshold", "isLevenshteinMatch", "usedRankPassword", "levenshteinDistanceEntry", "rank", "push", "pattern", "token", "matchedWord", "reversed", "MatchRegex", "match", "password", "regexes", "REGEXEN", "matches", "Object", "keys", "for<PERSON>ach", "name", "regex", "lastIndex", "regexMatch", "exec", "token", "push", "pattern", "i", "index", "j", "length", "regexName", "sorted", "nCk", "n", "k", "count", "<PERSON><PERSON><PERSON>", "i", "log10", "Math", "log", "log2", "factorial", "num", "rval", "token", "guesses", "BRUTEFORCE_CARDINALITY", "length", "Number", "POSITIVE_INFINITY", "MAX_VALUE", "minGuesses", "MIN_SUBMATCH_GUESSES_SINGLE_CHAR", "MIN_SUBMATCH_GUESSES_MULTI_CHAR", "Math", "max", "year", "separator", "yearSpace", "Math", "max", "abs", "REFERENCE_YEAR", "MIN_YEAR_SPACE", "guesses", "getVariations", "cleaned<PERSON><PERSON>", "wordArray", "split", "upperCaseCount", "filter", "char", "match", "ONE_UPPER", "length", "lowerCaseCount", "ONE_LOWER", "variations", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "i", "utils", "nCk", "word", "replace", "ALPHA_INVERTED", "ALL_LOWER_INVERTED", "toLowerCase", "commonCases", "START_UPPER", "END_UPPER", "ALL_UPPER_INVERTED", "commonCasesLength", "regex", "countSubstring", "string", "substring", "count", "pos", "indexOf", "length", "getCounts", "sub", "token", "tokenLower", "toLowerCase", "subbedCount", "substitution", "unsubbedCount", "letter", "l33t", "subs", "variations", "for<PERSON>ach", "p", "Math", "min", "possibilities", "i", "utils", "nCk", "rank", "reversed", "l33t", "subs", "token", "dictionaryName", "baseGuesses", "uppercaseVariations", "uppercaseVariant", "l33tVariations", "l33tVariant", "reversedVariations", "calculation", "regexName", "regexMatch", "token", "charClassBases", "alphaLower", "alphaUpper", "alpha", "alphanumeric", "digits", "symbols", "length", "Math", "max", "abs", "parseInt", "REFERENCE_YEAR", "MIN_YEAR_SPACE", "baseGuesses", "repeatCount", "token", "ascending", "firstChr", "char<PERSON>t", "baseGuesses", "startingPoints", "includes", "match", "length", "calcAverageDegree", "graph", "average", "Object", "keys", "for<PERSON>ach", "key", "neighbors", "filter", "entry", "length", "entries", "estimatePossiblePatterns", "token", "turns", "startingPosition", "zxcvbnOptions", "graphs", "averageDegree", "guesses", "token<PERSON><PERSON>th", "i", "possibleTurns", "Math", "min", "j", "utils", "nCk", "shiftedCount", "unShiftedCount", "shiftedVariations", "round", "SEPERATOR_CHAR_COUNT", "getMinGuesses", "match", "password", "minGuesses", "token", "length", "MIN_SUBMATCH_GUESSES_SINGLE_CHAR", "MIN_SUBMATCH_GUESSES_MULTI_CHAR", "matchers", "bruteforce", "bruteforceMatcher", "date", "date<PERSON><PERSON><PERSON>", "dictionary", "dictionaryMatcher", "regex", "regexMatcher", "repeat", "repeatM<PERSON>er", "sequence", "sequenceMatcher", "spatial", "spatialMatcher", "separator", "separatorM<PERSON>er", "getScoring", "name", "zxcvbnOptions", "scoring", "extraData", "guesses", "estimationResult", "pattern", "calculation", "baseGuesses", "uppercaseVariations", "l33tVariations", "matchGuesses", "Math", "max", "guessesLog10", "utils", "log10", "<PERSON><PERSON><PERSON><PERSON>", "password", "optimal", "excludeAdditive", "separatorRegex", "undefined", "fillA<PERSON>y", "size", "valueType", "result", "i", "value", "push", "makeBruteforceMatch", "j", "pattern", "token", "slice", "update", "match", "sequenceLength", "k", "estimatedMatch", "estimateGuesses", "pi", "guesses", "g", "utils", "factorial", "MIN_GUESSES_BEFORE_GROWING_SEQUENCE", "shouldSkip", "Object", "keys", "for<PERSON>ach", "competingPatternLength", "competingMetricMatch", "parseInt", "m", "bruteforceUpdate", "passwordCharIndex", "tmp", "lastMatch", "unwind", "<PERSON><PERSON><PERSON><PERSON>", "optimalMatchSequence", "temp", "candidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candidate<PERSON>etric<PERSON><PERSON>", "unshift", "mostGuessableMatchSequence", "matches", "length", "matchesByCoordinateJ", "map", "sort", "m1", "m2", "optimalSequenceLength", "getGuesses", "guessesLog10", "log10", "sequence", "MatchRepeat", "match", "password", "omniMatch", "matches", "lastIndex", "length", "greedyMatch", "getGreedyMatch", "lazyMatch", "getLazyMatch", "baseToken", "setMatchToken", "j", "index", "baseGuesses", "getBaseGuesses", "push", "normalizeMatch", "hasPromises", "some", "Promise", "all", "baseMatch", "pattern", "i", "token", "repeatCount", "then", "resolvedBaseGuesses", "greedy", "exec", "lazy", "lazyAnchored", "temp", "resolvedMatches", "baseAnalysis", "scoring", "mostGuessableMatchSequence", "guesses", "MatchSequence", "constructor", "MAX_DELTA", "match", "password", "result", "length", "i", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "k", "delta", "charCodeAt", "j", "update", "Math", "abs", "absoluteDelta", "token", "slice", "sequenceName", "sequenceSpace", "getSequence", "push", "pattern", "ascending", "ALL_LOWER", "test", "ALL_UPPER", "ALL_DIGIT", "MatchSpatial", "constructor", "SHIFTED_RX", "match", "password", "matches", "Object", "keys", "zxcvbnOptions", "graphs", "for<PERSON>ach", "graphName", "graph", "extend", "helper", "sorted", "checkIfShifted", "index", "includes", "test", "char<PERSON>t", "shiftedCount", "i", "<PERSON><PERSON><PERSON><PERSON>", "length", "j", "lastDirection", "turns", "prevChar", "adjacents", "found", "foundDirection", "curDirection", "cur<PERSON><PERSON>", "adjacentsLength", "k", "adjacent", "adjacentIndex", "indexOf", "push", "pattern", "token", "slice", "separatorRegex", "RegExp", "SEPERATOR_CHARS", "join", "MatchSeparator", "getMostUsedSeparatorChar", "password", "mostUsedSeperators", "split", "filter", "c", "test", "reduce", "memo", "m", "get", "set", "Map", "entries", "sort", "_a", "a", "_b", "b", "length", "undefined", "match", "getSeparatorRegex", "separator", "result", "mostUsedSpecial", "isSeparator", "matchAll", "index", "i", "push", "pattern", "token", "j", "Matching", "constructor", "matchers", "date", "date<PERSON><PERSON><PERSON>", "dictionary", "dictionaryMatcher", "regex", "regexMatcher", "repeat", "repeatM<PERSON>er", "sequence", "sequenceMatcher", "spatial", "spatialMatcher", "separator", "separatorM<PERSON>er", "match", "password", "matches", "promises", "Object", "keys", "zxcvbnOptions", "for<PERSON>ach", "key", "Matcher", "usedMatcher", "result", "omniMatch", "Promise", "then", "response", "extend", "push", "length", "resolve", "reject", "all", "sorted", "catch", "error", "SECOND", "MINUTE", "HOUR", "DAY", "MONTH", "YEAR", "CENTURY", "times", "second", "minute", "hour", "day", "month", "year", "century", "TimeEstimates", "translate", "displayStr", "value", "key", "undefined", "timeEstimation", "zxcvbnOptions", "translations", "replace", "estimateAttackTimes", "guesses", "crackTimesSeconds", "onlineThrottling100PerHour", "onlineNoThrottling10PerSecond", "offlineSlowHashing1e4PerSecond", "offlineFastHashing1e10PerSecond", "crackTimesDisplay", "Object", "keys", "for<PERSON>ach", "scenario", "seconds", "displayTime", "score", "guessesToScore", "DELTA", "base", "timeKeys", "foundIndex", "findIndex", "time", "Math", "round", "bruteforceMatcher", "date<PERSON><PERSON><PERSON>", "warning", "zxcvbnOptions", "translations", "warnings", "dates", "suggestions", "getDictionaryWarningPassword", "match", "isSoleMatch", "warning", "l33t", "reversed", "rank", "zxcvbnOptions", "translations", "warnings", "topTen", "topHundred", "common", "guessesLog10", "similarToCommon", "getDictionaryWarningWikipedia", "wordByItself", "getDictionaryWarningNames", "namesByThemselves", "commonNames", "getDictionaryWarning", "dictName", "dictionaryName", "isAName", "toLowerCase", "includes", "userInputs", "dictionaryMatcher", "suggestions", "word", "token", "START_UPPER", "push", "capitalization", "ALL_UPPER_INVERTED", "allUppercase", "length", "reverseWords", "regexMatcher", "match", "regexName", "warning", "zxcvbnOptions", "translations", "warnings", "recentYears", "suggestions", "associatedYears", "repeatM<PERSON>er", "match", "warning", "zxcvbnOptions", "translations", "warnings", "extendedRepeat", "baseToken", "length", "simpleRepeat", "suggestions", "repeated", "sequenceMatcher", "warning", "zxcvbnOptions", "translations", "warnings", "sequences", "suggestions", "spatialMatcher", "match", "warning", "zxcvbnOptions", "translations", "warnings", "keyPattern", "turns", "straightRow", "suggestions", "longerKeyboardPattern", "separatorM<PERSON>er", "defaultFeedback", "warning", "suggestions", "<PERSON><PERSON><PERSON>", "constructor", "matchers", "bruteforce", "bruteforceMatcher", "date", "date<PERSON><PERSON><PERSON>", "dictionary", "dictionaryMatcher", "regex", "regexMatcher", "repeat", "repeatM<PERSON>er", "sequence", "sequenceMatcher", "spatial", "spatialMatcher", "separator", "separatorM<PERSON>er", "setDefaultSuggestions", "push", "zxcvbnOptions", "translations", "useWords", "no<PERSON><PERSON>", "getFeedback", "score", "length", "extraFeedback", "anotherWord", "longestMatch", "getLongestMatch", "feedback", "getMatchFeedback", "undefined", "unshift", "slicedSequence", "slice", "for<PERSON>ach", "match", "token", "isSoleMatch", "pattern", "func", "wait", "isImmediate", "timeout", "debounce", "args", "context", "later", "undefined", "apply", "shouldCallNow", "clearTimeout", "setTimeout", "time", "Date", "getTime", "createReturnValue", "resolvedMatches", "password", "start", "feedback", "<PERSON><PERSON><PERSON>", "timeEstimates", "TimeEstimates", "matchSequence", "scoring", "mostGuessableMatchSequence", "calcTime", "attackTimes", "estimateAttackTimes", "guesses", "getFeedback", "score", "sequence", "main", "userInputs", "zxcvbnOptions", "extendUserInputsDictionary", "matching", "Matching", "match", "zxcvbn", "matches", "Promise", "Error", "zxcvbnAsync", "usedPassword", "substring", "max<PERSON><PERSON><PERSON>"]}