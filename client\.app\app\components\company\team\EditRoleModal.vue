<template>
  <TairoModal :open="isOpen" size="xs" @close="$emit('close')">
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Change Role
        </h3>
        <BaseButtonClose @click="$emit('close')" />
      </div>
    </template>
    <div class="p-4 md:p-6">
      <div v-if="member">
        <p class="text-muted-500 dark:text-muted-400 mb-4">
          Change role for {{ member.user?.firstName || 'Unknown' }}
          {{ member.user?.lastName || '' }}
        </p>

        <BaseListbox
          v-model="selectedRole"
          :items="roleOptions"
          :properties="{ value: 'value', label: 'label' }"
          label="Role"
          placeholder="Select role"
          size="sm"
          rounded="md"
          :error="error"
          required
        />
      </div>

      <div class="flex justify-end space-x-2 mt-4">
        <BaseButton type="button" color="muted" @click="$emit('close')"> Cancel </BaseButton>
        <BaseButton type="button" color="primary" @click="updateRole" :loading="isSubmitting">
          Update Role
        </BaseButton>
      </div>
    </div>
  </TairoModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  member: {
    type: Object,
    default: undefined,
  },
  isSubmitting: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close', 'update'])

// Role options - filter out OWNER role for editing
const roleOptions = [
  { label: 'Admin', value: 'ADMIN' },
  { label: 'Project Leader', value: 'PROJECTLEADER' },
  { label: 'Salesman', value: 'SALESMAN' },
  { label: 'Worker', value: 'WORKER' },
  { label: 'Client', value: 'CLIENT' },
]

// Selected role
const selectedRole = ref('')
const error = ref('')

// Watch for member changes to update the selected role
watch(
  () => props.member,
  (newMember) => {
    if (newMember) {
      selectedRole.value = newMember.role || ''
    }
  },
  { immediate: true }
)

// Update role
const updateRole = () => {
  if (!selectedRole.value) {
    error.value = 'Role is required'
    return
  }

  // Ensure role is properly formatted
  let roleValue = selectedRole.value

  // If role is an object from BaseListbox, extract the value
  if (typeof roleValue === 'object' && roleValue !== null && roleValue.value) {
    console.log('EditRoleModal: Converting role object to string value:', roleValue)
    roleValue = roleValue.value
  }

  error.value = ''
  console.log('EditRoleModal: Emitting update with role:', roleValue)
  emit('update', roleValue)
}
</script>
