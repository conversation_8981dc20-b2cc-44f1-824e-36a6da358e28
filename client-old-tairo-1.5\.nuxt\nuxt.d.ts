// Generated by nuxi
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@nuxt/eslint" />
/// <reference types="@nuxtjs/ionic" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="@cssninja/nuxt-toaster" />
/// <reference types="@nuxtjs/tailwindcss" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxt/icon" />
/// <reference types="@nuxtjs/color-mode" />
/// <reference types="@nuxt/icon" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference path="types/i18n-plugin.d.ts" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />
/// <reference path="./eslint-typegen.d.ts" />

export {}
