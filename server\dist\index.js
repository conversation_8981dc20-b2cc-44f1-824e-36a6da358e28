"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_session_1 = __importDefault(require("express-session"));
const cors_1 = __importDefault(require("cors"));
const http_errors_1 = __importDefault(require("http-errors"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = require("path");
const redis_1 = require("redis");
// Import RedisStore as a default import
const connect_redis_1 = __importDefault(require("connect-redis"));
// Import passport config
const passport_1 = __importDefault(require("./config/passport"));
// Import database disconnect function for graceful shutdown
const prisma_1 = require("./lib/prisma");
// Import subscription check scheduler
const check_subscriptions_1 = require("./scripts/check-subscriptions");
// Routes
const auth_1 = __importDefault(require("./routes/auth"));
const user_1 = __importDefault(require("./routes/user"));
const project_1 = __importDefault(require("./routes/project"));
const budget_1 = __importDefault(require("./routes/budget"));
const accounting_1 = __importDefault(require("./routes/accounting"));
const production_1 = __importDefault(require("./routes/production"));
const hr_1 = __importDefault(require("./routes/hr"));
const companies_1 = __importDefault(require("./routes/companies"));
const ai_1 = __importDefault(require("./routes/ai"));
const core_1 = __importDefault(require("./routes/core"));
const consolidatedSubscriptionRoutes_1 = __importDefault(require("./routes/consolidatedSubscriptionRoutes"));
const subscriptionPlanRoutes_1 = __importDefault(require("./routes/subscriptionPlanRoutes"));
const email_1 = __importDefault(require("./routes/email"));
const sse_1 = __importDefault(require("./routes/sse"));
const communication_1 = __importDefault(require("./routes/communication"));
const sales_1 = __importDefault(require("./routes/sales"));
const workforceRoutes_1 = __importDefault(require("./routes/recruitment/workforceRoutes"));
const formBuilderRoutes_1 = __importDefault(require("./routes/recruitment/formBuilderRoutes"));
const recruitment_1 = __importDefault(require("./routes/recruitment"));
const database_1 = __importDefault(require("./routes/database"));
const paymentRoutes_1 = __importDefault(require("./routes/paymentRoutes"));
const companyJoinRequest_1 = __importDefault(require("./routes/companyJoinRequest"));
const uploads_1 = __importDefault(require("./routes/uploads"));
const project_locations_routes_1 = __importDefault(require("./routes/timemanagement/project-locations.routes"));
const waitlist_1 = __importDefault(require("./routes/waitlist"));
const crowdfunding_1 = __importDefault(require("./routes/crowdfunding"));
const calendar_1 = __importDefault(require("./routes/calendar"));
const map_routes_1 = __importDefault(require("./routes/map.routes"));
// Load environment variables based on environment
const envFile = process.env.NODE_ENV === "production" ? ".env.production" : ".env";
dotenv_1.default.config({ path: (0, path_1.join)(__dirname, envFile) });
const app = (0, express_1.default)();
const port = process.env.PORT || 4004;
// CORS configuration
app.use((0, cors_1.default)({
    origin: process.env.NODE_ENV === "production" ? "https://comanager.ee" : true,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization", "Accept", "x-api-key"],
}));
// Enhanced Middleware
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
// Initialize Redis client and session store
let redisClient = null;
let sessionConfig;
if (process.env.NODE_ENV === "production") {
    // Production Redis configuration
    redisClient = (0, redis_1.createClient)({
        url: process.env.REDIS_URL || "redis://redis:6379",
        legacyMode: false,
    });
    // Connect to Redis
    (async () => {
        try {
            await redisClient.connect();
            console.log("Connected to Redis successfully");
        }
        catch (err) {
            console.error("Redis connection error:", err);
        }
    })();
    // Handle Redis errors
    redisClient.on("error", (err) => {
        console.error("Redis error:", err);
    });
    // Session configuration with Redis store
    sessionConfig = {
        store: new connect_redis_1.default({
            client: redisClient,
            prefix: "comanager:",
        }),
        secret: process.env.JWT_SECRET,
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: true,
            maxAge: 1000 * 60 * 60 * 24, // 1 day
        },
    };
}
else {
    // Development configuration - still using MemoryStore but with a warning
    console.warn("Using MemoryStore for session storage. This is not recommended for production.");
    sessionConfig = {
        secret: process.env.JWT_SECRET,
        resave: false,
        saveUninitialized: false,
        cookie: { secure: false },
    };
}
// Apply session middleware
app.use((0, express_session_1.default)(sessionConfig));
// Use the imported passport configuration
app.use(passport_1.default.initialize());
app.use(passport_1.default.session());
// Static file serving
app.use("/uploads", express_1.default.static((0, path_1.join)(__dirname, "public", "uploads")));
app.use("/downloads", express_1.default.static((0, path_1.join)(__dirname, "public", "downloads")));
// API Routes
app.use("/api/v1/core", core_1.default);
app.use("/api/v1/auth", auth_1.default);
app.use("/api/v1/users", user_1.default);
app.use("/api/v1/projects", project_1.default);
app.use("/api/v1/budget", budget_1.default);
app.use("/api/v1/accounting", accounting_1.default);
app.use("/api/v1/production", production_1.default);
app.use("/api/v1/hr", hr_1.default);
app.use("/api/v1/companies", companies_1.default);
app.use("/api/v1/ai", ai_1.default);
app.use("/api/v1/sales", sales_1.default);
app.use("/api/v1/subscriptions", consolidatedSubscriptionRoutes_1.default);
app.use("/api/v1/subscription-plans", subscriptionPlanRoutes_1.default);
app.use("/api/v1/email", email_1.default);
app.use("/api/v1/sse", sse_1.default);
app.use("/api/v1/communication", communication_1.default);
app.use("/api/v1/recruitment/workforce", workforceRoutes_1.default);
app.use("/api/v1/recruitment/forms", formBuilderRoutes_1.default);
app.use("/api/v1/recruitment", recruitment_1.default);
app.use("/api/v1/core/database", database_1.default);
app.use("/api/v1/payment", paymentRoutes_1.default);
app.use("/api/v1/recruitment/company-requests", companyJoinRequest_1.default);
app.use("/api/v1/uploads", uploads_1.default);
app.use("/api/v1/timemanagement/project-locations", project_locations_routes_1.default);
app.use("/api/v1/waitlist", waitlist_1.default);
app.use("/api/v1/crowdfunding", crowdfunding_1.default);
app.use("/api/v1/calendar", calendar_1.default);
app.use("/api/v1/map", map_routes_1.default);
// 404 handling
app.use((_req, _res, next) => {
    next((0, http_errors_1.default)(404, "Not Found"));
});
// Enhanced error handler
// Cast to any to avoid TypeScript errors with error middleware
app.use((err, _req, res, _next) => {
    console.error("Global Error Handler:", err.message);
    res.status(err.status || 500).json({
        message: err.message,
        stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
    });
});
app.listen(port, () => {
    console.log(`Server running on port ${port} in ${process.env.NODE_ENV} mode`);
    // Start subscription check scheduler
    (0, check_subscriptions_1.scheduleSubscriptionCheck)();
});
// Graceful shutdown handlers
// SIGINT is the signal sent when you press Ctrl+C in the terminal
process.on("SIGINT", async () => {
    console.log("Server shutdown initiated (Ctrl+C pressed). Closing connections...");
    await (0, prisma_1.disconnectPrisma)();
    // Close Redis connection if it exists
    if (redisClient !== null && process.env.NODE_ENV === "production") {
        await redisClient.quit();
        console.log("Redis connection closed.");
    }
    console.log("All connections closed. Server shutdown complete.");
    process.exit(0);
});
// SIGTERM is the signal sent by container orchestrators (like Docker, Kubernetes)
// when they want to gracefully shut down the application
process.on("SIGTERM", async () => {
    console.log("Server shutdown requested by system. Closing connections...");
    await (0, prisma_1.disconnectPrisma)();
    // Close Redis connection if it exists
    if (redisClient !== null && process.env.NODE_ENV === "production") {
        await redisClient.quit();
        console.log("Redis connection closed.");
    }
    console.log("All connections closed. Server shutdown complete.");
    process.exit(0);
});
exports.default = app;
//# sourceMappingURL=index.js.map