#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/devtools-wizard/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/devtools-wizard/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nuxt/devtools-wizard/cli.mjs" "$@"
else
  exec node  "$basedir/../@nuxt/devtools-wizard/cli.mjs" "$@"
fi
