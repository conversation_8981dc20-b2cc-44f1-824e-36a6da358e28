@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules\detect-libc\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules\detect-libc\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules\detect-libc\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules\detect-libc\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\detect-libc@1.0.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\detect-libc\bin\detect-libc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\detect-libc\bin\detect-libc.js" %*
)
