// client/.app/app/utils/api/client.ts

import type { AxiosError, AxiosInstance, AxiosResponse } from "axios";
import axios from "axios";

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export class ApiClient {
  private static instance: ApiClient | null = null;
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000, // Increased timeout to 30 seconds for slower operations like registration
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
  }

  public static getInstance(): ApiClient {
    // This will be properly initialized in the plugin
    if (!ApiClient.instance) {
      // Create a temporary instance with the correct development URL
      // This will be replaced by the proper instance from the plugin
      const baseURL =
        process.env.NODE_ENV === "development"
          ? "http://localhost:4004/api/v1"
          : "https://comanager.ee/api/v1";
      ApiClient.instance = new ApiClient(baseURL);
    }
    return ApiClient.instance;
  }

  // Method to set the singleton instance (called from plugin)
  public static setInstance(instance: ApiClient): void {
    ApiClient.instance = instance;
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Get token from localStorage or sessionStorage instead of useCookie
        const token =
          localStorage.getItem("authToken") ||
          sessionStorage.getItem("authToken");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(this.handleError(error))
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => Promise.reject(this.handleError(error))
    );
  }

  private handleError(error: AxiosError): ApiError {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to auth page
      if (typeof window !== "undefined") {
        window.location.href = "/";
      }
    }

    // Extract message from response data
    let errorMessage = error.message;
    let errorDetails = {};

    if (error.response?.data) {
      const responseData = error.response.data as any;

      // Check if the response has a message property
      if (typeof responseData === "object" && responseData.message) {
        errorMessage = responseData.message;
      } else if (typeof responseData === "string") {
        // If the response is a string, use it as the message
        errorMessage = responseData;
      }

      // Store the full response data as details
      errorDetails = responseData;
    }

    return {
      code: error.response?.status?.toString() || "UNKNOWN",
      message: errorMessage,
      details: errorDetails,
    };
  }

  public get<T>(url: string, params?: Record<string, any>): Promise<T> {
    console.log(`[API Client] GET request to ${url}`, { params });

    return this.axiosInstance
      .get<T>(url, { params })
      .then((response: AxiosResponse<T>) => {
        console.log(`[API Client] GET response from ${url}:`, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
        });

        // Check if the response is HTML instead of JSON
        if (
          typeof response.data === "string" &&
          response.data.includes("<!DOCTYPE html>")
        ) {
          console.error("[API Client] Received HTML response instead of JSON");
          console.error(
            "[API Client] Response preview:",
            (response.data as string).substring(0, 200) + "..."
          );
        }

        return response.data;
      })
      .catch((error) => {
        console.error(`[API Client] GET error for ${url}:`, {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers,
                data: error.response.data,
              }
            : "No response",
          request: error.request ? "Request exists" : "No request",
        });
        throw error;
      });
  }

  public post<T>(url: string, data?: any): Promise<T> {
    console.log(`[API Client] POST request to ${url}`, { data });

    return this.axiosInstance
      .post<T>(url, data)
      .then((response: AxiosResponse<T>) => {
        console.log(`[API Client] POST response from ${url}:`, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
        });

        // Check if the response is HTML instead of JSON
        if (
          typeof response.data === "string" &&
          response.data.includes("<!DOCTYPE html>")
        ) {
          console.error("[API Client] Received HTML response instead of JSON");
          console.error(
            "[API Client] Response preview:",
            (response.data as string).substring(0, 200) + "..."
          );
        }

        return response.data;
      })
      .catch((error) => {
        console.error(`[API Client] POST error for ${url}:`, {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers,
                data: error.response.data,
              }
            : "No response",
          request: error.request ? "Request exists" : "No request",
        });
        throw error;
      });
  }

  public put<T>(url: string, data?: any): Promise<T> {
    return this.axiosInstance
      .put<T>(url, data)
      .then((response: AxiosResponse<T>) => response.data);
  }

  public delete<T>(url: string): Promise<T> {
    return this.axiosInstance
      .delete<T>(url)
      .then((response: AxiosResponse<T>) => response.data);
  }

  public postFormData<T>(url: string, formData: FormData): Promise<T> {
    console.log(`[API Client] POST FormData request to ${url}`);
    console.log(`[API Client] FormData contents:`, formData);

    return this.axiosInstance
      .post<T>(url, formData, {
        headers: {
          // Explicitly remove Content-Type to let browser set it with boundary
          "Content-Type": undefined,
        },
      })
      .then((response: AxiosResponse<T>) => {
        console.log(`[API Client] POST FormData response from ${url}:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
        });
        return response.data;
      })
      .catch((error) => {
        console.error(`[API Client] POST FormData error for ${url}:`, {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                statusText: error.response.statusText,
                data: error.response.data,
              }
            : "No response",
        });
        throw this.handleError(error);
      });
  }

  public postBinary(url: string, data?: any): Promise<ArrayBuffer> {
    console.log(`[API Client] POST Binary request to ${url}`, { data });

    return this.axiosInstance
      .post(url, data, {
        responseType: "arraybuffer",
        headers: {
          "Content-Type": "application/json",
        },
      })
      .then((response: AxiosResponse<ArrayBuffer>) => {
        console.log(`[API Client] POST Binary response from ${url}:`, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          dataLength: response.data.byteLength,
        });
        return response.data;
      })
      .catch((error) => {
        console.error(`[API Client] POST Binary error for ${url}:`, {
          message: error.message,
          response: error.response
            ? {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers,
                dataLength: error.response.data?.byteLength || 0,
              }
            : "No response",
        });
        throw this.handleError(error);
      });
  }
}

// Export a function to create a new instance
export function createApiClient(baseURL: string): ApiClient {
  const instance = new ApiClient(baseURL);
  ApiClient.setInstance(instance);
  return instance;
}

// This will be properly initialized in the plugin
export const apiClient = ApiClient.getInstance();
