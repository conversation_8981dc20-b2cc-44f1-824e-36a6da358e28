<template>
  <div
    class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-5 bg-white dark:bg-muted-900 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 mb-3 border border-muted-200 dark:border-muted-700"
  >
    <!-- User Info -->
    <div class="flex items-center mb-3 sm:mb-0 gap-6">
      <BaseAvatar
        :src="member.user?.avatar || '../../../../../public/img/avatars/cute-astronout.svg'"
        size="lg"
        :squared="false"
      />
      <div class="flex flex-col">
        <h3 class="font-medium text-muted-900 dark:text-white text-base">
          {{ member.user?.firstName || 'Unknown' }} {{ member.user?.lastName || '' }}
        </h3>
        <p class="text-xs text-muted-400 mt-1">{{ member.user?.email || 'No email' }}</p>
      </div>
    </div>

    <!-- Member Details -->
    <div class="flex flex-wrap items-center gap-6 w-full sm:w-auto">
      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1 font-medium">Role</span>
        <BaseTag :color="getRoleColor(member.role)" flavor="pastel" condensed size="sm">
          {{ formatRole(member.role) }}
        </BaseTag>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1 font-medium">Status</span>
        <BaseTag
          :color="getStatusColor(member.status || 'ACTIVE')"
          flavor="pastel"
          condensed
          size="sm"
        >
          {{ member.status || 'Active' }}
        </BaseTag>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1 font-medium">Joined</span>
        <span class="text-sm text-muted-700 dark:text-muted-300 font-medium">
          {{ formatDate(member.createdAt) }}
        </span>
      </div>

      <div class="flex flex-col">
        <span class="text-xs text-muted-400 mb-1 font-medium">Actions</span>
        <div class="flex items-center space-x-2">
          <BaseButtonIcon
            v-if="canEdit"
            color="info"
            size="xs"
            tooltip="Edit Role"
            @click="$emit('edit', member)"
          >
            <Icon name="ph:pencil-duotone" class="h-4 w-4" />
          </BaseButtonIcon>
          <BaseButtonIcon
            v-if="canRemove"
            color="danger"
            size="xs"
            tooltip="Remove Member"
            @click="$emit('remove', member)"
          >
            <Icon name="ph:trash-duotone" class="h-4 w-4" />
          </BaseButtonIcon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

defineProps({
  member: {
    type: Object,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
  canRemove: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['edit', 'remove'])

// Helper functions
const formatRole = (role: string): string => {
  switch (role) {
    case 'ADMIN':
      return 'Admin'
    case 'PROJECTLEADER':
      return 'Project Leader'
    case 'SALESMAN':
      return 'Salesman'
    case 'WORKER':
      return 'Worker'
    case 'CLIENT':
      return 'Client'
    case 'OWNER':
      return 'Owner'
    default:
      return role || 'Unknown'
  }
}

const getRoleColor = (role: string): string => {
  switch (role) {
    case 'ADMIN':
      return 'info'
    case 'PROJECTLEADER':
      return 'warning'
    case 'SALESMAN':
      return 'purple'
    case 'WORKER':
      return 'success'
    case 'CLIENT':
      return 'yellow'
    case 'OWNER':
      return 'primary'
    default:
      return 'muted'
  }
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'INACTIVE':
      return 'danger'
    default:
      return 'muted'
  }
}

const formatDate = (dateString: string): string => {
  try {
    return format(new Date(dateString), 'MMM d, yyyy')
  } catch {
    return dateString || 'Unknown'
  }
}
</script>
