// Global AI Assistant Manager
// Coordinates between the AI button and the AI window

import { ref } from 'vue';

// Global state for AI assistant window
const isWindowOpen = ref(false);
const windowComponent = ref<any>(null);

export const useAiAssistantManager = () => {
  // Register the window component reference
  const registerWindow = (component: any) => {
    windowComponent.value = component;
  };

  // Open the AI window
  const openWindow = () => {
    if (windowComponent.value) {
      windowComponent.value.openWindow();
      isWindowOpen.value = true;
    }
  };

  // Close the AI window
  const closeWindow = () => {
    if (windowComponent.value) {
      windowComponent.value.closeWindow();
      isWindowOpen.value = false;
    }
  };

  // Toggle the AI window
  const toggleWindow = () => {
    if (isWindowOpen.value) {
      closeWindow();
    } else {
      openWindow();
    }
  };

  return {
    isWindowOpen,
    registerWindow,
    openWindow,
    closeWindow,
    toggleWindow,
  };
};
