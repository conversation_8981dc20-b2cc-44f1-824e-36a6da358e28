<script setup lang="ts">
import { ref } from "vue";

// Current month data
const currentMonth = ref("September");
const currentYear = ref(2025);

// Calendar data
const days = ref(["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sat"]);
const weeks = ref([
  [null, null, null, null, null, 1, 2],
  [3, 4, 5, 6, 7, 8, 9],
  [10, 11, 12, 13, 14, 15, 16],
  [17, 18, 19, 20, 21, 22, 23],
  [24, 25, 26, 27, 28, 29, 30],
]);

// Events data
const events = ref([
  { day: 5, title: "Team Meeting", type: "meeting" },
  { day: 12, title: "Project Deadline", type: "deadline" },
  { day: 15, title: "Client Call", type: "call" },
  { day: 20, title: "Product Launch", type: "event" },
  { day: 25, title: "Training Session", type: "training" },
]);

// Function to check if a day has an event
function hasEvent(day: number | null) {
  if (day === null) return false;
  return events.value.some((event) => event.day === day);
}

// Function to get event type for a day
function getEventType(day: number | null) {
  if (day === null) return null;
  const event = events.value.find((event) => event.day === day);
  return event ? event.type : null;
}

// Function to get event color based on type
function getEventColor(type: string | null) {
  if (!type) return "";

  const colors = {
    meeting: "bg-primary-500",
    deadline: "bg-danger-500",
    call: "bg-info-500",
    event: "bg-success-500",
    training: "bg-warning-500",
  };

  return colors[type as keyof typeof colors] || "bg-muted-500";
}

// Current day
const today = ref(15);
</script>

<template>
  <div class="h-full w-full">
    <!-- Calendar header -->
    <div class="flex items-center justify-between mb-2">
      <div class="text-sm font-medium text-muted-800 dark:text-white">
        {{ currentMonth }} {{ currentYear }}
      </div>
      <div class="flex gap-1">
        <button class="p-1 rounded-md bg-muted-100 dark:bg-muted-800">
          <Icon name="lucide:chevron-left" class="size-3 text-muted-500" />
        </button>
        <button class="p-1 rounded-md bg-muted-100 dark:bg-muted-800">
          <Icon name="lucide:chevron-right" class="size-3 text-muted-500" />
        </button>
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="grid grid-cols-7 gap-px">
      <!-- Day headers -->
      <div
        v-for="day in days"
        :key="day"
        class="text-center text-xs font-medium text-muted-500 py-1"
      >
        {{ day }}
      </div>

      <!-- Calendar days -->
      <template v-for="(week, weekIndex) in weeks" :key="weekIndex">
        <div
          v-for="(day, dayIndex) in week"
          :key="`${weekIndex}-${dayIndex}`"
          :class="[
            'h-8 flex flex-col items-center justify-center rounded-sm text-xs',
            day === today
              ? 'bg-primary-500 text-white'
              : 'bg-muted-100 dark:bg-muted-800 text-muted-800 dark:text-muted-200',
            day === null ? 'opacity-0' : '',
          ]"
        >
          <span>{{ day }}</span>
          <div
            v-if="hasEvent(day)"
            :class="[
              'w-1 h-1 rounded-full mt-0.5',
              getEventColor(getEventType(day)),
            ]"
          ></div>
        </div>
      </template>
    </div>

    <!-- Upcoming events preview -->
    <div class="mt-2 space-y-1">
      <div
        class="flex items-center justify-between text-xs p-1 bg-primary-500/10 rounded-sm"
      >
        <span class="text-muted-800 dark:text-muted-200 font-medium"
          >Team Meeting</span
        >
        <span class="text-muted-500 text-[10px]">Sep 5</span>
      </div>
      <div
        class="flex items-center justify-between text-xs p-1 bg-danger-500/10 rounded-sm"
      >
        <span class="text-muted-800 dark:text-muted-200 font-medium"
          >Project Deadline</span
        >
        <span class="text-muted-500 text-[10px]">Sep 12</span>
      </div>
      <div
        class="flex items-center justify-between text-xs p-1 bg-info-500/10 rounded-sm"
      >
        <span class="text-muted-800 dark:text-muted-200 font-medium"
          >Client Call</span
        >
        <span class="text-muted-500 text-[10px]">Sep 15</span>
      </div>
    </div>
  </div>
</template>
