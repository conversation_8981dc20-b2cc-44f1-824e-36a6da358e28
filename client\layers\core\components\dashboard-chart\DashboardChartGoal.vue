<script setup lang="ts">
import { ref } from 'vue'

const progress = ref(78)
</script>

<template>
  <div class="relative h-40 w-40 mx-auto">
    <div class="absolute inset-0 flex items-center justify-center">
      <BaseHeading as="h4" size="2xl" weight="bold" lead="tight" class="text-muted-900 dark:text-white">
        <span>{{ progress }}%</span>
      </BaseHeading>
    </div>
    <svg
      class="h-full w-full"
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Background circle -->
      <circle
        cx="50"
        cy="50"
        r="45"
        stroke="var(--color-primary-100)"
        stroke-width="10"
        class="dark:stroke-primary-500/20"
      />
      
      <!-- Progress circle -->
      <circle
        cx="50"
        cy="50"
        r="45"
        stroke="var(--color-primary-500)"
        stroke-width="10"
        stroke-dasharray="282.7"
        :stroke-dashoffset="282.7 * (1 - progress / 100)"
        transform="rotate(-90 50 50)"
      />
    </svg>
  </div>
</template>
