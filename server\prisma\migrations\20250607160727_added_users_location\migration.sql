-- Create<PERSON><PERSON>
CREATE TYPE "TrackableAssetType" AS ENUM ('VEHICLE', 'EQUIPMENT', 'TOOL', 'MACHINERY', 'DEVICE', 'OTHER');

-- CreateEnum
CREATE TYPE "TrackableAssetStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'MAINTENANC<PERSON>', 'REPAIR', 'LOST', 'STOLEN', 'DISPOSED');

-- Create<PERSON>num
CREATE TYPE "MapLayerType" AS ENUM ('PROJECTS', 'WORKERS', 'ASSETS', 'WORK_LOCATIONS', 'GEOFENCES', 'CUSTOM');

-- CreateTable
CREATE TABLE "gps_devices" (
    "id" SERIAL NOT NULL,
    "deviceId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "batteryLevel" DOUBLE PRECISION,
    "lastSeen" TIMESTAMP(3),
    "reportingInterval" INTEGER NOT NULL DEFAULT 300,
    "geofenceRadius" DOUBLE PRECISION,
    "assetId" INTEGER,
    "companyId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "gps_devices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "gps_locations" (
    "id" SERIAL NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "altitude" DOUBLE PRECISION,
    "accuracy" DOUBLE PRECISION,
    "speed" DOUBLE PRECISION,
    "heading" DOUBLE PRECISION,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "deviceId" INTEGER NOT NULL,
    "batteryLevel" DOUBLE PRECISION,
    "signalStrength" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "gps_locations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "trackable_assets" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "TrackableAssetType" NOT NULL,
    "status" "TrackableAssetStatus" NOT NULL DEFAULT 'ACTIVE',
    "serialNumber" TEXT,
    "model" TEXT,
    "manufacturer" TEXT,
    "purchaseDate" TIMESTAMP(3),
    "purchasePrice" DOUBLE PRECISION,
    "currentValue" DOUBLE PRECISION,
    "companyId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "trackable_assets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "trackable_asset_locations" (
    "id" SERIAL NOT NULL,
    "assetId" INTEGER NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "address" TEXT,
    "lastUpdate" TIMESTAMP(3) NOT NULL,
    "isInGeofence" BOOLEAN NOT NULL DEFAULT true,
    "geofenceName" TEXT,

    CONSTRAINT "trackable_asset_locations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "trackable_asset_assignments" (
    "id" SERIAL NOT NULL,
    "assetId" INTEGER NOT NULL,
    "projectId" INTEGER NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" INTEGER NOT NULL,
    "unassignedAt" TIMESTAMP(3),
    "unassignedBy" INTEGER,
    "notes" TEXT,

    CONSTRAINT "trackable_asset_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "worker_locations" (
    "id" SERIAL NOT NULL,
    "workerId" INTEGER NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "accuracy" DOUBLE PRECISION,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "projectId" INTEGER,
    "taskId" INTEGER,
    "timeEntryId" INTEGER,
    "isCompliant" BOOLEAN NOT NULL DEFAULT true,
    "workLocationId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "worker_locations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "map_layers" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "MapLayerType" NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "config" JSONB,
    "companyId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "map_layers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_MapLayerRoles" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_MapLayerRoles_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "gps_devices_deviceId_key" ON "gps_devices"("deviceId");

-- CreateIndex
CREATE INDEX "gps_locations_deviceId_timestamp_idx" ON "gps_locations"("deviceId", "timestamp");

-- CreateIndex
CREATE INDEX "gps_locations_latitude_longitude_idx" ON "gps_locations"("latitude", "longitude");

-- CreateIndex
CREATE UNIQUE INDEX "trackable_asset_locations_assetId_key" ON "trackable_asset_locations"("assetId");

-- CreateIndex
CREATE UNIQUE INDEX "trackable_asset_assignments_assetId_projectId_assignedAt_key" ON "trackable_asset_assignments"("assetId", "projectId", "assignedAt");

-- CreateIndex
CREATE INDEX "worker_locations_workerId_timestamp_idx" ON "worker_locations"("workerId", "timestamp");

-- CreateIndex
CREATE INDEX "worker_locations_projectId_timestamp_idx" ON "worker_locations"("projectId", "timestamp");

-- CreateIndex
CREATE INDEX "_MapLayerRoles_B_index" ON "_MapLayerRoles"("B");

-- AddForeignKey
ALTER TABLE "gps_devices" ADD CONSTRAINT "gps_devices_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "trackable_assets"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gps_devices" ADD CONSTRAINT "gps_devices_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "gps_locations" ADD CONSTRAINT "gps_locations_deviceId_fkey" FOREIGN KEY ("deviceId") REFERENCES "gps_devices"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_assets" ADD CONSTRAINT "trackable_assets_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_asset_locations" ADD CONSTRAINT "trackable_asset_locations_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "trackable_assets"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_asset_assignments" ADD CONSTRAINT "trackable_asset_assignments_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "trackable_assets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_asset_assignments" ADD CONSTRAINT "trackable_asset_assignments_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_asset_assignments" ADD CONSTRAINT "trackable_asset_assignments_assignedBy_fkey" FOREIGN KEY ("assignedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "trackable_asset_assignments" ADD CONSTRAINT "trackable_asset_assignments_unassignedBy_fkey" FOREIGN KEY ("unassignedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "worker_locations" ADD CONSTRAINT "worker_locations_workerId_fkey" FOREIGN KEY ("workerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "worker_locations" ADD CONSTRAINT "worker_locations_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "worker_locations" ADD CONSTRAINT "worker_locations_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "worker_locations" ADD CONSTRAINT "worker_locations_timeEntryId_fkey" FOREIGN KEY ("timeEntryId") REFERENCES "TimeEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "worker_locations" ADD CONSTRAINT "worker_locations_workLocationId_fkey" FOREIGN KEY ("workLocationId") REFERENCES "WorkLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "map_layers" ADD CONSTRAINT "map_layers_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_MapLayerRoles" ADD CONSTRAINT "_MapLayerRoles_A_fkey" FOREIGN KEY ("A") REFERENCES "map_layers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_MapLayerRoles" ADD CONSTRAINT "_MapLayerRoles_B_fkey" FOREIGN KEY ("B") REFERENCES "UserRole"("id") ON DELETE CASCADE ON UPDATE CASCADE;
