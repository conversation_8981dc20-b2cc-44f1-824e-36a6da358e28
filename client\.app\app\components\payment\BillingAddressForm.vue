<template>
  <div>
    <BaseHeading
      as="h4"
      size="xs"
      weight="medium"
      class="text-muted-800 dark:text-muted-100 mb-4"
    >
      {{ $t("auth.subscription.billing_address") }}
    </BaseHeading>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <BaseInput
        v-model="localAddress.line1"
        :label="$t('auth.subscription.address_line1')"
        :placeholder="$t('profile.address_line_1_placeholder')"
        :error="errors?.line1"
        required
        @update:model-value="updateAddress"
      />
      <BaseInput
        v-model="localAddress.line2"
        :label="$t('auth.subscription.address_line2')"
        :placeholder="$t('profile.address_line_2_placeholder')"
        :error="errors?.line2"
        @update:model-value="updateAddress"
      />
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <BaseInput
        v-model="localAddress.city"
        :label="$t('auth.subscription.city')"
        :placeholder="$t('profile.city_placeholder')"
        :error="errors?.city"
        required
        @update:model-value="updateAddress"
      />
      <BaseInput
        v-model="localAddress.postal_code"
        :label="$t('auth.subscription.postal_code')"
        :placeholder="$t('profile.postal_code_placeholder')"
        :error="errors?.postal_code"
        required
        @update:model-value="updateAddress"
      />
      <BaseInput
        v-model="localAddress.state"
        :label="$t('auth.subscription.state_province')"
        :placeholder="$t('profile.state_province_placeholder')"
        :error="errors?.state"
        @update:model-value="updateAddress"
      />
    </div>

    <div class="mb-4">
      <BaseSelect
        v-model="localAddress.country"
        :label="$t('auth.subscription.country')"
        :placeholder="$t('auth.subscription.select_country')"
        :error="errors?.country"
        required
        @update:model-value="updateAddress"
      >
        <BaseSelectItem
          v-for="country in countries"
          :key="country.code"
          :value="country.code"
        >
          {{ country.name }}
        </BaseSelectItem>
      </BaseSelect>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { countries } from "../../data/countries";
import { useUserStore } from "../../../stores/useUserStore";

// Props
interface BillingAddress {
  line1: string;
  line2: string;
  city: string;
  postal_code: string;
  state: string;
  country: string;
}

interface Props {
  modelValue: BillingAddress;
  errors?: Record<string, string>;
  prefillFromUser?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  prefillFromUser: false,
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: BillingAddress];
}>();

// Local state
const localAddress = ref<BillingAddress>({ ...props.modelValue });

// Methods
const updateAddress = () => {
  emit("update:modelValue", { ...localAddress.value });
};

// Prefill from user data if requested
const prefillFromUserData = () => {
  if (props.prefillFromUser) {
    const userStore = useUserStore();
    if (userStore?.user) {
      localAddress.value = {
        line1: userStore.user.address || "",
        line2: userStore.user.address2 || "",
        city: userStore.user.city || "",
        postal_code: userStore.user.postalCode || "",
        state: userStore.user.state || "",
        country: userStore.user.country || "",
      };
      updateAddress();
    }
  }
};

// Watch for external changes
watch(
  () => props.modelValue,
  (newValue) => {
    localAddress.value = { ...newValue };
  },
  { deep: true }
);

// Lifecycle
onMounted(() => {
  if (props.prefillFromUser) {
    prefillFromUserData();
  }
});
</script>
