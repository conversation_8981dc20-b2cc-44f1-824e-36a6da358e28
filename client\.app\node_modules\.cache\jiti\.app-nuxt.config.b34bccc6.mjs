"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  compatibilityDate: "2025-03-05",
  future: {
    compatibilityVersion: 4
  },
  devtools: { enabled: false },
  ssr: false,

  extends: [
  /**
   * This extends the base Tairo layer.
   *
   * Alternatively you can use the following:
   * ["gh:cssninjaStudio/tairo/layers/tairo#v1.4.0", {
   *    install: true,
   *    giget: { auth: import.meta.env.GITHUB_TOKEN },
   * }]
   *
   * @see https://github.com/unjs/c12#extending-config-layer-from-remote-sources
   *
   * This would allows you to create an empty git repository
   * with only your source code and no demo.
   */
  "../layers/tairo",

  /**
   * Extend from the Core module layer
   */
  "../layers/core",

  /**
   * Extend from the Budget module layer
   */
  "../layers/budget",

  /**
   * Extend from the HR module layer
   */
  "../layers/hr",

  /**
   * Extend from the Companies module layer
   */
  "../layers/companies",

  /**
   * Extend from the Production module layer
   */
  "../layers/production",

  /**
   * Extend from the Accounting module layer
   */
  "../layers/accounting",

  /**
   * Extend from the Recruitment module layer
   */
  "../layers/recruitment",

  /**
   * Extend from the Sales module layer
   */
  "../layers/sales",

  /**
   * Extend from the Time Management module layer
   */
  "../layers/timemanagement",

  /**
   * Extend from the Communication module layer
   */
  "../layers/communication"],


  modules: [
  "reka-ui/nuxt",
  "@vueuse/nuxt",
  "@nuxtjs/i18n",
  "@nuxt/image",
  "@nuxt/content",
  "@nuxt/fonts",
  "@pinia/nuxt",
  "pinia-plugin-persistedstate/nuxt",
  "@vite-pwa/nuxt"],


  pwa: {
    registerType: "autoUpdate",
    manifest: {
      name: "CoManager",
      short_name: "CoManager",
      description: "Company Management System",
      theme_color: "#ffffff",
      background_color: "#ffffff",
      start_url: "/",
      display: "standalone",
      icons: [
      {
        src: "pwa-192x192.png",
        sizes: "192x192",
        type: "image/png"
      },
      {
        src: "pwa-512x512.png",
        sizes: "512x512",
        type: "image/png"
      },
      {
        src: "pwa-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any maskable"
      }]

    },
    workbox: {
      navigateFallback: "/",
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
      runtimeCaching: [
      {
        urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
        handler: "CacheFirst",
        options: {
          cacheName: "google-fonts-cache",
          expiration: {
            maxEntries: 10,
            maxAgeSeconds: 60 * 60 * 24 * 365 // <== 365 days
          },
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      },
      {
        urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
        handler: "CacheFirst",
        options: {
          cacheName: "gstatic-fonts-cache",
          expiration: {
            maxEntries: 10,
            maxAgeSeconds: 60 * 60 * 24 * 365 // <== 365 days
          },
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      },
      {
        urlPattern: /\.(?:png|jpg|jpeg|svg|gif)$/i,
        handler: "CacheFirst",
        options: {
          cacheName: "images-cache",
          expiration: {
            maxEntries: 50,
            maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
          }
        }
      },
      {
        urlPattern: /\.(?:js|css)$/i,
        handler: "StaleWhileRevalidate",
        options: {
          cacheName: "static-resources"
        }
      }]

    },
    client: {
      installPrompt: true,
      periodicSyncForUpdates: 20
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallbackAllowlist: [/^\/$/],
      type: "module"
    }
  },
  content: {
    build: {
      markdown: {
        toc: { depth: 3, searchDepth: 2 },
        highlight: {
          theme: {
            default: "github-light",
            dark: "github-dark"
          }
        }
      }
    },
    renderer: {
      anchorLinks: true
    }
  },

  experimental: {
    viewTransition: true,
    // buildCache: true,
    sharedPrerenderData: true,
    defaults: {
      nuxtLink: {
        // Here we disable the prefetch for visibility and enable it for interaction.
        // This is a good balance between performance and user experience when having a lot of links.
        prefetchOn: {
          visibility: false,
          interaction: true
        }
      }
    }
  },
  $development: {
    experimental: {
      // Disable prefetch for development, this will make the development faster.
      defaults: {
        nuxtLink: {
          prefetch: false
        }
      }
    }
  },

  css: [
  /**
   * Load Tailwind CSS
   */
  "~/assets/main.css",
  /**
   * Load custom CSS overrides
   */
  "~/assets/custom.css",
  /**
   * Load input styles
   */
  "~/assets/css/input-styles.css",
  /**
   * Load flag icons CSS
   */
  "flag-icons/css/flag-icons.min.css"],

  fonts: {
    experimental: {
      processCSSVariables: true
    }
  },

  typescript: {
    tsConfig: {




      // Here you can customize the generated tsconfig.json file
      // vueCompilerOptions: {
      //   target: 3.4,
      // },
    } }, runtimeConfig: { public: {
      // mapbox config
      mapboxToken: process.env.NUXT_PUBLIC_MAPBOX_TOKEN || "", // set it via NUXT_PUBLIC_MAPBOX_TOKEN env
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || "", // set it via NUXT_PUBLIC_SITE_URL
      apiBase: process.env.NUXT_PUBLIC_API_BASE || "", // set it via NUXT_PUBLIC_API_BASE
      ENABLE_PWA: process.env.NUXT_PUBLIC_ENABLE_PWA || "", // set it via NUXT_PUBLIC_ENABLE_PWA
      DEBUG: process.env.NUXT_PUBLIC_DEBUG || "" // set it via NUXT_PUBLIC_DEBUG
    }
  },

  i18n: {
    baseUrl: "/",
    // We use no_prefix strategy to avoid having the locale prefix in the URL,
    // This may not be the best strategy for SEO, but it's the best for the demo.
    // We recommend using the default prefix_except_default strategy for SEO.
    strategy: "no_prefix",
    defaultLocale: "en",
    lazy: true,
    locales: [
    {
      code: "en",
      dir: "ltr",
      language: "en-US",
      file: "en-US.yaml",
      name: "English",
      isCatchallLocale: true
    },
    {
      code: "et",
      dir: "ltr",
      language: "et-EE",
      file: "et-EE.yaml",
      name: "Eesti"
    }],

    // Use i18n v10 features
    experimental: {
      generatedLocaleFilePathFormat: "off"
    },
    bundle: {
      optimizeTranslationDirective: false
    }
  },

  routeRules: {
    "/": {
      swr: 3600
    },
    "/demos": {
      swr: 3600
    },
    "/starters/**": {
      swr: 3600
    },
    "/auth/**": {
      swr: 3600
    },
    "/documentation": {
      swr: 3600
    },
    "/documentation/**": {
      swr: 3600
    },
    "/dashboards/**": {
      swr: 3600
    },
    "/layouts/**": {
      swr: 3600
    },
    "/wizard/**": {
      swr: 3600
    }
  },

  sourcemap: {
    server: false,
    client: false
  },

  app: {
    head: {
      link: [
      {
        rel: "icon",
        type: "image/svg+xml",
        href: "/favicon.svg"
      },
      {
        rel: "manifest",
        href: "/manifest.json"
      },
      {
        rel: "apple-touch-icon",
        href: "/pwa-192x192.png"
      }],

      meta: [
      {
        name: "theme-color",
        content: "#000000"
      },
      {
        name: "mobile-web-app-capable",
        content: "yes"
      },
      {
        name: "apple-mobile-web-app-capable",
        content: "yes"
      },
      {
        name: "apple-mobile-web-app-status-bar-style",
        content: "#000000"
      }]

    }
  },

  vite: {
    server: {
      https:
      process.env.NODE_ENV === "development" ?
      {
        key: process.env.SSL_KEY_FILE || "./ssl/localhost.key",
        cert: process.env.SSL_CERT_FILE || "./ssl/localhost.crt"
      } :
      undefined,
      proxy: {
        "/api": {
          target: "http://localhost:4004",
          changeOrigin: true,
          rewrite: (path) => path // Keep the full path including /api
        }
      }
    },
    define: {
      // Enable / disable Options API support. Disabling this will result in smaller bundles,
      // but may affect compatibility with 3rd party libraries if they rely on Options API.
      __VUE_OPTIONS_API__: false
    },
    css: {
      // LightningCSS is a rust based CSS minifier that is faster than the default CSS minifier.
      // @see https://vite.dev/guide/features.html#lightning-css
      // @see https://lightningcss.dev/
      transformer: "lightningcss"
    },
    build: {
      target: "esnext",
      cssMinify: "lightningcss",
      reportCompressedSize: false
    },
    // Defining the optimizeDeps.include option prebuilds the dependencies, this avoid
    // some reloads when navigating between pages during development.
    // It's also useful to track them usage.
    optimizeDeps: {
      include: [
      "scule",
      "klona",
      // AddonDatepicker
      "v-calendar",
      // AddonApexcharts
      "vue3-apexcharts",
      // AddonInputPhone
      "libphonenumber-js/max",
      "country-codes-list",
      // AddonInputPassword
      "@zxcvbn-ts/core",
      "@zxcvbn-ts/language-common",
      "@zxcvbn-ts/language-en",
      "@zxcvbn-ts/language-fr",
      // AddonMapboxLocationPicker
      "ohash",
      "mapbox-gl",
      "@mapbox/mapbox-gl-geocoder",
      // form validation
      "@vee-validate/zod",
      "vee-validate",
      "zod",
      // calendar app
      "vue3-smooth-dnd",
      "date-fns",
      "date-fns/locale",
      // profile edit page
      "imask"]

    }
  }
}); /* v9-c575623c3456e6e1 */
