<template>
  <div>
    <BaseCard class="p-6">
      <div class="flex flex-col">
        <BaseHeading
          tag="h3"
          size="sm"
          weight="medium"
          class="text-muted-800 dark:text-white mb-4"
        >
          Upload Document
        </BaseHeading>

        <form @submit.prevent="uploadDocument">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <BaseInput
                v-model="documentForm.name"
                label="Document Name"
                placeholder="Enter document name"
                :error="documentErrors.name"
                required
              />
            </div>
            <div>
              <BaseListbox
                v-model="documentForm.category"
                :items="categoryOptions"
                :properties="{ value: 'value', label: 'label' }"
                label="Category"
                placeholder="Select category"
                size="sm"
                rounded="md"
                :error="documentErrors.category"
              />
            </div>
          </div>

          <div class="mb-4">
            <BaseTextarea
              v-model="documentForm.description"
              label="Description (Optional)"
              placeholder="Enter document description"
              :rows="3"
            />
          </div>

          <div class="mb-6">
            <label
              class="block mb-1 text-sm font-medium text-muted-700 dark:text-muted-300"
            >
              Document File
              <span class="text-danger-500">*</span>
            </label>
            <div
              class="border-2 border-dashed border-muted-300 dark:border-muted-700 rounded-lg p-6 text-center"
              :class="{
                'border-primary-500 bg-primary-500/10': isDragging,
                'border-danger-500 bg-danger-500/10': documentErrors.file,
              }"
              @dragover.prevent="isDragging = true"
              @dragleave.prevent="isDragging = false"
              @drop.prevent="onFileDrop"
            >
              <div v-if="!selectedFile">
                <Icon
                  name="ph:upload-simple-duotone"
                  class="h-12 w-12 mx-auto text-muted-400 dark:text-muted-500"
                />
                <p class="mt-2 text-sm text-muted-500 dark:text-muted-400">
                  Drag and drop your file here, or
                  <span
                    class="text-primary-500 cursor-pointer hover:underline"
                    @click="$refs.fileInput.click()"
                  >
                    browse
                  </span>
                </p>
                <p class="mt-1 text-xs text-muted-400 dark:text-muted-500">
                  Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, etc.
                </p>
                <input
                  ref="fileInput"
                  type="file"
                  class="hidden"
                  @change="onFileChange"
                />
              </div>
              <div v-else class="flex items-center justify-between">
                <div class="flex items-center">
                  <Icon
                    :name="getFileIcon(selectedFile.type)"
                    class="h-10 w-10 text-primary-500"
                  />
                  <div class="ml-3 text-left">
                    <p
                      class="text-sm font-medium text-muted-800 dark:text-white truncate max-w-xs"
                    >
                      {{ selectedFile.name }}
                    </p>
                    <p class="text-xs text-muted-400 dark:text-muted-500">
                      {{ formatFileSize(selectedFile.size) }}
                    </p>
                  </div>
                </div>
                <BaseButton
                  color="danger"
                  shape="rounded"
                  size="sm"
                  @click="selectedFile = null"
                >
                  <Icon name="ph:trash-duotone" class="h-4 w-4" />
                </BaseButton>
              </div>
              <p
                v-if="documentErrors.file"
                class="mt-2 text-xs text-danger-500"
              >
                {{ documentErrors.file }}
              </p>
            </div>
          </div>

          <div class="flex justify-end">
            <BaseButton
              type="submit"
              color="primary"
              :loading="isUploading"
              :disabled="isUploading"
            >
              <Icon
                v-if="!isUploading"
                name="ph:upload-simple-duotone"
                class="h-4 w-4 mr-1"
              />
              {{ isUploading ? "Uploading..." : "Upload Document" }}
            </BaseButton>
          </div>
        </form>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useApi } from "../../../../app/composables/useApi";

const props = defineProps<{
  companyId: string;
}>();

const emit = defineEmits(["document-uploaded"]);

// State
const isDragging = ref(false);
const selectedFile = ref<File | null>(null);
const isUploading = ref(false);

// Form state
const documentForm = ref({
  name: "",
  category: "",
  description: "",
});

const documentErrors = ref({
  name: "",
  category: "",
  file: "",
});

// Services
const toaster = useNuiToasts();
const api = useApi();

// Category options
const categoryOptions = [
  { label: "Contract", value: "contract" },
  { label: "Invoice", value: "invoice" },
  { label: "Report", value: "report" },
  { label: "Legal", value: "legal" },
  { label: "Certificate", value: "certificate" },
  { label: "Other", value: "other" },
];

// Methods
const onFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    selectedFile.value = input.files[0];
    documentErrors.value.file = "";
  }
};

const onFileDrop = (event: DragEvent) => {
  isDragging.value = false;
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    selectedFile.value = event.dataTransfer.files[0];
    documentErrors.value.file = "";
  }
};

const validateForm = () => {
  let isValid = true;
  documentErrors.value = {
    name: "",
    category: "",
    file: "",
  };

  if (!documentForm.value.name) {
    documentErrors.value.name = "Document name is required";
    isValid = false;
  }

  if (!selectedFile.value) {
    documentErrors.value.file = "Please select a file to upload";
    isValid = false;
  } else if (selectedFile.value.size > 10 * 1024 * 1024) {
    documentErrors.value.file = "File size should not exceed 10MB";
    isValid = false;
  }

  return isValid;
};

const uploadDocument = async () => {
  if (!validateForm()) return;

  isUploading.value = true;

  try {
    const formData = new FormData();
    formData.append("name", documentForm.value.name);
    formData.append("category", documentForm.value.category || "other");
    formData.append("description", documentForm.value.description);
    formData.append("module", "company");

    if (selectedFile.value) {
      formData.append("file", selectedFile.value);
    }

    const response = await api.post(
      `/companies/${props.companyId}/documents`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    toaster.add({
      title: "Success",
      description: "Document uploaded successfully",
      icon: "ph:check-circle-duotone",
      progress: true,
      duration: 3000,
    });

    // Reset form
    documentForm.value = {
      name: "",
      category: "",
      description: "",
    };
    selectedFile.value = null;

    // Emit event to parent component
    emit("document-uploaded", response.data);
  } catch (error) {
    console.error("Error uploading document:", error);
    toaster.add({
      title: "Error",
      description: "Failed to upload document",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    isUploading.value = false;
  }
};

const getFileIcon = (mimeType: string) => {
  if (mimeType.includes("pdf")) {
    return "ph:file-pdf-duotone";
  } else if (mimeType.includes("word") || mimeType.includes("doc")) {
    return "ph:file-doc-duotone";
  } else if (
    mimeType.includes("excel") ||
    mimeType.includes("sheet") ||
    mimeType.includes("xls")
  ) {
    return "ph:file-xls-duotone";
  } else if (mimeType.includes("image")) {
    return "ph:file-image-duotone";
  } else if (mimeType.includes("text")) {
    return "ph:file-text-duotone";
  } else {
    return "ph:file-duotone";
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
</script>
