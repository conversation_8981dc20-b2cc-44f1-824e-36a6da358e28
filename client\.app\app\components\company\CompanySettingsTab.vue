<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="mb-4 text-muted-800 dark:text-white"
      >
        Notification Settings
      </BaseHeading>

      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Email Notifications
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Receive company notifications via email
            </BaseText>
          </div>
          <BaseSwitch
            v-model="notificationSettings.email.enabled"
            @update:modelValue="updateNotificationSettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Team Member Updates
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Receive notifications about team member changes
            </BaseText>
          </div>
          <BaseSwitch
            v-model="notificationSettings.email.teamUpdates"
            :disabled="!notificationSettings.email.enabled"
            @update:modelValue="updateNotificationSettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Document Notifications
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Receive notifications about document uploads and changes
            </BaseText>
          </div>
          <BaseSwitch
            v-model="notificationSettings.email.documentUpdates"
            :disabled="!notificationSettings.email.enabled"
            @update:modelValue="updateNotificationSettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Project Notifications
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Receive notifications about project updates
            </BaseText>
          </div>
          <BaseSwitch
            v-model="notificationSettings.email.projectUpdates"
            :disabled="!notificationSettings.email.enabled"
            @update:modelValue="updateNotificationSettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Marketing Updates
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Receive marketing and promotional emails
            </BaseText>
          </div>
          <BaseSwitch
            v-model="notificationSettings.email.marketing"
            :disabled="!notificationSettings.email.enabled"
            @update:modelValue="updateNotificationSettings"
          />
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="mb-4 text-muted-800 dark:text-white"
      >
        Privacy & Sharing
      </BaseHeading>

      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Public Profile
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Make your company profile visible to the public
            </BaseText>
          </div>
          <BaseSwitch
            v-model="privacySettings.publicProfile"
            @update:modelValue="updatePrivacySettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Show Contact Information
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Display company contact information on public profile
            </BaseText>
          </div>
          <BaseSwitch
            v-model="privacySettings.showContactInfo"
            :disabled="!privacySettings.publicProfile"
            @update:modelValue="updatePrivacySettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Allow Team Directory
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Show team members on public profile
            </BaseText>
          </div>
          <BaseSwitch
            v-model="privacySettings.showTeamMembers"
            :disabled="!privacySettings.publicProfile"
            @update:modelValue="updatePrivacySettings"
          />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Data Sharing
            </BaseHeading>
            <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
              Allow sharing company data with trusted partners
            </BaseText>
          </div>
          <BaseSwitch
            v-model="privacySettings.dataSharing"
            @update:modelValue="updatePrivacySettings"
          />
        </div>
      </div>

      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="mb-4 mt-8 text-muted-800 dark:text-white"
      >
        Danger Zone
      </BaseHeading>

      <div
        class="border border-danger-200 dark:border-danger-500/20 rounded-lg p-4 bg-danger-50 dark:bg-danger-500/5"
      >
        <BaseHeading
          tag="h3"
          size="sm"
          weight="medium"
          class="text-danger-500 mb-2"
        >
          Delete Company
        </BaseHeading>
        <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mb-4">
          Permanently delete your company and all associated data. This action
          cannot be undone.
        </BaseText>
        <BaseButton color="danger" @click="confirmDeleteCompany">
          Delete Company
        </BaseButton>
      </div>
    </div>

    <!-- Confirm Delete Modal -->
    <TairoModal
      :open="isConfirmDeleteModalOpen"
      size="sm"
      @close="closeConfirmDeleteModal"
    >
      <template #header>
        <div class="flex w-full items-center justify-between p-4 md:p-6">
          <h3
            class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white"
          >
            Delete Company
          </h3>
          <BaseButtonClose @click="closeConfirmDeleteModal" />
        </div>
      </template>
      <div class="p-4 md:p-6">
        <p class="text-muted-500 dark:text-muted-400 mb-4">
          Are you sure you want to delete
          <span class="font-medium text-muted-800 dark:text-white">{{
            company?.name
          }}</span
          >? This action cannot be undone and will permanently delete all
          company data.
        </p>

        <div class="mb-4">
          <BaseInput
            v-model="deleteConfirmation"
            label="Confirm by typing 'DELETE'"
            placeholder="Type DELETE to confirm"
            :error="deleteError"
          />
        </div>

        <div class="flex justify-end space-x-2">
          <BaseButton
            type="button"
            color="muted"
            @click="closeConfirmDeleteModal"
          >
            Cancel
          </BaseButton>
          <BaseButton
            type="button"
            color="danger"
            :loading="isDeleting"
            :disabled="deleteConfirmation !== 'DELETE'"
            @click="deleteCompany"
          >
            Delete Company
          </BaseButton>
        </div>
      </div>
    </TairoModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useApi } from "../../../app/composables/useApi";

const api = useApi();

const props = defineProps<{
  company?: {
    id?: string;
    name?: string;
    settings?: {
      notifications?: {
        email?: {
          enabled: boolean;
          teamUpdates: boolean;
          documentUpdates: boolean;
          projectUpdates: boolean;
          marketing: boolean;
        };
      };
      privacy?: {
        publicProfile: boolean;
        showContactInfo: boolean;
        showTeamMembers: boolean;
        dataSharing: boolean;
      };
    };
  };
}>();

const emit = defineEmits<{
  (e: "settingsUpdated"): void;
}>();

// State
const notificationSettings = ref({
  email: {
    enabled: true,
    teamUpdates: true,
    documentUpdates: true,
    projectUpdates: true,
    marketing: false,
  },
});

const privacySettings = ref({
  publicProfile: false,
  showContactInfo: false,
  showTeamMembers: false,
  dataSharing: false,
});

const isConfirmDeleteModalOpen = ref(false);
const isDeleting = ref(false);
const deleteConfirmation = ref("");
const deleteError = ref("");

// Services
const toaster = useNuiToasts();
const router = useRouter();

// Methods
const initSettings = () => {
  if (props.company?.settings) {
    if (props.company.settings.notifications?.email) {
      notificationSettings.value.email = {
        ...notificationSettings.value.email,
        ...props.company.settings.notifications.email,
      };
    }

    if (props.company.settings.privacy) {
      privacySettings.value = {
        ...privacySettings.value,
        ...props.company.settings.privacy,
      };
    }
  }
};

const updateNotificationSettings = async () => {
  if (!props.company?.id) return;

  try {
    const response = await api.put(
      `/companies/${props.company.id}/settings/notifications`,
      {
        email: notificationSettings.value.email,
      }
    );

    if (response.data) {
      toaster.add({
        title: "Success",
        description: "Notification settings updated successfully",
        icon: "ph:check-circle-duotone",
        progress: true,
        duration: 3000,
      });

      emit("settingsUpdated");
    }
  } catch (error) {
    console.error("Error updating notification settings:", error);
    toaster.add({
      title: "Error",
      description: "Failed to update notification settings",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  }
};

const updatePrivacySettings = async () => {
  if (!props.company?.id) return;

  try {
    const response = await api.put(
      `/companies/${props.company.id}/settings/privacy`,
      {
        ...privacySettings.value,
      }
    );

    if (response.data) {
      toaster.add({
        title: "Success",
        description: "Privacy settings updated successfully",
        icon: "ph:check-circle-duotone",
        progress: true,
        duration: 3000,
      });

      emit("settingsUpdated");
    }
  } catch (error) {
    console.error("Error updating privacy settings:", error);
    toaster.add({
      title: "Error",
      description: "Failed to update privacy settings",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  }
};

const confirmDeleteCompany = () => {
  deleteConfirmation.value = "";
  deleteError.value = "";
  isConfirmDeleteModalOpen.value = true;
};

const closeConfirmDeleteModal = () => {
  isConfirmDeleteModalOpen.value = false;
};

const deleteCompany = async () => {
  if (!props.company?.id) return;

  if (deleteConfirmation.value !== "DELETE") {
    deleteError.value = "Please type DELETE to confirm";
    return;
  }

  try {
    isDeleting.value = true;

    const response = await api.delete(`/companies/${props.company.id}`);

    if (response.data) {
      toaster.add({
        title: "Success",
        description: "Company deleted successfully",
        icon: "ph:check-circle-duotone",
        progress: true,
        duration: 3000,
      });

      // Redirect to home page
      router.push("/core");
    }
  } catch (error) {
    console.error("Error deleting company:", error);
    toaster.add({
      title: "Error",
      description: "Failed to delete company",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    isDeleting.value = false;
    closeConfirmDeleteModal();
  }
};

// Lifecycle hooks
onMounted(() => {
  initSettings();
});
</script>
