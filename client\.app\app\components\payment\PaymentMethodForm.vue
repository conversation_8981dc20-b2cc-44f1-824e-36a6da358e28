<template>
  <div>
    <form @submit.prevent="handleSubmit">
      <!-- Stripe Card Input -->
      <div class="mb-6">
        <StripeCardInput
          ref="stripeCardRef"
          :label="$t('auth.subscription.card_information')"
          :stripe-public-key="stripePublicKey"
          :error="cardError"
          @ready="onCardReady"
          @change="onCardChange"
          @error="onCardError"
        />
      </div>

      <!-- Cardholder Name -->
      <div class="mb-6">
        <BaseInput
          v-model="formData.cardholderName"
          :label="$t('profile.cardholder')"
          :placeholder="$t('auth.subscription.full_name')"
          :error="errors.cardholderName"
          required
        />
      </div>

      <!-- Billing Address -->
      <div class="mb-6">
        <BillingAddressForm
          v-model="formData.billingAddress"
          :errors="errors.billingAddress"
          :prefill-from-user="prefillFromUser"
        />
      </div>

      <!-- Set as Primary Card -->
      <div class="mb-6">
        <BaseCheckbox
          v-model="formData.isPrimary"
          :label="$t('profile.set_as_primary_card')"
        />
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end gap-3">
        <BaseButton variant="muted" type="button" @click="$emit('cancel')">
          {{ $t("profile.cancel") }}
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          color="primary"
          :loading="isProcessing"
          :disabled="!isCardReady || isProcessing"
        >
          <Icon name="solar:card-linear" class="size-4 mr-2" />
          {{ submitButtonText }}
        </BaseButton>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import StripeCardInput from "./StripeCardInput.vue";
import BillingAddressForm from "./BillingAddressForm.vue";
import { useUserStore } from "../../../stores/useUserStore.ts";

// Props
interface BillingAddress {
  line1: string;
  line2: string;
  city: string;
  postal_code: string;
  state: string;
  country: string;
}

interface PaymentFormData {
  cardholderName: string;
  billingAddress: BillingAddress;
  isPrimary: boolean;
}

interface Props {
  stripePublicKey: string;
  initialData?: Partial<PaymentFormData>;
  submitButtonText?: string;
  prefillFromUser?: boolean;
  isProcessing?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  submitButtonText: "Add Payment Method",
  prefillFromUser: true,
  isProcessing: false,
  initialData: () => ({}),
});

// Emits
const emit = defineEmits<{
  submit: [
    data: PaymentFormData,
    stripeElements: { stripe: any; cardElement: any }
  ];
  cancel: [];
}>();

// State
const stripeCardRef = ref(null);
const isCardReady = ref(false);
const cardError = ref("");

// Form data
const formData = ref<PaymentFormData>({
  cardholderName: "",
  billingAddress: {
    line1: "",
    line2: "",
    city: "",
    postal_code: "",
    state: "",
    country: "",
  },
  isPrimary: false,
  ...props.initialData,
});

// Form errors
const errors = ref<{
  cardholderName?: string;
  billingAddress?: Record<string, string>;
}>({});

// Methods
const onCardReady = (element: any) => {
  isCardReady.value = true;
};

const onCardChange = (event: any) => {
  // Handle card validation changes if needed
};

const onCardError = (error: string) => {
  cardError.value = error;
};

const validateForm = (): boolean => {
  const newErrors: typeof errors.value = {};

  // Validate cardholder name
  if (!formData.value.cardholderName.trim()) {
    newErrors.cardholderName = "Cardholder name is required";
  }

  // Validate billing address
  const billingErrors: Record<string, string> = {};
  if (!formData.value.billingAddress.line1.trim()) {
    billingErrors.line1 = "Address line 1 is required";
  }
  if (!formData.value.billingAddress.city.trim()) {
    billingErrors.city = "City is required";
  }
  if (!formData.value.billingAddress.postal_code.trim()) {
    billingErrors.postal_code = "Postal code is required";
  }
  if (!formData.value.billingAddress.country.trim()) {
    billingErrors.country = "Country is required";
  }

  if (Object.keys(billingErrors).length > 0) {
    newErrors.billingAddress = billingErrors;
  }

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }

  if (!stripeCardRef.value) {
    cardError.value = "Payment form not ready";
    return;
  }

  const stripe = stripeCardRef.value.getStripe();
  const cardElement = stripeCardRef.value.getElement();

  if (!stripe || !cardElement) {
    cardError.value = "Payment form not properly initialized";
    return;
  }

  emit("submit", formData.value, { stripe, cardElement });
};

// Initialize form with user data if needed
const initializeForm = () => {
  if (props.prefillFromUser) {
    const userStore = useUserStore();
    if (userStore?.user) {
      formData.value.cardholderName = `${userStore.user.firstName} ${userStore.user.lastName}`;
    }
  }
};

// Lifecycle
onMounted(() => {
  initializeForm();
});
</script>
