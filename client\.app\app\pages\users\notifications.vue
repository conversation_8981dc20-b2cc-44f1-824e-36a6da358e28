<!-- client/.app/app/pages/users/notifications.vue -->

<template>
  <div class="p-6 dark:bg-muted-900">
    <TairoContentWrapper>
      <template #left>
        <BaseHeading
          tag="h1"
          size="xl"
          weight="light"
          class="text-muted-800 dark:text-white"
        >
          Notifications
        </BaseHeading>
        <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
          View and manage your system notifications
        </BaseText>
      </template>

      <div class="mt-6">
        <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
          <div class="flex justify-between items-center mb-6">
            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Recent Notifications
            </BaseHeading>

            <div class="flex items-center space-x-2">
              <BaseSelect
                v-model="filterType"
                :options="filterOptions"
                shape="rounded"
                class="w-40"
              />
              <BaseButton
                v-if="notifications.length > 0"
                color="muted"
                @click="markAllAsRead"
              >
                Mark All as Read
              </BaseButton>
            </div>
          </div>

          <!-- Notifications List -->
          <div v-if="isLoading" class="flex justify-center py-8">
            <BasePlaceholderPage
              title="Loading notifications"
              subtitle="Please wait while we load your notifications"
            >
              <template #image>
                <BaseIconBox
                  size="xl"
                  shape="full"
                  class="bg-muted-100 dark:bg-muted-700/60"
                >
                  <Icon
                    name="ph:spinner-gap-duotone"
                    class="h-8 w-8 animate-spin text-primary-500"
                  />
                </BaseIconBox>
              </template>
            </BasePlaceholderPage>
          </div>

          <div
            v-else-if="filteredNotifications.length === 0"
            class="text-center py-8"
          >
            <Icon
              name="ph:bell-slash-duotone"
              class="h-12 w-12 mx-auto text-muted-400"
            />
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
              class="mt-2 text-muted-800 dark:text-white"
            >
              No Notifications
            </BaseHeading>
            <BaseText
              size="xs"
              class="text-muted-500 dark:text-muted-400 mt-1 max-w-md mx-auto"
            >
              {{
                filterType === "all"
                  ? "You don't have any notifications yet."
                  : "You don't have any " + filterType + " notifications."
              }}
            </BaseText>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="notification in filteredNotifications"
              :key="notification.id"
              class="border border-muted-200 dark:border-muted-700 rounded-lg p-4 hover:bg-muted-50 dark:hover:bg-muted-800/50 transition-colors duration-300"
              :class="{
                'bg-muted-50 dark:bg-muted-800/50': !notification.read,
              }"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0 mr-4">
                  <BaseIconBox
                    size="lg"
                    :color="getNotificationColor(notification.type)"
                    class="rounded-full"
                  >
                    <Icon
                      :name="getNotificationIcon(notification.type)"
                      class="h-5 w-5"
                    />
                  </BaseIconBox>
                </div>
                <div class="flex-grow">
                  <div class="flex justify-between items-start">
                    <BaseHeading
                      tag="h3"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      {{ notification.title }}
                    </BaseHeading>
                    <div class="flex items-center space-x-2">
                      <BaseTag
                        :color="notification.read ? 'muted' : 'success'"
                        size="sm"
                      >
                        {{ notification.read ? "Read" : "New" }}
                      </BaseTag>
                      <BaseText size="xs" class="text-muted-400">
                        {{ formatDate(notification.createdAt) }}
                      </BaseText>
                    </div>
                  </div>
                  <BaseText
                    size="sm"
                    class="text-muted-500 dark:text-muted-400 mt-1"
                  >
                    {{ notification.message }}
                  </BaseText>
                  <div class="flex justify-between items-center mt-2">
                    <div>
                      <BaseButton
                        v-if="notification.actionUrl"
                        color="primary"
                        variant="outline"
                        size="sm"
                        :to="notification.actionUrl"
                      >
                        {{ notification.actionText || "View Details" }}
                      </BaseButton>
                    </div>
                    <div class="flex items-center space-x-2">
                      <BaseButtonIcon
                        v-if="!notification.read"
                        color="info"
                        size="xs"
                        tooltip="Mark as Read"
                        @click="markAsRead(notification)"
                      >
                        <Icon name="ph:check-duotone" class="h-4 w-4" />
                      </BaseButtonIcon>
                      <BaseButtonIcon
                        color="danger"
                        size="xs"
                        tooltip="Delete"
                        @click="deleteNotification(notification)"
                      >
                        <Icon name="ph:trash-duotone" class="h-4 w-4" />
                      </BaseButtonIcon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div
            v-if="filteredNotifications.length > 0"
            class="flex justify-center mt-6"
          >
            <BasePagination
              :total-items="totalNotifications"
              :items-per-page="itemsPerPage"
              :current-page="currentPage"
              @update:current-page="changePage"
            />
          </div>
        </div>

        <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm mt-6">
          <BaseHeading
            tag="h2"
            size="md"
            weight="medium"
            class="mb-4 text-muted-800 dark:text-white"
          >
            Notification Settings
          </BaseHeading>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-4 text-muted-800 dark:text-white"
              >
                General Settings
              </BaseHeading>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Enable Notifications
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Receive system notifications
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Desktop Notifications
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Show browser notifications
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.desktop"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Sound Notifications
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Play sound for new notifications
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.sound"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>
              </div>
            </div>

            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-4 text-muted-800 dark:text-white"
              >
                Notification Types
              </BaseHeading>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      System Updates
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Notifications about system changes
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.types.system"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Team Updates
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Notifications about team changes
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.types.team"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Project Updates
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Notifications about project changes
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.types.project"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Security Alerts
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Notifications about security events
                    </BaseText>
                  </div>
                  <BaseSwitch
                    v-model="notificationSettings.types.security"
                    :disabled="!notificationSettings.enabled"
                    @update:modelValue="updateNotificationSettings"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TairoContentWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "../../../stores/useUserStore";
import { useApi } from "../../../app/composables/useApi";

// Types
interface Notification {
  id: number;
  userId: number;
  title: string;
  message: string;
  type: string;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  createdAt: string;
  updatedAt: string;
}

// State
const isLoading = ref(true);
const notifications = ref<Notification[]>([]);
const filterType = ref("all");
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalNotifications = ref(0);

// Filter options
const filterOptions = [
  { label: "All Notifications", value: "all" },
  { label: "Unread", value: "unread" },
  { label: "System", value: "system" },
  { label: "Team", value: "team" },
  { label: "Project", value: "project" },
  { label: "Security", value: "security" },
];

// Notification settings
const notificationSettings = ref({
  enabled: true,
  desktop: true,
  sound: true,
  types: {
    system: true,
    team: true,
    project: true,
    security: true,
  },
});

// Services
const userStore = useUserStore();
const api = useApi();
const toaster = useNuiToasts();

// Computed
const filteredNotifications = computed(() => {
  if (filterType.value === "all") {
    return notifications.value;
  } else if (filterType.value === "unread") {
    return notifications.value.filter((notification) => !notification.read);
  } else {
    return notifications.value.filter(
      (notification) => notification.type === filterType.value
    );
  }
});

// Methods
const fetchNotifications = async () => {
  try {
    isLoading.value = true;

    const response = await api.get("/notifications", {
      page: currentPage.value,
      limit: itemsPerPage.value,
      filter: filterType.value !== "unread" ? filterType.value : "all",
      unread: filterType.value === "unread",
    });

    if (response?.data) {
      notifications.value = response.data.notifications;
      totalNotifications.value = response.data.total;
    }
  } catch (error) {
    console.error("Error fetching notifications:", error);
    toaster.add({
      title: "Error",
      description: "Failed to load notifications",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  } finally {
    isLoading.value = false;
  }
};

const fetchNotificationSettings = async () => {
  try {
    const response = await api.get("/notifications/settings");

    if (response?.data) {
      notificationSettings.value = response.data;
    }
  } catch (error) {
    console.error("Error fetching notification settings:", error);
  }
};

const getNotificationIcon = (type: string): string => {
  switch (type) {
    case "system":
      return "ph:gear-six-duotone";
    case "team":
      return "ph:users-three-duotone";
    case "project":
      return "ph:briefcase-duotone";
    case "security":
      return "ph:shield-check-duotone";
    default:
      return "ph:bell-duotone";
  }
};

const getNotificationColor = (type: string): string => {
  switch (type) {
    case "system":
      return "info";
    case "team":
      return "success";
    case "project":
      return "warning";
    case "security":
      return "danger";
    default:
      return "primary";
  }
};

const formatDate = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffMs = now.getTime() - date.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSecs < 60) {
    return "Just now";
  } else if (diffMins < 60) {
    return `${diffMins} min${diffMins > 1 ? "s" : ""} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours > 1 ? "s" : ""} ago`;
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;
  } else {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }
};

const markAsRead = async (notification: Notification) => {
  try {
    const response = await api.put(`/notifications/${notification.id}/read`);

    if (response?.data) {
      // Update the notification in the list
      const index = notifications.value.findIndex(
        (n) => n.id === notification.id
      );
      if (index !== -1) {
        notifications.value[index].read = true;
      }

      toaster.add({
        title: "Success",
        description: "Notification marked as read",
        icon: "ph:check-circle-duotone",
        progress: true,
      });
    }
  } catch (error) {
    console.error("Error marking notification as read:", error);
    toaster.add({
      title: "Error",
      description: "Failed to mark notification as read",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  }
};

const markAllAsRead = async () => {
  try {
    const response = await api.put("/notifications/read-all");

    if (response?.data) {
      // Update all notifications in the list
      notifications.value = notifications.value.map((notification) => ({
        ...notification,
        read: true,
      }));

      toaster.add({
        title: "Success",
        description: "All notifications marked as read",
        icon: "ph:check-circle-duotone",
        progress: true,
      });
    }
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    toaster.add({
      title: "Error",
      description: "Failed to mark all notifications as read",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  }
};

const deleteNotification = async (notification: Notification) => {
  try {
    const response = await api.delete(`/notifications/${notification.id}`);

    if (response?.data) {
      // Remove the notification from the list
      notifications.value = notifications.value.filter(
        (n) => n.id !== notification.id
      );

      toaster.add({
        title: "Success",
        description: "Notification deleted",
        icon: "ph:check-circle-duotone",
        progress: true,
      });
    }
  } catch (error) {
    console.error("Error deleting notification:", error);
    toaster.add({
      title: "Error",
      description: "Failed to delete notification",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  }
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchNotifications();
};

const updateNotificationSettings = async () => {
  try {
    const response = await api.put(
      "/notifications/settings",
      notificationSettings.value
    );

    if (response?.data) {
      toaster.add({
        title: "Success",
        description: "Notification settings updated",
        icon: "ph:check-circle-duotone",
        progress: true,
      });
    }
  } catch (error) {
    console.error("Error updating notification settings:", error);
    toaster.add({
      title: "Error",
      description: "Failed to update notification settings",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  }
};

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser();
  }

  // Fetch notifications and settings
  fetchNotifications();
  fetchNotificationSettings();
});
</script>
