@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules\nuxt-component-meta\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules\nuxt-component-meta\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules\nuxt-component-meta\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules\nuxt-component-meta\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\nuxt-component-meta@0.10.1_magicast@0.3.5\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\nuxt-component-meta\bin\nuxt-component-meta.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\nuxt-component-meta\bin\nuxt-component-meta.mjs" %*
)
