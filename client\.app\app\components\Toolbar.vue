<!-- client/.app/app/components/Toolbar.vue -->

<script setup lang="ts">
// Import statements
import { onBeforeUnmount, onMounted, ref } from "vue";
import { useUserStore } from "../../stores/useUserStore";
import LanguageSelector from "./LanguageSelector.vue";

const props = defineProps<{
  isSubsidebarCollapsed?: boolean;
}>();

const emits = defineEmits<{
  toggleMobileNav: [];
  toggleSubsidebar: [collapsed: boolean];
}>();

const route = useRoute();
// Setup variables
const { t, locale, locales, setLocale } = useI18n();
const userStore = useUserStore();

// Dynamic page title based on current route
const pageTitle = computed(() => {
  const path = route.path;

  // First, check if the page has metadata with a title
  if (route.meta?.title) {
    return route.meta.title;
  }

  // Define page titles based on routes using translations
  const pageTitles: Record<string, string> = {
    "/": t("toolbar.page_titles.dashboard"),
    "/dashboard": t("toolbar.page_titles.dashboard"),
    "/users/profile": t("toolbar.page_titles.profile"),
    "/users/profile/edit": t("toolbar.page_titles.edit_profile"),
    "/users/company": t("toolbar.page_titles.company_profile"),
    "/users/company/edit": t("toolbar.page_titles.edit_company"),
    "/communication": t("toolbar.page_titles.communication"),
    "/communication/chat": t("toolbar.page_titles.chat"),
    "/communication/messages": t("toolbar.page_titles.messages"),
    "/calendar": t("toolbar.page_titles.calendar"),
    "/map": t("toolbar.page_titles.map"),
    "/production": t("toolbar.page_titles.production"),
    "/budget": t("toolbar.page_titles.budget"),
    "/hr": t("toolbar.page_titles.human_resources"),
    "/companies": t("toolbar.page_titles.companies"),
    "/accounting": t("toolbar.page_titles.accounting"),
    "/recruitment": t("toolbar.page_titles.recruitment"),
    "/sales": t("toolbar.page_titles.sales"),
    "/timemanagement": t("toolbar.page_titles.time_management"),
    "/subscriptions": t("toolbar.page_titles.subscriptions"),
    "/subscriptions/plans": t("toolbar.page_titles.subscription_plans"),
    "/workforce-hub": t("toolbar.page_titles.workforce_hub"),
  };

  // Check for exact match first
  if (pageTitles[path]) {
    return pageTitles[path];
  }

  // Check for dynamic routes (e.g., /companies/123)
  for (const [routePattern, title] of Object.entries(pageTitles)) {
    if (path.startsWith(routePattern) && routePattern !== "/") {
      return title;
    }
  }

  // Fallback: capitalize the last segment of the path
  const segments = path.split("/").filter(Boolean);
  if (segments.length > 0) {
    const lastSegment = segments[segments.length - 1];
    return (
      lastSegment.charAt(0).toUpperCase() +
      lastSegment.slice(1).replace(/-/g, " ")
    );
  }

  return "Dashboard";
});

// User role display - shows the user's primary role
const userRoleDisplay = computed(() => {
  if (!userStore.user) return t("sidebar.unknown_role");

  // Check for superadmin first
  if (userStore.user.isSuperAdmin) {
    return t("toolbar.roles.superadmin");
  }

  // Get the first role from user roles
  const roles = userStore.user.roles;
  if (roles.length === 0) return t("sidebar.unknown_role");

  const primaryRole = roles[0]?.role?.toLowerCase();

  // Map role to translation key
  const roleTranslationKey = `toolbar.roles.${primaryRole}`;
  return t(roleTranslationKey);
});

// Company relationship display - shows if user is owner or employee
const companyRelationshipDisplay = computed(() => {
  if (!userStore.user?.companies || userStore.user.companies.length === 0)
    return "";

  // Get the primary company (first one)
  const primaryCompany = userStore.user.companies[0];
  if (!primaryCompany) return "";

  // Check if user is the owner of the company
  // This is determined by the role in the company relationship
  const companyRole = primaryCompany.role;

  // If the role is 'OWNER' or if the user created the company, they are the owner
  if (companyRole === "OWNER" || companyRole === "ADMIN") {
    return t("toolbar.roles.owner");
  } else {
    return t("toolbar.roles.employee");
  }
});

// User full name computed
const userFullName = computed(() => {
  if (!userStore.user) return "";
  return `${userStore.user.firstName} ${userStore.user.lastName}`.trim();
});

// Primary company data computed
const primaryCompanyData = computed(() => {
  if (!userStore.user?.companies || userStore.user.companies.length === 0)
    return null;
  return userStore.user.companies[0];
});

// Check if current page should show collapse button
const shouldShowCollapseButton = computed(() => {
  const route = useRoute();

  // Don't show collapse button on special pages that shouldn't collapse
  const specialPages = [
    "/calendar",
    "/communication/email",
    "/communication/chat",
  ];

  return !specialPages.some((page) => route.path.startsWith(page));
});

// Toggle subsidebar collapse
const toggleSubsidebar = () => {
  const newCollapsedState = !props.isSubsidebarCollapsed;
  console.log("Toolbar: Toggling subsidebar to:", newCollapsedState);

  // Emit event to parent layout to handle the collapse
  emits("toggleSubsidebar", newCollapsedState);
};

// PWA installation state
const isPwaInstallable = ref(false);
const deferredPrompt = ref<any>(null);

// This function is now moved to the LanguageSelector component

// Function to switch language
async function switchLanguage(code: string) {
  try {
    // Update the i18n locale first for immediate UI update
    setLocale(code);

    // Then update the user preferences in the store
    await userStore.updatePreferences({
      language: code,
    });

    // User preferences updated successfully
  } catch (error) {
    // Handle error but ensure locale is still updated
    // Even if the API call fails, we still want to update the store's local state
    userStore.$patch({
      preferences: {
        ...userStore.preferences,
        language: code,
      },
    });
  }
}

/**
 * Handle the beforeinstallprompt event for PWA installation
 */
function handleBeforeInstallPrompt(e: Event) {
  // Prevent the default browser install prompt
  e.preventDefault();

  // Store the event for later use
  deferredPrompt.value = e;

  // Show the install button
  isPwaInstallable.value = true;
}

/**
 * Open the PWA installation modal
 */
function openPwaInstallModal() {
  // Find the PwaInstallModal component in the DOM
  const modalElements = document.querySelectorAll("*");
  let modalComponent = null;

  for (const element of modalElements) {
    // @ts-ignore
    if (
      element.__vueParentComponent &&
      element.__vueParentComponent.ctx &&
      // @ts-ignore
      element.__vueParentComponent.ctx.openInstallModal
    ) {
      modalComponent = element;
      break;
    }
  }

  if (modalComponent) {
    // @ts-ignore
    modalComponent.__vueParentComponent.ctx.openInstallModal();
  } else {
    // Fallback: If we can't find the modal component, trigger the browser's native prompt
    if (deferredPrompt.value) {
      deferredPrompt.value.prompt();
      deferredPrompt.value.userChoice.then((choiceResult: any) => {
        if (choiceResult.outcome === "accepted") {
          console.warn("User accepted the PWA installation");
          localStorage.setItem("pwaInstalled", "true");
        } else {
          console.warn("User dismissed the PWA installation");
        }
        deferredPrompt.value = null;
        isPwaInstallable.value = false;
      });
    }
  }
}

/**
 * Handle the appinstalled event
 */
function handleAppInstalled() {
  // Hide the install button
  isPwaInstallable.value = false;

  // Set a flag in localStorage to remember the app was installed
  localStorage.setItem("pwaInstalled", "true");

  // Log the installation
  console.warn("PWA was installed");
}

onMounted(() => {
  // Add event listeners for PWA installation
  window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
  window.addEventListener("appinstalled", handleAppInstalled);

  // Check if the app is already installed
  const isStandalone =
    window.matchMedia("(display-mode: standalone)").matches ||
    (window.navigator as any).standalone === true;
  const isInstalledFlag = localStorage.getItem("pwaInstalled") === "true";

  if (isStandalone || isInstalledFlag) {
    isPwaInstallable.value = false;
  }
});

onBeforeUnmount(() => {
  // Clean up event listeners
  window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
  window.removeEventListener("appinstalled", handleAppInstalled);
});
</script>

<template>
  <div
    class="pl-1 pr-4 relative z-50 w-full flex items-center justify-between h-14"
  >
    <div class="flex items-center gap-2">
      <!-- Desktop Subsidebar Collapse Button -->
      <button
        v-if="shouldShowCollapseButton"
        class="hidden md:flex items-center justify-center rounded-md h-10 w-10 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors"
        @click="toggleSubsidebar"
        :title="
          props.isSubsidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'
        "
      >
        <Icon
          :name="
            props.isSubsidebarCollapsed
              ? 'solar:double-alt-arrow-right-line-duotone'
              : 'solar:double-alt-arrow-left-line-duotone'
          "
          class="size-10 text-muted-400 transition-all duration-200"
        />
      </button>

      <!-- Mobile Menu Button -->
      <button
        class="md:hidden flex items-center justify-center h-10 w-10 rounded-full bg-white dark:bg-muted-900 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors"
        @click="emits('toggleMobileNav')"
      >
        <Icon name="lucide:menu" class="size-5 text-muted-400" />
      </button>

      <div class="flex items-center gap-4">
        <h1 class="text-lg font-medium text-muted-800 dark:text-white mr-6">
          {{ pageTitle }}
        </h1>

        <!-- User and Company Information -->
        <div class="flex flex-row items-center gap-4">
          <!-- User Info -->
          <div class="flex flex-col items-center justify-center">
            <BaseTag
              rounded="md"
              variant="none"
              class="bg-primary-500 text-white ring-1 ring-inset ring-yellow-400/30 !px-2 !py-0"
            >
              {{ userRoleDisplay }}
            </BaseTag>
            <BaseSnack
              :label="userFullName || $t('sidebar.unknown_user')"
              variant="default"
              size="xs"
              :image="userStore.user?.avatar || '/img/avatars/person-1.jpg'"
              class="whitespace-nowrap"
            />
          </div>

          <!-- Company Info -->
          <div
            class="flex-col items-center justify-center md:flex hidden"
            v-if="primaryCompanyData"
          >
            <BaseTag
              rounded="md"
              variant="none"
              class="bg-primary-500 text-white ring-1 ring-inset ring-yellow-400/30 !px-2 !py-0"
            >
              {{ companyRelationshipDisplay }}
            </BaseTag>
            <BaseSnack
              :label="primaryCompanyData.company.name"
              variant="default"
              size="xs"
              :image="primaryCompanyData.company.logo"
              :icon="
                !primaryCompanyData.company.logo ? 'ph:building' : undefined
              "
              class="whitespace-nowrap"
            />
          </div>

          <!-- AI Feedback placeholder removed -->
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end gap-x-3">
      <!-- PWA Install Button - Only shown when installation is available -->
      <BaseButton
        v-if="isPwaInstallable"
        color="primary"
        variant="default"
        class="hidden md:flex items-center gap-x-2"
        @click="openPwaInstallModal"
      >
        <Icon name="solar:download-minimalistic-linear" class="size-4" />
        <span>{{ $t("pwa.install.buttons.install") }}</span>
      </BaseButton>

      <!-- Theme toggle moved from sidebar -->
      <div class="flex items-center gap-2">
        <BaseThemeToggle class="scale-90" />

        <!-- Custom Language Selector -->
        <LanguageSelector
          :current-locale="locale"
          :available-locales="locales"
          @update:locale="switchLanguage"
          class="language-selector"
        />
      </div>
    </div>
  </div>
</template>
