#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules/escodegen/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules/escodegen/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules/escodegen/bin/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules/escodegen/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/escodegen@2.1.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../escodegen/bin/escodegen.js" "$@"
else
  exec node  "$basedir/../escodegen/bin/escodegen.js" "$@"
fi
