@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules\untun\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules\untun\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules\untun\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules\untun\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\untun@0.1.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\untun\bin\untun.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\untun\bin\untun.mjs" %*
)
