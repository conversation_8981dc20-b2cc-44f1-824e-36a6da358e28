<template>
  <TairoModal :open="isOpen" size="md" @close="$emit('close')">
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Invite Team Member
        </h3>
        <BaseButtonClose @click="$emit('close')" />
      </div>
    </template>
    <div class="p-4 md:p-6">
      <form @submit.prevent="handleSubmit">
        <div class="mb-4">
          <BaseInput
            v-model="form.email"
            label="Email Address"
            placeholder="Enter email address"
            icon="ph:envelope-duotone"
            :error="errors.email"
            required
          />
        </div>

        <div class="mb-4">
          <BaseListbox
            v-model="form.role"
            :items="roleOptions"
            :properties="{ value: 'value', label: 'label' }"
            label="Role"
            placeholder="Select role"
            size="sm"
            rounded="md"
            :error="errors.role"
            required
          />
        </div>

        <div class="mb-4">
          <BaseTextarea
            v-model="form.message"
            label="Message (Optional)"
            placeholder="Add a personal message to the invitation"
            :rows="3"
          />
        </div>

        <div class="flex justify-end space-x-2">
          <BaseButton type="button" color="muted" @click="$emit('close')"> Cancel </BaseButton>
          <BaseButton type="submit" color="primary" :loading="isSubmitting">
            Send Invitation
          </BaseButton>
        </div>
      </form>
    </div>
  </TairoModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  isSubmitting: {
    type: Boolean,
    default: false,
  },
  initialData: {
    type: Object,
    default: () => ({
      email: '',
      role: '',
      message: '',
    }),
  },
})

const emit = defineEmits(['close', 'submit'])

// Role options
const roleOptions = [
  { label: 'Admin', value: 'ADMIN' },
  { label: 'Project Leader', value: 'PROJECTLEADER' },
  { label: 'Salesman', value: 'SALESMAN' },
  { label: 'Worker', value: 'WORKER' },
  { label: 'Client', value: 'CLIENT' },
]

// Form state
const form = ref({
  email: props.initialData.email || '',
  role: props.initialData.role || '',
  message: props.initialData.message || '',
})

// Error state
const errors = ref({
  email: '',
  role: '',
})

// Validate form
const validateForm = () => {
  let isValid = true
  errors.value = {
    email: '',
    role: '',
  }

  // Validate email
  if (!form.value.email) {
    errors.value.email = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = 'Please enter a valid email address'
    isValid = false
  }

  // Validate role
  if (!form.value.role) {
    errors.value.role = 'Role is required'
    isValid = false
  }

  return isValid
}

// Handle form submission
const handleSubmit = () => {
  console.log('InviteModal: handleSubmit called')
  if (validateForm()) {
    // Ensure role is properly formatted
    const formData = { ...form.value }

    // If role is an object from BaseListbox, extract the value
    if (typeof formData.role === 'object' && formData.role !== null && formData.role.value) {
      console.log('InviteModal: Converting role object to string value:', formData.role)
      formData.role = formData.role.value
    }

    console.log('InviteModal: Form is valid, emitting submit event with data:', formData)
    emit('submit', formData)
  } else {
    console.log('InviteModal: Form validation failed with errors:', errors.value)
  }
}
</script>
