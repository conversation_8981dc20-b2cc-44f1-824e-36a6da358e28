import { Router } from "express";
import { auth } from "../middleware/auth.js";
import { wrapController } from "../utils/wrapController.js";

// Import card controllers
import {
  getUserCards,
  getCard,
  createCard,
  setPrimaryCard,
  updateCard,
  deleteCard,
} from "../controllers/payment/cards.controller.js";

// Import transaction controllers
import {
  getUserTransactions,
  getTransaction,
  createTransaction,
  updateTransactionStatus,
  getTransactionStats,
} from "../controllers/payment/transactions.controller.js";

const router = Router();

// Payment Cards Routes
// Get all user's payment cards
router.get("/cards", auth, wrapController(getUserCards));

// Get a specific payment card
router.get("/cards/:cardId", auth, wrapController(getCard));

// Create a new payment card
router.post("/cards", auth, wrapController(createCard));

// Set a card as primary
router.put("/cards/:cardId/set-primary", auth, wrapController(setPrimaryCard));

// Update a payment card
router.put("/cards/:cardId", auth, wrapController(updateCard));

// Delete a payment card
router.delete("/cards/:cardId", auth, wrapController(deleteCard));

// Payment Transactions Routes
// Get all user's payment transactions
router.get("/transactions", auth, wrapController(getUserTransactions));

// Get a specific payment transaction
router.get("/transactions/:transactionId", auth, wrapController(getTransaction));

// Create a new payment transaction
router.post("/transactions", auth, wrapController(createTransaction));

// Update transaction status
router.put("/transactions/:transactionId/status", auth, wrapController(updateTransactionStatus));

// Get transaction statistics
router.get("/transactions/stats", auth, wrapController(getTransactionStats));

export default router;
