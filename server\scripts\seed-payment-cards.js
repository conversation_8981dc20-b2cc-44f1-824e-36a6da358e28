const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedPaymentCards() {
  try {
    console.log('🌱 Starting payment cards seeding...');

    // Find <PERSON><PERSON>'s user record
    const timoUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });

    if (!timoUser) {
      console.error('❌ User <EMAIL> not found');
      return;
    }

    console.log(`✅ Found user: ${timoUser.firstName} ${timoUser.lastName} (ID: ${timoUser.id})`);

    // Clear existing payment cards for this user
    await prisma.paymentCard.deleteMany({
      where: {
        userId: timoUser.id
      }
    });

    console.log('🧹 Cleared existing payment cards');

    // Create payment cards for Timo
    const cards = [
      {
        userId: timoUser.id,
        stripePaymentMethodId: 'pm_1234567890_visa_4242',
        cardholderName: `${timoUser.firstName} ${timoUser.lastName}`,
        last4: '4242',
        brand: 'visa',
        expMonth: 12,
        expYear: 2025,
        fingerprint: 'visa_4242_fingerprint',
        isPrimary: true,
        isActive: true,
      },
      {
        userId: timoUser.id,
        stripePaymentMethodId: 'pm_1234567890_mastercard_5555',
        cardholderName: `${timoUser.firstName} ${timoUser.lastName}`,
        last4: '5555',
        brand: 'mastercard',
        expMonth: 8,
        expYear: 2026,
        fingerprint: 'mastercard_5555_fingerprint',
        isPrimary: false,
        isActive: true,
      },
      {
        userId: timoUser.id,
        stripePaymentMethodId: 'pm_1234567890_amex_1234',
        cardholderName: `${timoUser.firstName} ${timoUser.lastName}`,
        last4: '1234',
        brand: 'amex',
        expMonth: 3,
        expYear: 2027,
        fingerprint: 'amex_1234_fingerprint',
        isPrimary: false,
        isActive: true,
      }
    ];

    // Create the cards
    for (const cardData of cards) {
      const card = await prisma.paymentCard.create({
        data: cardData
      });
      console.log(`💳 Created ${card.brand.toUpperCase()} card ending in ${card.last4} (${card.isPrimary ? 'Primary' : 'Secondary'})`);
    }

    // Create some sample transactions
    const transactions = [
      {
        userId: timoUser.id,
        description: 'Standard Plan - January 2025',
        amount: 59.0,
        currency: 'USD',
        status: 'complete',
        type: 'subscription',
        stripePaymentIntentId: 'pi_1234567890_jan2025',
      },
      {
        userId: timoUser.id,
        description: 'Standard Plan - December 2024',
        amount: 59.0,
        currency: 'USD',
        status: 'complete',
        type: 'subscription',
        stripePaymentIntentId: 'pi_1234567890_dec2024',
      },
      {
        userId: timoUser.id,
        description: 'Basic Plan - November 2024',
        amount: 29.0,
        currency: 'USD',
        status: 'complete',
        type: 'subscription',
        stripePaymentIntentId: 'pi_1234567890_nov2024',
      },
      {
        userId: timoUser.id,
        description: 'Standard Plan - October 2024',
        amount: 59.0,
        currency: 'USD',
        status: 'complete',
        type: 'subscription',
        stripePaymentIntentId: 'pi_1234567890_oct2024',
      },
      {
        userId: timoUser.id,
        description: 'Setup Fee - October 2024',
        amount: 25.0,
        currency: 'USD',
        status: 'complete',
        type: 'one-time',
        stripePaymentIntentId: 'pi_1234567890_setup_oct2024',
      }
    ];

    // Clear existing transactions for this user
    await prisma.paymentTransaction.deleteMany({
      where: {
        userId: timoUser.id
      }
    });

    console.log('🧹 Cleared existing transactions');

    // Create the transactions
    for (const transactionData of transactions) {
      const transaction = await prisma.paymentTransaction.create({
        data: transactionData
      });
      console.log(`💰 Created transaction: ${transaction.description} - $${transaction.amount}`);
    }

    console.log('✅ Payment cards and transactions seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Created ${cards.length} payment cards`);
    console.log(`   - Created ${transactions.length} transactions`);
    console.log(`   - Primary card: ${cards.find(c => c.isPrimary).brand.toUpperCase()} ending in ${cards.find(c => c.isPrimary).last4}`);

  } catch (error) {
    console.error('❌ Error seeding payment cards:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedPaymentCards()
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedPaymentCards };
