import { NuxtModule, RuntimeConfig } from '@nuxt/schema'
declare module '@nuxt/schema' {
  interface NuxtOptions {
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/content-documentation`
     */
    ["content-documentation"]: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/content-documentation").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/purge-comments`
     */
    ["purge-comments"]: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/purge-comments").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `./../../modules/mock-component-meta`
     */
    ["mockComponentMeta"]: typeof import("./../../modules/mock-component-meta").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `./../../app/modules/aria-hidden-patch`
     */
    ["ariaHiddenPatch"]: typeof import("./../../app/modules/aria-hidden-patch").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `reka-ui/nuxt`
     */
    ["reka"]: typeof import("reka-ui/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@vueuse/nuxt`
     */
    ["vueuse"]: typeof import("@vueuse/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxtjs/i18n`
     */
    ["i18n"]: typeof import("@nuxtjs/i18n").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/image`
     */
    ["image"]: typeof import("@nuxt/image").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/fonts`
     */
    ["fonts"]: typeof import("@nuxt/fonts").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@pinia/nuxt`
     */
    ["pinia"]: typeof import("@pinia/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `pinia-plugin-persistedstate/nuxt`
     */
    ["piniaPluginPersistedstate"]: typeof import("pinia-plugin-persistedstate/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `nuxt-icon`
     */
    ["icon"]: typeof import("nuxt-icon").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module`
     */
    ["site"]: typeof import("C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `nuxt-og-image`
     */
    ["ogImage"]: typeof import("nuxt-og-image").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module`
     */
    ["devtools"]: typeof import("C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxtjs/color-mode`
     */
    ["colorMode"]: typeof import("@nuxtjs/color-mode").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/icon`
     */
    ["icon"]: typeof import("@nuxt/icon").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@shuriken-ui/nuxt`
     */
    ["nui"]: typeof import("@shuriken-ui/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/telemetry`
     */
    ["telemetry"]: typeof import("@nuxt/telemetry").default extends NuxtModule<infer O> ? O : Record<string, any>
  }
  interface NuxtConfig {
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/content-documentation`
     */
    ["content-documentation"]?: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/content-documentation").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/purge-comments`
     */
    ["purge-comments"]?: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/purge-comments").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `./../../modules/mock-component-meta`
     */
    ["mockComponentMeta"]?: typeof import("./../../modules/mock-component-meta").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `./../../app/modules/aria-hidden-patch`
     */
    ["ariaHiddenPatch"]?: typeof import("./../../app/modules/aria-hidden-patch").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `reka-ui/nuxt`
     */
    ["reka"]?: typeof import("reka-ui/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@vueuse/nuxt`
     */
    ["vueuse"]?: typeof import("@vueuse/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxtjs/i18n`
     */
    ["i18n"]?: typeof import("@nuxtjs/i18n").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/image`
     */
    ["image"]?: typeof import("@nuxt/image").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/fonts`
     */
    ["fonts"]?: typeof import("@nuxt/fonts").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@pinia/nuxt`
     */
    ["pinia"]?: typeof import("@pinia/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `pinia-plugin-persistedstate/nuxt`
     */
    ["piniaPluginPersistedstate"]?: typeof import("pinia-plugin-persistedstate/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `nuxt-icon`
     */
    ["icon"]?: typeof import("nuxt-icon").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module`
     */
    ["site"]?: typeof import("C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `nuxt-og-image`
     */
    ["ogImage"]?: typeof import("nuxt-og-image").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module`
     */
    ["devtools"]?: typeof import("C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxtjs/color-mode`
     */
    ["colorMode"]?: typeof import("@nuxtjs/color-mode").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/icon`
     */
    ["icon"]?: typeof import("@nuxt/icon").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@shuriken-ui/nuxt`
     */
    ["nui"]?: typeof import("@shuriken-ui/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/telemetry`
     */
    ["telemetry"]?: typeof import("@nuxt/telemetry").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    modules?: (undefined | null | false | NuxtModule<any> | string | [NuxtModule | string, Record<string, any>] | ["C:/Users/<USER>/comanager/client/.landing/modules/content-documentation", Exclude<NuxtConfig["content-documentation"], boolean>] | ["C:/Users/<USER>/comanager/client/.landing/modules/purge-comments", Exclude<NuxtConfig["purge-comments"], boolean>] | ["./../../modules/mock-component-meta", Exclude<NuxtConfig["mockComponentMeta"], boolean>] | ["./../../app/modules/aria-hidden-patch", Exclude<NuxtConfig["ariaHiddenPatch"], boolean>] | ["reka-ui/nuxt", Exclude<NuxtConfig["reka"], boolean>] | ["@vueuse/nuxt", Exclude<NuxtConfig["vueuse"], boolean>] | ["@nuxtjs/i18n", Exclude<NuxtConfig["i18n"], boolean>] | ["@nuxt/image", Exclude<NuxtConfig["image"], boolean>] | ["@nuxt/fonts", Exclude<NuxtConfig["fonts"], boolean>] | ["@pinia/nuxt", Exclude<NuxtConfig["pinia"], boolean>] | ["pinia-plugin-persistedstate/nuxt", Exclude<NuxtConfig["piniaPluginPersistedstate"], boolean>] | ["nuxt-icon", Exclude<NuxtConfig["icon"], boolean>] | ["C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module", Exclude<NuxtConfig["site"], boolean>] | ["nuxt-og-image", Exclude<NuxtConfig["ogImage"], boolean>] | ["C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module", Exclude<NuxtConfig["devtools"], boolean>] | ["@nuxtjs/color-mode", Exclude<NuxtConfig["colorMode"], boolean>] | ["@nuxt/icon", Exclude<NuxtConfig["icon"], boolean>] | ["@shuriken-ui/nuxt", Exclude<NuxtConfig["nui"], boolean>] | ["@nuxt/telemetry", Exclude<NuxtConfig["telemetry"], boolean>])[],
  }
}
declare module 'nuxt/schema' {
  interface NuxtOptions {
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/content-documentation`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/.landing/modules/content-documentation
     */
    ["content-documentation"]: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/content-documentation").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/purge-comments`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/.landing/modules/purge-comments
     */
    ["purge-comments"]: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/purge-comments").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `./../../modules/mock-component-meta`
     * @see https://www.npmjs.com/package/./../../modules/mock-component-meta
     */
    ["mockComponentMeta"]: typeof import("./../../modules/mock-component-meta").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `./../../app/modules/aria-hidden-patch`
     * @see https://www.npmjs.com/package/./../../app/modules/aria-hidden-patch
     */
    ["ariaHiddenPatch"]: typeof import("./../../app/modules/aria-hidden-patch").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `reka-ui/nuxt`
     * @see https://www.npmjs.com/package/reka-ui/nuxt
     */
    ["reka"]: typeof import("reka-ui/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@vueuse/nuxt`
     * @see https://www.npmjs.com/package/@vueuse/nuxt
     */
    ["vueuse"]: typeof import("@vueuse/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxtjs/i18n`
     * @see https://www.npmjs.com/package/@nuxtjs/i18n
     */
    ["i18n"]: typeof import("@nuxtjs/i18n").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/image`
     * @see https://www.npmjs.com/package/@nuxt/image
     */
    ["image"]: typeof import("@nuxt/image").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/fonts`
     * @see https://www.npmjs.com/package/@nuxt/fonts
     */
    ["fonts"]: typeof import("@nuxt/fonts").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@pinia/nuxt`
     * @see https://www.npmjs.com/package/@pinia/nuxt
     */
    ["pinia"]: typeof import("@pinia/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `pinia-plugin-persistedstate/nuxt`
     * @see https://www.npmjs.com/package/pinia-plugin-persistedstate/nuxt
     */
    ["piniaPluginPersistedstate"]: typeof import("pinia-plugin-persistedstate/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `nuxt-icon`
     * @see https://www.npmjs.com/package/nuxt-icon
     */
    ["icon"]: typeof import("nuxt-icon").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module
     */
    ["site"]: typeof import("C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `nuxt-og-image`
     * @see https://www.npmjs.com/package/nuxt-og-image
     */
    ["ogImage"]: typeof import("nuxt-og-image").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module
     */
    ["devtools"]: typeof import("C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxtjs/color-mode`
     * @see https://www.npmjs.com/package/@nuxtjs/color-mode
     */
    ["colorMode"]: typeof import("@nuxtjs/color-mode").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/icon`
     * @see https://www.npmjs.com/package/@nuxt/icon
     */
    ["icon"]: typeof import("@nuxt/icon").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@shuriken-ui/nuxt`
     * @see https://www.npmjs.com/package/@shuriken-ui/nuxt
     */
    ["nui"]: typeof import("@shuriken-ui/nuxt").default extends NuxtModule<infer O> ? O : Record<string, any>
    /**
     * Configuration for `@nuxt/telemetry`
     * @see https://www.npmjs.com/package/@nuxt/telemetry
     */
    ["telemetry"]: typeof import("@nuxt/telemetry").default extends NuxtModule<infer O> ? O : Record<string, any>
  }
  interface NuxtConfig {
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/content-documentation`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/.landing/modules/content-documentation
     */
    ["content-documentation"]?: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/content-documentation").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/.landing/modules/purge-comments`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/.landing/modules/purge-comments
     */
    ["purge-comments"]?: typeof import("C:/Users/<USER>/comanager/client/.landing/modules/purge-comments").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `./../../modules/mock-component-meta`
     * @see https://www.npmjs.com/package/./../../modules/mock-component-meta
     */
    ["mockComponentMeta"]?: typeof import("./../../modules/mock-component-meta").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `./../../app/modules/aria-hidden-patch`
     * @see https://www.npmjs.com/package/./../../app/modules/aria-hidden-patch
     */
    ["ariaHiddenPatch"]?: typeof import("./../../app/modules/aria-hidden-patch").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `reka-ui/nuxt`
     * @see https://www.npmjs.com/package/reka-ui/nuxt
     */
    ["reka"]?: typeof import("reka-ui/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@vueuse/nuxt`
     * @see https://www.npmjs.com/package/@vueuse/nuxt
     */
    ["vueuse"]?: typeof import("@vueuse/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxtjs/i18n`
     * @see https://www.npmjs.com/package/@nuxtjs/i18n
     */
    ["i18n"]?: typeof import("@nuxtjs/i18n").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/image`
     * @see https://www.npmjs.com/package/@nuxt/image
     */
    ["image"]?: typeof import("@nuxt/image").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/fonts`
     * @see https://www.npmjs.com/package/@nuxt/fonts
     */
    ["fonts"]?: typeof import("@nuxt/fonts").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@pinia/nuxt`
     * @see https://www.npmjs.com/package/@pinia/nuxt
     */
    ["pinia"]?: typeof import("@pinia/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `pinia-plugin-persistedstate/nuxt`
     * @see https://www.npmjs.com/package/pinia-plugin-persistedstate/nuxt
     */
    ["piniaPluginPersistedstate"]?: typeof import("pinia-plugin-persistedstate/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `nuxt-icon`
     * @see https://www.npmjs.com/package/nuxt-icon
     */
    ["icon"]?: typeof import("nuxt-icon").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module
     */
    ["site"]?: typeof import("C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `nuxt-og-image`
     * @see https://www.npmjs.com/package/nuxt-og-image
     */
    ["ogImage"]?: typeof import("nuxt-og-image").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module`
     * @see https://www.npmjs.com/package/C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module
     */
    ["devtools"]?: typeof import("C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxtjs/color-mode`
     * @see https://www.npmjs.com/package/@nuxtjs/color-mode
     */
    ["colorMode"]?: typeof import("@nuxtjs/color-mode").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/icon`
     * @see https://www.npmjs.com/package/@nuxt/icon
     */
    ["icon"]?: typeof import("@nuxt/icon").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@shuriken-ui/nuxt`
     * @see https://www.npmjs.com/package/@shuriken-ui/nuxt
     */
    ["nui"]?: typeof import("@shuriken-ui/nuxt").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    /**
     * Configuration for `@nuxt/telemetry`
     * @see https://www.npmjs.com/package/@nuxt/telemetry
     */
    ["telemetry"]?: typeof import("@nuxt/telemetry").default extends NuxtModule<infer O> ? Partial<O> : Record<string, any>
    modules?: (undefined | null | false | NuxtModule<any> | string | [NuxtModule | string, Record<string, any>] | ["C:/Users/<USER>/comanager/client/.landing/modules/content-documentation", Exclude<NuxtConfig["content-documentation"], boolean>] | ["C:/Users/<USER>/comanager/client/.landing/modules/purge-comments", Exclude<NuxtConfig["purge-comments"], boolean>] | ["./../../modules/mock-component-meta", Exclude<NuxtConfig["mockComponentMeta"], boolean>] | ["./../../app/modules/aria-hidden-patch", Exclude<NuxtConfig["ariaHiddenPatch"], boolean>] | ["reka-ui/nuxt", Exclude<NuxtConfig["reka"], boolean>] | ["@vueuse/nuxt", Exclude<NuxtConfig["vueuse"], boolean>] | ["@nuxtjs/i18n", Exclude<NuxtConfig["i18n"], boolean>] | ["@nuxt/image", Exclude<NuxtConfig["image"], boolean>] | ["@nuxt/fonts", Exclude<NuxtConfig["fonts"], boolean>] | ["@pinia/nuxt", Exclude<NuxtConfig["pinia"], boolean>] | ["pinia-plugin-persistedstate/nuxt", Exclude<NuxtConfig["piniaPluginPersistedstate"], boolean>] | ["nuxt-icon", Exclude<NuxtConfig["icon"], boolean>] | ["C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt-site-config@3.2.0_magi_c47fed06ea7922a70d88b1109ef80da8/node_modules/nuxt-site-config/dist/module", Exclude<NuxtConfig["site"], boolean>] | ["nuxt-og-image", Exclude<NuxtConfig["ogImage"], boolean>] | ["C:/Users/<USER>/AppData/Roaming/npm/node_modules/@nuxt/devtools/module", Exclude<NuxtConfig["devtools"], boolean>] | ["@nuxtjs/color-mode", Exclude<NuxtConfig["colorMode"], boolean>] | ["@nuxt/icon", Exclude<NuxtConfig["icon"], boolean>] | ["@shuriken-ui/nuxt", Exclude<NuxtConfig["nui"], boolean>] | ["@nuxt/telemetry", Exclude<NuxtConfig["telemetry"], boolean>])[],
  }
  interface RuntimeConfig {
   app: {
      buildId: string,

      baseURL: string,

      buildAssetsDir: string,

      cdnURL: string,
   },

   nitro: {
      envPrefix: string,
   },

   icon: {
      serverKnownCssClasses: Array<any>,
   },

   "nuxt-site-config": {
      stack: Array<{

      }>,

      version: string,

      debug: boolean,

      multiTenancy: Array<any>,
   },

   "nuxt-og-image": {
      version: string,

      satoriOptions: any,

      resvgOptions: any,

      sharpOptions: any,

      publicStoragePath: string,

      defaults: {
         emojis: string,

         renderer: string,

         component: string,

         extension: string,

         width: number,

         height: number,

         cacheMaxAgeSeconds: number,
      },

      debug: boolean,

      baseCacheKey: string,

      fonts: Array<{

      }>,

      hasNuxtIcon: boolean,

      colorPreference: string,

      strictNuxtContentPaths: any,

      isNuxtContentDocumentDriven: boolean,
   },
  }
  interface PublicRuntimeConfig {
   mapboxToken: string,

   siteUrl: string,

   apiBase: string,

   DEBUG: string,

   gtagId: string,

   piniaPluginPersistedstate: any,

   i18n: {
      baseUrl: string,

      defaultLocale: string,

      defaultDirection: string,

      strategy: string,

      lazy: boolean,

      rootRedirect: any,

      routesNameSeparator: string,

      defaultLocaleRouteNameSuffix: string,

      skipSettingLocaleOnNavigate: boolean,

      differentDomains: boolean,

      trailingSlash: boolean,

      locales: Array<{

      }>,

      detectBrowserLanguage: {
         alwaysRedirect: boolean,

         cookieCrossOrigin: boolean,

         cookieDomain: any,

         cookieKey: string,

         cookieSecure: boolean,

         fallbackLocale: string,

         redirectOn: string,

         useCookie: boolean,
      },

      experimental: {
         localeDetector: string,

         switchLocalePathLinkSSR: boolean,

         autoImportTranslationFunctions: boolean,

         typedPages: boolean,

         typedOptionsAndMessages: boolean,

         generatedLocaleFilePathFormat: string,

         alternateLinkCanonicalQueries: boolean,

         hmr: boolean,
      },

      multiDomainLocales: boolean,
   },
  }
}
declare module 'vue' {
        interface ComponentCustomProperties {
          $config: RuntimeConfig
        }
      }