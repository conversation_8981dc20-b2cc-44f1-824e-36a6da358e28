<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <BaseInput
          v-model="searchQuery"
          icon="ph:magnifying-glass-duotone"
          placeholder="Search documents..."
          class="w-full sm:w-64"
        />
        <BaseSelect
          v-model="categoryFilter"
          :options="[
            { label: 'All Categories', value: '' },
            ...categoryOptions,
          ]"
          placeholder="Category"
          class="ml-2 w-full sm:w-40"
        />
      </div>
      <div class="flex items-center">
        <BaseSelect
          v-model="sortBy"
          :options="sortOptions"
          placeholder="Sort by"
          class="w-full sm:w-40"
        />
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <BasePlaceholderPage
        title="Loading documents"
        subtitle="Please wait while we load your documents"
      >
        <template #image>
          <BaseIconBox
            size="xl"
            shape="full"
            class="bg-muted-100 dark:bg-muted-700/60"
          >
            <Icon
              name="ph:spinner-gap-duotone"
              class="h-8 w-8 animate-spin text-primary-500"
            />
          </BaseIconBox>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="flex justify-center py-8">
      <BasePlaceholderPage title="Error loading documents" :subtitle="error">
        <template #image>
          <img
            class="block dark:hidden"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-error.svg"
            alt="Error illustration"
          />
          <img
            class="hidden dark:block"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-error-dark.svg"
            alt="Error illustration"
          />
        </template>
        <template #action>
          <BaseButton color="primary" @click="fetchDocuments">
            Try Again
          </BaseButton>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="filteredDocuments.length === 0"
      class="flex justify-center py-8"
    >
      <BasePlaceholderPage
        :title="
          searchQuery || categoryFilter
            ? 'No matching documents'
            : 'No documents yet'
        "
        :subtitle="
          searchQuery || categoryFilter
            ? 'Try adjusting your search or filter criteria'
            : 'Upload your first document to get started'
        "
      >
        <template #image>
          <img
            class="block dark:hidden"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-file.svg"
            alt="No documents illustration"
          />
          <img
            class="hidden dark:block"
            src="../../../public/img/illustrations/placeholders/flat/placeholder-file-dark.svg"
            alt="No documents illustration"
          />
        </template>
        <template #action>
          <BaseButton
            v-if="searchQuery || categoryFilter"
            color="muted"
            @click="resetFilters"
          >
            Clear Filters
          </BaseButton>
        </template>
      </BasePlaceholderPage>
    </div>

    <!-- Documents list -->
    <div v-else>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <BaseCard
          v-for="document in filteredDocuments"
          :key="document.id"
          class="p-4 hover:shadow-lg transition-shadow duration-300"
        >
          <div class="flex flex-col h-full">
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-center">
                <div class="p-2 rounded-lg bg-primary-500/10 mr-3">
                  <Icon
                    :name="getFileIcon(document.mimeType)"
                    class="h-6 w-6 text-primary-500"
                  />
                </div>
                <div>
                  <BaseHeading
                    tag="h4"
                    size="xs"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    {{ document.name }}
                  </BaseHeading>
                  <BaseText size="xs" class="text-muted-400">
                    {{ formatFileSize(document.size) }}
                  </BaseText>
                </div>
              </div>
              <BaseDropdown>
                <template #button>
                  <BaseButton color="muted" shape="circle" size="sm">
                    <Icon
                      name="ph:dots-three-outline-duotone"
                      class="h-4 w-4"
                    />
                  </BaseButton>
                </template>
                <template #content>
                  <BaseDropdownItem @click="openDocument(document)">
                    <Icon name="ph:eye-duotone" class="h-4 w-4 mr-2" />
                    <span>View</span>
                  </BaseDropdownItem>
                  <BaseDropdownItem @click="downloadDocument(document)">
                    <Icon
                      name="ph:download-simple-duotone"
                      class="h-4 w-4 mr-2"
                    />
                    <span>Download</span>
                  </BaseDropdownItem>
                  <BaseDropdownItem @click="confirmDeleteDocument(document)">
                    <div class="flex items-center text-danger-500">
                      <Icon name="ph:trash-duotone" class="h-4 w-4 mr-2" />
                      <span>Delete</span>
                    </div>
                  </BaseDropdownItem>
                </template>
              </BaseDropdown>
            </div>

            <div class="flex-grow">
              <p
                v-if="document.description"
                class="text-sm text-muted-500 dark:text-muted-400 mb-3 line-clamp-2"
              >
                {{ document.description }}
              </p>
            </div>

            <div
              class="mt-3 pt-3 border-t border-muted-200 dark:border-muted-700"
            >
              <div class="flex items-center justify-between">
                <div>
                  <BaseTag
                    v-if="document.category"
                    :color="getCategoryColor(document.category)"
                    flavor="pastel"
                    condensed
                  >
                    {{ formatCategory(document.category) }}
                  </BaseTag>
                  <BaseTag
                    v-if="document.module"
                    color="info"
                    flavor="pastel"
                    condensed
                    class="ml-1"
                  >
                    {{ formatModule(document.module) }}
                  </BaseTag>
                </div>
                <BaseText size="xs" class="text-muted-400">
                  {{ formatDate(document.createdAt) }}
                </BaseText>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <TairoModal :open="isDeleteModalOpen" size="sm" @close="closeDeleteModal">
      <template #header>
        <div class="flex w-full items-center justify-between p-4 md:p-6">
          <h3
            class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white"
          >
            Delete Document
          </h3>
          <BaseButtonClose @click="closeDeleteModal" />
        </div>
      </template>
      <div class="p-4 md:p-6">
        <p class="text-muted-500 dark:text-muted-400 mb-4">
          Are you sure you want to delete
          <span class="font-medium text-muted-800 dark:text-white">{{
            selectedDocument?.name
          }}</span
          >? This action cannot be undone.
        </p>

        <div class="flex justify-end space-x-2">
          <BaseButton type="button" color="muted" @click="closeDeleteModal">
            Cancel
          </BaseButton>
          <BaseButton
            type="button"
            color="danger"
            :loading="isDeleting"
            @click="deleteDocument"
          >
            Delete
          </BaseButton>
        </div>
      </div>
    </TairoModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useApi } from "../../../../app/composables/useApi";
import { format } from "date-fns";

const props = defineProps<{
  companyId: string;
}>();

// State
const documents = ref<any[]>([]);
const isLoading = ref(true);
const error = ref("");
const searchQuery = ref("");
const categoryFilter = ref("");
const sortBy = ref("newest");
const isDeleteModalOpen = ref(false);
const isDeleting = ref(false);
const selectedDocument = ref<any>(null);

// Services
const toaster = useNuiToasts();
const api = useApi();

// Options
const categoryOptions = [
  { label: "Contract", value: "contract" },
  { label: "Invoice", value: "invoice" },
  { label: "Report", value: "report" },
  { label: "Legal", value: "legal" },
  { label: "Certificate", value: "certificate" },
  { label: "Other", value: "other" },
];

const sortOptions = [
  { label: "Newest First", value: "newest" },
  { label: "Oldest First", value: "oldest" },
  { label: "Name (A-Z)", value: "name_asc" },
  { label: "Name (Z-A)", value: "name_desc" },
  { label: "Size (Largest)", value: "size_desc" },
  { label: "Size (Smallest)", value: "size_asc" },
];

// Computed
const filteredDocuments = computed(() => {
  let result = [...documents.value];

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (doc) =>
        doc.name.toLowerCase().includes(query) ||
        (doc.description && doc.description.toLowerCase().includes(query))
    );
  }

  // Apply category filter
  if (categoryFilter.value) {
    result = result.filter((doc) => doc.category === categoryFilter.value);
  }

  // Apply sorting
  switch (sortBy.value) {
    case "newest":
      result.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      break;
    case "oldest":
      result.sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
      break;
    case "name_asc":
      result.sort((a, b) => a.name.localeCompare(b.name));
      break;
    case "name_desc":
      result.sort((a, b) => b.name.localeCompare(a.name));
      break;
    case "size_desc":
      result.sort((a, b) => b.size - a.size);
      break;
    case "size_asc":
      result.sort((a, b) => a.size - b.size);
      break;
  }

  return result;
});

// Methods
const fetchDocuments = async () => {
  if (!props.companyId) return;

  isLoading.value = true;
  error.value = "";

  try {
    const response = await api.get(`/companies/${props.companyId}/documents`);
    documents.value = response.data;
  } catch (err) {
    console.error("Error fetching documents:", err);
    error.value = "Failed to load documents. Please try again.";
  } finally {
    isLoading.value = false;
  }
};

const resetFilters = () => {
  searchQuery.value = "";
  categoryFilter.value = "";
};

const openDocument = (document: any) => {
  window.open(document.url, "_blank");
};

const downloadDocument = (document: any) => {
  const link = document.createElement("a");
  link.href = document.url;
  link.download = document.name;
  link.target = "_blank";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const confirmDeleteDocument = (document: any) => {
  selectedDocument.value = document;
  isDeleteModalOpen.value = true;
};

const closeDeleteModal = () => {
  isDeleteModalOpen.value = false;
  selectedDocument.value = null;
};

const deleteDocument = async () => {
  if (!selectedDocument.value || !props.companyId) return;

  isDeleting.value = true;

  try {
    await api.delete(
      `/companies/${props.companyId}/documents/${selectedDocument.value.id}`
    );

    toaster.add({
      title: "Success",
      description: "Document deleted successfully",
      icon: "ph:check-circle-duotone",
      progress: true,
      duration: 3000,
    });

    // Remove document from list
    documents.value = documents.value.filter(
      (doc) => doc.id !== selectedDocument.value.id
    );
    closeDeleteModal();
  } catch (error) {
    console.error("Error deleting document:", error);
    toaster.add({
      title: "Error",
      description: "Failed to delete document",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    isDeleting.value = false;
  }
};

const getFileIcon = (mimeType: string) => {
  if (!mimeType) return "ph:file-duotone";

  if (mimeType.includes("pdf")) {
    return "ph:file-pdf-duotone";
  } else if (mimeType.includes("word") || mimeType.includes("doc")) {
    return "ph:file-doc-duotone";
  } else if (
    mimeType.includes("excel") ||
    mimeType.includes("sheet") ||
    mimeType.includes("xls")
  ) {
    return "ph:file-xls-duotone";
  } else if (mimeType.includes("image")) {
    return "ph:file-image-duotone";
  } else if (mimeType.includes("text")) {
    return "ph:file-text-duotone";
  } else {
    return "ph:file-duotone";
  }
};

const formatFileSize = (bytes: number) => {
  if (!bytes) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch (e) {
    return dateString;
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case "contract":
      return "primary";
    case "invoice":
      return "success";
    case "report":
      return "info";
    case "legal":
      return "warning";
    case "certificate":
      return "purple";
    default:
      return "muted";
  }
};

const formatCategory = (category: string) => {
  return category.charAt(0).toUpperCase() + category.slice(1);
};

const formatModule = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1);
};

// Watch for changes in companyId
watch(
  () => props.companyId,
  () => {
    if (props.companyId) {
      fetchDocuments();
    }
  },
  { immediate: true }
);

// Expose methods to parent component
defineExpose({
  fetchDocuments,
});
</script>
