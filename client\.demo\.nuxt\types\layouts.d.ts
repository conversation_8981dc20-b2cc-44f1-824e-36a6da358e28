import type { ComputedRef, MaybeRef } from 'vue'
export type LayoutKey = "collapse" | "content-docs" | "default" | "empty" | "landing" | "sidenav" | "topnav-slim" | "topnav"
declare module "../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/composables" {
  interface PageMeta {
    layout?: MaybeRef<LayoutKey | false> | ComputedRef<LayoutKey | false>
  }
}