{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/types.js", "../../../../../../node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/deserialize.js", "../../../../../../node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/serialize.js", "../../../../../../node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js", "../../../../../../node_modules/.pnpm/unist-util-position@5.0.0/node_modules/unist-util-position/lib/index.js"], "sourcesContent": ["export const VOID       = -1;\nexport const PRIMITIVE  = 0;\nexport const ARRAY      = 1;\nexport const OBJECT     = 2;\nexport const DATE       = 3;\nexport const REGEXP     = 4;\nexport const MAP        = 5;\nexport const SET        = 6;\nexport const ERROR      = 7;\nexport const BIGINT     = 8;\n// export const SYMBOL = 9;\n", "import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView': {\n        const { buffer } = new Uint8Array(value);\n        return as(new DataView(buffer), value);\n      }\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map, serialized)(0);\n", "import {\n  VOID, PRIMITIVE,\n  ARRAY, OBJECT,\n  DATE, REGEXP, MAP, SET,\n  ERROR, BIGINT\n} from './types.js';\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [ARRAY, EMPTY];\n    case 'Object':\n      return [OBJECT, EMPTY];\n    case 'Date':\n      return [DATE, EMPTY];\n    case 'RegExp':\n      return [REGEXP, EMPTY];\n    case 'Map':\n      return [MAP, EMPTY];\n    case 'Set':\n      return [SET, EMPTY];\n    case 'DataView':\n      return [ARRAY, asString];\n  }\n\n  if (asString.includes('Array'))\n    return [ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [ERROR, asString];\n\n  return [OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case ARRAY: {\n        if (type) {\n          let spread = value;\n          if (type === 'DataView') {\n            spread = new Uint8Array(value.buffer);\n          }\n          else if (type === 'ArrayBuffer') {\n            spread = new Uint8Array(value);\n          }\n          return as([type, [...spread]], value);\n        }\n\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n export const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n", "import {deserialize} from './deserialize.js';\nimport {serialize} from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      deserialize(serialize(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => deserialize(serialize(any, options));\n  /* c8 ignore stop */\n\nexport {deserialize, serialize};\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    if (\n      typeof point.line === 'number' &&\n      point.line > 0 &&\n      typeof point.column === 'number' &&\n      point.column > 0\n    ) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset:\n          typeof point.offset === 'number' && point.offset > -1\n            ? point.offset\n            : undefined\n      }\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node)\n  const end = pointEnd(node)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n"], "mappings": ";AAAO,IAAM,OAAa;AACnB,IAAM,YAAa;AACnB,IAAM,QAAa;AACnB,IAAM,SAAa;AACnB,IAAM,OAAa;AACnB,IAAM,SAAa;AACnB,IAAM,MAAa;AACnB,IAAM,MAAa;AACnB,IAAM,QAAa;AACnB,IAAM,SAAa;;;ACF1B,IAAM,MAAM,OAAO,SAAS,WAAW,OAAO;AAE9C,IAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,QAAM,KAAK,CAAC,KAAK,UAAU;AACzB,MAAE,IAAI,OAAO,GAAG;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,WAAS;AACtB,QAAI,EAAE,IAAI,KAAK;AACb,aAAO,EAAE,IAAI,KAAK;AAEpB,UAAM,CAAC,MAAM,KAAK,IAAI,EAAE,KAAK;AAC7B,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK;AACH,eAAO,GAAG,OAAO,KAAK;AAAA,MACxB,KAAK,OAAO;AACV,cAAM,MAAM,GAAG,CAAC,GAAG,KAAK;AACxB,mBAAWA,UAAS;AAClB,cAAI,KAAK,OAAOA,MAAK,CAAC;AACxB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,QAAQ;AACX,cAAM,SAAS,GAAG,CAAC,GAAG,KAAK;AAC3B,mBAAW,CAAC,KAAKA,MAAK,KAAK;AACzB,iBAAO,OAAO,GAAG,CAAC,IAAI,OAAOA,MAAK;AACpC,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AACH,eAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK;AAAA,MAClC,KAAK,QAAQ;AACX,cAAM,EAAC,QAAQ,MAAK,IAAI;AACxB,eAAO,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG,KAAK;AAAA,MAC5C;AAAA,MACA,KAAK,KAAK;AACR,cAAM,MAAM,GAAG,oBAAI,OAAK,KAAK;AAC7B,mBAAW,CAAC,KAAKA,MAAK,KAAK;AACzB,cAAI,IAAI,OAAO,GAAG,GAAG,OAAOA,MAAK,CAAC;AACpC,eAAO;AAAA,MACT;AAAA,MACA,KAAK,KAAK;AACR,cAAM,MAAM,GAAG,oBAAI,OAAK,KAAK;AAC7B,mBAAWA,UAAS;AAClB,cAAI,IAAI,OAAOA,MAAK,CAAC;AACvB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO;AACV,cAAM,EAAC,MAAM,QAAO,IAAI;AACxB,eAAO,GAAG,IAAI,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;AAAA,MACzC;AAAA,MACA,KAAK;AACH,eAAO,GAAG,OAAO,KAAK,GAAG,KAAK;AAAA,MAChC,KAAK;AACH,eAAO,GAAG,OAAO,OAAO,KAAK,CAAC,GAAG,KAAK;AAAA,MACxC,KAAK;AACH,eAAO,GAAG,IAAI,WAAW,KAAK,EAAE,QAAQ,KAAK;AAAA,MAC/C,KAAK,YAAY;AACf,cAAM,EAAE,OAAO,IAAI,IAAI,WAAW,KAAK;AACvC,eAAO,GAAG,IAAI,SAAS,MAAM,GAAG,KAAK;AAAA,MACvC;AAAA,IACF;AACA,WAAO,GAAG,IAAI,IAAI,IAAI,EAAE,KAAK,GAAG,KAAK;AAAA,EACvC;AAEA,SAAO;AACT;AAWO,IAAM,cAAc,gBAAc,aAAa,oBAAI,OAAK,UAAU,EAAE,CAAC;;;AC7E5E,IAAM,QAAQ;AAEd,IAAM,EAAC,SAAQ,IAAI,CAAC;AACpB,IAAM,EAAC,KAAI,IAAI;AAEf,IAAM,SAAS,WAAS;AACtB,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,YAAY,CAAC;AACxB,WAAO,CAAC,WAAW,IAAI;AAEzB,QAAM,WAAW,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AACjD,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,CAAC,OAAO,KAAK;AAAA,IACtB,KAAK;AACH,aAAO,CAAC,QAAQ,KAAK;AAAA,IACvB,KAAK;AACH,aAAO,CAAC,MAAM,KAAK;AAAA,IACrB,KAAK;AACH,aAAO,CAAC,QAAQ,KAAK;AAAA,IACvB,KAAK;AACH,aAAO,CAAC,KAAK,KAAK;AAAA,IACpB,KAAK;AACH,aAAO,CAAC,KAAK,KAAK;AAAA,IACpB,KAAK;AACH,aAAO,CAAC,OAAO,QAAQ;AAAA,EAC3B;AAEA,MAAI,SAAS,SAAS,OAAO;AAC3B,WAAO,CAAC,OAAO,QAAQ;AAEzB,MAAI,SAAS,SAAS,OAAO;AAC3B,WAAO,CAAC,OAAO,QAAQ;AAEzB,SAAO,CAAC,QAAQ,QAAQ;AAC1B;AAEA,IAAM,aAAa,CAAC,CAAC,MAAM,IAAI,MAC7B,SAAS,cACR,SAAS,cAAc,SAAS;AAGnC,IAAM,aAAa,CAAC,QAAQ,MAAM,GAAG,MAAM;AAEzC,QAAM,KAAK,CAAC,KAAK,UAAU;AACzB,UAAM,QAAQ,EAAE,KAAK,GAAG,IAAI;AAC5B,MAAE,IAAI,OAAO,KAAK;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,WAAS;AACpB,QAAI,EAAE,IAAI,KAAK;AACb,aAAO,EAAE,IAAI,KAAK;AAEpB,QAAI,CAAC,MAAM,IAAI,IAAI,OAAO,KAAK;AAC/B,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,YAAI,QAAQ;AACZ,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,mBAAO;AACP,oBAAQ,MAAM,SAAS;AACvB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AACH,gBAAI;AACF,oBAAM,IAAI,UAAU,yBAAyB,IAAI;AACnD,oBAAQ;AACR;AAAA,UACF,KAAK;AACH,mBAAO,GAAG,CAAC,IAAI,GAAG,KAAK;AAAA,QAC3B;AACA,eAAO,GAAG,CAAC,MAAM,KAAK,GAAG,KAAK;AAAA,MAChC;AAAA,MACA,KAAK,OAAO;AACV,YAAI,MAAM;AACR,cAAI,SAAS;AACb,cAAI,SAAS,YAAY;AACvB,qBAAS,IAAI,WAAW,MAAM,MAAM;AAAA,UACtC,WACS,SAAS,eAAe;AAC/B,qBAAS,IAAI,WAAW,KAAK;AAAA,UAC/B;AACA,iBAAO,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK;AAAA,QACtC;AAEA,cAAM,MAAM,CAAC;AACb,cAAM,QAAQ,GAAG,CAAC,MAAM,GAAG,GAAG,KAAK;AACnC,mBAAW,SAAS;AAClB,cAAI,KAAK,KAAK,KAAK,CAAC;AACtB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,QAAQ;AACX,YAAI,MAAM;AACR,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,qBAAO,GAAG,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG,KAAK;AAAA,YAC3C,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,GAAG,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG,KAAK;AAAA,UAC5C;AAAA,QACF;AAEA,YAAI,QAAS,YAAY;AACvB,iBAAO,KAAK,MAAM,OAAO,CAAC;AAE5B,cAAM,UAAU,CAAC;AACjB,cAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,GAAG,KAAK;AACvC,mBAAW,OAAO,KAAK,KAAK,GAAG;AAC7B,cAAI,UAAU,CAAC,WAAW,OAAO,MAAM,GAAG,CAAC,CAAC;AAC1C,oBAAQ,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,QAC9C;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AACH,eAAO,GAAG,CAAC,MAAM,MAAM,YAAY,CAAC,GAAG,KAAK;AAAA,MAC9C,KAAK,QAAQ;AACX,cAAM,EAAC,QAAQ,MAAK,IAAI;AACxB,eAAO,GAAG,CAAC,MAAM,EAAC,QAAQ,MAAK,CAAC,GAAG,KAAK;AAAA,MAC1C;AAAA,MACA,KAAK,KAAK;AACR,cAAM,UAAU,CAAC;AACjB,cAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,GAAG,KAAK;AACvC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AAChC,cAAI,UAAU,EAAE,WAAW,OAAO,GAAG,CAAC,KAAK,WAAW,OAAO,KAAK,CAAC;AACjE,oBAAQ,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,KAAK;AACR,cAAM,UAAU,CAAC;AACjB,cAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,GAAG,KAAK;AACvC,mBAAW,SAAS,OAAO;AACzB,cAAI,UAAU,CAAC,WAAW,OAAO,KAAK,CAAC;AACrC,oBAAQ,KAAK,KAAK,KAAK,CAAC;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,EAAC,QAAO,IAAI;AAClB,WAAO,GAAG,CAAC,MAAM,EAAC,MAAM,MAAM,QAAO,CAAC,GAAG,KAAK;AAAA,EAChD;AAEA,SAAO;AACT;AAcQ,IAAM,YAAY,CAAC,OAAO,EAAC,MAAM,MAAK,IAAI,CAAC,MAAM;AACvD,QAAM,IAAI,CAAC;AACX,SAAO,WAAW,EAAE,QAAQ,QAAQ,CAAC,CAAC,MAAM,oBAAI,OAAK,CAAC,EAAE,KAAK,GAAG;AAClE;;;AC3JA,IAAO,cAAQ,OAAO,oBAAoB;AAAA;AAAA,EAExC,CAAC,KAAK,YACJ,YAAY,UAAU,WAAW,WAAW,WAC1C,YAAY,UAAU,KAAK,OAAO,CAAC,IAAI,gBAAgB,GAAG;AAAA,IAE9D,CAAC,KAAK,YAAY,YAAY,UAAU,KAAK,OAAO,CAAC;;;ACQhD,IAAM,WAAW,MAAM,KAAK;AAU5B,IAAM,aAAa,MAAM,OAAO;AAUvC,SAAS,MAAM,MAAM;AACnB,SAAOC;AAQP,WAASA,OAAM,MAAM;AACnB,UAAMA,SAAS,QAAQ,KAAK,YAAY,KAAK,SAAS,IAAI,KAAM,CAAC;AAEjE,QACE,OAAOA,OAAM,SAAS,YACtBA,OAAM,OAAO,KACb,OAAOA,OAAM,WAAW,YACxBA,OAAM,SAAS,GACf;AACA,aAAO;AAAA,QACL,MAAMA,OAAM;AAAA,QACZ,QAAQA,OAAM;AAAA,QACd,QACE,OAAOA,OAAM,WAAW,YAAYA,OAAM,SAAS,KAC/CA,OAAM,SACN;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAUO,SAAS,SAAS,MAAM;AAC7B,QAAM,QAAQ,WAAW,IAAI;AAC7B,QAAM,MAAM,SAAS,IAAI;AAEzB,MAAI,SAAS,KAAK;AAChB,WAAO,EAAC,OAAO,IAAG;AAAA,EACpB;AACF;", "names": ["index", "point"]}