import { Request, Response } from "express";
import { prisma } from "../../lib/prisma.js";

// Get all payment cards for the authenticated user
export const getUserCards = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    const cards = await prisma.paymentCard.findMany({
      where: {
        userId: Number(userId),
        isActive: true,
      },
      orderBy: [
        { isPrimary: 'desc' },
        { createdAt: 'desc' }
      ],
    });

    res.status(200).json(cards);
  } catch (error) {
    console.error("Error fetching user cards:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to fetch payment cards" 
    });
  }
};

// Get a specific payment card
export const getCard = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { cardId } = req.params;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    const card = await prisma.paymentCard.findFirst({
      where: {
        id: cardId,
        userId: Number(userId),
        isActive: true,
      },
    });

    if (!card) {
      res.status(404).json({ 
        success: false,
        error: "Payment card not found" 
      });
      return;
    }

    res.status(200).json(card);
  } catch (error) {
    console.error("Error fetching payment card:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to fetch payment card" 
    });
  }
};

// Create a new payment card
export const createCard = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { 
      stripePaymentMethodId, 
      cardholderName, 
      last4, 
      brand, 
      expMonth, 
      expYear,
      fingerprint,
      isPrimary = false 
    } = req.body;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    // Validate required fields
    if (!stripePaymentMethodId || !cardholderName || !last4 || !brand || !expMonth || !expYear) {
      res.status(400).json({ 
        success: false,
        error: "Missing required card information" 
      });
      return;
    }

    // If this is set as primary, unset other primary cards
    if (isPrimary) {
      await prisma.paymentCard.updateMany({
        where: {
          userId: Number(userId),
          isPrimary: true,
        },
        data: {
          isPrimary: false,
        },
      });
    }

    // Create the new card
    const card = await prisma.paymentCard.create({
      data: {
        userId: Number(userId),
        stripePaymentMethodId,
        cardholderName,
        last4,
        brand: brand.toLowerCase(),
        expMonth: Number(expMonth),
        expYear: Number(expYear),
        fingerprint,
        isPrimary,
        isActive: true,
      },
    });

    res.status(201).json({
      success: true,
      message: "Payment card added successfully",
      data: card,
    });
  } catch (error) {
    console.error("Error creating payment card:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to create payment card" 
    });
  }
};

// Set a card as primary
export const setPrimaryCard = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { cardId } = req.params;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    // Verify the card belongs to the user
    const card = await prisma.paymentCard.findFirst({
      where: {
        id: cardId,
        userId: Number(userId),
        isActive: true,
      },
    });

    if (!card) {
      res.status(404).json({ 
        success: false,
        error: "Payment card not found" 
      });
      return;
    }

    // Unset all other primary cards for this user
    await prisma.paymentCard.updateMany({
      where: {
        userId: Number(userId),
        isPrimary: true,
      },
      data: {
        isPrimary: false,
      },
    });

    // Set this card as primary
    const updatedCard = await prisma.paymentCard.update({
      where: {
        id: cardId,
      },
      data: {
        isPrimary: true,
      },
    });

    res.status(200).json({
      success: true,
      message: "Primary card updated successfully",
      data: updatedCard,
    });
  } catch (error) {
    console.error("Error setting primary card:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to set primary card" 
    });
  }
};

// Update a payment card
export const updateCard = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { cardId } = req.params;
    const { cardholderName, expMonth, expYear } = req.body;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    // Verify the card belongs to the user
    const card = await prisma.paymentCard.findFirst({
      where: {
        id: cardId,
        userId: Number(userId),
        isActive: true,
      },
    });

    if (!card) {
      res.status(404).json({ 
        success: false,
        error: "Payment card not found" 
      });
      return;
    }

    // Update the card
    const updatedCard = await prisma.paymentCard.update({
      where: {
        id: cardId,
      },
      data: {
        ...(cardholderName && { cardholderName }),
        ...(expMonth && { expMonth: Number(expMonth) }),
        ...(expYear && { expYear: Number(expYear) }),
        updatedAt: new Date(),
      },
    });

    res.status(200).json({
      success: true,
      message: "Payment card updated successfully",
      data: updatedCard,
    });
  } catch (error) {
    console.error("Error updating payment card:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to update payment card" 
    });
  }
};

// Delete a payment card
export const deleteCard = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { cardId } = req.params;

    if (!userId) {
      res.status(401).json({ 
        success: false,
        error: "Unauthorized" 
      });
      return;
    }

    // Verify the card belongs to the user
    const card = await prisma.paymentCard.findFirst({
      where: {
        id: cardId,
        userId: Number(userId),
        isActive: true,
      },
    });

    if (!card) {
      res.status(404).json({ 
        success: false,
        error: "Payment card not found" 
      });
      return;
    }

    // Soft delete the card
    await prisma.paymentCard.update({
      where: {
        id: cardId,
      },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    // If this was the primary card, set another card as primary
    if (card.isPrimary) {
      const nextCard = await prisma.paymentCard.findFirst({
        where: {
          userId: Number(userId),
          isActive: true,
          id: { not: cardId },
        },
        orderBy: { createdAt: 'desc' },
      });

      if (nextCard) {
        await prisma.paymentCard.update({
          where: {
            id: nextCard.id,
          },
          data: {
            isPrimary: true,
          },
        });
      }
    }

    res.status(200).json({
      success: true,
      message: "Payment card removed successfully",
    });
  } catch (error) {
    console.error("Error deleting payment card:", error);
    res.status(500).json({ 
      success: false,
      error: "Failed to delete payment card" 
    });
  }
};
