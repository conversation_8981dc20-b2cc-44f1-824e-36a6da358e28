<!-- client-old-tairo-1.5/layers/core/pages/health.vue -->

<template>
  <BaseLayerPage title="System Health" description="Monitor system performance and service status">
    <!-- Page Header Actions -->
    <template #header-actions>
      <BaseButton color="primary" @click="refreshAllData" :loading="loading">
        <Icon name="ph:arrows-clockwise-duotone" class="h-4 w-4 mr-1" />
        Refresh All
      </BaseButton>
    </template>

    <!-- Health Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <MetricCard
        title="System Health"
        :value="metrics.systemHealth"
        icon="ph:heartbeat-duotone"
        icon-color="success"
        format="percent"
        :trend="metrics.systemHealthTrend"
        subtitle="Overall performance"
      />

      <MetricCard
        title="Active Users"
        :value="metrics.activeUsers"
        icon="ph:users-duotone"
        icon-color="primary"
        format="number"
        :trend="metrics.activeUsersTrend"
        subtitle="Currently online"
      />

      <MetricCard
        title="API Requests"
        :value="metrics.apiRequests"
        icon="ph:globe-duotone"
        icon-color="info"
        format="number"
        :trend="metrics.apiRequestsTrend"
        subtitle="Requests per minute"
      />

      <MetricCard
        title="System Alerts"
        :value="metrics.systemAlerts"
        icon="ph:bell-ringing-duotone"
        icon-color="danger"
        format="number"
        :trend="metrics.systemAlertsTrend"
        :value-color="metrics.systemAlertsTrend > 0 ? 'text-danger-500' : 'text-success-500'"
        :trend-inverse="true"
        subtitle="Active alerts"
      />
    </div>

    <!-- Service Status and Resource Usage -->
    <div class="grid grid-cols-12 gap-6 mb-6">
      <!-- Service Status -->
      <div class="col-span-12 lg:col-span-6">
        <ServiceStatusCard
          title="Service Status"
          :services="services"
          :loading="loading"
          :last-updated="lastUpdated"
          @refresh="refreshServices"
        />
      </div>

      <!-- Resource Usage -->
      <div class="col-span-12 lg:col-span-6">
        <ResourceUsageCard
          title="Resource Usage"
          :resources="resources"
          :loading="loading"
          :last-updated="lastUpdated"
          @refresh="refreshResources"
        />
      </div>
    </div>

    <!-- Performance Metrics and Database Health -->
    <div class="grid grid-cols-12 gap-6 mb-6">
      <!-- Performance Metrics -->
      <div class="col-span-12 lg:col-span-8">
        <PerformanceMetricsCard
          title="Performance Metrics"
          :metrics="performanceMetrics"
          :loading="loading"
          :last-updated="lastUpdated"
          @refresh="refreshPerformanceMetrics"
        />
      </div>

      <!-- Database Health -->
      <div class="col-span-12 lg:col-span-4">
        <BaseCard class="h-full">
          <div class="p-4 border-b border-muted-200 dark:border-muted-700">
            <BaseHeading as="h3" size="sm" weight="medium" class="text-muted-900 dark:text-white">
              Database Health
            </BaseHeading>
          </div>

          <div class="p-4">
            <div v-if="loading" class="flex justify-center py-4">
              <BaseButtonIcon shape="rounded" color="primary" loading />
            </div>

            <div v-else class="space-y-4">
              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Connection Pool</BaseText
                >
                <div class="flex items-center">
                  <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                    {{ database.connectionPool.active }} / {{ database.connectionPool.max }}
                  </BaseText>
                  <BaseTag
                    :color="
                      database.connectionPool.active / database.connectionPool.max > 0.8
                        ? 'warning'
                        : 'success'
                    "
                    flavor="pastel"
                    size="sm"
                    class="ml-2"
                  >
                    {{
                      Math.round(
                        (database.connectionPool.active / database.connectionPool.max) * 100
                      )
                    }}%
                  </BaseTag>
                </div>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Query Cache Hit Rate</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ database.queryCacheHitRate }}%
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Avg. Query Time</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ database.avgQueryTime }}ms
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Slow Queries</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ database.slowQueries }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Failed Queries</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ database.failedQueries }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Database Size</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ formatBytes(database.size) }}
                </BaseText>
              </div>

              <div class="flex items-center justify-between">
                <BaseText size="sm" class="text-muted-500 dark:text-muted-400"
                  >Last Backup</BaseText
                >
                <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                  {{ formatDate(database.lastBackup) }}
                </BaseText>
              </div>
            </div>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <BaseButton
              color="primary"
              class="w-full"
              @click="navigateTo('/core/settings/database')"
            >
              <Icon name="ph:database-duotone" class="h-4 w-4 mr-1" />
              Database Management
            </BaseButton>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- System Alerts -->
    <BaseCard>
      <div
        class="p-4 border-b border-muted-200 dark:border-muted-700 flex justify-between items-center"
      >
        <BaseHeading as="h3" size="sm" weight="medium" class="text-muted-900 dark:text-white">
          System Alerts
        </BaseHeading>
        <BaseButton
          color="primary"
          flavor="link"
          size="sm"
          @click="navigateTo('/core/monitoring/alerts')"
        >
          View All Alerts
        </BaseButton>
      </div>

      <div class="p-4">
        <div v-if="loading" class="flex justify-center py-4">
          <BaseButtonIcon shape="rounded" color="primary" loading />
        </div>

        <div v-else-if="alerts.length === 0" class="text-center py-6">
          <Icon name="ph:check-circle-duotone" class="h-12 w-12 text-success-500 mx-auto mb-3" />
          <BaseText class="text-muted-500 dark:text-muted-400">
            No active alerts at this time
          </BaseText>
        </div>

        <div v-else>
          <div class="overflow-x-auto">
            <table class="w-full text-left">
              <thead>
                <tr class="border-b border-muted-200 dark:border-muted-700">
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Severity</th>
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Alert</th>
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Source</th>
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Time</th>
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Status</th>
                  <th class="p-3 font-medium text-muted-700 dark:text-muted-300">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(alert, index) in alerts"
                  :key="index"
                  class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
                >
                  <td class="p-3">
                    <BaseTag :color="getSeverityColor(alert.severity)" flavor="pastel">
                      {{ alert.severity }}
                    </BaseTag>
                  </td>
                  <td class="p-3">
                    <div>
                      <BaseText weight="medium" class="text-muted-900 dark:text-white">
                        {{ alert.title }}
                      </BaseText>
                      <BaseText size="xs" class="text-muted-500 dark:text-muted-400">
                        {{ alert.message }}
                      </BaseText>
                    </div>
                  </td>
                  <td class="p-3">
                    <BaseText class="text-muted-500 dark:text-muted-400">
                      {{ alert.source }}
                    </BaseText>
                  </td>
                  <td class="p-3">
                    <BaseText class="text-muted-500 dark:text-muted-400">
                      {{ formatTime(alert.timestamp) }}
                    </BaseText>
                  </td>
                  <td class="p-3">
                    <BaseTag :color="getStatusColor(alert.status)" flavor="pastel" size="sm">
                      {{ alert.status }}
                    </BaseTag>
                  </td>
                  <td class="p-3">
                    <div class="flex gap-2">
                      <BaseButton
                        color="primary"
                        flavor="link"
                        size="sm"
                        @click="acknowledgeAlert(alert)"
                      >
                        Acknowledge
                      </BaseButton>
                      <BaseButton
                        color="danger"
                        flavor="link"
                        size="sm"
                        @click="resolveAlert(alert)"
                      >
                        Resolve
                      </BaseButton>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </BaseCard>
  </BaseLayerPage>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { format, formatDistanceToNow } from 'date-fns'
import { useToaster } from '../../../composables/toaster'

// Import components
import MetricCard from '../components/dashboard/MetricCard.vue'
import ServiceStatusCard from '../components/health/ServiceStatusCard.vue'
import ResourceUsageCard from '../components/health/ResourceUsageCard.vue'
import PerformanceMetricsCard from '../components/health/PerformanceMetricsCard.vue'

const router = useRouter()
const toaster = useToaster()

// State
const loading = ref(false)
const lastUpdated = ref(new Date())
const refreshInterval = ref<number | null>(null)
const autoRefresh = ref(false)

// Metrics data
const metrics = ref({
  systemHealth: 98,
  systemHealthTrend: 2,
  activeUsers: 42,
  activeUsersTrend: 8,
  apiRequests: 1250,
  apiRequestsTrend: 15,
  systemAlerts: 3,
  systemAlertsTrend: -2,
})

// Services data
const services = ref([
  {
    name: 'Database',
    description: 'Primary database server',
    status: 'operational',
    metric: 'Response: 45ms',
  },
  {
    name: 'API Server',
    description: 'REST API endpoints',
    status: 'operational',
    metric: 'Uptime: 99.9%',
  },
  {
    name: 'File Storage',
    description: 'Document storage service',
    status: 'degraded',
    metric: 'I/O: High',
  },
  {
    name: 'Authentication',
    description: 'User authentication service',
    status: 'operational',
    metric: 'Response: 120ms',
  },
  {
    name: 'Email Service',
    description: 'Notification emails',
    status: 'operational',
    metric: 'Queue: 0',
  },
  {
    name: 'Background Jobs',
    description: 'Scheduled tasks and jobs',
    status: 'operational',
    metric: 'Jobs: 5/min',
  },
  {
    name: 'Search Service',
    description: 'Full-text search engine',
    status: 'operational',
    metric: 'Latency: 85ms',
  },
  {
    name: 'Cache Service',
    description: 'Data caching layer',
    status: 'operational',
    metric: 'Hit rate: 92%',
  },
])

// Resource usage data
const resources = ref({
  cpu: {
    percentage: 45,
    cores: 8,
    load: 2.34,
  },
  memory: {
    percentage: 62,
    used: 8 * 1024 * 1024 * 1024, // 8 GB
    total: 16 * 1024 * 1024 * 1024, // 16 GB
  },
  disk: {
    percentage: 72,
    used: 720 * 1024 * 1024 * 1024, // 720 GB
    total: 1000 * 1024 * 1024 * 1024, // 1 TB
  },
  network: {
    download: 5 * 1024 * 1024, // 5 MB/s
    upload: 2 * 1024 * 1024, // 2 MB/s
    downloadPercentage: 50,
    uploadPercentage: 40,
  },
})

// Performance metrics data
const performanceMetrics = ref({
  apiResponseTime: 120,
  apiResponseTimeTrend: -5,
  dbQueryTime: 45,
  dbQueryTimeTrend: -8,
  errorRate: 0.5,
  errorRateTrend: -10,
  pageLoadTime: 850,
  pageLoadTimeTrend: -3,
  timeLabels: ['1h ago', '50m ago', '40m ago', '30m ago', '20m ago', '10m ago', 'Now'],
  apiResponseTimeHistory: [145, 140, 135, 130, 125, 122, 120],
  dbQueryTimeHistory: [60, 55, 52, 50, 48, 46, 45],
  errorRateHistory: [0.8, 0.7, 0.7, 0.6, 0.6, 0.5, 0.5],
  pageLoadTimeHistory: [920, 900, 890, 880, 870, 860, 850],
})

// Database health data
const database = ref({
  connectionPool: {
    active: 12,
    max: 50,
  },
  queryCacheHitRate: 85,
  avgQueryTime: 45,
  slowQueries: 3,
  failedQueries: 0,
  size: 2.5 * 1024 * 1024 * 1024, // 2.5 GB
  lastBackup: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
})

// Alerts data
const alerts = ref([
  {
    id: 'alert-1',
    severity: 'High',
    title: 'Storage Space Low',
    message: 'File storage is running low on space. Consider cleaning up unused files.',
    source: 'File Storage',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: 'Active',
  },
  {
    id: 'alert-2',
    severity: 'Medium',
    title: 'High CPU Usage',
    message: 'CPU usage has been above 80% for the last 15 minutes.',
    source: 'System Monitor',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    status: 'Active',
  },
  {
    id: 'alert-3',
    severity: 'Low',
    title: 'Slow Database Queries',
    message: '3 database queries took longer than 1 second to execute.',
    source: 'Database',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: 'Acknowledged',
  },
])

// Methods
const navigateTo = (path: string) => {
  router.push(path)
}

const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

const formatDate = (date: Date | string) => {
  if (!date) return 'N/A'

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return format(dateObj, 'MMM d, yyyy')
  } catch (error) {
    return 'N/A'
  }
}

const formatTime = (date: Date | string) => {
  if (!date) return 'N/A'

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return formatDistanceToNow(dateObj, { addSuffix: true })
  } catch (error) {
    return 'N/A'
  }
}

const getSeverityColor = (severity: string) => {
  const colorMap: Record<string, string> = {
    Critical: 'danger',
    High: 'danger',
    Medium: 'warning',
    Low: 'info',
  }

  return colorMap[severity] || 'muted'
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    Active: 'danger',
    Acknowledged: 'warning',
    Resolved: 'success',
    Closed: 'muted',
  }

  return colorMap[status] || 'muted'
}

const refreshAllData = () => {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date()

    // Update metrics
    metrics.value = {
      ...metrics.value,
      systemHealth: 99,
      systemHealthTrend: 3,
    }

    // Update services
    services.value = services.value.map((service) => {
      if (service.name === 'File Storage') {
        return { ...service, status: 'operational', metric: 'I/O: Normal' }
      }
      return service
    })

    loading.value = false
    toaster.success('System health data refreshed')
  }, 1500)
}

const refreshServices = () => {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date()

    // Update services
    services.value = services.value.map((service) => {
      if (service.name === 'File Storage') {
        return { ...service, status: 'operational', metric: 'I/O: Normal' }
      }
      return service
    })

    loading.value = false
    toaster.success('Service status refreshed')
  }, 1000)
}

const refreshResources = () => {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date()

    // Update resource usage
    resources.value = {
      ...resources.value,
      cpu: {
        ...resources.value.cpu,
        percentage: 42,
        load: 2.1,
      },
      memory: {
        ...resources.value.memory,
        percentage: 60,
      },
    }

    loading.value = false
    toaster.success('Resource usage refreshed')
  }, 1000)
}

const refreshPerformanceMetrics = () => {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    // Update last updated timestamp
    lastUpdated.value = new Date()

    // Update performance metrics
    performanceMetrics.value = {
      ...performanceMetrics.value,
      apiResponseTime: 115,
      apiResponseTimeTrend: -8,
      dbQueryTime: 42,
      dbQueryTimeTrend: -10,
    }

    loading.value = false
    toaster.success('Performance metrics refreshed')
  }, 1000)
}

const acknowledgeAlert = (alert: any) => {
  // Simulate API call
  setTimeout(() => {
    // Update alert status
    alerts.value = alerts.value.map((a) => {
      if (a.id === alert.id) {
        return { ...a, status: 'Acknowledged' }
      }
      return a
    })

    toaster.success(`Alert "${alert.title}" acknowledged`)
  }, 500)
}

const resolveAlert = (alert: any) => {
  // Simulate API call
  setTimeout(() => {
    // Update alert status
    alerts.value = alerts.value.map((a) => {
      if (a.id === alert.id) {
        return { ...a, status: 'Resolved' }
      }
      return a
    })

    toaster.success(`Alert "${alert.title}" resolved`)
  }, 500)
}

// Fetch data on mount
onMounted(() => {
  loading.value = true

  // Simulate API call
  setTimeout(() => {
    loading.value = false
  }, 1500)
})

// Clean up on unmount
onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>
