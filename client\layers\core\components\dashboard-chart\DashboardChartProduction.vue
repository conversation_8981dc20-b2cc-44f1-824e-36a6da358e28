<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();
const currentLocale = computed(() => locale.value);

const tasks = ref([
  {
    id: 1,
    name: "Design",
    start: 0,
    duration: 20,
    color: "var(--color-primary-500)",
  },
  {
    id: 2,
    name: "Development",
    start: 15,
    duration: 30,
    color: "var(--color-info-500)",
  },
  {
    id: 3,
    name: "Testing",
    start: 40,
    duration: 15,
    color: "var(--color-warning-500)",
  },
  {
    id: 4,
    name: "Deployment",
    start: 55,
    duration: 25,
    color: "var(--color-success-500)",
  },
]);

// Month names based on locale
const months = computed(() => {
  return currentLocale.value === "et"
    ? [
        "Jaan",
        "Veeb",
        "Märts",
        "Apr",
        "<PERSON>",
        "<PERSON>uni",
        "<PERSON>uli",
        "Aug",
        "Sept",
        "Okt",
        "Nov",
        "Dets",
      ]
    : [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ];
});

// Current month is September (index 8)
const currentMonthIndex = 8;
</script>

<template>
  <div class="h-32 w-full">
    <svg
      class="h-full w-full"
      viewBox="0 0 100 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Timeline background -->
      <rect
        x="0"
        y="0"
        width="100"
        height="24"
        fill="var(--color-muted-100)"
        class="dark:fill-muted-800"
        rx="4"
      />

      <!-- Task bars -->
      <g v-for="(task, index) in tasks" :key="task.id">
        <rect
          :x="task.start"
          :y="index * 6"
          :width="task.duration"
          height="5"
          :fill="task.color"
          rx="2"
        />
        <text
          :x="task.start + 2"
          :y="index * 6 + 3.5"
          fill="white"
          font-size="3"
          font-weight="bold"
        >
          {{ task.name }}
        </text>
      </g>

      <!-- Timeline markers -->
      <line
        x1="0"
        y1="24"
        x2="0"
        y2="22"
        stroke="var(--color-muted-500)"
        stroke-width="0.5"
      />
      <line
        x1="25"
        y1="24"
        x2="25"
        y2="22"
        stroke="var(--color-muted-500)"
        stroke-width="0.5"
      />
      <line
        x1="50"
        y1="24"
        x2="50"
        y2="22"
        stroke="var(--color-muted-500)"
        stroke-width="0.5"
      />
      <line
        x1="75"
        y1="24"
        x2="75"
        y2="22"
        stroke="var(--color-muted-500)"
        stroke-width="0.5"
      />
      <line
        x1="100"
        y1="24"
        x2="100"
        y2="22"
        stroke="var(--color-muted-500)"
        stroke-width="0.5"
      />

      <!-- Week labels -->
      <text x="0" y="27" fill="var(--color-muted-500)" font-size="2.5">
        Week 1
      </text>
      <text x="25" y="27" fill="var(--color-muted-500)" font-size="2.5">
        Week 2
      </text>
      <text x="50" y="27" fill="var(--color-muted-500)" font-size="2.5">
        Week 3
      </text>
      <text x="75" y="27" fill="var(--color-muted-500)" font-size="2.5">
        Week 4
      </text>

      <!-- Date labels -->
      <text x="0" y="31" fill="var(--color-muted-500)" font-size="2.5">
        {{ months[currentMonthIndex] }} 1
      </text>
      <text x="25" y="31" fill="var(--color-muted-500)" font-size="2.5">
        {{ months[currentMonthIndex] }} 8
      </text>
      <text x="50" y="31" fill="var(--color-muted-500)" font-size="2.5">
        {{ months[currentMonthIndex] }} 15
      </text>
      <text x="75" y="31" fill="var(--color-muted-500)" font-size="2.5">
        {{ months[currentMonthIndex] }} 22
      </text>
      <text x="95" y="31" fill="var(--color-muted-500)" font-size="2.5">
        {{ months[currentMonthIndex + 1] }} 1
      </text>
    </svg>
  </div>
</template>
