{"name": "app", "type": "module", "private": true, "scripts": {"prepare": "nuxt prepare", "dev": "cp .env.development .env && nuxt dev --open", "dev:https": "cp .env.development .env && npm run generate-ssl-certs && NODE_ENV=development nuxt dev --https --open", "build": "cp .env.production .env && nuxt build", "generate": "cp .env.production .env && nuxt generate", "typecheck": "nuxt typecheck", "clean": "rimraf .nuxt .output node_modules", "extract-menu": "node scripts/extractExactMenu.js", "generate-ssl-certs": "mkdir -p ssl && cd ssl && bash ../ssl/generate-certs.sh"}, "dependencies": {"@iconify-json/solar": "^1.2.2", "@nuxt/content": "3.4.0", "@nuxt/image": "1.10.0", "@nuxtjs/i18n": "^9.5.3", "@pinia/nuxt": "^0.11.0", "@stripe/stripe-js": "^2.4.0", "@tailwindcss/typography": "^0.5.16", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.0.0", "@vueuse/nuxt": "^13.0.0", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.2", "apexcharts": "^3.46.0", "axios": "^1.8.4", "flag-icons": "^7.5.0", "mapbox-gl": "3.12.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "reka-ui": "^2.2.0", "stripe": "^14.20.0", "v-calendar": "3.1.2", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue3-apexcharts": "^1.5.2", "vue3-smooth-dnd": "0.0.6", "zod": "^3.24.1"}, "devDependencies": {"@iconify-json/fa6-brands": "^1.2.5", "@iconify-json/ph": "^1.2.2", "@iconify-json/simple-icons": "^1.2.30", "@nuxt/fonts": "^0.11.1", "@shuriken-ui/nuxt": "4.0.0-beta.4", "@vite-pwa/nuxt": "^1.0.0", "@vue/compiler-sfc": "^3.3.4", "nuxt": "3.16.2", "tailwindcss": "^4.1.3", "ts-node": "^10.9.1", "typescript": "5.8.3"}}