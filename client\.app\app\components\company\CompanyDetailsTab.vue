<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="mb-4 text-muted-800 dark:text-white"
      >
        Basic Information
      </BaseHeading>

      <form @submit.prevent="updateCompanyDetails">
        <div class="mb-4">
          <BaseInput
            v-model="companyForm.name"
            label="Company Name"
            placeholder="Enter company name"
            :error="companyErrors.name"
          />
        </div>

        <div class="mb-4">
          <label
            class="block mb-1 text-sm font-medium text-muted-700 dark:text-muted-300"
            >Company Type</label
          >
          <BaseListbox
            v-model="companyForm.type"
            :items="companyTypeOptions"
            :properties="{ value: 'value', label: 'label' }"
            placeholder="Select company type"
            size="sm"
            rounded="md"
            :error="companyErrors.type"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="companyForm.registrationNumber"
            label="Registration Number"
            placeholder="Enter registration number"
            :error="companyErrors.registrationNumber"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="companyForm.vatNumber"
            label="VAT Number"
            placeholder="Enter VAT number"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="companyForm.website"
            label="Website"
            placeholder="Enter company website"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="companyForm.email"
            label="Email"
            placeholder="Enter company email"
            :error="companyErrors.email"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="companyForm.phone"
            label="Phone"
            placeholder="Enter company phone"
            :error="companyErrors.phone"
          />
        </div>

        <div class="flex justify-end">
          <BaseButton
            type="submit"
            variant="solid"
            color="primary"
            :loading="isUpdating"
          >
            <Icon name="solar:diskette-linear" class="h-4 w-4 mr-2" />
            Save Changes
          </BaseButton>
        </div>
      </form>
    </div>

    <div class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm">
      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="mb-4 text-muted-800 dark:text-white"
      >
        Address Information
      </BaseHeading>

      <form @submit.prevent="updateCompanyAddress">
        <div class="mb-4">
          <BaseInput
            v-model="addressForm.address"
            label="Address Line 1"
            placeholder="Enter company address"
            :error="addressErrors.address"
          />
        </div>

        <div class="mb-4">
          <BaseInput
            v-model="addressForm.address2"
            label="Address Line 2 (Optional)"
            placeholder="Apartment, suite, etc."
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <BaseInput
            v-model="addressForm.city"
            label="City"
            placeholder="Enter city"
            :error="addressErrors.city"
          />
          <BaseInput
            v-model="addressForm.postalCode"
            label="Postal Code"
            placeholder="Enter postal code"
            :error="addressErrors.postalCode"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <BaseInput
            v-model="addressForm.state"
            label="State/Province"
            placeholder="Enter state/province"
          />
          <BaseInput
            v-model="addressForm.country"
            label="Country"
            placeholder="Enter country"
            :error="addressErrors.country"
          />
        </div>

        <div class="mb-4">
          <BaseTextarea
            v-model="addressForm.notes"
            label="Additional Notes"
            placeholder="Enter additional notes about the company"
            rows="4"
          />
        </div>

        <div class="flex justify-end">
          <BaseButton
            type="submit"
            color="primary"
            :loading="isUpdatingAddress"
          >
            Save Address
          </BaseButton>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useApi } from "../../../app/composables/useApi";

const props = defineProps<{
  company?: {
    id?: string;
    name?: string;
    type?: string;
    registrationNumber?: string;
    vatNumber?: string;
    website?: string;
    email?: string;
    phone?: string;
    address?: string;
    address2?: string;
    city?: string;
    postalCode?: string;
    state?: string;
    country?: string;
    notes?: string;
    contractTerms?: { legalEntityType?: string; [key: string]: any };
  };
}>();

const emit = defineEmits<{
  (e: "companyUpdated"): void;
}>();

const api = useApi();
// State
const isUpdating = ref(false);
const isUpdatingAddress = ref(false);

// Define type for company type option
interface CompanyTypeOption {
  label: string;
  value: string;
}

// Form state
const companyForm = ref({
  name: "",
  type: "" as CompanyTypeOption | string,
  registrationNumber: "",
  vatNumber: "",
  website: "",
  email: "",
  phone: "",
});

const addressForm = ref({
  address: "",
  address2: "",
  city: "",
  postalCode: "",
  state: "",
  country: "",
  notes: "",
});

// Form errors
const companyErrors = ref({
  name: "",
  type: "",
  registrationNumber: "",
  email: "",
  phone: "",
});

const addressErrors = ref({
  address: "",
  city: "",
  postalCode: "",
  country: "",
});

// Company type options
const companyTypeOptions: CompanyTypeOption[] = [
  { label: "Limited Liability Company (LLC)", value: "LLC" },
  { label: "Corporation", value: "CORPORATION" },
  { label: "Partnership", value: "PARTNERSHIP" },
  { label: "Sole Proprietorship", value: "SOLE_PROPRIETORSHIP" },
  { label: "Non-Profit", value: "NON_PROFIT" },
];

// Services
const toaster = useNuiToasts();

// Methods
const initForms = () => {
  if (props.company) {
    // Extract legal entity type from contractTerms if available
    let legalEntityType = "";
    if (
      props.company.contractTerms &&
      typeof props.company.contractTerms === "object"
    ) {
      try {
        const contractTerms = props.company.contractTerms;
        legalEntityType = contractTerms.legalEntityType || "";
      } catch (e) {
        console.error("Error parsing contractTerms:", e);
      }
    }

    // Find the matching company type option
    let typeOption: CompanyTypeOption | string = "";

    if (legalEntityType) {
      const foundOption = companyTypeOptions.find(
        (option) => option.value === legalEntityType
      );
      if (foundOption) {
        typeOption = foundOption;
      }
    }

    console.log("Legal entity type:", legalEntityType);
    console.log("Type option:", typeOption);

    companyForm.value = {
      name: props.company.name || "",
      type: typeOption,
      registrationNumber: props.company.registrationNumber || "",
      vatNumber: props.company.vatNumber || "",
      website: props.company.website || "",
      email: props.company.email || "",
      phone: props.company.phone || "",
    };

    addressForm.value = {
      address: props.company.address || "",
      address2: props.company.address2 || "",
      city: props.company.city || "",
      postalCode: props.company.postalCode || "",
      state: props.company.state || "",
      country: props.company.country || "",
      notes: props.company.notes || "",
    };
  }
};

const validateCompanyForm = () => {
  let isValid = true;
  companyErrors.value = {
    name: "",
    type: "",
    registrationNumber: "",
    email: "",
    phone: "",
  };

  console.log("Validating company form with type:", companyForm.value.type);

  if (!companyForm.value.name) {
    companyErrors.value.name = "Company name is required";
    isValid = false;
  }

  // Check if type is empty (either empty string or object without value)
  if (
    !companyForm.value.type ||
    (typeof companyForm.value.type === "object" &&
      !companyForm.value.type.value)
  ) {
    companyErrors.value.type = "Company type is required";
    isValid = false;
  }

  if (!companyForm.value.registrationNumber) {
    companyErrors.value.registrationNumber = "Registration number is required";
    isValid = false;
  }

  if (
    companyForm.value.email &&
    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyForm.value.email)
  ) {
    companyErrors.value.email = "Invalid email format";
    isValid = false;
  }

  return isValid;
};

const validateAddressForm = () => {
  let isValid = true;
  addressErrors.value = {
    address: "",
    city: "",
    postalCode: "",
    country: "",
  };

  if (!addressForm.value.address) {
    addressErrors.value.address = "Address is required";
    isValid = false;
  }

  if (!addressForm.value.city) {
    addressErrors.value.city = "City is required";
    isValid = false;
  }

  if (!addressForm.value.postalCode) {
    addressErrors.value.postalCode = "Postal code is required";
    isValid = false;
  }

  if (!addressForm.value.country) {
    addressErrors.value.country = "Country is required";
    isValid = false;
  }

  return isValid;
};

const updateCompanyDetails = async () => {
  if (!validateCompanyForm() || !props.company?.id) return;

  try {
    isUpdating.value = true;

    const response = await api.put(`/companies/${props.company.id}`, {
      name: companyForm.value.name,
      type: companyForm.value.type, // This will be handled by the backend
      registrationNumber: companyForm.value.registrationNumber,
      vatNumber: companyForm.value.vatNumber,
      website: companyForm.value.website,
      email: companyForm.value.email,
      phone: companyForm.value.phone,
    });

    if (response?.data) {
      emit("companyUpdated");
      toaster.add({
        title: "Success",
        description: "Company details updated successfully",
        icon: "ph:check-circle-duotone",
        progress: true,
      });
    }
  } catch (error) {
    console.error("Error updating company details:", error);
    toaster.add({
      title: "Error",
      description: "Failed to update company details",
      icon: "ph:x-circle-duotone",
      progress: true,
    });
  } finally {
    isUpdating.value = false;
  }
};

const updateCompanyAddress = async () => {
  if (!validateAddressForm() || !props.company?.id) return;

  try {
    isUpdatingAddress.value = true;

    const response = await api.put(`/companies/${props.company.id}`, {
      address: addressForm.value.address,
      address2: addressForm.value.address2,
      city: addressForm.value.city,
      postalCode: addressForm.value.postalCode,
      state: addressForm.value.state,
      country: addressForm.value.country,
      notes: addressForm.value.notes,
    });

    if (response.data) {
      emit("companyUpdated");
    }
  } catch (error) {
    console.error("Error updating company address:", error);
    toaster.add({
      title: "Error",
      description: "Failed to update company address",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    isUpdatingAddress.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  initForms();
});
</script>
