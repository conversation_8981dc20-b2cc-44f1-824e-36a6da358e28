import {
  __commonJS,
  __export,
  __reExport,
  __toESM
} from "./chunk-IKZWERSR.js";

// ../node_modules/.pnpm/smooth-dnd@0.12.1_patch_has_4e4f799aa1aa3c4114f95d045588fa15/node_modules/smooth-dnd/dist/index.js
var require_dist = __commonJS({
  "../node_modules/.pnpm/smooth-dnd@0.12.1_patch_has_4e4f799aa1aa3c4114f95d045588fa15/node_modules/smooth-dnd/dist/index.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? t(exports) : "function" == typeof define && define.amd ? define(["exports"], t) : t((e = e || self).SmoothDnD = {});
    }(exports, function(e) {
      "use strict";
      var l, t, r = "smooth-dnd-container-instance", f = "smooth-dnd-draggable-wrapper", o = "animated", p = "__smooth_dnd_draggable_translation_value", u = "__smooth_dnd_draggable_visibility_value", v = "smooth-dnd-ghost", g = "smooth-dnd-container", d = "smooth-dnd-extra-size-for-insertion", h2 = "smooth-dnd-stretcher-element", y = "smooth-dnd-stretcher-instance", s = "smooth-dnd-disable-touch-action", c = "smooth-dnd-no-user-select", i = "smooth-dnd-prevent-auto-scroll-class", b = "smooth-dnd-drop-preview-default-class", w = "smooth-dnd-drop-preview-inner-class", x = "smooth-dnd-drop-preview-constant-class", E = "smooth-dnd-drop-preview-flex-container-class", n = Object.freeze({ containerInstance: r, defaultGroupName: "@@smooth-dnd-default-group@@", wrapperClass: f, defaultGrabHandleClass: "smooth-dnd-default-grap-handle", animationClass: o, translationValue: p, visibilityValue: u, ghostClass: v, containerClass: g, extraSizeForInsertion: d, stretcherElementClass: h2, stretcherElementInstance: y, isDraggableDetached: "smoth-dnd-is-draggable-detached", disbaleTouchActions: s, noUserSelectClass: c, preventAutoScrollClass: i, dropPlaceholderDefaultClass: b, dropPlaceholderInnerClass: w, dropPlaceholderWrapperClass: x, dropPlaceholderFlexContainerClass: E }), C = { groupName: void 0, behaviour: "move", orientation: "vertical", getChildPayload: void 0, animationDuration: 250, autoScrollEnabled: true, shouldAcceptDrop: void 0, shouldAnimateDrop: void 0 };
      function a(e2) {
        return (a = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e3) {
          return typeof e3;
        } : function(e3) {
          return e3 && "function" == typeof Symbol && e3.constructor === Symbol && e3 !== Symbol.prototype ? "symbol" : typeof e3;
        })(e2);
      }
      function m(e2, t2, n2) {
        return t2 in e2 ? Object.defineProperty(e2, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e2[t2] = n2, e2;
      }
      function D(e2) {
        return function(e3) {
          if (Array.isArray(e3)) {
            for (var t2 = 0, n2 = new Array(e3.length); t2 < e3.length; t2++) n2[t2] = e3[t2];
            return n2;
          }
        }(e2) || function(e3) {
          if (Symbol.iterator in Object(e3) || "[object Arguments]" === Object.prototype.toString.call(e3)) return Array.from(e3);
        }(e2) || function() {
          throw new TypeError("Invalid attempt to spread non-iterable instance");
        }();
      }
      (t = l || (l = {})).x = "x", t.y = "y", t.xy = "xy";
      function S(e2, t2, n2) {
        return "x" === n2 ? { left: Math.max(e2.left, t2.left), top: e2.top, right: Math.min(e2.right, t2.right), bottom: e2.bottom } : { left: e2.left, top: Math.max(e2.top, t2.top), right: e2.right, bottom: Math.min(e2.bottom, t2.bottom) };
      }
      function O(e2) {
        var t2 = window.getComputedStyle(e2), n2 = t2.overflow;
        if ("auto" === n2 || "scroll" === n2) return l.xy;
        var o2 = t2["overflow-x"], r2 = "auto" === o2 || "scroll" === o2, i2 = t2["overflow-y"], a2 = "auto" === i2 || "scroll" === i2;
        return r2 && a2 ? l.xy : r2 ? l.x : a2 ? l.y : null;
      }
      function R(e2, t2) {
        var n2 = window.getComputedStyle(e2), o2 = n2.overflow, r2 = n2["overflow-".concat(t2)];
        return "auto" === o2 || "scroll" === o2 || ("auto" === r2 || "scroll" === r2);
      }
      var A = function(e2, t2) {
        return { left: Math.max(e2.left, t2.left), top: Math.max(e2.top, t2.top), right: Math.min(e2.right, t2.right), bottom: Math.min(e2.bottom, t2.bottom) };
      }, I = function(e2) {
        var t2 = e2.getBoundingClientRect(), n2 = { left: t2.left, right: t2.right, top: t2.top, bottom: t2.bottom };
        if (P(e2, "x") && !B(e2, "x")) {
          var o2 = n2.right - n2.left;
          n2.right = n2.right + e2.scrollWidth - o2;
        }
        if (P(e2, "y") && !B(e2, "y")) {
          var r2 = n2.bottom - n2.top;
          n2.bottom = n2.bottom + e2.scrollHeight - r2;
        }
        return n2;
      }, B = function(e2, t2) {
        var n2 = window.getComputedStyle(e2), o2 = n2.overflow, r2 = n2["overflow-".concat(t2)];
        return "auto" === o2 || "scroll" === o2 || "hidden" === o2 || ("auto" === r2 || "scroll" === r2 || "hidden" === r2);
      }, P = function(e2, t2) {
        return "x" === t2 ? e2.scrollWidth > e2.clientWidth : e2.scrollHeight > e2.clientHeight;
      }, T = function(e2, t2) {
        var n2 = e2, o2 = t2 || I(e2);
        for (n2 = e2.parentElement; n2; ) P(n2, "x") && B(n2, "x") && (o2 = S(o2, n2.getBoundingClientRect(), "x")), P(n2, "y") && B(n2, "y") && (o2 = S(o2, n2.getBoundingClientRect(), "y")), n2 = n2.parentElement;
        return o2;
      }, z = function(e2, n2) {
        for (var o2 = e2; o2; ) {
          if (o2[r]) {
            var t2 = function() {
              var t3 = o2[r];
              if (n2.some(function(e3) {
                return e3 === t3;
              })) return { v: t3 };
            }();
            if ("object" === a(t2)) return t2.v;
          }
          o2 = o2.parentElement;
        }
        return null;
      }, N = function(e2, t2) {
        for (var n2 = e2; n2; ) {
          if (n2.matches(t2)) return n2;
          n2 = n2.parentElement;
        }
        return null;
      }, L = function(e2, t2) {
        return -1 < e2.className.split(" ").map(function(e3) {
          return e3;
        }).indexOf(t2);
      }, M = function(e2, t2) {
        if (e2) {
          var n2 = e2.className.split(" ").filter(function(e3) {
            return e3;
          });
          -1 === n2.indexOf(t2) && (n2.unshift(t2), e2.className = n2.join(" "));
        }
      }, j = function(e2, t2) {
        if (e2) {
          var n2 = e2.className.split(" ").filter(function(e3) {
            return e3 && e3 !== t2;
          });
          e2.className = n2.join(" ");
        }
      }, _ = function(e2, t2) {
        return e2.removeChild(e2.children[t2]);
      }, F = function(e2, t2, n2) {
        n2 >= e2.children.length ? e2.appendChild(t2) : e2.insertBefore(t2, e2.children[n2]);
      }, V = function() {
        window.getSelection ? window.getSelection().empty ? window.getSelection().empty() : window.getSelection().removeAllRanges && window.getSelection().removeAllRanges() : window.document.selection && window.document.selection.empty();
      }, X = function(e2) {
        if (e2) {
          var t2 = window.getComputedStyle(e2);
          if (t2) return t2.cursor;
        }
        return null;
      };
      function H(e2) {
        return !(e2.bottom <= e2.top || e2.right <= e2.left);
      }
      function Y(e2) {
        var s2 = e2.element, c2 = e2.draggables;
        return function(e3, t2) {
          var n2 = e3, o2 = n2.removedIndex, r2 = n2.addedIndex, i2 = n2.droppedElement, a2 = null;
          if (null !== o2 && (a2 = _(s2, o2), c2.splice(o2, 1)), null !== r2) {
            var l2 = window.document.createElement("div");
            l2.className = "".concat(f), l2.appendChild(a2 && a2.firstElementChild ? a2.firstElementChild : i2), F(s2, l2, r2), r2 >= c2.length ? c2.push(l2) : c2.splice(r2, 0, l2);
          }
          t2 && t2(e3);
        };
      }
      var k = Object.freeze({ domDropHandler: Y, reactDropHandler: function() {
        return { handler: function() {
          return function(e2, t2) {
            t2 && t2(e2);
          };
        } };
      } }), G = { size: "offsetWidth", distanceToParent: "offsetLeft", translate: "transform", begin: "left", end: "right", dragPosition: "x", scrollSize: "scrollWidth", offsetSize: "offsetWidth", scrollValue: "scrollLeft", scale: "scaleX", setSize: "width", setters: { translate: function(e2) {
        return "translate3d(".concat(e2, "px, 0, 0)");
      } } }, W = { size: "offsetHeight", distanceToParent: "offsetTop", translate: "transform", begin: "top", end: "bottom", dragPosition: "y", scrollSize: "scrollHeight", offsetSize: "offsetHeight", scrollValue: "scrollTop", scale: "scaleY", setSize: "height", setters: { translate: function(e2) {
        return "translate3d(0,".concat(e2, "px, 0)");
      } } };
      function q(n2, s2, e2) {
        n2[d] = 0;
        var o2 = /* @__PURE__ */ function(o3) {
          return { get: function(e3, t3) {
            return e3[o3[t3] || t3];
          }, set: function(e3, t3, n3) {
            e3[o3[t3]] = o3.setters[t3] ? o3.setters[t3](n3) : n3;
          } };
        }("horizontal" === s2 ? G : W), c2 = { translation: 0 };
        function t2() {
          r2(n2), function(e3) {
            var t3 = e3.getBoundingClientRect();
            c2.scaleX = e3.offsetWidth ? (t3.right - t3.left) / e3.offsetWidth : 1, c2.scaleY = e3.offsetHeight ? (t3.bottom - t3.top) / e3.offsetHeight : 1;
          }(n2);
        }
        function r2(e3) {
          c2.rect = I(e3);
          var t3 = T(e3, c2.rect);
          H(t3) && (c2.lastVisibleRect = c2.visibleRect), c2.visibleRect = t3;
        }
        function i2(e3) {
          var t3 = e3;
          if (t3.tagName) {
            var n3 = t3.getBoundingClientRect();
            return "vertical" === s2 ? n3.bottom - n3.top : n3.right - n3.left;
          }
          return o2.get(e3, "size") * o2.get(c2, "scale");
        }
        function a2(e3) {
          return o2.get(e3, "dragPosition");
        }
        return window.addEventListener("resize", function() {
          r2(n2);
        }), setTimeout(function() {
          t2();
        }, 10), { getSize: i2, getContainerRectangles: function() {
          return { rect: c2.rect, visibleRect: c2.visibleRect, lastVisibleRect: c2.lastVisibleRect };
        }, getBeginEndOfDOMRect: function(e3) {
          return { begin: o2.get(e3, "begin"), end: o2.get(e3, "end") };
        }, getBeginEndOfContainer: function() {
          return { begin: o2.get(c2.rect, "begin") + c2.translation, end: o2.get(c2.rect, "end") + c2.translation };
        }, getBeginEndOfContainerVisibleRect: function() {
          return { begin: o2.get(c2.visibleRect, "begin") + c2.translation, end: o2.get(c2.visibleRect, "end") + c2.translation };
        }, getBeginEnd: function(e3) {
          var t3 = function(e4) {
            return (o2.get(e4, "distanceToParent") + (e4[p] || 0)) * o2.get(c2, "scale");
          }(e3) + (o2.get(c2.rect, "begin") + c2.translation) - o2.get(n2, "scrollValue");
          return { begin: t3, end: t3 + i2(e3) * o2.get(c2, "scale") };
        }, getAxisValue: a2, setTranslation: function(e3, t3) {
          t3 ? o2.set(e3.style, "translate", t3) : e3.style.removeProperty("transform"), e3[p] = t3;
        }, getTranslation: function(e3) {
          return e3[p];
        }, setVisibility: function(e3, t3) {
          void 0 !== e3[u] && e3[u] === t3 || (t3 ? e3.style.removeProperty("visibility") : e3.style.visibility = "hidden", e3[u] = t3);
        }, isVisible: function(e3) {
          return void 0 === e3[u] || e3[u];
        }, isInVisibleRect: function(e3, t3) {
          var n3 = c2.visibleRect, o3 = n3.left, r3 = n3.top, i3 = n3.right, a3 = n3.bottom;
          a3 - r3 < 2 && (a3 = r3 + 30);
          var l2 = c2.rect;
          return "vertical" === s2 ? e3 > l2.left && e3 < l2.right && r3 < t3 && t3 < a3 : o3 < e3 && e3 < i3 && t3 > l2.top && t3 < l2.bottom;
        }, setSize: function(e3, t3) {
          o2.set(e3, "setSize", t3);
        }, getTopLeftOfElementBegin: function(e3) {
          var t3 = 0;
          return { top: "horizontal" === s2 ? (t3 = e3, c2.rect.top) : (t3 = c2.rect.left, e3), left: t3 };
        }, getScrollSize: function(e3) {
          return o2.get(e3, "scrollSize");
        }, getScrollValue: function(e3) {
          return o2.get(e3, "scrollValue");
        }, setScrollValue: function(e3, t3) {
          return o2.set(e3, "scrollValue", t3);
        }, invalidate: t2, invalidateRects: function() {
          r2(n2);
        }, getPosition: function(e3) {
          return a2(e3);
        }, setBegin: function(e3, t3) {
          o2.set(e3, "begin", t3);
        } };
      }
      function U(e2, t2, n2) {
        var o2, r2, i2, a2 = n2.left, l2 = n2.right, s2 = n2.top, c2 = n2.bottom, u2 = e2.x, d2 = e2.y;
        if (u2 < a2 || l2 < u2 || d2 < s2 || c2 < d2) return null;
        i2 = "x" === t2 ? (o2 = a2, r2 = l2, u2) : (o2 = s2, r2 = c2, d2);
        var f2 = r2 - o2, g2 = 400 < f2 ? 100 : f2 / 4;
        return r2 - i2 < g2 ? { direction: "end", speedFactor: (g2 - (r2 - i2)) / g2 } : i2 - o2 < g2 ? { direction: "begin", speedFactor: (g2 - (i2 - o2)) / g2 } : null;
      }
      function J(l2) {
        var s2 = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : "y", c2 = null, u2 = null, d2 = null, f2 = null;
        return { animate: function(e2, t2) {
          d2 = e2, f2 = t2, function a2() {
            null === c2 && (c2 = requestAnimationFrame(function(e3) {
              null === u2 && (u2 = e3);
              var t3 = e3 - u2;
              u2 = e3;
              var n2, o2, r2, i2 = t3 / 1e3 * f2;
              o2 = s2, r2 = i2 = "begin" === d2 ? 0 - i2 : i2, (n2 = l2) && (n2 !== window ? "x" === o2 ? n2.scrollLeft += r2 : n2.scrollTop += r2 : "x" === o2 ? n2.scrollBy(r2, 0) : n2.scrollBy(0, r2)), c2 = null, a2();
            }));
          }();
        }, stop: function() {
          null !== c2 && (cancelAnimationFrame(c2), c2 = null), u2 = null;
        } };
      }
      function K(e2) {
        return function() {
          return T(e2, e2.getBoundingClientRect());
        };
      }
      var Q, Z, $, ee = function(e2) {
        var u2 = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : 1500, r2 = e2.reduce(function(e3, t2) {
          var n2 = function(e4) {
            for (var t3 = [], n3 = e4.element; n3; ) {
              var o2 = O(n3);
              if (o2 && !L(n3, i)) {
                var r3 = {};
                switch (o2) {
                  case l.xy:
                    r3.x = { animator: J(n3, "x") }, r3.y = { animator: J(n3, "y") };
                    break;
                  case l.x:
                    r3.x = { animator: J(n3, "x") };
                    break;
                  case l.y:
                    r3.y = { animator: J(n3, "y") };
                }
                t3.push({ axisAnimations: r3, getRect: K(n3), scrollerElement: n3 });
              }
              n3 = n3.parentElement;
            }
            return t3;
          }(t2).filter(function(t3) {
            return !e3.find(function(e4) {
              return e4.scrollerElement === t3.scrollerElement;
            });
          });
          return [].concat(D(e3), D(n2));
        }, []);
        return function(e3) {
          var t2 = e3.draggableInfo;
          if (e3.reset) r2.forEach(function(e4) {
            e4.axisAnimations.x && e4.axisAnimations.x.animator.stop(), e4.axisAnimations.y && e4.axisAnimations.y.animator.stop();
          });
          else if (t2) {
            !function(e4, o3) {
              e4.forEach(function(e5) {
                var t3 = e5.axisAnimations, n3 = (0, e5.getRect)();
                t3.x && (t3.x.scrollParams = U(o3, "x", n3), e5.cachedRect = n3), t3.y && (t3.y.scrollParams = U(o3, "y", n3), e5.cachedRect = n3);
              });
            }(r2, t2.mousePosition), r2.forEach(function(e4) {
              var t3 = e4.axisAnimations, n3 = t3.x, o3 = t3.y;
              if (n3) if (n3.scrollParams) {
                var r3 = n3.scrollParams, i2 = r3.direction, a2 = r3.speedFactor;
                n3.animator.animate(i2, a2 * u2);
              } else n3.animator.stop();
              if (o3) if (o3.scrollParams) {
                var l2 = o3.scrollParams, s2 = l2.direction, c2 = l2.speedFactor;
                o3.animator.animate(s2, c2 * u2);
              } else o3.animator.stop();
            });
            var n2 = r2.filter(function(e4) {
              return e4.cachedRect;
            });
            if (n2.length && 1 < n2.length) {
              var o2 = function(e4, t3) {
                for (var n3 = document.elementFromPoint(t3.x, t3.y); n3; ) {
                  var o3 = e4.find(function(e5) {
                    return e5.scrollerElement === n3;
                  });
                  if (o3) return o3;
                  n3 = n3.parentElement;
                }
                return null;
              }(n2, t2.mousePosition);
              o2 && n2.forEach(function(e4) {
                e4 !== o2 && (e4.axisAnimations.x && e4.axisAnimations.x.animator.stop(), e4.axisAnimations.y && e4.axisAnimations.y.animator.stop());
              });
            }
          }
        };
      };
      "undefined" != typeof window && ((Q = Element) && Q.prototype && !Q.prototype.matches && (Q.prototype.matches = Q.prototype.matchesSelector || Q.prototype.mozMatchesSelector || Q.prototype.msMatchesSelector || Q.prototype.oMatchesSelector || Q.prototype.webkitMatchesSelector || function(e2) {
        for (var t2 = (this.document || this.ownerDocument).querySelectorAll(e2), n2 = t2.length; 0 <= --n2 && t2.item(n2) !== this; ) ;
        return -1 < n2;
      }), (Z = Node || Element) && Z.prototype && null == Z.prototype.firstElementChild && Object.defineProperty(Z.prototype, "firstElementChild", { get: function() {
        for (var e2, t2 = this.childNodes, n2 = 0; e2 = t2[n2++]; ) if (1 === e2.nodeType) return e2;
        return null;
      } }), Array.prototype.some || (Array.prototype.some = function(e2) {
        if (null == this) throw new TypeError("Array.prototype.some called on null or undefined");
        if ("function" != typeof e2) throw new TypeError();
        for (var t2 = Object(this), n2 = t2.length >>> 0, o2 = 2 <= arguments.length ? arguments[1] : void 0, r2 = 0; r2 < n2; r2++) if (r2 in t2 && e2.call(o2, t2[r2], r2, t2)) return true;
        return false;
      }));
      var te = { overflow: "hidden", display: "block" }, ne = { height: "100%", display: "table-cell", "vertical-align": "top" }, oe = (m($ = {}, ".".concat(g), { position: "relative", "min-height": "30px", "min-width": "30px" }), m($, ".".concat(g, ".horizontal"), { display: "table" }), m($, ".".concat(g, ".horizontal > .").concat(h2), { display: "inline-block" }), m($, ".".concat(g, ".horizontal > .").concat(f), ne), m($, ".".concat(g, ".vertical > .").concat(f), te), m($, ".".concat(f), { "box-sizing": "border-box" }), m($, ".".concat(f, ".horizontal"), ne), m($, ".".concat(f, ".vertical"), te), m($, ".".concat(f, ".animated"), { transition: "transform ease" }), m($, ".".concat(v), { "box-sizing": "border-box" }), m($, ".".concat(v, ".animated"), { transition: "all ease-in-out" }), m($, ".".concat(v, " *"), { "pointer-events": "none" }), m($, ".".concat(s, " *"), { "touch-action": "none", "-ms-touch-action": "none" }), m($, ".".concat(c), { "-webkit-touch-callout": "none", "-webkit-user-select": "none", "-khtml-user-select": "none", "-moz-user-select": "none", "-ms-user-select": "none", "user-select": "none" }), m($, ".".concat(w), { flex: "1" }), m($, ".".concat(g, ".horizontal > .").concat(x), { height: "100%", overflow: "hidden", display: "table-cell", "vertical-align": "top" }), m($, ".".concat(g, ".vertical > .").concat(x), { overflow: "hidden", display: "block", width: "100%" }), m($, ".".concat(E), { width: "100%", height: "100%", display: "flex", "justify-content": "stretch", "align-items": "stretch" }), m($, ".".concat(b), { "background-color": "rgba(150, 150, 150, 0.1)", border: "1px solid #ccc" }), $);
      function re(o2) {
        return Object.keys(o2).reduce(function(e2, t2) {
          var n2 = o2[t2];
          return "object" === a(n2) ? "".concat(e2).concat(t2, "{").concat(re(n2), "}") : "".concat(e2).concat(t2, ":").concat(n2, ";");
        }, "");
      }
      function ie(e2) {
        if (e2 && "undefined" != typeof window) {
          var t2 = window.document.head || window.document.getElementsByTagName("head")[0], n2 = window.document.createElement("style"), o2 = re({ "body *": { cursor: "".concat(e2, " !important") } });
          return n2.type = "text/css", n2.styleSheet ? n2.styleSheet.cssText = o2 : n2.appendChild(window.document.createTextNode(o2)), t2.appendChild(n2), n2;
        }
        return null;
      }
      var ae, le, se = ["mousedown", "touchstart"], ce = ["mousemove", "touchmove"], ue = ["mouseup", "touchend"], de = null, fe = null, ge = null, me = null, pe = [], ve = false, he = false, ye = false, be = false, we = null, xe = null, Ee = null, Ce = null, De = (ae = null, le = false, { start: function() {
        le || (le = true, function e2() {
          ae = requestAnimationFrame(function() {
            de.forEach(function(e3) {
              return e3.layout.invalidateRects();
            }), setTimeout(function() {
              null !== ae && e2();
            }, 50);
          });
        }());
      }, stop: function() {
        null !== ae && (cancelAnimationFrame(ae), ae = null), le = false;
      } }), Se = "undefined" != typeof window && !!(window.navigator.userAgent.match(/Android/i) || window.navigator.userAgent.match(/webOS/i) || window.navigator.userAgent.match(/iPhone/i) || window.navigator.userAgent.match(/iPad/i) || window.navigator.userAgent.match(/iPod/i) || window.navigator.userAgent.match(/BlackBerry/i) || window.navigator.userAgent.match(/Windows Phone/i));
      function Oe() {
        "undefined" != typeof window && se.forEach(function(e2) {
          window.document.addEventListener(e2, Ve, { passive: false });
        });
      }
      function Re() {
        return me && me.ghostParent ? me.ghostParent : fe && fe.parentElement || window.document.body;
      }
      var Ae, Ie, Be, Pe, Te, ze, Ne = (Pe = null, Te = 1, ze = 5, function(e2, t2, n2) {
        Ae = Je(e2), Be = n2, (Ie = "number" == typeof t2 ? t2 : Se ? 200 : 0) && (Pe = setTimeout(Fe, Ie)), ce.forEach(function(e3) {
          return window.document.addEventListener(e3, Le);
        }, { passive: false }), ue.forEach(function(e3) {
          return window.document.addEventListener(e3, Me);
        }, { passive: false }), window.document.addEventListener("drag", je, { passive: false });
      });
      function Le(e2) {
        var t2 = Je(e2), n2 = t2.clientX, o2 = t2.clientY;
        if (Ie) (Math.abs(Ae.clientX - n2) > ze || Math.abs(Ae.clientY - o2) > ze) && _e();
        else if (Math.abs(Ae.clientX - n2) > Te || Math.abs(Ae.clientY - o2) > Te) return Fe();
      }
      function Me() {
        _e();
      }
      function je() {
        _e();
      }
      function _e() {
        clearTimeout(Pe), ce.forEach(function(e2) {
          return window.document.removeEventListener(e2, Le);
        }, { passive: false }), ue.forEach(function(e2) {
          return window.document.removeEventListener(e2, Me);
        }, { passive: false }), window.document.removeEventListener("drag", je, { passive: false });
      }
      function Fe() {
        clearTimeout(Pe), _e(), Be();
      }
      function Ve(e2) {
        var t2 = Je(e2);
        if (!ve && (void 0 === t2.button || 0 === t2.button) && (fe = N(t2.target, "." + f))) {
          var n2 = N(fe, "." + g), o2 = pe.filter(function(e3) {
            return e3.element === n2;
          })[0], r2 = o2.getOptions().dragHandleSelector, i2 = o2.getOptions().nonDragAreaSelector, a2 = true;
          if (r2 && !N(t2.target, r2) && (a2 = false), i2 && N(t2.target, i2) && (a2 = false), a2) {
            o2.layout.invalidate(), M(window.document.body, s), M(window.document.body, c);
            window.document.addEventListener("mouseup", function e3() {
              j(window.document.body, s), j(window.document.body, c), window.document.removeEventListener("mouseup", e3);
            });
          }
          a2 && Ne(t2, o2.getOptions().dragBeginDelay, function() {
            V(), et(t2, X(e2.target)), ce.forEach(function(e3) {
              window.document.addEventListener(e3, Xe, { passive: false });
            }), ue.forEach(function(e3) {
              window.document.addEventListener(e3, Ue, { passive: false });
            });
          });
        }
      }
      function Xe(e2) {
        e2.preventDefault();
        var t2 = Je(e2);
        if (me) {
          var n2 = me.container.getOptions();
          "contain" === n2.behaviour ? function(e3) {
            var t3, n3, o2, r2, i2 = e3.clientX, a2 = e3.clientY, l2 = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : "vertical", s2 = me.container.layout.getBeginEndOfContainerVisibleRect();
            r2 = "vertical" === l2 ? (t3 = a2, n3 = "y", o2 = "top", me.size.offsetHeight) : (t3 = i2, n3 = "x", o2 = "left", me.size.offsetWidth);
            var c2 = s2.begin, u2 = s2.end - r2, d2 = Math.max(c2, Math.min(u2, t3 + ge.positionDelta[o2]));
            ge.topLeft[n3] = d2, me.position[n3] = Math.max(s2.begin, Math.min(s2.end, t3 + ge.centerDelta[n3])), me.mousePosition[n3] = Math.max(s2.begin, Math.min(s2.end, t3)), me.position[n3] < s2.begin + r2 / 2 && (me.position[n3] = s2.begin + 2), me.position[n3] > s2.end - r2 / 2 && (me.position[n3] = s2.end - 2);
          }(t2, n2.orientation) : Ee ? "y" === Ee ? (ge.topLeft.y = t2.clientY + ge.positionDelta.top, me.position.y = t2.clientY + ge.centerDelta.y, me.mousePosition.y = t2.clientY) : "x" === Ee && (ge.topLeft.x = t2.clientX + ge.positionDelta.left, me.position.x = t2.clientX + ge.centerDelta.x, me.mousePosition.x = t2.clientX) : (ge.topLeft.x = t2.clientX + ge.positionDelta.left, ge.topLeft.y = t2.clientY + ge.positionDelta.top, me.position.x = t2.clientX + ge.centerDelta.x, me.position.y = t2.clientY + ge.centerDelta.y, me.mousePosition.x = t2.clientX, me.mousePosition.y = t2.clientY), nt(), (be = !we(me)) && We();
        } else et(t2, X(e2.target));
      }
      var He, Ye, ke, Ge, We = (He = qe, ke = !(Ye = 20), Ge = null, function() {
        for (var e2 = arguments.length, t2 = new Array(e2), n2 = 0; n2 < e2; n2++) t2[n2] = arguments[n2];
        Ge && clearTimeout(Ge), ke && !Ge ? He.call.apply(He, [null].concat(t2)) : Ge = setTimeout(function() {
          Ge = null, He.call.apply(He, [null].concat(t2));
        }, Ye);
      });
      function qe() {
        be && (be = false, Ke(me, de));
      }
      function Ue() {
        ce.forEach(function(e2) {
          window.document.removeEventListener(e2, Xe, { passive: false });
        }), ue.forEach(function(e2) {
          window.document.removeEventListener(e2, Ue, { passive: false });
        }), xe({ reset: true }), Ce && (function(e2) {
          e2 && "undefined" != typeof window && (window.document.head || window.document.getElementsByTagName("head")[0]).removeChild(e2);
        }(Ce), Ce = null), me && (De.stop(), qe(), ye = true, function(e2) {
          function i2() {
            try {
              j(ge.ghost, "animated"), ge.ghost.style.transitionDuration = null, Re().removeChild(ge.ghost);
            } catch {
            } finally {
              e2();
            }
          }
          function t2(e3, t3, n3) {
            var o3 = e3.top, r3 = e3.left;
            M(ge.ghost, "animated"), n3 && M(ge.ghost.firstElementChild, n3), ge.topLeft.x = r3, ge.topLeft.y = o3, nt(t3), setTimeout(function() {
              i2();
            }, t3 + 20);
          }
          function n2(e3, t3) {
            M(ge.ghost, "animated"), nt(e3, 0.9, true), setTimeout(function() {
              t3();
            }, e3 + 20);
          }
          if (me.targetElement) {
            var o2 = pe.filter(function(e3) {
              return e3.element === me.targetElement;
            })[0];
            if (!(p2 = o2.getOptions()).shouldAnimateDrop || p2.shouldAnimateDrop(me.container.getOptions(), me.payload)) t2(o2.getDragResult().shadowBeginEnd.rect, Math.max(150, o2.getOptions().animationDuration / 2), o2.getOptions().dropClass);
            else i2();
          } else {
            var r2 = pe.filter(function(e3) {
              return e3 === me.container;
            })[0];
            if (r2) {
              var a2 = r2.getOptions(), l2 = a2.behaviour, s2 = a2.removeOnDropOut;
              if ("move" !== l2 && "contain" !== l2 || !he && s2 || !r2.getDragResult()) n2(r2.getOptions().animationDuration, i2);
              else {
                var c2 = r2.layout.getContainerRectangles();
                if (!H(c2.visibleRect) && H(c2.lastVisibleRect)) t2({ top: c2.lastVisibleRect.top, left: c2.lastVisibleRect.left }, r2.getOptions().animationDuration, r2.getOptions().dropClass);
                else {
                  var u2 = r2.getDragResult(), d2 = u2.removedIndex, f2 = u2.elementSize, g2 = r2.layout;
                  r2.getTranslateCalculator({ dragResult: { removedIndex: d2, addedIndex: d2, elementSize: f2, pos: void 0, shadowBeginEnd: void 0 } });
                  var m2 = 0 < d2 ? g2.getBeginEnd(r2.draggables[d2 - 1]).end : g2.getBeginEndOfContainer().begin;
                  t2(g2.getTopLeftOfElementBegin(m2), r2.getOptions().animationDuration, r2.getOptions().dropClass);
                }
              }
            } else n2(C.animationDuration, i2);
          }
          var p2;
        }(function() {
          $e(ve = false);
          for (var e2 = de || [], t2 = e2.shift(); void 0 !== t2; ) t2.handleDrop(me), t2 = e2.shift();
          we = Ee = me = ge = fe = de = null, ye = false;
        }));
      }
      function Je(e2) {
        return e2.touches ? e2.touches[0] : e2;
      }
      function Ke(n2, e2) {
        var o2 = false;
        e2.forEach(function(e3) {
          var t2 = e3.handleDrag(n2);
          o2 = !!t2.containerBoxChanged || false, t2.containerBoxChanged = false;
        }), o2 && (o2 = false, requestAnimationFrame(function() {
          pe.forEach(function(e3) {
            e3.layout.invalidateRects(), e3.onTranslated();
          });
        }));
      }
      function Qe(e2) {
        var t2 = e2, n2 = null;
        return function(e3) {
          return !(null !== n2 || !ve || ye) && (n2 = requestAnimationFrame(function() {
            ve && !ye && (Ke(e3, t2), xe({ draggableInfo: e3 })), n2 = null;
          }), true);
        };
      }
      function Ze(e2, t2) {
        return e2.getOptions().autoScrollEnabled ? ee(t2, e2.getScrollMaxSpeed()) : function(e3) {
          return null;
        };
      }
      function $e(o2) {
        pe.forEach(function(e2) {
          var t2 = o2 ? e2.getOptions().onDragStart : e2.getOptions().onDragEnd;
          if (t2) {
            var n2 = { isSource: e2 === me.container, payload: me.payload };
            e2.isDragRelevant(me.container, me.payload) ? n2.willAcceptDrop = true : n2.willAcceptDrop = false, t2(n2);
          }
        });
      }
      function et(e2, t2) {
        if (null !== fe) {
          ve = true;
          var n2 = pe.filter(function(e3) {
            return fe.parentElement === e3.element;
          })[0];
          n2.setDraggables(), Ee = n2.getOptions().lockAxis ? n2.getOptions().lockAxis.toLowerCase() : null, me = function(t3) {
            var e3 = pe.filter(function(e4) {
              return t3.parentElement === e4.element;
            })[0], n3 = e3.draggables.indexOf(t3), o2 = e3.getOptions().getGhostParent, r2 = t3.getBoundingClientRect();
            return { container: e3, element: t3, size: { offsetHeight: r2.bottom - r2.top, offsetWidth: r2.right - r2.left }, elementIndex: n3, payload: e3.getOptions().getChildPayload ? e3.getOptions().getChildPayload(n3) : void 0, targetElement: null, position: { x: 0, y: 0 }, groupName: e3.getOptions().groupName, ghostParent: o2 ? o2() : null, invalidateShadow: null, mousePosition: null, relevantContainers: null };
          }(fe), ge = function(e3, t3, n3, o2) {
            var r2 = t3.x, i2 = t3.y, a2 = e3.getBoundingClientRect(), l2 = a2.left, s2 = a2.top, c2 = a2.right, u2 = a2.bottom, d2 = A(n3.layout.getContainerRectangles().visibleRect, a2), f2 = d2.left + (d2.right - d2.left) / 2, g2 = d2.top + (d2.bottom - d2.top) / 2, m2 = e3.cloneNode(true);
            return m2.style.zIndex = "1000", m2.style.boxSizing = "border-box", m2.style.position = "fixed", m2.style.top = "0px", m2.style.left = "0px", m2.style.transform = null, m2.style.removeProperty("transform"), n3.shouldUseTransformForGhost() ? m2.style.transform = "translate3d(".concat(l2, "px, ").concat(s2, "px, 0)") : (m2.style.top = "".concat(s2, "px"), m2.style.left = "".concat(l2, "px")), m2.style.width = c2 - l2 + "px", m2.style.height = u2 - s2 + "px", m2.style.overflow = "visible", m2.style.transition = null, m2.style.removeProperty("transition"), m2.style.pointerEvents = "none", m2.style.userSelect = "none", n3.getOptions().dragClass ? setTimeout(function() {
              M(m2.firstElementChild, n3.getOptions().dragClass);
              var e4 = window.getComputedStyle(m2.firstElementChild).cursor;
              Ce = ie(e4);
            }) : Ce = ie(o2), M(m2, n3.getOptions().orientation || "vertical"), M(m2, v), { ghost: m2, centerDelta: { x: f2 - r2, y: g2 - i2 }, positionDelta: { left: l2 - r2, top: s2 - i2 }, topLeft: { x: l2, y: s2 } };
          }(fe, { x: e2.clientX, y: e2.clientY }, me.container, t2), me.position = { x: e2.clientX + ge.centerDelta.x, y: e2.clientY + ge.centerDelta.y }, me.mousePosition = { x: e2.clientX, y: e2.clientY }, de = pe.filter(function(e3) {
            return e3.isDragRelevant(n2, me.payload);
          }), me.relevantContainers = de, we = Qe(de), xe && xe({ reset: true, draggableInfo: void 0 }), xe = Ze(n2, de), de.forEach(function(e3) {
            return e3.prepareDrag(e3, de);
          }), $e(true), we(me), Re().appendChild(ge.ghost), De.start();
        }
      }
      var tt = null;
      function nt() {
        var e2 = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : 0, t2 = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : 1, n2 = 2 < arguments.length && void 0 !== arguments[2] && arguments[2], o2 = ge, r2 = o2.ghost, i2 = o2.topLeft, a2 = i2.x, l2 = i2.y, s2 = !me.container || me.container.shouldUseTransformForGhost(), c2 = s2 ? "translate3d(".concat(a2, "px,").concat(l2, "px, 0)") : null;
        if (1 !== t2 && (c2 = c2 ? "".concat(c2, " scale(").concat(t2, ")") : "scale(".concat(t2, ")")), 0 < e2) return ge.ghost.style.transitionDuration = e2 + "ms", void requestAnimationFrame(function() {
          c2 && (r2.style.transform = c2), s2 || (r2.style.left = a2 + "px", r2.style.top = l2 + "px"), tt = null, n2 && (r2.style.opacity = "0");
        });
        null === tt && (tt = requestAnimationFrame(function() {
          c2 && (r2.style.transform = c2), s2 || (r2.style.left = a2 + "px", r2.style.top = l2 + "px"), tt = null, n2 && (r2.style.opacity = "0");
        }));
      }
      function ot() {
        if (ve && !he && !ye) {
          be = !(he = true);
          var t2 = Object.assign({}, me, { targetElement: null, position: { x: Number.MAX_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER }, mousePosition: { x: Number.MAX_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER } });
          de.forEach(function(e2) {
            e2.handleDrag(t2);
          }), me.targetElement = null, me.cancelDrop = true, Ue(), he = false;
        }
      }
      "undefined" != typeof window && function() {
        if ("undefined" != typeof window) {
          var e2 = window.document.head || window.document.getElementsByTagName("head")[0], t2 = window.document.createElement("style");
          t2.id = "smooth-dnd-style-definitions";
          var n2 = re(oe);
          t2.type = "text/css", t2.styleSheet ? t2.styleSheet.cssText = n2 : t2.appendChild(window.document.createTextNode(n2)), e2.appendChild(t2);
        }
      }();
      var rt = (Oe(), { register: function(e2) {
        !function(e3) {
          pe.push(e3), ve && me && e3.isDragRelevant(me.container, me.payload) && (de.push(e3), e3.prepareDrag(e3, de), xe && xe({ reset: true, draggableInfo: void 0 }), xe = Ze(e3, de), we = Qe(de), e3.handleDrag(me));
        }(e2);
      }, unregister: function(e2) {
        !function(e3) {
          if (pe.splice(pe.indexOf(e3), 1), ve && me) {
            me.container === e3 && e3.fireRemoveElement(), me.targetElement === e3.element && (me.targetElement = null);
            var t2 = de.indexOf(e3);
            -1 < t2 && (de.splice(t2, 1), xe && xe({ reset: true, draggableInfo: void 0 }), xe = Ze(e3, de), we = Qe(de));
          }
        }(e2);
      }, isDragging: function() {
        return ve;
      }, cancelDrag: ot });
      function it(e2, t2) {
        var n2 = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : C.animationDuration;
        t2 ? (M(e2, o), e2.style.transitionDuration = n2 + "ms") : (j(e2, o), e2.style.removeProperty("transition-duration"));
      }
      function at(n2) {
        var o2 = [];
        return Array.prototype.forEach.call(n2.children, function(e2) {
          if (e2.nodeType === Node.ELEMENT_NODE) {
            var t2 = e2;
            L(e2, f) || (t2 = function(e3) {
              if (It.wrapChild) {
                var t3 = window.document.createElement("div");
                return t3.className = "".concat(f), e3.parentElement.insertBefore(t3, e3), t3.appendChild(e3), t3;
              }
              return e3;
            }(e2)), t2[p] = 0, o2.push(t2);
          } else n2.removeChild(e2);
        }), o2;
      }
      function lt(e2) {
        var g2 = e2.layout;
        return function(e3, t2) {
          var n2 = 2 < arguments.length && void 0 !== arguments[2] && arguments[2];
          return function e4(t3, n3, o2, r2) {
            var i2 = 4 < arguments.length && void 0 !== arguments[4] && arguments[4];
            if (r2 < o2) return o2;
            if (o2 === r2) {
              var a2 = g2.getBeginEnd(t3[o2]), l2 = a2.begin, s2 = a2.end;
              return i2 ? n3 < (s2 + l2) / 2 ? o2 : o2 + 1 : o2;
            }
            var c2 = Math.floor((r2 + o2) / 2), u2 = g2.getBeginEnd(t3[c2]), d2 = u2.begin, f2 = u2.end;
            return n3 < d2 ? e4(t3, n3, o2, c2 - 1, i2) : f2 < n3 ? e4(t3, n3, c2 + 1, r2, i2) : i2 ? n3 < (f2 + d2) / 2 ? c2 : c2 + 1 : c2;
          }(e3, t2, 0, e3.length - 1, n2);
        };
      }
      function st(e2) {
        var t2 = e2.element, n2 = e2.draggables, o2 = e2.layout, a2 = e2.getOptions, l2 = function(e3) {
          var t3 = e3.element, n3 = e3.draggables, o3 = e3.layout;
          return function() {
            n3.forEach(function(e4) {
              it(e4, false), o3.setTranslation(e4, 0), o3.setVisibility(e4, true);
            }), t3[y] && (t3[y].parentNode.removeChild(t3[y]), t3[y] = null);
          };
        }({ element: t2, draggables: n2, layout: o2, getOptions: a2 }), s2 = (It.dropHandler || Y)({ element: t2, draggables: n2, layout: o2, getOptions: a2 });
        return function(e3, t3) {
          var n3 = t3.addedIndex, o3 = t3.removedIndex, r2 = 2 < arguments.length && void 0 !== arguments[2] && arguments[2];
          if (l2(), !e3.cancelDrop && (e3.targetElement || a2().removeOnDropOut || r2)) {
            var i2 = { removedIndex: o3, addedIndex: null !== n3 ? null !== o3 && o3 < n3 ? n3 - 1 : n3 : null, payload: e3.payload };
            s2(i2, a2().onDrop);
          }
        };
      }
      function ct(e2) {
        var o2 = e2.element, r2 = e2.getOptions, i2 = null;
        return function(e3) {
          var t2 = e3.draggableInfo, n2 = i2;
          return null == i2 && t2.container.element === o2 && "copy" !== r2().behaviour && (n2 = i2 = t2.elementIndex), { removedIndex: n2 };
        };
      }
      function ut(e2) {
        var n2 = e2.draggables, o2 = e2.layout;
        return function(e3) {
          var t2 = e3.dragResult;
          null !== t2.removedIndex && o2.setVisibility(n2[t2.removedIndex], false);
        };
      }
      function dt(e2) {
        var r2 = e2.element, i2 = e2.layout;
        return function(e3) {
          var t2 = e3.draggableInfo, n2 = document.elementFromPoint(t2.position.x, t2.position.y);
          if (n2) {
            var o2 = z(n2, t2.relevantContainers);
            if (o2 && o2.element === r2) return { pos: i2.getPosition(t2.position) };
          }
          return { pos: null };
        };
      }
      function ft(e2) {
        var n2 = e2.layout, o2 = null;
        return function(e3) {
          var t2 = e3.draggableInfo;
          return null === e3.dragResult.pos ? o2 = null : { elementSize: o2 = o2 || n2.getSize(t2.size) };
        };
      }
      function gt(e2) {
        var o2 = e2.element;
        return function(e3) {
          var t2 = e3.draggableInfo, n2 = e3.dragResult;
          !function(e4, t3) {
            var n3 = !(2 < arguments.length && void 0 !== arguments[2]) || arguments[2];
            t3 && n3 ? e4.targetElement = t3 : e4.targetElement === t3 && (e4.targetElement = null);
          }(t2, o2, !!n2.pos);
        };
      }
      function mt() {
        return function(e2) {
          return null !== e2.dragResult.pos ? { addedIndex: 0 } : { addedIndex: null };
        };
      }
      function pt(e2) {
        var r2 = e2.layout, i2 = null;
        return function(e3) {
          var t2 = e3.dragResult.addedIndex;
          if (t2 === i2) return null;
          i2 = t2;
          var n2 = r2.getBeginEndOfContainer(), o2 = n2.begin;
          n2.end;
          return { shadowBeginEnd: { rect: r2.getTopLeftOfElementBegin(o2) } };
        };
      }
      function vt(e2) {
        var g2 = e2.layout, m2 = e2.element, p2 = e2.getOptions, v2 = null;
        return function(e3) {
          var t2 = e3.dragResult, n2 = t2.elementSize, o2 = t2.shadowBeginEnd, r2 = t2.addedIndex, i2 = t2.dropPlaceholderContainer, a2 = p2();
          if (a2.dropPlaceholder) {
            var l2 = "boolean" == typeof a2.dropPlaceholder ? {} : a2.dropPlaceholder, s2 = l2.animationDuration, c2 = l2.className, u2 = l2.showOnTop;
            if (null === r2) return i2 && null !== v2 && m2.removeChild(i2), v2 = null, { dropPlaceholderContainer: void 0 };
            if (!i2) {
              var d2 = document.createElement("div"), f2 = document.createElement("div");
              f2.className = E, d2.className = "".concat(w, " ").concat(c2 || b), (i2 = document.createElement("div")).className = "".concat(x), i2.style.position = "absolute", void 0 !== s2 && (i2.style.transition = "all ".concat(s2, "ms ease")), i2.appendChild(f2), f2.appendChild(d2), g2.setSize(i2.style, n2 + "px"), i2.style.pointerEvents = "none", u2 ? m2.appendChild(i2) : m2.insertBefore(i2, m2.firstElementChild);
            }
            return v2 !== r2 && o2.dropArea && g2.setBegin(i2.style, o2.dropArea.begin - g2.getBeginEndOfContainer().begin + "px"), v2 = r2, { dropPlaceholderContainer: i2 };
          }
          return null;
        };
      }
      function ht(e2) {
        var o2 = Et(e2);
        return function(e3) {
          var t2 = e3.draggableInfo, n2 = e3.dragResult;
          return t2.invalidateShadow ? o2({ draggableInfo: t2, dragResult: n2 }) : null;
        };
      }
      function yt(e2) {
        var o2 = function(e3) {
          var i2 = e3.draggables, a2 = lt({ layout: e3.layout });
          return function(e4) {
            var t2 = e4.dragResult, n2 = t2.shadowBeginEnd, o3 = t2.pos;
            if (n2) return n2.begin + n2.beginAdjustment <= o3 && n2.end >= o3 ? null : o3 < n2.begin + n2.beginAdjustment ? a2(i2, o3) : o3 > n2.end ? a2(i2, o3) + 1 : i2.length;
            var r2 = a2(i2, o3, true);
            return null !== r2 ? r2 : i2.length;
          };
        }(e2);
        return function(e3) {
          var t2 = e3.dragResult, n2 = null;
          return null !== t2.pos && null === (n2 = o2({ dragResult: t2 })) && (n2 = t2.addedIndex), { addedIndex: n2 };
        };
      }
      function bt() {
        var r2 = null;
        return function(e2) {
          var t2 = e2.dragResult, n2 = t2.addedIndex, o2 = t2.shadowBeginEnd;
          n2 !== r2 && null !== r2 && o2 && (o2.beginAdjustment = 0), r2 = n2;
        };
      }
      function wt(e2) {
        var u2 = e2.element, d2 = e2.draggables, f2 = e2.layout, g2 = e2.getOptions, m2 = null;
        return function(e3) {
          var t2 = e3.dragResult, n2 = t2.addedIndex, o2 = t2.removedIndex, r2 = t2.elementSize;
          if (null === o2) {
            if (null !== n2) {
              if (!m2) {
                var i2 = f2.getBeginEndOfContainer();
                i2.end = i2.begin + f2.getSize(u2);
                var a2 = f2.getScrollSize(u2) > f2.getSize(u2) ? i2.begin + f2.getScrollSize(u2) - f2.getScrollValue(u2) : i2.end, l2 = 0 < d2.length ? f2.getBeginEnd(d2[d2.length - 1]).end - d2[d2.length - 1][p] : i2.begin;
                if (a2 < l2 + r2) {
                  (m2 = window.document.createElement("div")).className = h2 + " " + g2().orientation;
                  var s2 = 0 < d2.length ? r2 + l2 - a2 : r2;
                  return f2.setSize(m2.style, "".concat(s2, "px")), u2.appendChild(m2), u2[y] = m2, { containerBoxChanged: true };
                }
              }
            } else if (m2) {
              f2.setTranslation(m2, 0);
              var c2 = m2;
              return m2 = null, u2.removeChild(c2), { containerBoxChanged: !(u2[y] = null) };
            }
          }
        };
      }
      function xt(e2) {
        var s2 = e2.draggables, c2 = e2.layout, u2 = null, d2 = null;
        return function(e3) {
          var t2 = e3.dragResult, n2 = t2.addedIndex, o2 = t2.removedIndex, r2 = t2.elementSize;
          if (n2 !== u2 || o2 !== d2) {
            for (var i2 = 0; i2 < s2.length; i2++) if (i2 !== o2) {
              var a2 = s2[i2], l2 = 0;
              null !== o2 && o2 < i2 && (l2 -= r2), null !== n2 && n2 <= i2 && (l2 += r2), c2.setTranslation(a2, l2);
            }
            return { addedIndex: u2 = n2, removedIndex: d2 = o2 };
          }
        };
      }
      function Et(e2) {
        var x2 = e2.draggables, E2 = e2.layout, C2 = null;
        return function(e3) {
          var t2 = e3.draggableInfo, n2 = e3.dragResult, o2 = n2.addedIndex, r2 = n2.removedIndex, i2 = n2.elementSize, a2 = n2.pos, l2 = n2.shadowBeginEnd;
          if (null === a2) return { shadowBeginEnd: C2 = null };
          if (null === o2 || !t2.invalidateShadow && o2 === C2) return null;
          var s2 = o2 - 1, c2 = Number.MIN_SAFE_INTEGER, u2 = 0, d2 = 0, f2 = null, g2 = null;
          if (s2 === r2 && s2--, -1 < s2) {
            var m2 = E2.getSize(x2[s2]);
            if (g2 = E2.getBeginEnd(x2[s2]), i2 < m2) {
              var p2 = (m2 - i2) / 2;
              c2 = g2.end - p2;
            } else c2 = g2.end;
            u2 = g2.end;
          } else g2 = { end: E2.getBeginEndOfContainer().begin }, u2 = E2.getBeginEndOfContainer().begin;
          var v2 = Number.MAX_SAFE_INTEGER, h3 = o2;
          if (h3 === r2 && h3++, h3 < x2.length) {
            var y2 = E2.getSize(x2[h3]);
            if (f2 = E2.getBeginEnd(x2[h3]), i2 < y2) {
              var b2 = (y2 - i2) / 2;
              v2 = f2.begin + b2;
            } else v2 = f2.begin;
            d2 = f2.begin;
          } else f2 = { begin: E2.getContainerRectangles().rect.end }, d2 = E2.getContainerRectangles().rect.end - E2.getContainerRectangles().rect.begin;
          var w2 = g2 && f2 ? E2.getTopLeftOfElementBegin(g2.end) : null;
          return C2 = o2, { shadowBeginEnd: { dropArea: { begin: u2, end: d2 }, begin: c2, end: v2, rect: w2, beginAdjustment: l2 ? l2.beginAdjustment : 0 } };
        };
      }
      function Ct() {
        var a2 = null;
        return function(e2) {
          var t2 = e2.dragResult, n2 = t2.pos, o2 = t2.addedIndex, r2 = t2.shadowBeginEnd;
          if (null !== n2) {
            if (null != o2 && null === a2) {
              if (n2 < r2.begin) {
                var i2 = n2 - r2.begin - 5;
                r2.beginAdjustment = i2;
              }
              a2 = o2;
            }
          } else a2 = null;
        };
      }
      function Dt(e2) {
        var t2 = e2.getOptions, n2 = false, o2 = t2();
        return function(e3) {
          var t3 = !!e3.dragResult.pos;
          t3 !== n2 && ((n2 = t3) ? o2.onDragEnter && o2.onDragEnter() : o2.onDragLeave && o2.onDragLeave());
        };
      }
      function St(e2) {
        var t2 = e2.getOptions, s2 = null, c2 = t2();
        return function(e3) {
          var t3 = e3.dragResult, n2 = t3.addedIndex, o2 = t3.removedIndex, r2 = e3.draggableInfo, i2 = r2.payload, a2 = r2.element;
          if (c2.onDropReady && null !== n2 && s2 !== n2) {
            var l2 = s2 = n2;
            null !== o2 && o2 < n2 && l2--, c2.onDropReady({ addedIndex: l2, removedIndex: o2, payload: i2, element: a2 ? a2.firstElementChild : void 0 });
          }
        };
      }
      function Ot(e2) {
        return "drop-zone" === e2.getOptions().behaviour ? Rt(e2)(ct, ut, dt, ft, gt, mt, pt, Dt, St) : Rt(e2)(ct, ut, dt, ft, gt, ht, yt, bt, wt, xt, Et, vt, Ct, Dt, St);
      }
      function Rt(i2) {
        return function() {
          for (var e2 = arguments.length, t2 = new Array(e2), n2 = 0; n2 < e2; n2++) t2[n2] = arguments[n2];
          var o2 = t2.map(function(e3) {
            return e3(i2);
          }), r2 = null;
          return function(n3) {
            return r2 = o2.reduce(function(e3, t3) {
              return Object.assign(e3, t3({ draggableInfo: n3, dragResult: e3 }));
            }, r2 || { addedIndex: null, removedIndex: null, elementSize: null, pos: null, shadowBeginEnd: null });
          };
        };
      }
      function At(d2) {
        return function(e2) {
          var n2 = Object.assign({}, C, e2), t2 = null, o2 = null, r2 = function(e3, t3) {
            var n3 = at(e3), o3 = t3();
            return M(e3, "".concat(g, " ").concat(o3.orientation)), { element: e3, draggables: n3, getOptions: t3, layout: q(e3, o3.orientation, o3.animationDuration) };
          }(d2, u2), i2 = Ot(r2), a2 = st(r2), l2 = function(t3, n3) {
            var o3 = [];
            function e3() {
              o3 && (o3.forEach(function(e4) {
                return e4.removeEventListener("scroll", n3);
              }), window.removeEventListener("scroll", n3));
            }
            return function() {
              var e4 = t3;
              for (; e4; ) (R(e4, "x") || R(e4, "y")) && o3.push(e4), e4 = e4.parentElement;
            }(), { dispose: function() {
              e3(), o3 = null;
            }, start: function() {
              o3 && (o3.forEach(function(e4) {
                return e4.addEventListener("scroll", n3);
              }), window.addEventListener("scroll", n3));
            }, stop: e3 };
          }(d2, function() {
            r2.layout.invalidateRects(), s2();
          });
          function s2() {
            null !== o2 && (o2.invalidateShadow = true, t2 = i2(o2), o2.invalidateShadow = false);
          }
          function c2(e3, t3) {
            for (var n3 = at(t3), o3 = 0; o3 < n3.length; o3++) e3[o3] = n3[o3];
            for (var r3 = 0; r3 < e3.length - n3.length; r3++) e3.pop();
          }
          function u2() {
            return n2;
          }
          return { element: d2, draggables: r2.draggables, isDragRelevant: function(e3) {
            var r3 = e3.element, i3 = e3.getOptions;
            return function(e4, t3) {
              var n3 = i3();
              if (n3.shouldAcceptDrop) return n3.shouldAcceptDrop(e4.getOptions(), t3);
              var o3 = e4.getOptions();
              return "copy" !== n3.behaviour && (N(r3, "." + f) !== e4.element && (e4.element === r3 || !(!o3.groupName || o3.groupName !== n3.groupName)));
            };
          }(r2), layout: r2.layout, dispose: function(e3) {
            l2.dispose(), function(t3) {
              It.wrapChild && Array.prototype.forEach.call(t3.children, function(e4) {
                e4.nodeType === Node.ELEMENT_NODE && L(e4, f) && (t3.insertBefore(e4.firstElementChild, e4), t3.removeChild(e4));
              });
            }(e3.element);
          }, prepareDrag: function(e3, t3) {
            var n3 = e3.element, o3 = r2.draggables;
            c2(o3, n3), e3.layout.invalidateRects(), o3.forEach(function(e4) {
              return it(e4, true, u2().animationDuration);
            }), l2.start();
          }, handleDrag: function(e3) {
            return t2 = i2(o2 = e3);
          }, handleDrop: function(e3) {
            l2.stop(), t2 && t2.dropPlaceholderContainer && d2.removeChild(t2.dropPlaceholderContainer), o2 = null, i2 = Ot(r2), a2(e3, t2), t2 = null;
          }, fireRemoveElement: function() {
            a2(o2, Object.assign({}, t2, { addedIndex: null }), true), t2 = null;
          }, getDragResult: function() {
            return t2;
          }, getTranslateCalculator: function(e3) {
            return xt(r2)(e3);
          }, onTranslated: function() {
            s2();
          }, setDraggables: function() {
            c2(r2.draggables, d2);
          }, getScrollMaxSpeed: function() {
            return It.maxScrollSpeed;
          }, shouldUseTransformForGhost: function() {
            return true === It.useTransformForGhost;
          }, getOptions: u2, setOptions: function(e3) {
            var t3 = !(1 < arguments.length && void 0 !== arguments[1]) || arguments[1];
            n2 = false === t3 ? Object.assign({}, C, e3) : Object.assign({}, C, n2, e3);
          } };
        };
      }
      var It = function(e2, t2) {
        var n2 = At(e2)(t2);
        return e2[r] = n2, rt.register(n2), { dispose: function() {
          rt.unregister(n2), n2.dispose(n2);
        }, setOptions: function(e3, t3) {
          n2.setOptions(e3, t3);
        } };
      };
      function Bt(e2, t2, n2) {
        Object.defineProperty(e2, n2, { set: function(e3) {
          t2[n2] = e3;
        }, get: function() {
          return t2[n2];
        } });
      }
      It.wrapChild = true, It.cancelDrag = function() {
        rt.cancelDrag();
      }, It.isDragging = function() {
        return rt.isDragging();
      };
      function Pt(e2, t2) {
        return console.warn('default export is deprecated. please use named export "smoothDnD"'), It(e2, t2);
      }
      Pt.cancelDrag = function() {
        It.cancelDrag();
      }, Pt.isDragging = function() {
        return It.isDragging();
      }, Bt(Pt, It, "useTransformForGhost"), Bt(Pt, It, "maxScrollSpeed"), Bt(Pt, It, "wrapChild"), Bt(Pt, It, "dropHandler"), e.smoothDnD = It, e.constants = n, e.dropHandlers = k, e.default = Pt, Object.defineProperty(e, "__esModule", { value: true });
    });
  }
});

// ../node_modules/.pnpm/vue3-smooth-dnd@0.0.6_vue@3.5.16_typescript@5.8.3_/node_modules/vue3-smooth-dnd/dist/vue3-smooth-dnd.esm.js
var vue3_smooth_dnd_esm_exports = {};
__export(vue3_smooth_dnd_esm_exports, {
  Container: () => Container,
  Draggable: () => Draggable
});
var import_smooth_dnd = __toESM(require_dist());
__reExport(vue3_smooth_dnd_esm_exports, __toESM(require_dist()));
import { defineComponent, h } from "vue";
function validateTagProp(tag) {
  if (tag) {
    if (typeof tag === "string") return true;
    if (typeof tag === "object") {
      if (typeof tag.value === "string" || typeof tag.value === "function" || typeof tag.value === "object") {
        return true;
      }
    }
    return false;
  }
  return true;
}
function getTagProps(ctx, tagClasses) {
  const tag = ctx.$props.tag;
  if (tag) {
    if (typeof tag === "string") {
      const result = { value: tag };
      if (tagClasses) {
        result.props = { class: tagClasses };
      }
      return result;
    } else if (typeof tag === "object") {
      const result = { value: tag.value || "div", props: tag.props || {} };
      if (tagClasses) {
        if (result.props.class) {
          if (Array.isArray(result.props.class)) {
            result.props.class.push(tagClasses);
          } else {
            result.props.class = [tagClasses, result.props.class];
          }
        } else {
          result.props.class = tagClasses;
        }
      }
      return result;
    }
  }
  return { value: "div" };
}
import_smooth_dnd.smoothDnD.dropHandler = import_smooth_dnd.dropHandlers.reactDropHandler().handler;
import_smooth_dnd.smoothDnD.wrapChild = false;
var eventEmitterMap = {
  "drag-start": "onDragStart",
  "drag-end": "onDragEnd",
  "drop": "onDrop",
  "drag-enter": "onDragEnter",
  "drag-leave": "onDragLeave",
  "drop-ready": "onDropReady"
};
var Container = defineComponent({
  name: "Container",
  mounted() {
    const options = Object.assign({}, this.$props);
    for (const key in eventEmitterMap) {
      options[eventEmitterMap[key]] = (props) => {
        this.$emit(key, props);
      };
    }
    this.containerElement = this.$refs.container || this.$el;
    this.container = (0, import_smooth_dnd.smoothDnD)(this.containerElement, options);
  },
  unmounted() {
    if (this.container) {
      try {
        this.container.dispose();
      } catch {
      }
    }
  },
  emits: ["drop", "drag-start", "drag-end", "drag-enter", "drag-leave", "drop-ready"],
  props: {
    orientation: { type: String, default: "vertical" },
    removeOnDropOut: { type: Boolean, default: false },
    autoScrollEnabled: { type: Boolean, default: true },
    animationDuration: { type: Number, default: 250 },
    behaviour: String,
    groupName: String,
    dragHandleSelector: String,
    nonDragAreaSelector: String,
    lockAxis: String,
    dragClass: String,
    dropClass: String,
    dragBeginDelay: Number,
    getChildPayload: Function,
    shouldAnimateDrop: Function,
    shouldAcceptDrop: Function,
    getGhostParent: Function,
    dropPlaceholder: [Object, Boolean],
    tag: {
      validator: validateTagProp,
      default: "div"
    }
  },
  render() {
    const tagProps = getTagProps(this);
    return h(
      tagProps.value,
      Object.assign({}, { ref: "container" }, tagProps.props),
      this.$slots.default()
    );
  }
});
var Draggable = defineComponent({
  name: "Draggable",
  props: {
    tag: {
      validator: validateTagProp,
      default: "div"
    }
  },
  render: function() {
    const tagProps = getTagProps(this, import_smooth_dnd.constants.wrapperClass);
    return h(
      tagProps.value,
      Object.assign({}, tagProps.props),
      this.$slots.default()
    );
  }
});
export {
  Container,
  Draggable
};
//# sourceMappingURL=vue3-smooth-dnd.js.map
