<!-- client/.app/app/pages/users/profile.vue -->

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header with Cover Image and Avatar -->
    <div class="relative mb-16 mt-8">
      <!-- Cover Image -->
      <div
        class="h-40 md:h-60 w-full rounded-lg bg-muted-200 dark:bg-muted-800 overflow-hidden relative z-0"
      >
        <img
          v-if="user?.coverImage"
          :src="user.coverImage"
          alt="Cover"
          class="w-full h-full object-cover z-50"
        />

        <div
          v-else
          class="w-full h-full flex items-center justify-center bg-gradient-to-r from-primary-500 to-primary-700"
        >
          <span class="text-white text-lg font-medium"
            >{{ user?.firstName }} {{ user?.lastName }}</span
          >
        </div>

        <div class="absolute top-1/2 transform -translate-y-1/2 left-20 z-10">
          <div class="relative">
            <BaseAvatar
              v-if="user?.avatar"
              :src="user.avatar"
              size="3xl"
              class="relative z-20 ring-2 ring-cyan-200 dark:ring-cyan-700"
              style="object-fit: cover"
              ring
            />
            <BaseAvatar
              v-else
              :initials="userInitials"
              size="3xl"
              class="relative z-20 shadow-lg ring-2 ring-cyan-200 dark:ring-cyan-700"
            />

            <div class="absolute -bottom-1 -right-1 z-30">
              <BaseButton
                variant="primary"
                color="primary"
                rounded="full"
                size="sm"
                class="h-8 w-8 !p-0"
                @click="openAvatarUpload"
              >
                <Icon name="solar:camera-linear" class="h-4 w-4" />
              </BaseButton>
              <input
                ref="avatarInput"
                type="file"
                accept="image/*"
                class="hidden"
                @change="handleAvatarUpload"
              />
            </div>
          </div>
        </div>

        <div class="absolute bottom-4 right-4">
          <BaseButton
            variant="primary"
            color="primary"
            size="sm"
            @click="openCoverImageUpload"
          >
            <Icon name="solar:camera-linear" class="h-4 w-4 mr-1" />
            {{ $t("profile.change_cover") }}
          </BaseButton>
          <input
            ref="coverImageInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleCoverImageUpload"
          />
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div class="mt-6 grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-3">
        <BaseCard rounded="md" class="p-4 md:p-6">
          <div class="mb-6 flex items-center gap-2">
            <h4
              class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
            >
              {{ $t("profile.navigation") }}
            </h4>
          </div>
          <ul class="space-y-1 font-sans text-sm">
            <li>
              <button
                @click="activeTab = 'personal'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'personal'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:user-rounded-linear" class="size-5" />
                <span>{{ $t("profile.personal_info") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'companies'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'companies'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:buildings-2-linear" class="size-5" />
                <span>{{ $t("profile.companies") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'subscriptions'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'subscriptions'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:card-linear" class="size-5" />
                <span>{{ $t("profile.subscriptions") }}</span>
              </button>
            </li>
            <li>
              <button
                @click="activeTab = 'security'"
                :class="[
                  'flex items-center gap-2 rounded-lg p-3 transition-colors duration-300 w-full text-left',
                  activeTab === 'security'
                    ? 'text-primary-500! bg-primary-500/10!'
                    : 'text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:bg-muted-50 dark:hover:bg-muted-700/50',
                ]"
              >
                <Icon name="solar:shield-check-linear" class="size-5" />
                <span>{{ $t("profile.security") }}</span>
              </button>
            </li>
          </ul>
        </BaseCard>
      </div>
      <div class="col-span-12 lg:col-span-9">
        <!-- Personal Information Tab -->
        <div v-show="activeTab === 'personal'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.general_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.general_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.about_you") }}
                </BaseHeading>
                <div
                  class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
                >
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/profile/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.full_name") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ user?.firstName }} {{ user?.lastName }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("profile.edit") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/profile/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.email_address") }}
                        </BaseHeading>
                        <div class="flex items-center gap-2">
                          <BaseText
                            size="sm"
                            class="text-muted-800 dark:text-muted-200 font-medium"
                            >{{ user?.email }}</BaseText
                          >
                          <BaseTag
                            variant="none"
                            size="sm"
                            rounded="md"
                            :class="
                              user?.emailConfirmed
                                ? 'bg-green-400/20 text-green-600 ring-1 ring-inset ring-green-400/30'
                                : 'bg-red-400/20 text-red-600 ring-1 ring-inset ring-red-400/30'
                            "
                          >
                            {{
                              user?.emailConfirmed
                                ? $t("profile.verified")
                                : $t("profile.unverified")
                            }}
                          </BaseTag>
                        </div>
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("profile.edit") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/profile/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.phone_number") }}
                        </BaseHeading>
                        <div class="flex items-center gap-2">
                          <BaseText
                            size="sm"
                            class="text-muted-800 dark:text-muted-200 font-medium"
                            >{{
                              user?.phone || $t("profile.no_phone_provided")
                            }}</BaseText
                          >
                          <BaseTag
                            v-if="user?.phone"
                            variant="none"
                            size="sm"
                            rounded="md"
                            :class="
                              user?.phoneConfirmed
                                ? 'bg-green-400/20 text-green-600 ring-1 ring-inset ring-green-400/30'
                                : 'bg-red-400/20 text-red-600 ring-1 ring-inset ring-red-400/30'
                            "
                          >
                            {{
                              user?.phoneConfirmed
                                ? $t("profile.verified")
                                : $t("profile.unverified")
                            }}
                          </BaseTag>
                        </div>
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("profile.edit") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/profile/edit"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.birth_date") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ formatDisplayDate(user?.birthdate) }}</BaseText
                        >
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("profile.edit") }}
                      </BaseText>
                    </NuxtLink>
                  </div>

                  <!-- Item -->
                  <div class="group">
                    <div
                      class="font-heading text-muted-600 dark:text-muted-400 flex items-center gap-4 p-4 text-sm"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.member_since") }}
                        </BaseHeading>
                        <BaseText
                          size="sm"
                          class="text-muted-800 dark:text-muted-200 font-medium"
                          >{{ formatDisplayDate(user?.createdAt) }}</BaseText
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.address_info") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.address_info_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <BaseHeading
                    as="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    {{ $t("profile.current_address") }}
                  </BaseHeading>
                  <div
                    v-if="hasAddress"
                    class="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <div v-if="user?.address">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.address_line_1") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.address }}
                      </BaseText>
                    </div>
                    <div v-if="user?.address2">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.address_line_2") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.address2 }}
                      </BaseText>
                    </div>
                    <div v-if="user?.street">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.street") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.street }}
                      </BaseText>
                    </div>
                    <div v-if="user?.city">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.city") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.city }}
                      </BaseText>
                    </div>
                    <div v-if="user?.postalCode">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.postal_code") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.postalCode }}
                      </BaseText>
                    </div>
                    <div v-if="user?.state">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.state_province") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.state }}
                      </BaseText>
                    </div>
                    <div v-if="user?.country">
                      <BaseHeading
                        as="h5"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-1"
                      >
                        {{ $t("profile.country") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ user.country }}
                      </BaseText>
                    </div>
                  </div>
                  <div v-else>
                    <BaseParagraph
                      size="sm"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ $t("profile.no_address_provided") }}
                    </BaseParagraph>
                  </div>
                  <div class="mt-2">
                    <NuxtLink
                      to="/users/profile/edit?tab=address"
                      class="text-primary-500 hover:text-primary-600 text-sm font-medium inline-flex items-center gap-1"
                    >
                      {{
                        hasAddress
                          ? $t("profile.edit_address")
                          : $t("profile.add_address")
                      }}
                      <Icon name="solar:arrow-right-linear" class="size-4" />
                    </NuxtLink>
                  </div>
                </div>
              </div>
            </div>

            <!-- Identification Codes Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.identification_codes") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.identification_codes_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <div class="flex items-center justify-between mb-4">
                    <BaseHeading
                      as="h3"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ $t("profile.scannable_codes") }}
                    </BaseHeading>
                    <BaseButton
                      v-if="user?.barcode || user?.qrCode"
                      variant="muted"
                      size="sm"
                      @click="printCode"
                    >
                      <Icon name="solar:printer-linear" class="size-4 mr-1" />
                      {{ $t("profile.print_codes") }}
                    </BaseButton>
                  </div>

                  <div
                    v-if="user?.barcode || user?.qrCode"
                    class="grid grid-cols-1 md:grid-cols-2 gap-6"
                  >
                    <div v-if="user?.barcode" class="flex flex-col">
                      <BaseHeading
                        as="h4"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-2"
                      >
                        {{ $t("profile.barcode") }}
                      </BaseHeading>
                      <div
                        class="flex-1 flex flex-col justify-center items-center p-6 bg-white rounded-lg border border-muted-200 dark:border-muted-700 min-h-[200px]"
                      >
                        <!-- Generate barcode using JsBarcode -->
                        <div
                          class="flex-1 flex items-center justify-center w-full overflow-hidden"
                        >
                          <canvas
                            :id="`barcode-${user.id}`"
                            class="max-w-full h-auto"
                          ></canvas>
                        </div>
                        <BaseText
                          size="xs"
                          class="text-muted-600 font-mono mt-2 break-all"
                        >
                          {{ user.barcode }}
                        </BaseText>
                      </div>
                    </div>
                    <div v-if="user?.qrCode" class="flex flex-col">
                      <BaseHeading
                        as="h4"
                        size="xs"
                        weight="medium"
                        class="text-muted-400 mb-2"
                      >
                        {{ $t("profile.qr_code") }}
                      </BaseHeading>
                      <div
                        class="flex-1 flex flex-col justify-center items-center p-6 bg-white rounded-lg border border-muted-200 dark:border-muted-700 min-h-[200px]"
                      >
                        <div class="flex-1 flex items-center justify-center">
                          <img
                            :src="user.qrCode"
                            alt="QR Code"
                            class="w-32 h-32 object-contain"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-center py-8">
                    <div
                      class="w-20 h-20 mx-auto mb-4 bg-muted-100 dark:bg-muted-800 rounded-2xl flex items-center justify-center"
                    >
                      <Icon
                        name="solar:qr-code-linear"
                        class="size-10 text-muted-400"
                      />
                    </div>
                    <BaseHeading
                      as="h4"
                      size="lg"
                      weight="medium"
                      class="text-muted-800 dark:text-white mb-2"
                    >
                      {{ $t("profile.no_identification_codes") }}
                    </BaseHeading>
                    <BaseParagraph
                      size="sm"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ $t("profile.no_codes_desc") }}
                    </BaseParagraph>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Card Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.main_card") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.main_card_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <!-- Card Display -->
                  <div class="flex flex-col space-y-6">
                    <!-- Credit Card Component - Left Aligned -->
                    <div class="w-full">
                      <DemoCreditCardReal
                        v-if="userCard"
                        :name="`${user?.firstName} ${user?.lastName}`"
                        :number="`•••• •••• •••• ${userCard.last4}`"
                        :expiry-month="
                          String(userCard.expMonth).padStart(2, '0')
                        "
                        :expiry-year="String(userCard.expYear).slice(-2)"
                        :cvc="'•••'"
                        :centered="false"
                        contrast="low"
                      />
                      <DemoCreditCardReal
                        v-else
                        :name="'•••••• ••••••'"
                        :number="'•••• •••• •••• ••••'"
                        :expiry-month="'••'"
                        :expiry-year="'••'"
                        :cvc="'•••'"
                        :centered="false"
                        contrast="low"
                      />
                    </div>

                    <!-- Card Info and Actions - Below the card -->
                    <div class="w-full space-y-4">
                      <div v-if="userCard">
                        <BaseHeading
                          as="h3"
                          size="lg"
                          weight="medium"
                          class="text-muted-800 dark:text-muted-100 mb-2"
                        >
                          {{
                            userCard.brand?.charAt(0).toUpperCase() +
                              userCard.brand?.slice(1) || $t("profile.credit")
                          }}
                          {{ $t("profile.card") }}
                        </BaseHeading>
                        <div class="space-y-2 mb-6">
                          <div class="flex items-center gap-2">
                            <BaseText
                              size="xs"
                              class="text-muted-400 uppercase tracking-wider"
                            >
                              {{ $t("profile.card_number") }}:
                            </BaseText>
                            <BaseText
                              size="sm"
                              class="text-muted-800 dark:text-muted-200 font-mono"
                            >
                              •••• •••• •••• {{ userCard.last4 }}
                            </BaseText>
                          </div>
                          <div class="flex items-center gap-2">
                            <BaseText
                              size="xs"
                              class="text-muted-400 uppercase tracking-wider"
                            >
                              {{ $t("profile.expires") }}:
                            </BaseText>
                            <BaseText
                              size="sm"
                              class="text-muted-800 dark:text-muted-200 font-mono"
                            >
                              {{
                                String(userCard.expMonth).padStart(2, "0")
                              }}/{{ String(userCard.expYear).slice(-2) }}
                            </BaseText>
                          </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-3">
                          <BaseButton
                            variant="primary"
                            color="primary"
                            size="sm"
                            @click="openCardManagement"
                          >
                            <Icon
                              name="solar:pen-2-linear"
                              class="size-4 mr-1"
                            />
                            {{ $t("profile.update_card") }}
                          </BaseButton>
                          <BaseButton
                            variant="muted"
                            color="danger"
                            size="sm"
                            @click="removeCard"
                          >
                            <Icon
                              name="solar:trash-bin-minimalistic-linear"
                              class="size-4 mr-1"
                            />
                            {{ $t("profile.remove_card") }}
                          </BaseButton>
                        </div>
                      </div>

                      <!-- No Card State -->
                      <div v-else>
                        <BaseHeading
                          as="h3"
                          size="lg"
                          weight="medium"
                          class="text-muted-800 dark:text-muted-100 mb-2"
                        >
                          {{ $t("profile.no_payment_method") }}
                        </BaseHeading>
                        <BaseParagraph
                          size="sm"
                          class="text-muted-500 dark:text-muted-400 mb-6"
                        >
                          {{ $t("profile.add_payment_method_desc") }}
                        </BaseParagraph>
                        <BaseButton
                          variant="primary"
                          color="primary"
                          size="md"
                          @click="openCardManagement"
                        >
                          <Icon name="solar:card-linear" class="size-4 mr-2" />
                          {{ $t("profile.add_main_card") }}
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Companies Tab -->
        <div v-show="activeTab === 'companies'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <div class="mb-8 flex items-center gap-2">
              <h4
                class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
              >
                {{ $t("profile.your_companies") }}
              </h4>
            </div>

            <div v-if="user?.companies && user.companies.length > 0">
              <div
                v-for="company in user.companies"
                :key="company.id"
                class="border border-muted-200 dark:border-muted-700 rounded-lg p-4 mb-4 flex items-center"
              >
                <div class="flex-shrink-0 mr-4">
                  <BaseAvatar
                    v-if="company.company.logo"
                    :src="company.company.logo"
                    size="lg"
                  />
                  <BaseAvatar
                    v-else
                    :initials="getCompanyInitials(company.company.name)"
                    size="lg"
                  />
                </div>
                <div class="flex-grow">
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    {{ company.company.name }}
                  </BaseHeading>
                  <div class="flex items-center mt-1">
                    <BaseTag
                      variant="none"
                      size="sm"
                      rounded="md"
                      :class="
                        getCompanyStatusClass(
                          company.company.status || 'active'
                        )
                      "
                    >
                      {{ company.company.status || $t("active") }}
                    </BaseTag>
                    <BaseTag
                      variant="none"
                      size="sm"
                      rounded="md"
                      class="ml-2 bg-cyan-400/20 text-cyan-600 ring-1 ring-inset ring-cyan-400/30"
                    >
                      {{ formatRole(company.role) }}
                    </BaseTag>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <NuxtLink to="/users/company">
                    <BaseButton color="primary" variant="primary" size="sm">
                      <Icon name="ph:arrow-right-duotone" class="h-4 w-4" />
                      {{ $t("view") }}
                    </BaseButton>
                  </NuxtLink>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <Icon
                name="ph:buildings-duotone"
                class="h-12 w-12 mx-auto text-muted-400 mb-4"
              />
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-2"
              >
                {{ $t("profile.no_companies_found") }}
              </BaseHeading>
              <BaseText
                size="sm"
                class="text-muted-500 dark:text-muted-400 mb-4"
              >
                {{ $t("profile.no_companies_desc") }}
              </BaseText>
              <NuxtLink to="/users/company">
                <BaseButton color="primary">
                  <Icon name="ph:plus-duotone" class="h-4 w-4 mr-1" />
                  {{ $t("profile.view_company_page") }}
                </BaseButton>
              </NuxtLink>
            </div>
          </BaseCard>
        </div>

        <!-- Subscriptions Tab -->
        <div v-show="activeTab === 'subscriptions'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Current Subscription Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.current_plan") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.current_plan_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.subscription_details") }}
                </BaseHeading>
                <div
                  class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
                >
                  <!-- Current Plan Item -->
                  <div class="group" v-if="currentSubscription">
                    <div class="p-4">
                      <div class="flex items-center justify-between mb-2">
                        <BaseHeading
                          as="h3"
                          size="sm"
                          weight="medium"
                          class="text-muted-800 dark:text-muted-100"
                        >
                          {{
                            currentSubscription?.plan?.name ||
                            $t("profile.unknown_plan")
                          }}
                        </BaseHeading>
                        <BaseTag
                          variant="none"
                          size="sm"
                          rounded="md"
                          :class="
                            getSubscriptionStatusClass(
                              currentSubscription?.status
                            )
                          "
                        >
                          {{
                            currentSubscription?.status || $t("profile.unknown")
                          }}
                        </BaseTag>
                      </div>
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <BaseText
                            size="xs"
                            class="text-muted-500 dark:text-muted-400"
                          >
                            {{ $t("price") }}
                          </BaseText>
                          <BaseText
                            size="sm"
                            class="text-muted-800 dark:text-white font-medium"
                          >
                            ${{
                              currentSubscription?.plan?.monthlyPrice || 0
                            }}
                            / {{ $t("profile.month") }}
                          </BaseText>
                        </div>
                        <div>
                          <BaseText
                            size="xs"
                            class="text-muted-500 dark:text-muted-400"
                          >
                            {{ $t("profile.next_billing") }}
                          </BaseText>
                          <BaseText
                            size="sm"
                            class="text-muted-800 dark:text-white font-medium"
                          >
                            {{
                              currentSubscription?.nextBillingDate
                                ? formatDate(
                                    currentSubscription.nextBillingDate
                                  )
                                : $t("profile.not_available")
                            }}
                          </BaseText>
                        </div>
                        <div>
                          <BaseText
                            size="xs"
                            class="text-muted-500 dark:text-muted-400"
                          >
                            {{ $t("status") }}
                          </BaseText>
                          <BaseText
                            size="sm"
                            class="text-muted-800 dark:text-white font-medium"
                          >
                            {{
                              currentSubscription?.status ||
                              $t("profile.unknown")
                            }}
                          </BaseText>
                        </div>
                      </div>
                      <div class="flex gap-2">
                        <BaseButton
                          variant="solid"
                          color="primary"
                          size="sm"
                          @click="openPlanSelection"
                        >
                          <Icon
                            name="solar:refresh-linear"
                            class="size-4 mr-1"
                          />
                          {{ $t("profile.change_plan") }}
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                  <!-- No Plan Item -->
                  <div class="group" v-else>
                    <div class="p-4 text-center">
                      <Icon
                        name="solar:card-linear"
                        class="h-12 w-12 mx-auto text-muted-400 mb-4"
                      />
                      <BaseHeading
                        as="h3"
                        size="sm"
                        weight="medium"
                        class="text-muted-800 dark:text-white mb-2"
                      >
                        {{ $t("profile.no_active_subscription") }}
                      </BaseHeading>
                      <BaseText
                        size="sm"
                        class="text-muted-500 dark:text-muted-400 mb-4"
                      >
                        {{ $t("profile.no_subscription_desc") }}
                      </BaseText>
                      <BaseButton
                        variant="solid"
                        color="primary"
                        size="lg"
                        class="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg"
                        @click="openPlanSelection"
                      >
                        <Icon name="solar:card-linear" class="size-5 mr-2" />
                        {{ $t("profile.select_plan") }}
                      </BaseButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Method Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.payment_method") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.payment_method_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <div
                  class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
                >
                  <!-- Card Display -->
                  <div v-if="userCard">
                    <BaseCard
                      class="p-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white mb-4"
                    >
                      <div class="flex justify-between items-start mb-4">
                        <div>
                          <BaseText size="xs" class="text-white/80">
                            {{ userCard.brand?.toUpperCase() }}
                          </BaseText>
                          <BaseText size="lg" class="font-mono">
                            •••• •••• •••• {{ userCard.last4 }}
                          </BaseText>
                        </div>
                        <Icon
                          name="solar:card-linear"
                          class="size-8 text-white/80"
                        />
                      </div>
                      <div class="flex justify-between items-end">
                        <div>
                          <BaseText size="xs" class="text-white/80">
                            {{ $t("profile.expires") }}
                          </BaseText>
                          <BaseText size="sm">
                            {{ userCard.expMonth }}/{{ userCard.expYear }}
                          </BaseText>
                        </div>
                        <div>
                          <BaseText size="xs" class="text-white/80">
                            {{ $t("profile.cardholder") }}
                          </BaseText>
                          <BaseText size="sm">
                            {{ user?.firstName }} {{ user?.lastName }}
                          </BaseText>
                        </div>
                      </div>
                    </BaseCard>
                    <div class="flex gap-2">
                      <BaseButton
                        variant="solid"
                        color="primary"
                        size="sm"
                        @click="openCardManagement"
                      >
                        <Icon name="solar:pen-2-linear" class="size-4 mr-1" />
                        {{ $t("profile.update_card") }}
                      </BaseButton>
                      <BaseButton
                        variant="outline"
                        color="danger"
                        size="sm"
                        @click="removeCard"
                      >
                        <Icon
                          name="solar:trash-bin-minimalistic-linear"
                          class="size-4 mr-1"
                        />
                        {{ $t("profile.remove") }}
                      </BaseButton>
                    </div>
                  </div>
                  <!-- No Card State -->
                  <div v-else class="text-center py-8">
                    <Icon
                      name="solar:card-linear"
                      class="h-12 w-12 mx-auto text-muted-400 mb-4"
                    />
                    <BaseHeading
                      as="h3"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-white mb-2"
                    >
                      {{ $t("profile.no_payment_method") }}
                    </BaseHeading>
                    <BaseText
                      size="sm"
                      class="text-muted-500 dark:text-muted-400 mb-4"
                    >
                      {{ $t("profile.no_payment_method_connected_desc") }}
                    </BaseText>
                    <BaseButton
                      variant="solid"
                      color="primary"
                      @click="openCardManagement"
                    >
                      <Icon name="solar:card-linear" class="size-4 mr-1" />
                      {{ $t("profile.add_payment_method") }}
                    </BaseButton>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Security Tab -->
        <div v-show="activeTab === 'security'" class="flex flex-col gap-4">
          <BaseCard rounded="md" class="p-4 md:p-8">
            <!-- Section -->
            <div class="grid gap-8 pt-6 md:grid-cols-12">
              <!-- Column -->
              <div class="md:col-span-5">
                <div class="w-full max-w-xs">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    class="text-muted-800 dark:text-muted-100 mb-1"
                  >
                    {{ $t("profile.security_settings") }}
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ $t("profile.security_settings_desc") }}
                  </BaseParagraph>
                </div>
              </div>
              <!-- Column -->
              <div class="md:col-span-7">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
                >
                  {{ $t("profile.account_security") }}
                </BaseHeading>
                <div
                  class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
                >
                  <!-- Password Item -->
                  <div class="group">
                    <NuxtLink
                      to="/users/profile/edit?tab=security"
                      class="font-heading text-muted-600 dark:text-muted-400 hover:bg-muted-200/60 dark:hover:bg-muted-800 flex items-center gap-4 p-4 text-sm transition-colors duration-300"
                    >
                      <div>
                        <BaseHeading
                          as="h3"
                          size="xs"
                          weight="medium"
                          class="text-muted-400"
                        >
                          {{ $t("profile.password") }}
                        </BaseHeading>
                        <BaseText size="sm">{{
                          $t("profile.password_last_updated")
                        }}</BaseText>
                      </div>
                      <Icon name="solar:pen-2-linear" class="ms-auto size-4" />
                      <BaseText
                        size="xs"
                        weight="semibold"
                        class="text-primary-500 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      >
                        {{ $t("edit") }}
                      </BaseText>
                    </NuxtLink>
                  </div>
                  <!-- Two-Factor Item -->
                  <div class="group">
                    <div class="p-4">
                      <div class="flex items-center justify-between">
                        <div>
                          <BaseHeading
                            as="h3"
                            size="xs"
                            weight="medium"
                            class="text-muted-400"
                          >
                            {{ $t("profile.two_factor_auth") }}
                          </BaseHeading>
                          <BaseText size="sm">
                            {{
                              user?.twoFactorEnabled
                                ? $t("profile.enabled")
                                : $t("profile.disabled")
                            }}
                          </BaseText>
                        </div>
                        <BaseButton
                          :variant="
                            user?.twoFactorEnabled ? 'outline' : 'solid'
                          "
                          :color="user?.twoFactorEnabled ? 'danger' : 'primary'"
                          size="sm"
                          @click="toggleTwoFactor"
                          :loading="isTogglingTwoFactor"
                        >
                          {{
                            user?.twoFactorEnabled
                              ? $t("profile.disable")
                              : $t("profile.enable")
                          }}
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watchEffect,
  watch,
  nextTick,
} from "vue";
import { useUserStore } from "../../../../stores/useUserStore";

// Add JsBarcode library
useHead({
  script: [
    {
      src: "https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js",
      defer: true,
    },
  ],
});

// Declare global JsBarcode
declare global {
  interface Window {
    JsBarcode: any;
  }
}

// Initialize user store and composables
const userStore = useUserStore();
const toaster = useNuiToasts();
const api = useApi();
const { t } = useI18n();

// State
const activeTab = ref("personal");
const isUpdatingProfile = ref(false);
const isUpdatingAddress = ref(false);
const isChangingPassword = ref(false);
const avatarInput = ref<HTMLInputElement | null>(null);
const coverImageInput = ref<HTMLInputElement | null>(null);

// Subscription and card data
const currentSubscription = ref(null);
const userCard = ref(null);

// Computed properties
const user = computed(() => userStore.user);
const userInitials = computed(() => userStore.initials);
const isTogglingTwoFactor = ref(false);

const hasAddress = computed(() => {
  return !!(
    user.value?.address ||
    user.value?.address2 ||
    user.value?.street ||
    user.value?.city ||
    user.value?.postalCode ||
    user.value?.state ||
    user.value?.country
  );
});

// Helper function to format date
const formatDisplayDate = (dateString: string): string => {
  if (!dateString) return t("profile.not_provided");
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Helper function to print barcode/QR code
const printCode = () => {
  const printWindow = window.open("", "_blank");
  if (!printWindow) return;

  const content = `
    <html>
      <head>
        <title>User Codes - ${user.value?.firstName} ${
    user.value?.lastName
  }</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .code-section { margin: 20px 0; text-align: center; }
          .code-section h3 { margin-bottom: 10px; }
          .code-section img { max-width: 200px; height: auto; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <h1>User Identification Codes</h1>
        <p><strong>Name:</strong> ${user.value?.firstName} ${
    user.value?.lastName
  }</p>
        <p><strong>Email:</strong> ${user.value?.email}</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>

        ${
          user.value?.barcode
            ? `
          <div class="code-section">
            <h3>Barcode</h3>
            <p>Code: ${user.value.barcode}</p>
          </div>
        `
            : ""
        }

        ${
          user.value?.qrCode
            ? `
          <div class="code-section">
            <h3>QR Code</h3>
            <img src="${user.value.qrCode}" alt="QR Code" />
          </div>
        `
            : ""
        }
      </body>
    </html>
  `;

  printWindow.document.write(content);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
};

// Generate barcode when user data is available
const generateBarcode = () => {
  if (user.value?.barcode && user.value?.id) {
    nextTick(() => {
      const canvas = document.getElementById(`barcode-${user.value.id}`);
      if (canvas && window.JsBarcode) {
        try {
          // Get container width for responsive sizing
          const container = canvas.parentElement;
          const containerWidth = container ? container.clientWidth - 48 : 200; // 48px for padding
          const barcodeWidth = Math.min(containerWidth, 250);

          window.JsBarcode(canvas, user.value.barcode, {
            format: "CODE128",
            width: Math.max(1, Math.floor(barcodeWidth / 100)), // Responsive width
            height: 50,
            displayValue: false,
            background: "transparent",
            lineColor: "#000000",
            margin: 0,
          });
        } catch (error) {
          console.error("Error generating barcode:", error);
        }
      }
    });
  }
};

// Watch for user changes to regenerate barcode
watch(
  user,
  () => {
    if (user.value?.barcode) {
      generateBarcode();
    }
  },
  { immediate: true }
);

// Methods

const openAvatarUpload = () => {
  if (avatarInput.value) {
    avatarInput.value.click();
  }
};

const handleAvatarUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    await userStore.uploadAvatar(file);
  }
};

const openCoverImageUpload = () => {
  if (coverImageInput.value) {
    coverImageInput.value.click();
  }
};

const handleCoverImageUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    try {
      await userStore.uploadCoverImage(file);
      // Toast is handled in the store
    } catch (error) {
      console.error("Error uploading cover image:", error);
      // Error toast is handled in the store
    }
  }
};

const toggleTwoFactor = async () => {
  try {
    isTogglingTwoFactor.value = true;
    // TODO: Implement two-factor authentication toggle
    toaster.add({
      title: t("common.info"),
      description: t("profile.two_factor_coming_soon"),
      icon: "solar:info-circle-linear",
      progress: true,
    });
  } catch (error) {
    console.error("Error toggling two-factor authentication:", error);
    toaster.add({
      title: t("common.error"),
      description: t("profile.two_factor_toggle_failed"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isTogglingTwoFactor.value = false;
  }
};

const getSubscriptionStatusColor = (status: string): string => {
  if (!status) return "muted";
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "success";
    case "TRIAL":
      return "info";
    case "EXPIRED":
      return "danger";
    case "CANCELLED":
      return "warning";
    default:
      return "muted";
  }
};

const getSubscriptionStatusClass = (status: string): string => {
  if (!status)
    return "bg-cyan-400/20 text-cyan-600 ring-1 ring-inset ring-cyan-400/30";
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "bg-green-400/20 text-green-600 ring-1 ring-inset ring-green-400/30";
    case "TRIAL":
      return "bg-green-400/20 text-green-600 ring-1 ring-inset ring-green-400/30";
    case "EXPIRED":
      return "bg-red-400/20 text-red-600 ring-1 ring-inset ring-red-400/30";
    case "CANCELLED":
      return "bg-red-400/20 text-red-600 ring-1 ring-inset ring-red-400/30";
    case "INACTIVE":
      return "bg-gray-400/20 text-gray-600 ring-1 ring-inset ring-gray-400/30";
    default:
      return "bg-cyan-400/20 text-cyan-600 ring-1 ring-inset ring-cyan-400/30";
  }
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price);
};

// Debug user data
watchEffect(() => {
  if (user.value) {
    console.log("🔍 Current user data:", user.value);
    console.log("🏠 Address fields:", {
      address: user.value.address,
      address2: user.value.address2,
      street: user.value.street,
      city: user.value.city,
      postalCode: user.value.postalCode,
      state: user.value.state,
      country: user.value.country,
    });
    console.log("📅 Birthdate:", user.value.birthdate);
    console.log("📱 Codes:", {
      barcode: user.value.barcode,
      qrCode: user.value.qrCode,
    });
  }
});

// Subscription and card management functions
const fetchSubscriptionData = async () => {
  try {
    const response = await api.get("/subscriptions/current");
    currentSubscription.value = response;
  } catch (error) {
    console.error("Error fetching subscription:", error);
  }
};

const fetchCardData = async () => {
  try {
    const response = await api.get("/payment/cards");
    if (response && Array.isArray(response) && response.length > 0) {
      userCard.value = response[0]; // Get the first/default card
    }
  } catch (error) {
    // Don't log 404 errors as they're expected when no cards exist
    if (error?.code !== "404") {
      console.error("Error fetching card data:", error);
    }
  }
};

const openPlanSelection = () => {
  // Navigate to plan selection page
  navigateTo("/subscriptions/plans");
};

const openCardManagement = () => {
  // Navigate to card management page
  navigateTo("/users/profile/edit?tab=payment");
};

const removeCard = async () => {
  if (!userCard.value) return;

  try {
    await api.delete(`/payment/cards/${userCard.value.id}`);
    userCard.value = null;
    toaster.add({
      title: t("common.success"),
      description: t("profile.payment_method_removed"),
      icon: "solar:check-circle-linear",
      progress: true,
    });
  } catch (error) {
    console.error("Error removing card:", error);
    toaster.add({
      title: t("common.error"),
      description: t("profile.payment_method_remove_failed"),
      icon: "solar:close-circle-linear",
      progress: true,
    });
  }
};

// Add missing function for verification
const verifyPhone = async () => {
  // TODO: Implement phone verification logic
  toaster.add({
    title: t("common.info"),
    description: t("profile.phone_verification_coming_soon"),
    icon: "solar:info-circle-linear",
    progress: true,
  });
};

// Helper function for company initials
const getCompanyInitials = (name: string): string => {
  if (!name) return "CO";
  const parts = name.split(" ");
  if (parts.length > 1) {
    return `${parts[0].charAt(0)}${parts[1].charAt(0)}`.toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

// Helper function for status colors
const getStatusColor = (status: string): string => {
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "success";
    case "PENDING":
      return "warning";
    case "SUSPENDED":
      return "danger";
    case "INACTIVE":
      return "muted";
    default:
      return "info";
  }
};

// Helper function for company status classes with standardized colors
const getCompanyStatusClass = (status: string): string => {
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "bg-green-400/20 text-green-600 ring-1 ring-inset ring-green-400/30";
    case "PENDING":
      return "bg-cyan-400/20 text-cyan-600 ring-1 ring-inset ring-cyan-400/30";
    case "SUSPENDED":
      return "bg-red-400/20 text-red-600 ring-1 ring-inset ring-red-400/30";
    case "INACTIVE":
      return "bg-gray-400/20 text-gray-600 ring-1 ring-inset ring-gray-400/30";
    default:
      return "bg-cyan-400/20 text-cyan-600 ring-1 ring-inset ring-cyan-400/30";
  }
};

// Helper function for role formatting
const formatRole = (role: string): string => {
  switch (role) {
    case "ADMIN":
      return t("roles.administrator");
    case "PROJECTLEADER":
      return t("roles.project_leader");
    case "SALESMAN":
      return t("roles.salesman");
    case "WORKER":
      return t("roles.specialist");
    case "CLIENT":
      return t("roles.client");
    default:
      return role;
  }
};

// Helper function already defined above

// Card management and plan selection functions already defined above

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.user) {
    await userStore.fetchUser();
  }

  // Fetch subscription and card data
  await fetchSubscriptionData();
  await fetchCardData();

  // Add resize listener for responsive barcode
  window.addEventListener("resize", generateBarcode);
});

// Cleanup on unmount
onUnmounted(() => {
  window.removeEventListener("resize", generateBarcode);
});
</script>
