import express from "express";
import { auth, wrapController } from "../utils/route-helpers.js";
import {
  createPaymentIntent,
  confirmPayment,
  createSubscription,
  getUserCards as getStripeUserCards,
} from "../controllers/paymentController.js";

// Import new card management controllers
import {
  getUserCards,
  getCard,
  createCard,
  setPrimaryCard,
  updateCard,
  deleteCard,
} from "../controllers/payment/cards.controller.js";

// Import transaction controllers
import {
  getUserTransactions,
  getTransaction,
  createTransaction,
  updateTransactionStatus,
  getTransactionStats,
} from "../controllers/payment/transactions.controller.js";

const router = express.Router();

// Public routes
router.post("/create-intent", wrapController(createPaymentIntent));
router.post("/confirm", wrapController(confirmPayment));

// Protected routes - Legacy Stripe integration
router.get("/stripe/cards", auth, wrapController(getStripeUserCards));
router.post("/create-subscription", auth, wrapController(createSubscription));

// Protected routes - New Card Management System
router.get("/cards", auth, wrapController(getUserCards));
router.get("/cards/:cardId", auth, wrapController(getCard));
router.post("/cards", auth, wrapController(createCard));
router.put("/cards/:cardId/set-primary", auth, wrapController(setPrimaryCard));
router.put("/cards/:cardId", auth, wrapController(updateCard));
router.delete("/cards/:cardId", auth, wrapController(deleteCard));

// Protected routes - Transaction Management
router.get("/transactions", auth, wrapController(getUserTransactions));
router.get(
  "/transactions/:transactionId",
  auth,
  wrapController(getTransaction)
);
router.post("/transactions", auth, wrapController(createTransaction));
router.put(
  "/transactions/:transactionId/status",
  auth,
  wrapController(updateTransactionStatus)
);
router.get("/transactions/stats", auth, wrapController(getTransactionStats));

export default router;
