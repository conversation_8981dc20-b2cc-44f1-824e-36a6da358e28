export const AccountMenu = () => import('./../../app/components/AccountMenu.vue')
export const AddonApexcharts = () => import('./../../app/components/AddonApexcharts.vue')
export const AddonInputPassword = () => import('./../../app/components/AddonInputPassword.vue')
export const AiAssistantButton = () => import('./../../app/components/AiAssistantButton.vue')
export const BaseLayerPage = () => import('./../../app/components/BaseLayerPage.vue')
export const BaseLoader = () => import('./../../app/components/BaseLoader.vue')
export const CalendarEvent = () => import('./../../app/components/CalendarEvent.vue')
export const CalendarEventPending = () => import('./../../app/components/CalendarEventPending.vue')
export const CalendarSidebarCategories = () => import('./../../app/components/CalendarSidebarCategories.vue')
export const DemoCreditCardReal = () => import('./../../app/components/DemoCreditCardReal.vue')
export const DemoPanelActivity = () => import('./../../app/components/DemoPanelActivity.vue')
export const DemoPanelInvest = () => import('./../../app/components/DemoPanelInvest.vue')
export const DemoPanelLanguage = () => import('./../../app/components/DemoPanelLanguage.vue')
export const LanguageSelector = () => import('./../../app/components/LanguageSelector.vue')
export const LanguageToggle = () => import('./../../app/components/LanguageToggle.vue')
export const Logo = () => import('./../../app/components/Logo.vue')
export const LogoText = () => import('./../../app/components/LogoText.vue')
export const MapMarker = () => import('./../../app/components/MapMarker.vue')
export const PasswordInputWithEye = () => import('./../../app/components/PasswordInputWithEye.vue')
export const PhoneInput = () => import('./../../app/components/PhoneInput.vue')
export const PwaInstallButton = () => import('./../../app/components/PwaInstallButton.vue')
export const PwaInstallModal = () => import('./../../app/components/PwaInstallModal.vue')
export const PwaThemeColor = () => import('./../../app/components/PwaThemeColor.vue')
export const Sidebar = () => import('./../../app/components/Sidebar.vue')
export const SidebarBackdrop = () => import('./../../app/components/SidebarBackdrop.vue')
export const SidebarContent = () => import('./../../app/components/SidebarContent.vue')
export const SidebarLayout = () => import('./../../app/components/SidebarLayout.vue')
export const SidebarLink = () => import('./../../app/components/SidebarLink.vue')
export const SidebarLinks = () => import('./../../app/components/SidebarLinks.vue')
export const SidebarNav = () => import('./../../app/components/SidebarNav.vue')
export const SidebarSubsidebar = () => import('./../../app/components/SidebarSubsidebar.vue')
export const SidebarSubsidebarCollapsible = () => import('./../../app/components/SidebarSubsidebarCollapsible.vue')
export const SidebarSubsidebarCollapsibleLink = () => import('./../../app/components/SidebarSubsidebarCollapsibleLink.vue')
export const SidebarSubsidebarCollapsibleTrigger = () => import('./../../app/components/SidebarSubsidebarCollapsibleTrigger.vue')
export const SidebarSubsidebarContent = () => import('./../../app/components/SidebarSubsidebarContent.vue')
export const SidebarSubsidebarHeader = () => import('./../../app/components/SidebarSubsidebarHeader.vue')
export const SidebarSubsidebarLink = () => import('./../../app/components/SidebarSubsidebarLink.vue')
export const SidebarTrigger = () => import('./../../app/components/SidebarTrigger.vue')
export const SubsidebarChat = () => import('./../../app/components/SubsidebarChat.vue')
export const SubsidebarMessaging = () => import('./../../app/components/SubsidebarMessaging.vue')
export const ThemeColorUpdater = () => import('./../../app/components/ThemeColorUpdater.vue')
export const Toolbar = () => import('./../../app/components/Toolbar.vue')
export const AuthEmailVerification = () => import('./../../app/components/auth/EmailVerification.vue')
export const AuthPasswordInput = () => import('./../../app/components/auth/PasswordInput.vue')
export const AuthPaymentForm = () => import('./../../app/components/auth/PaymentForm.vue')
export const AuthRegistrationForm = () => import('./../../app/components/auth/RegistrationForm.vue')
export const AuthRegistrationSuccess = () => import('./../../app/components/auth/RegistrationSuccess.vue')
export const AuthRoleSelection = () => import('./../../app/components/auth/RoleSelection.vue')
export const AuthStripePaymentForm = () => import('./../../app/components/auth/StripePaymentForm.vue')
export const AuthSubscriptionPlanSelection = () => import('./../../app/components/auth/SubscriptionPlanSelection.vue')
export const CompanyDetailsTab = () => import('./../../app/components/company/CompanyDetailsTab.vue')
export const CompanyDocumentsTab = () => import('./../../app/components/company/CompanyDocumentsTab.vue')
export const CompanyHeader = () => import('./../../app/components/company/CompanyHeader.vue')
export const CompanySettingsTab = () => import('./../../app/components/company/CompanySettingsTab.vue')
export const CompanyTeamTab = () => import('./../../app/components/company/CompanyTeamTab.vue')
export const CompanyDocumentsDocumentList = () => import('./../../app/components/company/documents/DocumentList.vue')
export const CompanyDocumentsDocumentUploader = () => import('./../../app/components/company/documents/DocumentUploader.vue')
export const CompanyTeamConfirmRemoveModal = () => import('./../../app/components/company/team/ConfirmRemoveModal.vue')
export const CompanyTeamEditRoleModal = () => import('./../../app/components/company/team/EditRoleModal.vue')
export const CompanyTeamInvitationRow = () => import('./../../app/components/company/team/InvitationRow.vue')
export const CompanyTeamInvitationsList = () => import('./../../app/components/company/team/InvitationsList.vue')
export const CompanyTeamInviteModal = () => import('./../../app/components/company/team/InviteModal.vue')
export const CompanyTeamMemberRow = () => import('./../../app/components/company/team/TeamMemberRow.vue')
export const CompanyTeamMembersList = () => import('./../../app/components/company/team/TeamMembersList.vue')
export const PaymentBillingAddressForm = () => import('./../../app/components/payment/BillingAddressForm.vue')
export const PaymentMethodForm = () => import('./../../app/components/payment/PaymentMethodForm.vue')
export const PaymentStripeCardInput = () => import('./../../app/components/payment/StripeCardInput.vue')
export const TairoCheckAnimated = () => import('./../../../layers/tairo/components/TairoCheckAnimated.vue')
export const TairoCheckboxAnimated = () => import('./../../../layers/tairo/components/TairoCheckboxAnimated.vue')
export const TairoCheckboxCardIcon = () => import('./../../../layers/tairo/components/TairoCheckboxCardIcon.vue')
export const TairoContentWrapper = () => import('./../../../layers/tairo/components/TairoContentWrapper.vue')
export const TairoContentWrapperTabbed = () => import('./../../../layers/tairo/components/TairoContentWrapperTabbed.vue')
export const TairoError = () => import('./../../../layers/tairo/components/TairoError.vue')
export const TairoFlexTable = () => import('./../../../layers/tairo/components/TairoFlexTable.vue')
export const TairoFlexTableCell = () => import('./../../../layers/tairo/components/TairoFlexTableCell.vue')
export const TairoFlexTableHeading = () => import('./../../../layers/tairo/components/TairoFlexTableHeading.vue')
export const TairoFlexTableRow = () => import('./../../../layers/tairo/components/TairoFlexTableRow.vue')
export const TairoFormGroup = () => import('./../../../layers/tairo/components/TairoFormGroup.vue')
export const TairoFormSave = () => import('./../../../layers/tairo/components/TairoFormSave.vue')
export const TairoFullscreenDropfile = () => import('./../../../layers/tairo/components/TairoFullscreenDropfile.vue')
export const TairoImageZoom = () => import('./../../../layers/tairo/components/TairoImageZoom.vue')
export const TairoInput = () => import('./../../../layers/tairo/components/TairoInput.vue')
export const TairoInputFileHeadless = () => import('./../../../layers/tairo/components/TairoInputFileHeadless.vue')
export const TairoMobileDrawer = () => import('./../../../layers/tairo/components/TairoMobileDrawer.vue')
export const TairoPanels = () => import('./../../../layers/tairo/components/TairoPanels.vue')
export const TairoRadioCard = () => import('./../../../layers/tairo/components/TairoRadioCard.vue')
export const TairoSelect = () => import('./../../../layers/tairo/components/TairoSelect.vue')
export const TairoSelectItem = () => import('./../../../layers/tairo/components/TairoSelectItem.vue')
export const TairoTable = () => import('./../../../layers/tairo/components/TairoTable.vue')
export const TairoTableCell = () => import('./../../../layers/tairo/components/TairoTableCell.vue')
export const TairoTableHeading = () => import('./../../../layers/tairo/components/TairoTableHeading.vue')
export const TairoTableRow = () => import('./../../../layers/tairo/components/TairoTableRow.vue')
export const TairoWelcome = () => import('./../../../layers/tairo/components/TairoWelcome.vue')
export const TairoCollapseBackdrop = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseBackdrop.vue')
export const TairoCollapseCollapsible = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsible.vue')
export const TairoCollapseCollapsibleLink = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleLink.vue')
export const TairoCollapseCollapsibleTrigger = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseCollapsibleTrigger.vue')
export const TairoCollapseContent = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseContent.vue')
export const TairoCollapseLayout = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseLayout.vue')
export const TairoCollapseSidebar = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseSidebar.vue')
export const TairoCollapseSidebarClose = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarClose.vue')
export const TairoCollapseSidebarHeader = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarHeader.vue')
export const TairoCollapseSidebarLink = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLink.vue')
export const TairoCollapseSidebarLinks = () => import('./../../../layers/tairo/components/tairo-collapse/TairoCollapseSidebarLinks.vue')
export const TairoMenu = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenu.vue')
export const TairoMenuContent = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuContent.vue')
export const TairoMenuIndicator = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuIndicator.vue')
export const TairoMenuItem = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuItem.vue')
export const TairoMenuLink = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuLink.vue')
export const TairoMenuLinkTab = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuLinkTab.vue')
export const TairoMenuList = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuList.vue')
export const TairoMenuListItems = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuListItems.vue')
export const TairoMenuTrigger = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuTrigger.vue')
export const TairoMenuViewport = () => import('./../../../layers/tairo/components/tairo-menu/TairoMenuViewport.vue')
export const TairoSidebar = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebar.vue')
export const TairoSidebarBackdrop = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarBackdrop.vue')
export const TairoSidebarContent = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarContent.vue')
export const TairoSidebarLayout = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarLayout.vue')
export const TairoSidebarLink = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarLink.vue')
export const TairoSidebarLinks = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarLinks.vue')
export const TairoSidebarNav = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarNav.vue')
export const TairoSidebarSubsidebar = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebar.vue')
export const TairoSidebarSubsidebarCollapsible = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsible.vue')
export const TairoSidebarSubsidebarCollapsibleLink = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleLink.vue')
export const TairoSidebarSubsidebarCollapsibleTrigger = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarCollapsibleTrigger.vue')
export const TairoSidebarSubsidebarContent = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarContent.vue')
export const TairoSidebarSubsidebarHeader = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarHeader.vue')
export const TairoSidebarSubsidebarLink = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarSubsidebarLink.vue')
export const TairoSidebarTrigger = () => import('./../../../layers/tairo/components/tairo-sidebar/TairoSidebarTrigger.vue')
export const TairoSidenavBackdrop = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavBackdrop.vue')
export const TairoSidenavCollapsible = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsible.vue')
export const TairoSidenavCollapsibleLink = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleLink.vue')
export const TairoSidenavCollapsibleTrigger = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavCollapsibleTrigger.vue')
export const TairoSidenavContent = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavContent.vue')
export const TairoSidenavLayout = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavLayout.vue')
export const TairoSidenavSidebar = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebar.vue')
export const TairoSidenavSidebarDivider = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarDivider.vue')
export const TairoSidenavSidebarHeader = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarHeader.vue')
export const TairoSidenavSidebarLink = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLink.vue')
export const TairoSidenavSidebarLinks = () => import('./../../../layers/tairo/components/tairo-sidenav/TairoSidenavSidebarLinks.vue')
export const TairoTopnavContent = () => import('./../../../layers/tairo/components/tairo-topnav/TairoTopnavContent.vue')
export const TairoTopnavHeader = () => import('./../../../layers/tairo/components/tairo-topnav/TairoTopnavHeader.vue')
export const TairoTopnavLayout = () => import('./../../../layers/tairo/components/tairo-topnav/TairoTopnavLayout.vue')
export const TairoTopnavNavbar = () => import('./../../../layers/tairo/components/tairo-topnav/TairoTopnavNavbar.vue')
export const BusinessRuleModal = () => import('./../../../layers/core/components/BusinessRuleModal.vue')
export const CoreDashboard = () => import('./../../../layers/core/components/CoreDashboard.vue')
export const ActivityFilters = () => import('./../../../layers/core/components/activity/ActivityFilters.vue')
export const ActivityLogDetails = () => import('./../../../layers/core/components/activity/ActivityLogDetails.vue')
export const ActivityLogItem = () => import('./../../../layers/core/components/activity/ActivityLogItem.vue')
export const ActivityLogList = () => import('./../../../layers/core/components/activity/ActivityLogList.vue')
export const AiDecisionCard = () => import('./../../../layers/core/components/ai/DecisionCard.vue')
export const AiDecisionDetailModal = () => import('./../../../layers/core/components/ai/DecisionDetailModal.vue')
export const ApiEndpointCard = () => import('./../../../layers/core/components/api/ApiEndpointCard.vue')
export const ApiEndpointDetailModal = () => import('./../../../layers/core/components/api/ApiEndpointDetailModal.vue')
export const AutomationRuleCard = () => import('./../../../layers/core/components/automation/RuleCard.vue')
export const BrandAssetCard = () => import('./../../../layers/core/components/brand/BrandAssetCard.vue')
export const ChartsBarChart = () => import('./../../../layers/core/components/charts/BarChart.vue')
export const ChartsLineChart = () => import('./../../../layers/core/components/charts/LineChart.vue')
export const ChartsPieChart = () => import('./../../../layers/core/components/charts/PieChart.vue')
export const ChartsRadialChart = () => import('./../../../layers/core/components/charts/RadialChart.vue')
export const DashboardChartDashboardCalendar = () => import('./../../../layers/core/components/dashboard-chart/DashboardCalendar.vue')
export const DashboardChartBudget = () => import('./../../../layers/core/components/dashboard-chart/DashboardChartBudget.vue')
export const DashboardChartGoal = () => import('./../../../layers/core/components/dashboard-chart/DashboardChartGoal.vue')
export const DashboardChartProduction = () => import('./../../../layers/core/components/dashboard-chart/DashboardChartProduction.vue')
export const DashboardChartRevenue = () => import('./../../../layers/core/components/dashboard-chart/DashboardChartRevenue.vue')
export const DashboardChartSales = () => import('./../../../layers/core/components/dashboard-chart/DashboardChartSales.vue')
export const DashboardChartDashboardCheckbox = () => import('./../../../layers/core/components/dashboard-chart/DashboardCheckbox.vue')
export const DashboardActivityCard = () => import('./../../../layers/core/components/dashboard/ActivityCard.vue')
export const DashboardBudgetAllocation = () => import('./../../../layers/core/components/dashboard/BudgetAllocation.vue')
export const DashboardEmployeeActivity = () => import('./../../../layers/core/components/dashboard/EmployeeActivity.vue')
export const DashboardMetricCard = () => import('./../../../layers/core/components/dashboard/MetricCard.vue')
export const DashboardModuleOverview = () => import('./../../../layers/core/components/dashboard/ModuleOverview.vue')
export const DashboardModuleUsageCard = () => import('./../../../layers/core/components/dashboard/ModuleUsageCard.vue')
export const DashboardNotificationsCard = () => import('./../../../layers/core/components/dashboard/NotificationsCard.vue')
export const DashboardProjectStatus = () => import('./../../../layers/core/components/dashboard/ProjectStatus.vue')
export const DashboardRecentActivities = () => import('./../../../layers/core/components/dashboard/RecentActivities.vue')
export const DashboardSalesPerformance = () => import('./../../../layers/core/components/dashboard/SalesPerformance.vue')
export const DashboardStatsOverview = () => import('./../../../layers/core/components/dashboard/StatsOverview.vue')
export const DashboardSystemHealth = () => import('./../../../layers/core/components/dashboard/SystemHealth.vue')
export const DashboardSystemHealthCard = () => import('./../../../layers/core/components/dashboard/SystemHealthCard.vue')
export const DashboardUpcomingEvents = () => import('./../../../layers/core/components/dashboard/UpcomingEvents.vue')
export const DashboardUserActivityCard = () => import('./../../../layers/core/components/dashboard/UserActivityCard.vue')
export const DatabaseConnectionBuilderForm = () => import('./../../../layers/core/components/database/ConnectionBuilderForm.vue')
export const DatabaseConnectionPoolMonitor = () => import('./../../../layers/core/components/database/ConnectionPoolMonitor.vue')
export const DatabaseConnectionStringForm = () => import('./../../../layers/core/components/database/ConnectionStringForm.vue')
export const DatabaseExplorer = () => import('./../../../layers/core/components/database/DatabaseExplorer.vue')
export const DatabaseHealth = () => import('./../../../layers/core/components/database/DatabaseHealth.vue')
export const DatabaseSchema = () => import('./../../../layers/core/components/database/DatabaseSchema.vue')
export const DatabaseTerminalWindow = () => import('./../../../layers/core/components/database/TerminalWindow.vue')
export const DocumentsDocumentCard = () => import('./../../../layers/core/components/documents/DocumentCard.vue')
export const DocumentsDocumentFilters = () => import('./../../../layers/core/components/documents/DocumentFilters.vue')
export const DocumentsDocumentUploadModal = () => import('./../../../layers/core/components/documents/DocumentUploadModal.vue')
export const EventsEventCard = () => import('./../../../layers/core/components/events/EventCard.vue')
export const EventsEventDetailModal = () => import('./../../../layers/core/components/events/EventDetailModal.vue')
export const ExamplesComponentAccessExample = () => import('./../../../layers/core/components/examples/ComponentAccessExample.vue')
export const HealthPerformanceMetricsCard = () => import('./../../../layers/core/components/health/PerformanceMetricsCard.vue')
export const HealthResourceUsageCard = () => import('./../../../layers/core/components/health/ResourceUsageCard.vue')
export const HealthServiceStatusCard = () => import('./../../../layers/core/components/health/ServiceStatusCard.vue')
export const LegalDocumentCard = () => import('./../../../layers/core/components/legal/LegalDocumentCard.vue')
export const MlDatasetCard = () => import('./../../../layers/core/components/ml/DatasetCard.vue')
export const MlTrainingDataUploader = () => import('./../../../layers/core/components/ml/TrainingDataUploader.vue')
export const MlTrainingFormatGuide = () => import('./../../../layers/core/components/ml/TrainingFormatGuide.vue')
export const ModulesModuleCard = () => import('./../../../layers/core/components/modules/ModuleCard.vue')
export const ModulesModuleDetailModal = () => import('./../../../layers/core/components/modules/ModuleDetailModal.vue')
export const MonitoringAlertDetailsModal = () => import('./../../../layers/core/components/monitoring/AlertDetailsModal.vue')
export const MonitoringAlertsListCard = () => import('./../../../layers/core/components/monitoring/AlertsListCard.vue')
export const MonitoringAlertsSummaryCard = () => import('./../../../layers/core/components/monitoring/AlertsSummaryCard.vue')
export const MonitoringApiPerformanceCard = () => import('./../../../layers/core/components/monitoring/ApiPerformanceCard.vue')
export const MonitoringDatabaseResourcesCard = () => import('./../../../layers/core/components/monitoring/DatabaseResourcesCard.vue')
export const MonitoringErrorDetailsModal = () => import('./../../../layers/core/components/monitoring/ErrorDetailsModal.vue')
export const MonitoringErrorLogsCard = () => import('./../../../layers/core/components/monitoring/ErrorLogsCard.vue')
export const MonitoringErrorSummaryCard = () => import('./../../../layers/core/components/monitoring/ErrorSummaryCard.vue')
export const MonitoringPageLoadTimesCard = () => import('./../../../layers/core/components/monitoring/PageLoadTimesCard.vue')
export const MonitoringPerformanceMetricsCard = () => import('./../../../layers/core/components/monitoring/PerformanceMetricsCard.vue')
export const MonitoringResourceOverviewCard = () => import('./../../../layers/core/components/monitoring/ResourceOverviewCard.vue')
export const MonitoringServerResourcesCard = () => import('./../../../layers/core/components/monitoring/ServerResourcesCard.vue')
export const PoliciesPolicyCard = () => import('./../../../layers/core/components/policies/PolicyCard.vue')
export const PoliciesPolicyCategoryFilter = () => import('./../../../layers/core/components/policies/PolicyCategoryFilter.vue')
export const PoliciesPolicyDetailModal = () => import('./../../../layers/core/components/policies/PolicyDetailModal.vue')
export const RulesCategoryCard = () => import('./../../../layers/core/components/rules/CategoryCard.vue')
export const RulesRuleCard = () => import('./../../../layers/core/components/rules/RuleCard.vue')
export const RulesRuleDetailModal = () => import('./../../../layers/core/components/rules/RuleDetailModal.vue')
export const RulesRuleFilters = () => import('./../../../layers/core/components/rules/RuleFilters.vue')
export const RulesTemplateCard = () => import('./../../../layers/core/components/rules/TemplateCard.vue')
export const RulesTemplateDetailModal = () => import('./../../../layers/core/components/rules/TemplateDetailModal.vue')
export const RulesValidationTester = () => import('./../../../layers/core/components/rules/ValidationTester.vue')
export const SecurityAuditLogEntry = () => import('./../../../layers/core/components/security/AuditLogEntry.vue')
export const SecurityAuditLogFilters = () => import('./../../../layers/core/components/security/AuditLogFilters.vue')
export const SecurityEditRoleModal = () => import('./../../../layers/core/components/security/EditRoleModal.vue')
export const SecurityEditUserRolesModal = () => import('./../../../layers/core/components/security/EditUserRolesModal.vue')
export const SecurityRolePermissionsCard = () => import('./../../../layers/core/components/security/RolePermissionsCard.vue')
export const SecurityUserRolesTable = () => import('./../../../layers/core/components/security/UserRolesTable.vue')
export const SettingsAppearanceSettingsCard = () => import('./../../../layers/core/components/settings/AppearanceSettingsCard.vue')
export const SettingsBackupHistoryCard = () => import('./../../../layers/core/components/settings/BackupHistoryCard.vue')
export const SettingsBackupSettingsCard = () => import('./../../../layers/core/components/settings/BackupSettingsCard.vue')
export const SettingsBackupStatusCard = () => import('./../../../layers/core/components/settings/BackupStatusCard.vue')
export const SettingsDeleteConfirmationModal = () => import('./../../../layers/core/components/settings/DeleteConfirmationModal.vue')
export const SettingsEnvironmentInfoCard = () => import('./../../../layers/core/components/settings/EnvironmentInfoCard.vue')
export const SettingsEnvironmentVariablesCard = () => import('./../../../layers/core/components/settings/EnvironmentVariablesCard.vue')
export const SettingsFormatSettingsCard = () => import('./../../../layers/core/components/settings/FormatSettingsCard.vue')
export const SettingsGeneralSettingsCard = () => import('./../../../layers/core/components/settings/GeneralSettingsCard.vue')
export const SettingsLanguageSettingsCard = () => import('./../../../layers/core/components/settings/LanguageSettingsCard.vue')
export const SettingsPerformanceSettingsCard = () => import('./../../../layers/core/components/settings/PerformanceSettingsCard.vue')
export const SettingsQuickActionsCard = () => import('./../../../layers/core/components/settings/QuickActionsCard.vue')
export const SettingsRestoreConfirmationModal = () => import('./../../../layers/core/components/settings/RestoreConfirmationModal.vue')
export const SettingsRestoreFromFileModal = () => import('./../../../layers/core/components/settings/RestoreFromFileModal.vue')
export const SettingsRestoreSettingsCard = () => import('./../../../layers/core/components/settings/RestoreSettingsCard.vue')
export const WebhooksWebhookCard = () => import('./../../../layers/core/components/webhooks/WebhookCard.vue')
export const WebhooksWebhookDetailModal = () => import('./../../../layers/core/components/webhooks/WebhookDetailModal.vue')
export const BudgetChartsSection = () => import('./../../../layers/budget/components/BudgetChartsSection.vue')
export const BudgetDashboard = () => import('./../../../layers/budget/components/BudgetDashboard.vue')
export const BudgetIncomeForm = () => import('./../../../layers/budget/components/BudgetIncomeForm.vue')
export const DatePicker = () => import('./../../../layers/budget/components/DatePicker.vue')
export const DemoAccountMenu = () => import('./../../../layers/budget/components/DemoAccountMenu.vue')
export const DemoCalendarEvent = () => import('./../../../layers/budget/components/DemoCalendarEvent.vue')
export const DemoCalendarEventPending = () => import('./../../../layers/budget/components/DemoCalendarEventPending.vue')
export const ExpensesSection = () => import('./../../../layers/budget/components/ExpensesSection.vue')
export const GlobalFooter = () => import('./../../../layers/budget/components/GlobalFooter.vue')
export const GlobalHeader = () => import('./../../../layers/budget/components/GlobalHeader.vue')
export const IncomeSection = () => import('./../../../layers/budget/components/IncomeSection.vue')
export const TairoLogo = () => import('./../../../layers/budget/components/TairoLogo.vue')
export const ChartsBudgetChartsSection = () => import('./../../../layers/budget/components/charts/BudgetChartsSection.vue')
export const DashboardBudgetGoalsCard = () => import('./../../../layers/budget/components/dashboard/BudgetGoalsCard.vue')
export const DashboardBudgetSummaryCard = () => import('./../../../layers/budget/components/dashboard/BudgetSummaryCard.vue')
export const DashboardBudgetTrendsChart = () => import('./../../../layers/budget/components/dashboard/BudgetTrendsChart.vue')
export const DashboardBudgetTypeSelector = () => import('./../../../layers/budget/components/dashboard/BudgetTypeSelector.vue')
export const DashboardCategoryBreakdownCard = () => import('./../../../layers/budget/components/dashboard/CategoryBreakdownCard.vue')
export const DashboardRecentTransactionsCard = () => import('./../../../layers/budget/components/dashboard/RecentTransactionsCard.vue')
export const ExpensesMultipleExpenses = () => import('./../../../layers/budget/components/expenses/MultipleExpenses.vue')
export const ExpensesOneTimeExpenses = () => import('./../../../layers/budget/components/expenses/OneTimeExpenses.vue')
export const ExpensesRepeatedExpenses = () => import('./../../../layers/budget/components/expenses/RepeatedExpenses.vue')
export const ForecastingCategories = () => import('./../../../layers/budget/components/forecasting/ForecastingCategories.vue')
export const ForecastingChart = () => import('./../../../layers/budget/components/forecasting/ForecastingChart.vue')
export const ForecastingControls = () => import('./../../../layers/budget/components/forecasting/ForecastingControls.vue')
export const ForecastingScenarios = () => import('./../../../layers/budget/components/forecasting/ForecastingScenarios.vue')
export const ForecastingSummary = () => import('./../../../layers/budget/components/forecasting/ForecastingSummary.vue')
export const IncomesMultipleIncomes = () => import('./../../../layers/budget/components/incomes/MultipleIncomes.vue')
export const IncomesOneTimeIncomes = () => import('./../../../layers/budget/components/incomes/OneTimeIncomes.vue')
export const IncomesRepeatedIncomes = () => import('./../../../layers/budget/components/incomes/RepeatedIncomes.vue')
export const PlannerBudgetComparisonCard = () => import('./../../../layers/budget/components/planner/BudgetComparisonCard.vue')
export const PlannerMonthlyBudgetCard = () => import('./../../../layers/budget/components/planner/MonthlyBudgetCard.vue')
export const QuickBudgetCategoryBreakdown = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetCategoryBreakdown.vue')
export const QuickBudgetEntryForm = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetEntryForm.vue')
export const QuickBudgetExpensesList = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetExpensesList.vue')
export const QuickBudgetGoals = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetGoals.vue')
export const QuickBudgetIncomeList = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetIncomeList.vue')
export const QuickBudgetSummaryCard = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetSummaryCard.vue')
export const QuickBudgetTransactionsList = () => import('./../../../layers/budget/components/quick-budget/QuickBudgetTransactionsList.vue')
export const StatsSection = () => import('./../../../layers/budget/components/stats/StatsSection.vue')
export const TransactionsListCard = () => import('./../../../layers/budget/components/transactions/TransactionsListCard.vue')
export const EmployeeFilters = () => import('./../../../layers/hr/components/EmployeeFilters.vue')
export const EmployeeForm = () => import('./../../../layers/hr/components/EmployeeForm.vue')
export const EmployeeList = () => import('./../../../layers/hr/components/EmployeeList.vue')
export const HrDashboard = () => import('./../../../layers/hr/components/HrDashboard.vue')
export const WorkerForm = () => import('./../../../layers/hr/components/WorkerForm.vue')
export const AttendanceRecordsTab = () => import('./../../../layers/hr/components/attendance/AttendanceRecordsTab.vue')
export const AttendanceReportsTab = () => import('./../../../layers/hr/components/attendance/AttendanceReportsTab.vue')
export const AttendanceDailyAttendanceTab = () => import('./../../../layers/hr/components/attendance/DailyAttendanceTab.vue')
export const BenefitsBenefitPlansTab = () => import('./../../../layers/hr/components/benefits/BenefitPlansTab.vue')
export const BenefitsBenefitSettingsTab = () => import('./../../../layers/hr/components/benefits/BenefitSettingsTab.vue')
export const BenefitsEmployeeBenefitsTab = () => import('./../../../layers/hr/components/benefits/EmployeeBenefitsTab.vue')
export const BonusesAllBonusesTab = () => import('./../../../layers/hr/components/bonuses/AllBonusesTab.vue')
export const BonusesBonusReportsTab = () => import('./../../../layers/hr/components/bonuses/BonusReportsTab.vue')
export const BonusesEmployeeBonusesTab = () => import('./../../../layers/hr/components/bonuses/EmployeeBonusesTab.vue')
export const CareerPathDetails = () => import('./../../../layers/hr/components/career/CareerPathDetails.vue')
export const CareerPathFormModal = () => import('./../../../layers/hr/components/career/CareerPathFormModal.vue')
export const CareerPathOverview = () => import('./../../../layers/hr/components/career/CareerPathOverview.vue')
export const DepartmentsDepartmentCard = () => import('./../../../layers/hr/components/departments/DepartmentCard.vue')
export const DocumentsCompanyPoliciesTab = () => import('./../../../layers/hr/components/documents/CompanyPoliciesTab.vue')
export const DocumentsTab = () => import('./../../../layers/hr/components/documents/DocumentsTab.vue')
export const DocumentsEmployeeDocumentsTab = () => import('./../../../layers/hr/components/documents/EmployeeDocumentsTab.vue')
export const EmployeesDocumentUploadForm = () => import('./../../../layers/hr/components/employees/DocumentUploadForm.vue')
export const EmployeesEmployeeDocuments = () => import('./../../../layers/hr/components/employees/EmployeeDocuments.vue')
export const EmployeesEmployeeLeave = () => import('./../../../layers/hr/components/employees/EmployeeLeave.vue')
export const EmployeesEmployeeOverview = () => import('./../../../layers/hr/components/employees/EmployeeOverview.vue')
export const EmployeesEmployeePayroll = () => import('./../../../layers/hr/components/employees/EmployeePayroll.vue')
export const EmployeesEmployeePerformance = () => import('./../../../layers/hr/components/employees/EmployeePerformance.vue')
export const EmployeesEmployeeProfileCard = () => import('./../../../layers/hr/components/employees/EmployeeProfileCard.vue')
export const LeaveBalancesTab = () => import('./../../../layers/hr/components/leave/LeaveBalancesTab.vue')
export const LeaveCalendarTab = () => import('./../../../layers/hr/components/leave/LeaveCalendarTab.vue')
export const LeaveRequestsTab = () => import('./../../../layers/hr/components/leave/LeaveRequestsTab.vue')
export const LeaveSettingsTab = () => import('./../../../layers/hr/components/leave/LeaveSettingsTab.vue')
export const PayrollRunTab = () => import('./../../../layers/hr/components/payroll/PayrollRunTab.vue')
export const PayrollPayslipsTab = () => import('./../../../layers/hr/components/payroll/PayslipsTab.vue')
export const PayrollTaxSettingsTab = () => import('./../../../layers/hr/components/payroll/TaxSettingsTab.vue')
export const PerformanceEmployeePerformanceTab = () => import('./../../../layers/hr/components/performance/EmployeePerformanceTab.vue')
export const PerformanceAnalyticsTab = () => import('./../../../layers/hr/components/performance/PerformanceAnalyticsTab.vue')
export const PerformanceReviewsTab = () => import('./../../../layers/hr/components/performance/PerformanceReviewsTab.vue')
export const ShiftsShiftAssignmentsTab = () => import('./../../../layers/hr/components/shifts/ShiftAssignmentsTab.vue')
export const ShiftsShiftScheduleTab = () => import('./../../../layers/hr/components/shifts/ShiftScheduleTab.vue')
export const ShiftsShiftTypesTab = () => import('./../../../layers/hr/components/shifts/ShiftTypesTab.vue')
export const TrainingCertificationsTab = () => import('./../../../layers/hr/components/training/CertificationsTab.vue')
export const TrainingEmployeeTrainingTab = () => import('./../../../layers/hr/components/training/EmployeeTrainingTab.vue')
export const TrainingEnrollEmployeesModal = () => import('./../../../layers/hr/components/training/EnrollEmployeesModal.vue')
export const TrainingDetailsModal = () => import('./../../../layers/hr/components/training/TrainingDetailsModal.vue')
export const TrainingFormModal = () => import('./../../../layers/hr/components/training/TrainingFormModal.vue')
export const TrainingProgramCard = () => import('./../../../layers/hr/components/training/TrainingProgramCard.vue')
export const TrainingProgramsTab = () => import('./../../../layers/hr/components/training/TrainingProgramsTab.vue')
export const CompaniesDashboard = () => import('./../../../layers/companies/components/CompaniesDashboard.vue')
export const CompanyDashboard = () => import('./../../../layers/companies/components/CompanyDashboard.vue')
export const CompanyForm = () => import('./../../../layers/companies/components/CompanyForm.vue')
export const ClientsClientCard = () => import('./../../../layers/companies/components/clients/ClientCard.vue')
export const ClientsTable = () => import('./../../../layers/companies/components/clients/ClientsTable.vue')
export const ComplianceAudits = () => import('./../../../layers/companies/components/compliance/ComplianceAudits.vue')
export const ComplianceOverview = () => import('./../../../layers/companies/components/compliance/ComplianceOverview.vue')
export const ComplianceRequirements = () => import('./../../../layers/companies/components/compliance/ComplianceRequirements.vue')
export const RiskMitigationPlans = () => import('./../../../layers/companies/components/risk/MitigationPlans.vue')
export const RiskDashboard = () => import('./../../../layers/companies/components/risk/RiskDashboard.vue')
export const RiskRegister = () => import('./../../../layers/companies/components/risk/RiskRegister.vue')
export const SettingsGeneralSettings = () => import('./../../../layers/companies/components/settings/GeneralSettings.vue')
export const SettingsIntegrationSettings = () => import('./../../../layers/companies/components/settings/IntegrationSettings.vue')
export const SettingsNotificationSettings = () => import('./../../../layers/companies/components/settings/NotificationSettings.vue')
export const SettingsPermissionSettings = () => import('./../../../layers/companies/components/settings/PermissionSettings.vue')
export const SuppliersSupplierCard = () => import('./../../../layers/companies/components/suppliers/SupplierCard.vue')
export const SuppliersSupplierDocuments = () => import('./../../../layers/companies/components/suppliers/SupplierDocuments.vue')
export const SuppliersSupplierOrders = () => import('./../../../layers/companies/components/suppliers/SupplierOrders.vue')
export const SuppliersSupplierOverview = () => import('./../../../layers/companies/components/suppliers/SupplierOverview.vue')
export const SuppliersSupplierPerformance = () => import('./../../../layers/companies/components/suppliers/SupplierPerformance.vue')
export const SuppliersSupplierPerformanceMetrics = () => import('./../../../layers/companies/components/suppliers/SupplierPerformanceMetrics.vue')
export const SuppliersSupplierTransactions = () => import('./../../../layers/companies/components/suppliers/SupplierTransactions.vue')
export const SuppliersTable = () => import('./../../../layers/companies/components/suppliers/SuppliersTable.vue')
export const WorkforceCapacityPlanning = () => import('./../../../layers/companies/components/workforce/CapacityPlanning.vue')
export const WorkforceSkillsGapAnalysis = () => import('./../../../layers/companies/components/workforce/SkillsGapAnalysis.vue')
export const WorkforceOverview = () => import('./../../../layers/companies/components/workforce/WorkforceOverview.vue')
export const ProductionDashboard = () => import('./../../../layers/production/components/ProductionDashboard.vue')
export const DashboardTaskStatusChart = () => import('./../../../layers/production/components/dashboard/TaskStatusChart.vue')
export const DocumentsUploadDocumentModal = () => import('./../../../layers/production/components/documents/UploadDocumentModal.vue')
export const DocumentsViewDocumentModal = () => import('./../../../layers/production/components/documents/ViewDocumentModal.vue')
export const QualityChecklistCard = () => import('./../../../layers/production/components/quality/ChecklistCard.vue')
export const QualityChecklistDetailModal = () => import('./../../../layers/production/components/quality/ChecklistDetailModal.vue')
export const QualityChecklistItem = () => import('./../../../layers/production/components/quality/ChecklistItem.vue')
export const QualityNewChecklistModal = () => import('./../../../layers/production/components/quality/NewChecklistModal.vue')
export const QualityPhotoCard = () => import('./../../../layers/production/components/quality/PhotoCard.vue')
export const QualityPhotoDetailModal = () => import('./../../../layers/production/components/quality/PhotoDetailModal.vue')
export const QualityUploadPhotoModal = () => import('./../../../layers/production/components/quality/UploadPhotoModal.vue')
export const ReportsBarChart = () => import('./../../../layers/production/components/reports/BarChart.vue')
export const ReportsLineChart = () => import('./../../../layers/production/components/reports/LineChart.vue')
export const ReportsMetricCard = () => import('./../../../layers/production/components/reports/MetricCard.vue')
export const ReportsPieChart = () => import('./../../../layers/production/components/reports/PieChart.vue')
export const ReportsReportCard = () => import('./../../../layers/production/components/reports/ReportCard.vue')
export const ResourcesEquipmentCard = () => import('./../../../layers/production/components/resources/EquipmentCard.vue')
export const ResourcesEquipmentDetailModal = () => import('./../../../layers/production/components/resources/EquipmentDetailModal.vue')
export const ResourcesMaterialCard = () => import('./../../../layers/production/components/resources/MaterialCard.vue')
export const ResourcesMaterialDetailModal = () => import('./../../../layers/production/components/resources/MaterialDetailModal.vue')
export const ResourcesNewEquipmentModal = () => import('./../../../layers/production/components/resources/NewEquipmentModal.vue')
export const ResourcesNewMaterialModal = () => import('./../../../layers/production/components/resources/NewMaterialModal.vue')
export const ResourcesNewWorkerModal = () => import('./../../../layers/production/components/resources/NewWorkerModal.vue')
export const ResourcesWorkforceCard = () => import('./../../../layers/production/components/resources/WorkforceCard.vue')
export const ResourcesWorkforceDetailModal = () => import('./../../../layers/production/components/resources/WorkforceDetailModal.vue')
export const StatisticsProjectMetricsChart = () => import('./../../../layers/production/components/statistics/ProjectMetricsChart.vue')
export const StatisticsProjectStatusChart = () => import('./../../../layers/production/components/statistics/ProjectStatusChart.vue')
export const StatisticsTaskCompletionChart = () => import('./../../../layers/production/components/statistics/TaskCompletionChart.vue')
export const TasksNewTaskModal = () => import('./../../../layers/production/components/tasks/NewTaskModal.vue')
export const TasksTaskBoard = () => import('./../../../layers/production/components/tasks/TaskBoard.vue')
export const TasksTaskCard = () => import('./../../../layers/production/components/tasks/TaskCard.vue')
export const TasksTaskColumn = () => import('./../../../layers/production/components/tasks/TaskColumn.vue')
export const TasksTaskDetailModal = () => import('./../../../layers/production/components/tasks/TaskDetailModal.vue')
export const TimelineGanttChart = () => import('./../../../layers/production/components/timeline/GanttChart.vue')
export const AccountingDashboard = () => import('./../../../layers/accounting/components/AccountingDashboard.vue')
export const JournalEntryForm = () => import('./../../../layers/accounting/components/JournalEntryForm.vue')
export const RecruitmentSidebarItem = () => import('./../../../layers/recruitment/components/RecruitmentSidebarItem.vue')
export const AdminLanguageManager = () => import('./../../../layers/recruitment/components/admin/LanguageManager.vue')
export const AdminTranslationsManager = () => import('./../../../layers/recruitment/components/admin/TranslationsManager.vue')
export const FormsFormBuilderFieldEditor = () => import('./../../../layers/recruitment/components/forms/FormBuilderFieldEditor.vue')
export const FormsFormBuilderFieldList = () => import('./../../../layers/recruitment/components/forms/FormBuilderFieldList.vue')
export const FormsFormBuilderPreview = () => import('./../../../layers/recruitment/components/forms/FormBuilderPreview.vue')
export const FormsFormBuilderPreviewField = () => import('./../../../layers/recruitment/components/forms/FormBuilderPreviewField.vue')
export const FormsFormBuilderSettings = () => import('./../../../layers/recruitment/components/forms/FormBuilderSettings.vue')
export const FormsFormBuilderSidebar = () => import('./../../../layers/recruitment/components/forms/FormBuilderSidebar.vue')
export const FormsFormBuilderStepEditor = () => import('./../../../layers/recruitment/components/forms/FormBuilderStepEditor.vue')
export const FormsPublicFormField = () => import('./../../../layers/recruitment/components/forms/PublicFormField.vue')
export const WorkforceFormAdditionalInfoStep = () => import('./../../../layers/recruitment/components/workforce-form/AdditionalInfoStep.vue')
export const WorkforceFormConfirmationStep = () => import('./../../../layers/recruitment/components/workforce-form/ConfirmationStep.vue')
export const WorkforceFormIntroductionStep = () => import('./../../../layers/recruitment/components/workforce-form/IntroductionStep.vue')
export const WorkforceFormLanguageSelector = () => import('./../../../layers/recruitment/components/workforce-form/LanguageSelector.vue')
export const WorkforceFormPersonalInfoStep = () => import('./../../../layers/recruitment/components/workforce-form/PersonalInfoStep.vue')
export const WorkforceFormProfessionalExperienceStep = () => import('./../../../layers/recruitment/components/workforce-form/ProfessionalExperienceStep.vue')
export const WorkforceFormQualificationsStep = () => import('./../../../layers/recruitment/components/workforce-form/QualificationsStep.vue')
export const WorkforceFormReferencesStep = () => import('./../../../layers/recruitment/components/workforce-form/ReferencesStep.vue')
export const WorkforceFormSpecialtiesStep = () => import('./../../../layers/recruitment/components/workforce-form/SpecialtiesStep.vue')
export const WorkforceAssignmentDetailsModal = () => import('./../../../layers/recruitment/components/workforce/AssignmentDetailsModal.vue')
export const WorkforceAssignmentFilters = () => import('./../../../layers/recruitment/components/workforce/AssignmentFilters.vue')
export const WorkforceAssignmentsPagination = () => import('./../../../layers/recruitment/components/workforce/AssignmentsPagination.vue')
export const WorkforceJobDetailsModal = () => import('./../../../layers/recruitment/components/workforce/JobDetailsModal.vue')
export const WorkforceJobFilters = () => import('./../../../layers/recruitment/components/workforce/JobFilters.vue')
export const WorkforceJobFormModal = () => import('./../../../layers/recruitment/components/workforce/JobFormModal.vue')
export const WorkforceJobsPagination = () => import('./../../../layers/recruitment/components/workforce/JobsPagination.vue')
export const WorkforceMatchDetailsModal = () => import('./../../../layers/recruitment/components/workforce/MatchDetailsModal.vue')
export const WorkforceMatchFilters = () => import('./../../../layers/recruitment/components/workforce/MatchFilters.vue')
export const WorkforceMatchesPagination = () => import('./../../../layers/recruitment/components/workforce/MatchesPagination.vue')
export const WorkforceWorkerDetailsModal = () => import('./../../../layers/recruitment/components/workforce/WorkerDetailsModal.vue')
export const WorkforceWorkerFormModal = () => import('./../../../layers/recruitment/components/workforce/WorkerFormModal.vue')
export const WorkforceWorkersPagination = () => import('./../../../layers/recruitment/components/workforce/WorkersPagination.vue')
export const ActivitiesActivityFormModal = () => import('./../../../layers/sales/components/activities/ActivityFormModal.vue')
export const ActivitiesActivityViewModal = () => import('./../../../layers/sales/components/activities/ActivityViewModal.vue')
export const AnalyticsDateRangeFilter = () => import('./../../../layers/sales/components/analytics/DateRangeFilter.vue')
export const AnalyticsForecastScenariosChart = () => import('./../../../layers/sales/components/analytics/ForecastScenariosChart.vue')
export const AnalyticsForecastSummaryCard = () => import('./../../../layers/sales/components/analytics/ForecastSummaryCard.vue')
export const AnalyticsGoalTrackingChart = () => import('./../../../layers/sales/components/analytics/GoalTrackingChart.vue')
export const AnalyticsKpiCard = () => import('./../../../layers/sales/components/analytics/KpiCard.vue')
export const AnalyticsReportCategoriesList = () => import('./../../../layers/sales/components/analytics/ReportCategoriesList.vue')
export const AnalyticsReportFormModal = () => import('./../../../layers/sales/components/analytics/ReportFormModal.vue')
export const AnalyticsReportViewModal = () => import('./../../../layers/sales/components/analytics/ReportViewModal.vue')
export const AnalyticsReportsList = () => import('./../../../layers/sales/components/analytics/ReportsList.vue')
export const AnalyticsRevenueForecastChart = () => import('./../../../layers/sales/components/analytics/RevenueForecastChart.vue')
export const AnalyticsRevenueTrendChart = () => import('./../../../layers/sales/components/analytics/RevenueTrendChart.vue')
export const AnalyticsSalesByCategoryChart = () => import('./../../../layers/sales/components/analytics/SalesByCategoryChart.vue')
export const AnalyticsSalesByRepresentativeChart = () => import('./../../../layers/sales/components/analytics/SalesByRepresentativeChart.vue')
export const AnalyticsSalesPipelineChart = () => import('./../../../layers/sales/components/analytics/SalesPipelineChart.vue')
export const AnalyticsScheduleReportModal = () => import('./../../../layers/sales/components/analytics/ScheduleReportModal.vue')
export const AnalyticsWinProbabilityChart = () => import('./../../../layers/sales/components/analytics/WinProbabilityChart.vue')
export const CampaignsAddPaymentMethodModal = () => import('./../../../layers/sales/components/campaigns/AddPaymentMethodModal.vue')
export const CampaignsCampaignCard = () => import('./../../../layers/sales/components/campaigns/CampaignCard.vue')
export const CampaignsCampaignCreateModal = () => import('./../../../layers/sales/components/campaigns/CampaignCreateModal.vue')
export const CampaignsCampaignEditModal = () => import('./../../../layers/sales/components/campaigns/CampaignEditModal.vue')
export const CampaignsCampaignViewModal = () => import('./../../../layers/sales/components/campaigns/CampaignViewModal.vue')
export const CampaignsEmailTemplateFormModal = () => import('./../../../layers/sales/components/campaigns/EmailTemplateFormModal.vue')
export const CampaignsEmailTemplateViewModal = () => import('./../../../layers/sales/components/campaigns/EmailTemplateViewModal.vue')
export const CampaignsStepsCampaignAiGenerationStep = () => import('./../../../layers/sales/components/campaigns/steps/CampaignAiGenerationStep.vue')
export const CampaignsStepsCampaignBriefStep = () => import('./../../../layers/sales/components/campaigns/steps/CampaignBriefStep.vue')
export const CampaignsStepsCampaignDetailsStep = () => import('./../../../layers/sales/components/campaigns/steps/CampaignDetailsStep.vue')
export const CampaignsStepsCampaignPaymentStep = () => import('./../../../layers/sales/components/campaigns/steps/CampaignPaymentStep.vue')
export const CampaignsStepsCampaignReviewStep = () => import('./../../../layers/sales/components/campaigns/steps/CampaignReviewStep.vue')
export const CommonSalesQuickActionButton = () => import('./../../../layers/sales/components/common/SalesQuickActionButton.vue')
export const DashboardSalesPipelineChart = () => import('./../../../layers/sales/components/dashboard/SalesPipelineChart.vue')
export const DealsDealProductsTable = () => import('./../../../layers/sales/components/deals/DealProductsTable.vue')
export const ModalsDealQuickCreateModal = () => import('./../../../layers/sales/components/modals/DealQuickCreateModal.vue')
export const ModalsLeadConvertModal = () => import('./../../../layers/sales/components/modals/LeadConvertModal.vue')
export const ModalsLeadQuickCreateModal = () => import('./../../../layers/sales/components/modals/LeadQuickCreateModal.vue')
export const ModalsMeetingQuickCreateModal = () => import('./../../../layers/sales/components/modals/MeetingQuickCreateModal.vue')
export const ProductsProductFormModal = () => import('./../../../layers/sales/components/products/ProductFormModal.vue')
export const ProductsProductViewModal = () => import('./../../../layers/sales/components/products/ProductViewModal.vue')
export const QuotationsQuotationEmptyState = () => import('./../../../layers/sales/components/quotations/QuotationEmptyState.vue')
export const QuotationsQuotationFilters = () => import('./../../../layers/sales/components/quotations/QuotationFilters.vue')
export const QuotationsQuotationFormModal = () => import('./../../../layers/sales/components/quotations/QuotationFormModal.vue')
export const QuotationsQuotationList = () => import('./../../../layers/sales/components/quotations/QuotationList.vue')
export const QuotationsQuotationViewModal = () => import('./../../../layers/sales/components/quotations/QuotationViewModal.vue')
export const QuotationsQuoteTemplateFormModal = () => import('./../../../layers/sales/components/quotations/QuoteTemplateFormModal.vue')
export const QuotationsQuoteTemplateViewModal = () => import('./../../../layers/sales/components/quotations/QuoteTemplateViewModal.vue')
export const SettingsApiKeyItem = () => import('./../../../layers/sales/components/settings/ApiKeyItem.vue')
export const SettingsGoalCard = () => import('./../../../layers/sales/components/settings/GoalCard.vue')
export const SettingsGoalFormModal = () => import('./../../../layers/sales/components/settings/GoalFormModal.vue')
export const SettingsIntegrationCard = () => import('./../../../layers/sales/components/settings/IntegrationCard.vue')
export const SettingsIntegrationConfigModal = () => import('./../../../layers/sales/components/settings/IntegrationConfigModal.vue')
export const SettingsStageFormModal = () => import('./../../../layers/sales/components/settings/StageFormModal.vue')
export const SettingsWorkflowCard = () => import('./../../../layers/sales/components/settings/WorkflowCard.vue')
export const SettingsWorkflowFormModal = () => import('./../../../layers/sales/components/settings/WorkflowFormModal.vue')
export const SettingsWorkflowLogsModal = () => import('./../../../layers/sales/components/settings/WorkflowLogsModal.vue')
export const BasePagination = () => import('./../../../layers/timemanagement/components/BasePagination.vue')
export const AnalyticsBillableHoursChart = () => import('./../../../layers/timemanagement/components/analytics/BillableHoursChart.vue')
export const AnalyticsProjectDistributionChart = () => import('./../../../layers/timemanagement/components/analytics/ProjectDistributionChart.vue')
export const AnalyticsTeamProductivityChart = () => import('./../../../layers/timemanagement/components/analytics/TeamProductivityChart.vue')
export const AnalyticsTimeTrendsChart = () => import('./../../../layers/timemanagement/components/analytics/TimeTrendsChart.vue')
export const AssignmentsAssignmentDetailsModal = () => import('./../../../layers/timemanagement/components/assignments/AssignmentDetailsModal.vue')
export const AssignmentsCreateAssignmentModal = () => import('./../../../layers/timemanagement/components/assignments/CreateAssignmentModal.vue')
export const AssignmentsEditAssignmentModal = () => import('./../../../layers/timemanagement/components/assignments/EditAssignmentModal.vue')
export const CalendarAddEventModal = () => import('./../../../layers/timemanagement/components/calendar/AddEventModal.vue')
export const CalendarControls = () => import('./../../../layers/timemanagement/components/calendar/CalendarControls.vue')
export const CalendarDayView = () => import('./../../../layers/timemanagement/components/calendar/DayView.vue')
export const CalendarEventDetailsModal = () => import('./../../../layers/timemanagement/components/calendar/EventDetailsModal.vue')
export const CalendarMonthView = () => import('./../../../layers/timemanagement/components/calendar/MonthView.vue')
export const CalendarTeamView = () => import('./../../../layers/timemanagement/components/calendar/TeamView.vue')
export const CalendarWeekView = () => import('./../../../layers/timemanagement/components/calendar/WeekView.vue')
export const ClientReportsClientReportDetailModal = () => import('./../../../layers/timemanagement/components/client-reports/ClientReportDetailModal.vue')
export const ClientReportsClientReportFilters = () => import('./../../../layers/timemanagement/components/client-reports/ClientReportFilters.vue')
export const ClientReportsCreateClientReportModal = () => import('./../../../layers/timemanagement/components/client-reports/CreateClientReportModal.vue')
export const DashboardActivityItem = () => import('./../../../layers/timemanagement/components/dashboard/ActivityItem.vue')
export const DashboardHoursByProjectChart = () => import('./../../../layers/timemanagement/components/dashboard/HoursByProjectChart.vue')
export const DashboardHoursByTeamMemberChart = () => import('./../../../layers/timemanagement/components/dashboard/HoursByTeamMemberChart.vue')
export const DashboardSummaryCard = () => import('./../../../layers/timemanagement/components/dashboard/SummaryCard.vue')
export const EntriesTimeEntryFormModal = () => import('./../../../layers/timemanagement/components/entries/TimeEntryFormModal.vue')
export const LocationsLocationAlertItem = () => import('./../../../layers/timemanagement/components/locations/LocationAlertItem.vue')
export const LocationsWorkLocationItem = () => import('./../../../layers/timemanagement/components/locations/WorkLocationItem.vue')
export const LocationsWorkerLocationItem = () => import('./../../../layers/timemanagement/components/locations/WorkerLocationItem.vue')
export const MapComponent = () => import('./../../../layers/timemanagement/components/map/MapComponent.vue')
export const SettingsLocationFormModal = () => import('./../../../layers/timemanagement/components/settings/LocationFormModal.vue')
export const SettingsLocationPickerMap = () => import('./../../../layers/timemanagement/components/settings/LocationPickerMap.vue')
export const SettingsMapComponent = () => import('./../../../layers/timemanagement/components/settings/MapComponent.vue')
export const SettingsWorkLocationSettingsItem = () => import('./../../../layers/timemanagement/components/settings/WorkLocationSettingsItem.vue')
export const SettingsApprovalsApprovalSettings = () => import('./../../../layers/timemanagement/components/settings/approvals/ApprovalSettings.vue')
export const SettingsApprovalsDeleteWorkflowModal = () => import('./../../../layers/timemanagement/components/settings/approvals/DeleteWorkflowModal.vue')
export const SettingsApprovalsWorkflowFormModal = () => import('./../../../layers/timemanagement/components/settings/approvals/WorkflowFormModal.vue')
export const SettingsApprovalsWorkflowList = () => import('./../../../layers/timemanagement/components/settings/approvals/WorkflowList.vue')
export const SettingsIntegrationsIntegrationCard = () => import('./../../../layers/timemanagement/components/settings/integrations/IntegrationCard.vue')
export const SettingsIntegrationsIntegrationConfigModal = () => import('./../../../layers/timemanagement/components/settings/integrations/IntegrationConfigModal.vue')
export const SettingsIntegrationsSyncHistoryList = () => import('./../../../layers/timemanagement/components/settings/integrations/SyncHistoryList.vue')
export const TeamMemberDetails = () => import('./../../../layers/timemanagement/components/team/MemberDetails.vue')
export const TeamMemberItem = () => import('./../../../layers/timemanagement/components/team/TeamMemberItem.vue')
export const TimesheetsCreateTimesheetModal = () => import('./../../../layers/timemanagement/components/timesheets/CreateTimesheetModal.vue')
export const TimesheetsTimesheetDetailModal = () => import('./../../../layers/timemanagement/components/timesheets/TimesheetDetailModal.vue')
export const TimesheetsTimesheetFilters = () => import('./../../../layers/timemanagement/components/timesheets/TimesheetFilters.vue')
export const TimesheetsTimesheetList = () => import('./../../../layers/timemanagement/components/timesheets/TimesheetList.vue')
export const TimesheetsTimesheetSummary = () => import('./../../../layers/timemanagement/components/timesheets/TimesheetSummary.vue')
export const WorkerAssignmentItem = () => import('./../../../layers/timemanagement/components/worker/AssignmentItem.vue')
export const WorkerCurrentAssignmentCard = () => import('./../../../layers/timemanagement/components/worker/CurrentAssignmentCard.vue')
export const WorkerMapComponent = () => import('./../../../layers/timemanagement/components/worker/MapComponent.vue')
export const WorkerProjectSelectionModal = () => import('./../../../layers/timemanagement/components/worker/ProjectSelectionModal.vue')
export const WorkerTimeEntryItem = () => import('./../../../layers/timemanagement/components/worker/TimeEntryItem.vue')
export const WorkerTimeTrackingButton = () => import('./../../../layers/timemanagement/components/worker/TimeTrackingButton.vue')
export const WorkerTimeTrackingModal = () => import('./../../../layers/timemanagement/components/worker/TimeTrackingModal.vue')
export const WorkerWeeklyHoursChart = () => import('./../../../layers/timemanagement/components/worker/WeeklyHoursChart.vue')
export const EndpointsEndpointDetailsModal = () => import('./../../../layers/communication/components/endpoints/EndpointDetailsModal.vue')
export const EndpointsEndpointFieldEditor = () => import('./../../../layers/communication/components/endpoints/EndpointFieldEditor.vue')
export const ExamplesAddonsDatepicker = () => import('./../../examples/addons/datepicker.vue')
export const ExamplesAddonsMapbox = () => import('./../../examples/addons/mapbox.vue')
export const ExamplesApexchartsBase = () => import('./../../examples/apexcharts/base.vue')
export const ExamplesFlexTableCurved = () => import('./../../examples/flex-table/curved.vue')
export const ExamplesFlexTableRounded = () => import('./../../examples/flex-table/rounded.vue')
export const ExamplesFlexTableSmooth = () => import('./../../examples/flex-table/smooth.vue')
export const ExamplesFlexTableStraight = () => import('./../../examples/flex-table/straight.vue')
export const ExamplesInputPasswordBase = () => import('./../../examples/input-password/base.vue')
export const ExamplesInputPasswordDisabled = () => import('./../../examples/input-password/disabled.vue')
export const ExamplesInputPasswordLocale = () => import('./../../examples/input-password/locale.vue')
export const ExamplesInputPasswordUserInput = () => import('./../../examples/input-password/user-input.vue')
export const ExamplesInputPasswordValidation = () => import('./../../examples/input-password/validation.vue')
export const ExamplesInputPhoneBase = () => import('./../../examples/input-phone/base.vue')
export const ExamplesInputPhoneCountry = () => import('./../../examples/input-phone/country.vue')
export const ExamplesInputPhoneDisabled = () => import('./../../examples/input-phone/disabled.vue')
export const ExamplesInputPhoneFormat = () => import('./../../examples/input-phone/format.vue')
export const ExamplesInputPhoneShape = () => import('./../../examples/input-phone/shape.vue')
export const ExamplesInputPhoneSize = () => import('./../../examples/input-phone/size.vue')
export const ExamplesInputPhoneValidation = () => import('./../../examples/input-phone/validation.vue')
export const ExamplesLightweightChartsBase = () => import('./../../examples/lightweight-charts/base.vue')
export const ExamplesPanelActivity = () => import('./../../examples/panel/activity.vue')
export const ExamplesPanelLanguage = () => import('./../../examples/panel/language.vue')
export const ExamplesPanelSearch = () => import('./../../examples/panel/search.vue')
export const ExamplesPanelTask = () => import('./../../examples/panel/task.vue')
export const ExamplesTableCurved = () => import('./../../examples/table/curved.vue')
export const ExamplesTableMediaCurved = () => import('./../../examples/table/media-curved.vue')
export const ExamplesTableMediaRounded = () => import('./../../examples/table/media-rounded.vue')
export const ExamplesTableMediaSmooth = () => import('./../../examples/table/media-smooth.vue')
export const ExamplesTableMediaStraight = () => import('./../../examples/table/media-straight.vue')
export const ExamplesTableRounded = () => import('./../../examples/table/rounded.vue')
export const ExamplesTableSmooth = () => import('./../../examples/table/smooth.vue')
export const ExamplesTableStraight = () => import('./../../examples/table/straight.vue')
export const ExamplesTairoCheckAnimated = () => import('./../../examples/tairo/check-animated.vue')
export const ExamplesTairoCheckboxAnimated = () => import('./../../examples/tairo/checkbox-animated.vue')
export const ExamplesTairoCheckboxCardIcon = () => import('./../../examples/tairo/checkbox-card-icon.vue')
export const ExamplesTairoCircularMenu = () => import('./../../examples/tairo/circular-menu.vue')
export const ExamplesTairoError = () => import('./../../examples/tairo/error.vue')
export const ExamplesTairoFormGroup = () => import('./../../examples/tairo/form-group.vue')
export const ExamplesTairoFormSave = () => import('./../../examples/tairo/form-save.vue')
export const ExamplesTairoInput = () => import('./../../examples/tairo/input.vue')
export const ExamplesTairoLogo = () => import('./../../examples/tairo/logo.vue')
export const ExamplesTairoLogotext = () => import('./../../examples/tairo/logotext.vue')
export const ExamplesTairoMenuComplete = () => import('./../../examples/tairo/menu-complete.vue')
export const ExamplesTairoMenu = () => import('./../../examples/tairo/menu.vue')
export const ExamplesTairoMobileDrawer = () => import('./../../examples/tairo/mobile-drawer.vue')
export const ExamplesTairoRadioCard = () => import('./../../examples/tairo/radio-card.vue')
export const ExamplesTairoSelect = () => import('./../../examples/tairo/select.vue')
export const ExamplesTairoValidation = () => import('./../../examples/tairo/validation.vue')
export const BaseAccordion = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Accordion.vue')
export const BaseAccordionItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AccordionItem.vue')
export const BaseAutocomplete = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Autocomplete.vue')
export const BaseAutocompleteGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteGroup.vue')
export const BaseAutocompleteItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteItem.vue')
export const BaseAutocompleteLabel = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteLabel.vue')
export const BaseAutocompleteSeparator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AutocompleteSeparator.vue')
export const BaseAvatar = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Avatar.vue')
export const BaseAvatarGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/AvatarGroup.vue')
export const BaseBreadcrumb = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Breadcrumb.vue')
export const BaseButton = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Button.vue')
export const BaseCard = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Card.vue')
export const BaseCheckbox = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Checkbox.vue')
export const BaseCheckboxGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/CheckboxGroup.vue')
export const BaseChip = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Chip.vue')
export const BaseDropdown = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Dropdown.vue')
export const BaseDropdownArrow = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownArrow.vue')
export const BaseDropdownCheckbox = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownCheckbox.vue')
export const BaseDropdownItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownItem.vue')
export const BaseDropdownLabel = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownLabel.vue')
export const BaseDropdownRadioGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioGroup.vue')
export const BaseDropdownRadioItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownRadioItem.vue')
export const BaseDropdownSeparator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSeparator.vue')
export const BaseDropdownSub = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/DropdownSub.vue')
export const BaseField = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Field.vue')
export const BaseHeading = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Heading.vue')
export const BaseIconBox = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/IconBox.vue')
export const BaseInput = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Input.vue')
export const BaseInputFile = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputFile.vue')
export const BaseInputNumber = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/InputNumber.vue')
export const BaseKbd = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Kbd.vue')
export const BaseLink = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Link.vue')
export const BaseList = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/List.vue')
export const BaseListItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ListItem.vue')
export const BaseMessage = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Message.vue')
export const BasePaginationButtonFirst = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonFirst.vue')
export const BasePaginationButtonLast = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonLast.vue')
export const BasePaginationButtonNext = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonNext.vue')
export const BasePaginationButtonPrev = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationButtonPrev.vue')
export const BasePaginationItems = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PaginationItems.vue')
export const BaseParagraph = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Paragraph.vue')
export const BasePlaceholderPage = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PlaceholderPage.vue')
export const BasePlaceload = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Placeload.vue')
export const BasePopover = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Popover.vue')
export const BasePrimitiveField = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveField.vue')
export const BasePrimitiveFieldController = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldController.vue')
export const BasePrimitiveFieldDescription = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldDescription.vue')
export const BasePrimitiveFieldError = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldError.vue')
export const BasePrimitiveFieldErrorIndicator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldErrorIndicator.vue')
export const BasePrimitiveFieldLabel = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLabel.vue')
export const BasePrimitiveFieldLoadingIndicator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldLoadingIndicator.vue')
export const BasePrimitiveFieldRequiredIndicator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldRequiredIndicator.vue')
export const BasePrimitiveFieldSuccessIndicator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/PrimitiveFieldSuccessIndicator.vue')
export const BaseProgress = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Progress.vue')
export const BaseProgressCircle = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ProgressCircle.vue')
export const BaseProse = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Prose.vue')
export const BaseProviders = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Providers.vue')
export const BaseRadio = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Radio.vue')
export const BaseRadioGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/RadioGroup.vue')
export const BaseSelect = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Select.vue')
export const BaseSelectGroup = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectGroup.vue')
export const BaseSelectItem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectItem.vue')
export const BaseSelectLabel = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectLabel.vue')
export const BaseSelectSeparator = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SelectSeparator.vue')
export const BaseSlider = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Slider.vue')
export const BaseSnack = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Snack.vue')
export const BaseSwitchBall = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchBall.vue')
export const BaseSwitchThin = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/SwitchThin.vue')
export const BaseTabs = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tabs.vue')
export const BaseTabsContent = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsContent.vue')
export const BaseTabsTrigger = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/TabsTrigger.vue')
export const BaseTag = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tag.vue')
export const BaseText = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Text.vue')
export const BaseTextarea = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Textarea.vue')
export const BaseThemeSwitch = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSwitch.vue')
export const BaseThemeSystem = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeSystem.vue')
export const BaseThemeToggle = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ThemeToggle.vue')
export const BaseToast = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Toast.vue')
export const BaseToastProvider = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/ToastProvider.vue')
export const BaseTooltip = () => import('./../../../node_modules/.pnpm/@shuriken-ui+nuxt@4.0.0-bet_06132fd1381949fd043819ba0fbb370a/node_modules/@shuriken-ui/nuxt/dist/runtime/components/Tooltip.vue')
export const NuxtWelcome = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/welcome.vue')
export const NuxtLayout = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-layout')
export const NuxtErrorBoundary = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-error-boundary')
export const ClientOnly = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/client-only')
export const DevOnly = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/dev-only')
export const ServerPlaceholder = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/server-placeholder')
export const NuxtLink = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-link')
export const NuxtLoadingIndicator = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-loading-indicator')
export const NuxtRouteAnnouncer = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-route-announcer')
export const NuxtImg = () => import('./../../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue')
export const NuxtPicture = () => import('./../../../node_modules/.pnpm/@nuxt+image@1.10.0_db0@0.3._b771e7fc4ca1c270220badb766b7c6f7/node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue')
export const AccordionContent = () => import('reka-ui')
export const AccordionHeader = () => import('reka-ui')
export const AccordionItem = () => import('reka-ui')
export const AccordionRoot = () => import('reka-ui')
export const AccordionTrigger = () => import('reka-ui')
export const AlertDialogRoot = () => import('reka-ui')
export const AlertDialogTrigger = () => import('reka-ui')
export const AlertDialogPortal = () => import('reka-ui')
export const AlertDialogContent = () => import('reka-ui')
export const AlertDialogOverlay = () => import('reka-ui')
export const AlertDialogCancel = () => import('reka-ui')
export const AlertDialogTitle = () => import('reka-ui')
export const AlertDialogDescription = () => import('reka-ui')
export const AlertDialogAction = () => import('reka-ui')
export const AspectRatio = () => import('reka-ui')
export const AvatarRoot = () => import('reka-ui')
export const AvatarFallback = () => import('reka-ui')
export const AvatarImage = () => import('reka-ui')
export const CalendarRoot = () => import('reka-ui')
export const CalendarHeader = () => import('reka-ui')
export const CalendarHeading = () => import('reka-ui')
export const CalendarGrid = () => import('reka-ui')
export const CalendarCell = () => import('reka-ui')
export const CalendarHeadCell = () => import('reka-ui')
export const CalendarNext = () => import('reka-ui')
export const CalendarPrev = () => import('reka-ui')
export const CalendarGridHead = () => import('reka-ui')
export const CalendarGridBody = () => import('reka-ui')
export const CalendarGridRow = () => import('reka-ui')
export const CalendarCellTrigger = () => import('reka-ui')
export const CheckboxGroupRoot = () => import('reka-ui')
export const CheckboxRoot = () => import('reka-ui')
export const CheckboxIndicator = () => import('reka-ui')
export const CollapsibleRoot = () => import('reka-ui')
export const CollapsibleTrigger = () => import('reka-ui')
export const CollapsibleContent = () => import('reka-ui')
export const ComboboxRoot = () => import('reka-ui')
export const ComboboxInput = () => import('reka-ui')
export const ComboboxAnchor = () => import('reka-ui')
export const ComboboxEmpty = () => import('reka-ui')
export const ComboboxTrigger = () => import('reka-ui')
export const ComboboxCancel = () => import('reka-ui')
export const ComboboxGroup = () => import('reka-ui')
export const ComboboxLabel = () => import('reka-ui')
export const ComboboxContent = () => import('reka-ui')
export const ComboboxViewport = () => import('reka-ui')
export const ComboboxVirtualizer = () => import('reka-ui')
export const ComboboxItem = () => import('reka-ui')
export const ComboboxItemIndicator = () => import('reka-ui')
export const ComboboxSeparator = () => import('reka-ui')
export const ComboboxArrow = () => import('reka-ui')
export const ComboboxPortal = () => import('reka-ui')
export const ContextMenuRoot = () => import('reka-ui')
export const ContextMenuTrigger = () => import('reka-ui')
export const ContextMenuPortal = () => import('reka-ui')
export const ContextMenuContent = () => import('reka-ui')
export const ContextMenuArrow = () => import('reka-ui')
export const ContextMenuItem = () => import('reka-ui')
export const ContextMenuGroup = () => import('reka-ui')
export const ContextMenuSeparator = () => import('reka-ui')
export const ContextMenuCheckboxItem = () => import('reka-ui')
export const ContextMenuItemIndicator = () => import('reka-ui')
export const ContextMenuLabel = () => import('reka-ui')
export const ContextMenuRadioGroup = () => import('reka-ui')
export const ContextMenuRadioItem = () => import('reka-ui')
export const ContextMenuSub = () => import('reka-ui')
export const ContextMenuSubContent = () => import('reka-ui')
export const ContextMenuSubTrigger = () => import('reka-ui')
export const DateFieldRoot = () => import('reka-ui')
export const DateFieldInput = () => import('reka-ui')
export const DatePickerRoot = () => import('reka-ui')
export const DatePickerHeader = () => import('reka-ui')
export const DatePickerHeading = () => import('reka-ui')
export const DatePickerGrid = () => import('reka-ui')
export const DatePickerCell = () => import('reka-ui')
export const DatePickerHeadCell = () => import('reka-ui')
export const DatePickerNext = () => import('reka-ui')
export const DatePickerPrev = () => import('reka-ui')
export const DatePickerGridHead = () => import('reka-ui')
export const DatePickerGridBody = () => import('reka-ui')
export const DatePickerGridRow = () => import('reka-ui')
export const DatePickerCellTrigger = () => import('reka-ui')
export const DatePickerInput = () => import('reka-ui')
export const DatePickerCalendar = () => import('reka-ui')
export const DatePickerField = () => import('reka-ui')
export const DatePickerAnchor = () => import('reka-ui')
export const DatePickerArrow = () => import('reka-ui')
export const DatePickerClose = () => import('reka-ui')
export const DatePickerTrigger = () => import('reka-ui')
export const DatePickerContent = () => import('reka-ui')
export const DateRangePickerRoot = () => import('reka-ui')
export const DateRangePickerHeader = () => import('reka-ui')
export const DateRangePickerHeading = () => import('reka-ui')
export const DateRangePickerGrid = () => import('reka-ui')
export const DateRangePickerCell = () => import('reka-ui')
export const DateRangePickerHeadCell = () => import('reka-ui')
export const DateRangePickerNext = () => import('reka-ui')
export const DateRangePickerPrev = () => import('reka-ui')
export const DateRangePickerGridHead = () => import('reka-ui')
export const DateRangePickerGridBody = () => import('reka-ui')
export const DateRangePickerGridRow = () => import('reka-ui')
export const DateRangePickerCellTrigger = () => import('reka-ui')
export const DateRangePickerInput = () => import('reka-ui')
export const DateRangePickerCalendar = () => import('reka-ui')
export const DateRangePickerField = () => import('reka-ui')
export const DateRangePickerAnchor = () => import('reka-ui')
export const DateRangePickerArrow = () => import('reka-ui')
export const DateRangePickerClose = () => import('reka-ui')
export const DateRangePickerTrigger = () => import('reka-ui')
export const DateRangePickerContent = () => import('reka-ui')
export const DateRangeFieldRoot = () => import('reka-ui')
export const DateRangeFieldInput = () => import('reka-ui')
export const DialogRoot = () => import('reka-ui')
export const DialogTrigger = () => import('reka-ui')
export const DialogPortal = () => import('reka-ui')
export const DialogContent = () => import('reka-ui')
export const DialogOverlay = () => import('reka-ui')
export const DialogClose = () => import('reka-ui')
export const DialogTitle = () => import('reka-ui')
export const DialogDescription = () => import('reka-ui')
export const DropdownMenuRoot = () => import('reka-ui')
export const DropdownMenuTrigger = () => import('reka-ui')
export const DropdownMenuPortal = () => import('reka-ui')
export const DropdownMenuContent = () => import('reka-ui')
export const DropdownMenuArrow = () => import('reka-ui')
export const DropdownMenuItem = () => import('reka-ui')
export const DropdownMenuGroup = () => import('reka-ui')
export const DropdownMenuSeparator = () => import('reka-ui')
export const DropdownMenuCheckboxItem = () => import('reka-ui')
export const DropdownMenuItemIndicator = () => import('reka-ui')
export const DropdownMenuLabel = () => import('reka-ui')
export const DropdownMenuRadioGroup = () => import('reka-ui')
export const DropdownMenuRadioItem = () => import('reka-ui')
export const DropdownMenuSub = () => import('reka-ui')
export const DropdownMenuSubContent = () => import('reka-ui')
export const DropdownMenuSubTrigger = () => import('reka-ui')
export const EditableRoot = () => import('reka-ui')
export const EditableArea = () => import('reka-ui')
export const EditableInput = () => import('reka-ui')
export const EditablePreview = () => import('reka-ui')
export const EditableSubmitTrigger = () => import('reka-ui')
export const EditableCancelTrigger = () => import('reka-ui')
export const EditableEditTrigger = () => import('reka-ui')
export const HoverCardRoot = () => import('reka-ui')
export const HoverCardTrigger = () => import('reka-ui')
export const HoverCardPortal = () => import('reka-ui')
export const HoverCardContent = () => import('reka-ui')
export const HoverCardArrow = () => import('reka-ui')
export const Label = () => import('reka-ui')
export const ListboxRoot = () => import('reka-ui')
export const ListboxContent = () => import('reka-ui')
export const ListboxFilter = () => import('reka-ui')
export const ListboxItem = () => import('reka-ui')
export const ListboxItemIndicator = () => import('reka-ui')
export const ListboxVirtualizer = () => import('reka-ui')
export const ListboxGroup = () => import('reka-ui')
export const ListboxGroupLabel = () => import('reka-ui')
export const MenubarRoot = () => import('reka-ui')
export const MenubarTrigger = () => import('reka-ui')
export const MenubarPortal = () => import('reka-ui')
export const MenubarContent = () => import('reka-ui')
export const MenubarArrow = () => import('reka-ui')
export const MenubarItem = () => import('reka-ui')
export const MenubarGroup = () => import('reka-ui')
export const MenubarSeparator = () => import('reka-ui')
export const MenubarCheckboxItem = () => import('reka-ui')
export const MenubarItemIndicator = () => import('reka-ui')
export const MenubarLabel = () => import('reka-ui')
export const MenubarRadioGroup = () => import('reka-ui')
export const MenubarRadioItem = () => import('reka-ui')
export const MenubarSub = () => import('reka-ui')
export const MenubarSubContent = () => import('reka-ui')
export const MenubarSubTrigger = () => import('reka-ui')
export const MenubarMenu = () => import('reka-ui')
export const NavigationMenuRoot = () => import('reka-ui')
export const NavigationMenuContent = () => import('reka-ui')
export const NavigationMenuIndicator = () => import('reka-ui')
export const NavigationMenuItem = () => import('reka-ui')
export const NavigationMenuLink = () => import('reka-ui')
export const NavigationMenuList = () => import('reka-ui')
export const NavigationMenuSub = () => import('reka-ui')
export const NavigationMenuTrigger = () => import('reka-ui')
export const NavigationMenuViewport = () => import('reka-ui')
export const NumberFieldRoot = () => import('reka-ui')
export const NumberFieldInput = () => import('reka-ui')
export const NumberFieldIncrement = () => import('reka-ui')
export const NumberFieldDecrement = () => import('reka-ui')
export const PaginationRoot = () => import('reka-ui')
export const PaginationEllipsis = () => import('reka-ui')
export const PaginationFirst = () => import('reka-ui')
export const PaginationLast = () => import('reka-ui')
export const PaginationList = () => import('reka-ui')
export const PaginationListItem = () => import('reka-ui')
export const PaginationNext = () => import('reka-ui')
export const PaginationPrev = () => import('reka-ui')
export const PinInputRoot = () => import('reka-ui')
export const PinInputInput = () => import('reka-ui')
export const PopoverRoot = () => import('reka-ui')
export const PopoverTrigger = () => import('reka-ui')
export const PopoverPortal = () => import('reka-ui')
export const PopoverContent = () => import('reka-ui')
export const PopoverArrow = () => import('reka-ui')
export const PopoverClose = () => import('reka-ui')
export const PopoverAnchor = () => import('reka-ui')
export const ProgressRoot = () => import('reka-ui')
export const ProgressIndicator = () => import('reka-ui')
export const RadioGroupRoot = () => import('reka-ui')
export const RadioGroupItem = () => import('reka-ui')
export const RadioGroupIndicator = () => import('reka-ui')
export const RangeCalendarRoot = () => import('reka-ui')
export const RangeCalendarHeader = () => import('reka-ui')
export const RangeCalendarHeading = () => import('reka-ui')
export const RangeCalendarGrid = () => import('reka-ui')
export const RangeCalendarCell = () => import('reka-ui')
export const RangeCalendarHeadCell = () => import('reka-ui')
export const RangeCalendarNext = () => import('reka-ui')
export const RangeCalendarPrev = () => import('reka-ui')
export const RangeCalendarGridHead = () => import('reka-ui')
export const RangeCalendarGridBody = () => import('reka-ui')
export const RangeCalendarGridRow = () => import('reka-ui')
export const RangeCalendarCellTrigger = () => import('reka-ui')
export const ScrollAreaRoot = () => import('reka-ui')
export const ScrollAreaViewport = () => import('reka-ui')
export const ScrollAreaScrollbar = () => import('reka-ui')
export const ScrollAreaThumb = () => import('reka-ui')
export const ScrollAreaCorner = () => import('reka-ui')
export const SelectRoot = () => import('reka-ui')
export const SelectTrigger = () => import('reka-ui')
export const SelectPortal = () => import('reka-ui')
export const SelectContent = () => import('reka-ui')
export const SelectArrow = () => import('reka-ui')
export const SelectSeparator = () => import('reka-ui')
export const SelectItemIndicator = () => import('reka-ui')
export const SelectLabel = () => import('reka-ui')
export const SelectGroup = () => import('reka-ui')
export const SelectItem = () => import('reka-ui')
export const SelectItemText = () => import('reka-ui')
export const SelectViewport = () => import('reka-ui')
export const SelectScrollUpButton = () => import('reka-ui')
export const SelectScrollDownButton = () => import('reka-ui')
export const SelectValue = () => import('reka-ui')
export const SelectIcon = () => import('reka-ui')
export const Separator = () => import('reka-ui')
export const SliderRoot = () => import('reka-ui')
export const SliderThumb = () => import('reka-ui')
export const SliderTrack = () => import('reka-ui')
export const SliderRange = () => import('reka-ui')
export const SplitterGroup = () => import('reka-ui')
export const SplitterPanel = () => import('reka-ui')
export const SplitterResizeHandle = () => import('reka-ui')
export const StepperRoot = () => import('reka-ui')
export const StepperItem = () => import('reka-ui')
export const StepperTrigger = () => import('reka-ui')
export const StepperDescription = () => import('reka-ui')
export const StepperTitle = () => import('reka-ui')
export const StepperIndicator = () => import('reka-ui')
export const StepperSeparator = () => import('reka-ui')
export const SwitchRoot = () => import('reka-ui')
export const SwitchThumb = () => import('reka-ui')
export const TabsRoot = () => import('reka-ui')
export const TabsList = () => import('reka-ui')
export const TabsContent = () => import('reka-ui')
export const TabsTrigger = () => import('reka-ui')
export const TabsIndicator = () => import('reka-ui')
export const TagsInputRoot = () => import('reka-ui')
export const TagsInputInput = () => import('reka-ui')
export const TagsInputItem = () => import('reka-ui')
export const TagsInputItemText = () => import('reka-ui')
export const TagsInputItemDelete = () => import('reka-ui')
export const TagsInputClear = () => import('reka-ui')
export const TimeFieldInput = () => import('reka-ui')
export const TimeFieldRoot = () => import('reka-ui')
export const ToastProvider = () => import('reka-ui')
export const ToastRoot = () => import('reka-ui')
export const ToastPortal = () => import('reka-ui')
export const ToastAction = () => import('reka-ui')
export const ToastClose = () => import('reka-ui')
export const ToastViewport = () => import('reka-ui')
export const ToastTitle = () => import('reka-ui')
export const ToastDescription = () => import('reka-ui')
export const Toggle = () => import('reka-ui')
export const ToggleGroupRoot = () => import('reka-ui')
export const ToggleGroupItem = () => import('reka-ui')
export const ToolbarRoot = () => import('reka-ui')
export const ToolbarButton = () => import('reka-ui')
export const ToolbarLink = () => import('reka-ui')
export const ToolbarToggleGroup = () => import('reka-ui')
export const ToolbarToggleItem = () => import('reka-ui')
export const ToolbarSeparator = () => import('reka-ui')
export const TooltipRoot = () => import('reka-ui')
export const TooltipTrigger = () => import('reka-ui')
export const TooltipContent = () => import('reka-ui')
export const TooltipArrow = () => import('reka-ui')
export const TooltipPortal = () => import('reka-ui')
export const TooltipProvider = () => import('reka-ui')
export const TreeRoot = () => import('reka-ui')
export const TreeItem = () => import('reka-ui')
export const TreeVirtualizer = () => import('reka-ui')
export const Viewport = () => import('reka-ui')
export const ConfigProvider = () => import('reka-ui')
export const FocusScope = () => import('reka-ui')
export const RovingFocusGroup = () => import('reka-ui')
export const RovingFocusItem = () => import('reka-ui')
export const Presence = () => import('reka-ui')
export const Primitive = () => import('reka-ui')
export const Slot = () => import('reka-ui')
export const VisuallyHidden = () => import('reka-ui')
export const NuxtLinkLocale = () => import('./../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale')
export const SwitchLocalePathLink = () => import('./../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink')
export const ContentRenderer = () => import('./../../../node_modules/.pnpm/@nuxt+content@3.4.0_magicast@0.3.5_typescript@5.8.3/node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue')
export const MDC = () => import('./../../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue')
export const MDCRenderer = () => import('./../../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue')
export const MDCSlot = () => import('./../../../node_modules/.pnpm/@nuxtjs+mdc@0.16.1_magicast@0.3.5/node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue')
export const VitePwaManifest = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest')
export const NuxtPwaManifest = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/VitePwaManifest')
export const NuxtPwaAssets = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/NuxtPwaAssets')
export const PwaAppleImage = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleImage.vue')
export const PwaAppleSplashScreenImage = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaAppleSplashScreenImage.vue')
export const PwaFaviconImage = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaFaviconImage.vue')
export const PwaMaskableImage = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaMaskableImage.vue')
export const PwaTransparentImage = () => import('./../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/components/PwaTransparentImage.vue')
export const ColorScheme = () => import('./../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue')
export const NuxtPage = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/page')
export const NoScript = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Link = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Base = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Title = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Meta = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Style = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Head = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Html = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const Body = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/components')
export const NuxtIsland = () => import('./../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/components/nuxt-island')
export const globalComponents: string[] = ["ProseA","ProseBlockquote","ProseCode","ProseEm","ProseH1","ProseH2","ProseH3","ProseH4","ProseH5","ProseH6","ProseHr","ProseImg","ProseLi","ProseOl","ProseP","ProsePre","ProseScript","ProseStrong","ProseTable","ProseTbody","ProseTd","ProseTh","ProseThead","ProseTr","ProseUl","Icon"]
export const localComponents: string[] = ["AccountMenu","AddonApexcharts","AddonInputPassword","AiAssistantButton","BaseLayerPage","BaseLoader","CalendarEvent","CalendarEventPending","CalendarSidebarCategories","DemoCreditCardReal","DemoPanelActivity","DemoPanelInvest","DemoPanelLanguage","LanguageSelector","LanguageToggle","Logo","LogoText","MapMarker","PasswordInputWithEye","PhoneInput","PwaInstallButton","PwaInstallModal","PwaThemeColor","Sidebar","SidebarBackdrop","SidebarContent","SidebarLayout","SidebarLink","SidebarLinks","SidebarNav","SidebarSubsidebar","SidebarSubsidebarCollapsible","SidebarSubsidebarCollapsibleLink","SidebarSubsidebarCollapsibleTrigger","SidebarSubsidebarContent","SidebarSubsidebarHeader","SidebarSubsidebarLink","SidebarTrigger","SubsidebarChat","SubsidebarMessaging","ThemeColorUpdater","Toolbar","AuthEmailVerification","AuthPasswordInput","AuthPaymentForm","AuthRegistrationForm","AuthRegistrationSuccess","AuthRoleSelection","AuthStripePaymentForm","AuthSubscriptionPlanSelection","CompanyDetailsTab","CompanyDocumentsTab","CompanyHeader","CompanySettingsTab","CompanyTeamTab","CompanyDocumentsDocumentList","CompanyDocumentsDocumentUploader","CompanyTeamConfirmRemoveModal","CompanyTeamEditRoleModal","CompanyTeamInvitationRow","CompanyTeamInvitationsList","CompanyTeamInviteModal","CompanyTeamMemberRow","CompanyTeamMembersList","PaymentBillingAddressForm","PaymentMethodForm","PaymentStripeCardInput","TairoCheckAnimated","TairoCheckboxAnimated","TairoCheckboxCardIcon","TairoContentWrapper","TairoContentWrapperTabbed","TairoError","TairoFlexTable","TairoFlexTableCell","TairoFlexTableHeading","TairoFlexTableRow","TairoFormGroup","TairoFormSave","TairoFullscreenDropfile","TairoImageZoom","TairoInput","TairoInputFileHeadless","TairoMobileDrawer","TairoPanels","TairoRadioCard","TairoSelect","TairoSelectItem","TairoTable","TairoTableCell","TairoTableHeading","TairoTableRow","TairoWelcome","TairoCollapseBackdrop","TairoCollapseCollapsible","TairoCollapseCollapsibleLink","TairoCollapseCollapsibleTrigger","TairoCollapseContent","TairoCollapseLayout","TairoCollapseSidebar","TairoCollapseSidebarClose","TairoCollapseSidebarHeader","TairoCollapseSidebarLink","TairoCollapseSidebarLinks","TairoMenu","TairoMenuContent","TairoMenuIndicator","TairoMenuItem","TairoMenuLink","TairoMenuLinkTab","TairoMenuList","TairoMenuListItems","TairoMenuTrigger","TairoMenuViewport","TairoSidebar","TairoSidebarBackdrop","TairoSidebarContent","TairoSidebarLayout","TairoSidebarLink","TairoSidebarLinks","TairoSidebarNav","TairoSidebarSubsidebar","TairoSidebarSubsidebarCollapsible","TairoSidebarSubsidebarCollapsibleLink","TairoSidebarSubsidebarCollapsibleTrigger","TairoSidebarSubsidebarContent","TairoSidebarSubsidebarHeader","TairoSidebarSubsidebarLink","TairoSidebarTrigger","TairoSidenavBackdrop","TairoSidenavCollapsible","TairoSidenavCollapsibleLink","TairoSidenavCollapsibleTrigger","TairoSidenavContent","TairoSidenavLayout","TairoSidenavSidebar","TairoSidenavSidebarDivider","TairoSidenavSidebarHeader","TairoSidenavSidebarLink","TairoSidenavSidebarLinks","TairoTopnavContent","TairoTopnavHeader","TairoTopnavLayout","TairoTopnavNavbar","BusinessRuleModal","CoreDashboard","ActivityFilters","ActivityLogDetails","ActivityLogItem","ActivityLogList","AiDecisionCard","AiDecisionDetailModal","ApiEndpointCard","ApiEndpointDetailModal","AutomationRuleCard","BrandAssetCard","ChartsBarChart","ChartsLineChart","ChartsPieChart","ChartsRadialChart","DashboardChartDashboardCalendar","DashboardChartBudget","DashboardChartGoal","DashboardChartProduction","DashboardChartRevenue","DashboardChartSales","DashboardChartDashboardCheckbox","DashboardActivityCard","DashboardBudgetAllocation","DashboardEmployeeActivity","DashboardMetricCard","DashboardModuleOverview","DashboardModuleUsageCard","DashboardNotificationsCard","DashboardProjectStatus","DashboardRecentActivities","DashboardSalesPerformance","DashboardStatsOverview","DashboardSystemHealth","DashboardSystemHealthCard","DashboardUpcomingEvents","DashboardUserActivityCard","DatabaseConnectionBuilderForm","DatabaseConnectionPoolMonitor","DatabaseConnectionStringForm","DatabaseExplorer","DatabaseHealth","DatabaseSchema","DatabaseTerminalWindow","DocumentsDocumentCard","DocumentsDocumentFilters","DocumentsDocumentUploadModal","EventsEventCard","EventsEventDetailModal","ExamplesComponentAccessExample","HealthPerformanceMetricsCard","HealthResourceUsageCard","HealthServiceStatusCard","LegalDocumentCard","MlDatasetCard","MlTrainingDataUploader","MlTrainingFormatGuide","ModulesModuleCard","ModulesModuleDetailModal","MonitoringAlertDetailsModal","MonitoringAlertsListCard","MonitoringAlertsSummaryCard","MonitoringApiPerformanceCard","MonitoringDatabaseResourcesCard","MonitoringErrorDetailsModal","MonitoringErrorLogsCard","MonitoringErrorSummaryCard","MonitoringPageLoadTimesCard","MonitoringPerformanceMetricsCard","MonitoringResourceOverviewCard","MonitoringServerResourcesCard","PoliciesPolicyCard","PoliciesPolicyCategoryFilter","PoliciesPolicyDetailModal","RulesCategoryCard","RulesRuleCard","RulesRuleDetailModal","RulesRuleFilters","RulesTemplateCard","RulesTemplateDetailModal","RulesValidationTester","SecurityAuditLogEntry","SecurityAuditLogFilters","SecurityEditRoleModal","SecurityEditUserRolesModal","SecurityRolePermissionsCard","SecurityUserRolesTable","SettingsAppearanceSettingsCard","SettingsBackupHistoryCard","SettingsBackupSettingsCard","SettingsBackupStatusCard","SettingsDeleteConfirmationModal","SettingsEnvironmentInfoCard","SettingsEnvironmentVariablesCard","SettingsFormatSettingsCard","SettingsGeneralSettingsCard","SettingsLanguageSettingsCard","SettingsPerformanceSettingsCard","SettingsQuickActionsCard","SettingsRestoreConfirmationModal","SettingsRestoreFromFileModal","SettingsRestoreSettingsCard","WebhooksWebhookCard","WebhooksWebhookDetailModal","BudgetChartsSection","BudgetDashboard","BudgetIncomeForm","DatePicker","DemoAccountMenu","DemoCalendarEvent","DemoCalendarEventPending","ExpensesSection","GlobalFooter","GlobalHeader","IncomeSection","TairoLogo","ChartsBudgetChartsSection","DashboardBudgetGoalsCard","DashboardBudgetSummaryCard","DashboardBudgetTrendsChart","DashboardBudgetTypeSelector","DashboardCategoryBreakdownCard","DashboardRecentTransactionsCard","ExpensesMultipleExpenses","ExpensesOneTimeExpenses","ExpensesRepeatedExpenses","ForecastingCategories","ForecastingChart","ForecastingControls","ForecastingScenarios","ForecastingSummary","IncomesMultipleIncomes","IncomesOneTimeIncomes","IncomesRepeatedIncomes","PlannerBudgetComparisonCard","PlannerMonthlyBudgetCard","QuickBudgetCategoryBreakdown","QuickBudgetEntryForm","QuickBudgetExpensesList","QuickBudgetGoals","QuickBudgetIncomeList","QuickBudgetSummaryCard","QuickBudgetTransactionsList","StatsSection","TransactionsListCard","EmployeeFilters","EmployeeForm","EmployeeList","HrDashboard","WorkerForm","AttendanceRecordsTab","AttendanceReportsTab","AttendanceDailyAttendanceTab","BenefitsBenefitPlansTab","BenefitsBenefitSettingsTab","BenefitsEmployeeBenefitsTab","BonusesAllBonusesTab","BonusesBonusReportsTab","BonusesEmployeeBonusesTab","CareerPathDetails","CareerPathFormModal","CareerPathOverview","DepartmentsDepartmentCard","DocumentsCompanyPoliciesTab","DocumentsTab","DocumentsEmployeeDocumentsTab","EmployeesDocumentUploadForm","EmployeesEmployeeDocuments","EmployeesEmployeeLeave","EmployeesEmployeeOverview","EmployeesEmployeePayroll","EmployeesEmployeePerformance","EmployeesEmployeeProfileCard","LeaveBalancesTab","LeaveCalendarTab","LeaveRequestsTab","LeaveSettingsTab","PayrollRunTab","PayrollPayslipsTab","PayrollTaxSettingsTab","PerformanceEmployeePerformanceTab","PerformanceAnalyticsTab","PerformanceReviewsTab","ShiftsShiftAssignmentsTab","ShiftsShiftScheduleTab","ShiftsShiftTypesTab","TrainingCertificationsTab","TrainingEmployeeTrainingTab","TrainingEnrollEmployeesModal","TrainingDetailsModal","TrainingFormModal","TrainingProgramCard","TrainingProgramsTab","CompaniesDashboard","CompanyDashboard","CompanyForm","ClientsClientCard","ClientsTable","ComplianceAudits","ComplianceOverview","ComplianceRequirements","RiskMitigationPlans","RiskDashboard","RiskRegister","SettingsGeneralSettings","SettingsIntegrationSettings","SettingsNotificationSettings","SettingsPermissionSettings","SuppliersSupplierCard","SuppliersSupplierDocuments","SuppliersSupplierOrders","SuppliersSupplierOverview","SuppliersSupplierPerformance","SuppliersSupplierPerformanceMetrics","SuppliersSupplierTransactions","SuppliersTable","WorkforceCapacityPlanning","WorkforceSkillsGapAnalysis","WorkforceOverview","ProductionDashboard","DashboardTaskStatusChart","DocumentsUploadDocumentModal","DocumentsViewDocumentModal","QualityChecklistCard","QualityChecklistDetailModal","QualityChecklistItem","QualityNewChecklistModal","QualityPhotoCard","QualityPhotoDetailModal","QualityUploadPhotoModal","ReportsBarChart","ReportsLineChart","ReportsMetricCard","ReportsPieChart","ReportsReportCard","ResourcesEquipmentCard","ResourcesEquipmentDetailModal","ResourcesMaterialCard","ResourcesMaterialDetailModal","ResourcesNewEquipmentModal","ResourcesNewMaterialModal","ResourcesNewWorkerModal","ResourcesWorkforceCard","ResourcesWorkforceDetailModal","StatisticsProjectMetricsChart","StatisticsProjectStatusChart","StatisticsTaskCompletionChart","TasksNewTaskModal","TasksTaskBoard","TasksTaskCard","TasksTaskColumn","TasksTaskDetailModal","TimelineGanttChart","AccountingDashboard","JournalEntryForm","RecruitmentSidebarItem","AdminLanguageManager","AdminTranslationsManager","FormsFormBuilderFieldEditor","FormsFormBuilderFieldList","FormsFormBuilderPreview","FormsFormBuilderPreviewField","FormsFormBuilderSettings","FormsFormBuilderSidebar","FormsFormBuilderStepEditor","FormsPublicFormField","WorkforceFormAdditionalInfoStep","WorkforceFormConfirmationStep","WorkforceFormIntroductionStep","WorkforceFormLanguageSelector","WorkforceFormPersonalInfoStep","WorkforceFormProfessionalExperienceStep","WorkforceFormQualificationsStep","WorkforceFormReferencesStep","WorkforceFormSpecialtiesStep","WorkforceAssignmentDetailsModal","WorkforceAssignmentFilters","WorkforceAssignmentsPagination","WorkforceJobDetailsModal","WorkforceJobFilters","WorkforceJobFormModal","WorkforceJobsPagination","WorkforceMatchDetailsModal","WorkforceMatchFilters","WorkforceMatchesPagination","WorkforceWorkerDetailsModal","WorkforceWorkerFormModal","WorkforceWorkersPagination","ActivitiesActivityFormModal","ActivitiesActivityViewModal","AnalyticsDateRangeFilter","AnalyticsForecastScenariosChart","AnalyticsForecastSummaryCard","AnalyticsGoalTrackingChart","AnalyticsKpiCard","AnalyticsReportCategoriesList","AnalyticsReportFormModal","AnalyticsReportViewModal","AnalyticsReportsList","AnalyticsRevenueForecastChart","AnalyticsRevenueTrendChart","AnalyticsSalesByCategoryChart","AnalyticsSalesByRepresentativeChart","AnalyticsSalesPipelineChart","AnalyticsScheduleReportModal","AnalyticsWinProbabilityChart","CampaignsAddPaymentMethodModal","CampaignsCampaignCard","CampaignsCampaignCreateModal","CampaignsCampaignEditModal","CampaignsCampaignViewModal","CampaignsEmailTemplateFormModal","CampaignsEmailTemplateViewModal","CampaignsStepsCampaignAiGenerationStep","CampaignsStepsCampaignBriefStep","CampaignsStepsCampaignDetailsStep","CampaignsStepsCampaignPaymentStep","CampaignsStepsCampaignReviewStep","CommonSalesQuickActionButton","DashboardSalesPipelineChart","DealsDealProductsTable","ModalsDealQuickCreateModal","ModalsLeadConvertModal","ModalsLeadQuickCreateModal","ModalsMeetingQuickCreateModal","ProductsProductFormModal","ProductsProductViewModal","QuotationsQuotationEmptyState","QuotationsQuotationFilters","QuotationsQuotationFormModal","QuotationsQuotationList","QuotationsQuotationViewModal","QuotationsQuoteTemplateFormModal","QuotationsQuoteTemplateViewModal","SettingsApiKeyItem","SettingsGoalCard","SettingsGoalFormModal","SettingsIntegrationCard","SettingsIntegrationConfigModal","SettingsStageFormModal","SettingsWorkflowCard","SettingsWorkflowFormModal","SettingsWorkflowLogsModal","BasePagination","AnalyticsBillableHoursChart","AnalyticsProjectDistributionChart","AnalyticsTeamProductivityChart","AnalyticsTimeTrendsChart","AssignmentsAssignmentDetailsModal","AssignmentsCreateAssignmentModal","AssignmentsEditAssignmentModal","CalendarAddEventModal","CalendarControls","CalendarDayView","CalendarEventDetailsModal","CalendarMonthView","CalendarTeamView","CalendarWeekView","ClientReportsClientReportDetailModal","ClientReportsClientReportFilters","ClientReportsCreateClientReportModal","DashboardActivityItem","DashboardHoursByProjectChart","DashboardHoursByTeamMemberChart","DashboardSummaryCard","EntriesTimeEntryFormModal","LocationsLocationAlertItem","LocationsWorkLocationItem","LocationsWorkerLocationItem","MapComponent","SettingsLocationFormModal","SettingsLocationPickerMap","SettingsMapComponent","SettingsWorkLocationSettingsItem","SettingsApprovalsApprovalSettings","SettingsApprovalsDeleteWorkflowModal","SettingsApprovalsWorkflowFormModal","SettingsApprovalsWorkflowList","SettingsIntegrationsIntegrationCard","SettingsIntegrationsIntegrationConfigModal","SettingsIntegrationsSyncHistoryList","TeamMemberDetails","TeamMemberItem","TimesheetsCreateTimesheetModal","TimesheetsTimesheetDetailModal","TimesheetsTimesheetFilters","TimesheetsTimesheetList","TimesheetsTimesheetSummary","WorkerAssignmentItem","WorkerCurrentAssignmentCard","WorkerMapComponent","WorkerProjectSelectionModal","WorkerTimeEntryItem","WorkerTimeTrackingButton","WorkerTimeTrackingModal","WorkerWeeklyHoursChart","EndpointsEndpointDetailsModal","EndpointsEndpointFieldEditor","ExamplesAddonsDatepicker","ExamplesAddonsMapbox","ExamplesApexchartsBase","ExamplesFlexTableCurved","ExamplesFlexTableRounded","ExamplesFlexTableSmooth","ExamplesFlexTableStraight","ExamplesInputPasswordBase","ExamplesInputPasswordDisabled","ExamplesInputPasswordLocale","ExamplesInputPasswordUserInput","ExamplesInputPasswordValidation","ExamplesInputPhoneBase","ExamplesInputPhoneCountry","ExamplesInputPhoneDisabled","ExamplesInputPhoneFormat","ExamplesInputPhoneShape","ExamplesInputPhoneSize","ExamplesInputPhoneValidation","ExamplesLightweightChartsBase","ExamplesPanelActivity","ExamplesPanelLanguage","ExamplesPanelSearch","ExamplesPanelTask","ExamplesTableCurved","ExamplesTableMediaCurved","ExamplesTableMediaRounded","ExamplesTableMediaSmooth","ExamplesTableMediaStraight","ExamplesTableRounded","ExamplesTableSmooth","ExamplesTableStraight","ExamplesTairoCheckAnimated","ExamplesTairoCheckboxAnimated","ExamplesTairoCheckboxCardIcon","ExamplesTairoCircularMenu","ExamplesTairoError","ExamplesTairoFormGroup","ExamplesTairoFormSave","ExamplesTairoInput","ExamplesTairoLogo","ExamplesTairoLogotext","ExamplesTairoMenuComplete","ExamplesTairoMenu","ExamplesTairoMobileDrawer","ExamplesTairoRadioCard","ExamplesTairoSelect","ExamplesTairoValidation","BaseAccordion","BaseAccordionItem","BaseAutocomplete","BaseAutocompleteGroup","BaseAutocompleteItem","BaseAutocompleteLabel","BaseAutocompleteSeparator","BaseAvatar","BaseAvatarGroup","BaseBreadcrumb","BaseButton","BaseCard","BaseCheckbox","BaseCheckboxGroup","BaseChip","BaseDropdown","BaseDropdownArrow","BaseDropdownCheckbox","BaseDropdownItem","BaseDropdownLabel","BaseDropdownRadioGroup","BaseDropdownRadioItem","BaseDropdownSeparator","BaseDropdownSub","BaseField","BaseHeading","BaseIconBox","BaseInput","BaseInputFile","BaseInputNumber","BaseKbd","BaseLink","BaseList","BaseListItem","BaseMessage","BasePaginationButtonFirst","BasePaginationButtonLast","BasePaginationButtonNext","BasePaginationButtonPrev","BasePaginationItems","BaseParagraph","BasePlaceholderPage","BasePlaceload","BasePopover","BasePrimitiveField","BasePrimitiveFieldController","BasePrimitiveFieldDescription","BasePrimitiveFieldError","BasePrimitiveFieldErrorIndicator","BasePrimitiveFieldLabel","BasePrimitiveFieldLoadingIndicator","BasePrimitiveFieldRequiredIndicator","BasePrimitiveFieldSuccessIndicator","BaseProgress","BaseProgressCircle","BaseProse","BaseProviders","BaseRadio","BaseRadioGroup","BaseSelect","BaseSelectGroup","BaseSelectItem","BaseSelectLabel","BaseSelectSeparator","BaseSlider","BaseSnack","BaseSwitchBall","BaseSwitchThin","BaseTabs","BaseTabsContent","BaseTabsTrigger","BaseTag","BaseText","BaseTextarea","BaseThemeSwitch","BaseThemeSystem","BaseThemeToggle","BaseToast","BaseToastProvider","BaseTooltip","NuxtWelcome","NuxtLayout","NuxtErrorBoundary","ClientOnly","DevOnly","ServerPlaceholder","NuxtLink","NuxtLoadingIndicator","NuxtRouteAnnouncer","NuxtImg","NuxtPicture","AccordionContent","AccordionHeader","AccordionItem","AccordionRoot","AccordionTrigger","AlertDialogRoot","AlertDialogTrigger","AlertDialogPortal","AlertDialogContent","AlertDialogOverlay","AlertDialogCancel","AlertDialogTitle","AlertDialogDescription","AlertDialogAction","AspectRatio","AvatarRoot","AvatarFallback","AvatarImage","CalendarRoot","CalendarHeader","CalendarHeading","CalendarGrid","CalendarCell","CalendarHeadCell","CalendarNext","CalendarPrev","CalendarGridHead","CalendarGridBody","CalendarGridRow","CalendarCellTrigger","CheckboxGroupRoot","CheckboxRoot","CheckboxIndicator","CollapsibleRoot","CollapsibleTrigger","CollapsibleContent","ComboboxRoot","ComboboxInput","ComboboxAnchor","ComboboxEmpty","ComboboxTrigger","ComboboxCancel","ComboboxGroup","ComboboxLabel","ComboboxContent","ComboboxViewport","ComboboxVirtualizer","ComboboxItem","ComboboxItemIndicator","ComboboxSeparator","ComboboxArrow","ComboboxPortal","ContextMenuRoot","ContextMenuTrigger","ContextMenuPortal","ContextMenuContent","ContextMenuArrow","ContextMenuItem","ContextMenuGroup","ContextMenuSeparator","ContextMenuCheckboxItem","ContextMenuItemIndicator","ContextMenuLabel","ContextMenuRadioGroup","ContextMenuRadioItem","ContextMenuSub","ContextMenuSubContent","ContextMenuSubTrigger","DateFieldRoot","DateFieldInput","DatePickerRoot","DatePickerHeader","DatePickerHeading","DatePickerGrid","DatePickerCell","DatePickerHeadCell","DatePickerNext","DatePickerPrev","DatePickerGridHead","DatePickerGridBody","DatePickerGridRow","DatePickerCellTrigger","DatePickerInput","DatePickerCalendar","DatePickerField","DatePickerAnchor","DatePickerArrow","DatePickerClose","DatePickerTrigger","DatePickerContent","DateRangePickerRoot","DateRangePickerHeader","DateRangePickerHeading","DateRangePickerGrid","DateRangePickerCell","DateRangePickerHeadCell","DateRangePickerNext","DateRangePickerPrev","DateRangePickerGridHead","DateRangePickerGridBody","DateRangePickerGridRow","DateRangePickerCellTrigger","DateRangePickerInput","DateRangePickerCalendar","DateRangePickerField","DateRangePickerAnchor","DateRangePickerArrow","DateRangePickerClose","DateRangePickerTrigger","DateRangePickerContent","DateRangeFieldRoot","DateRangeFieldInput","DialogRoot","DialogTrigger","DialogPortal","DialogContent","DialogOverlay","DialogClose","DialogTitle","DialogDescription","DropdownMenuRoot","DropdownMenuTrigger","DropdownMenuPortal","DropdownMenuContent","DropdownMenuArrow","DropdownMenuItem","DropdownMenuGroup","DropdownMenuSeparator","DropdownMenuCheckboxItem","DropdownMenuItemIndicator","DropdownMenuLabel","DropdownMenuRadioGroup","DropdownMenuRadioItem","DropdownMenuSub","DropdownMenuSubContent","DropdownMenuSubTrigger","EditableRoot","EditableArea","EditableInput","EditablePreview","EditableSubmitTrigger","EditableCancelTrigger","EditableEditTrigger","HoverCardRoot","HoverCardTrigger","HoverCardPortal","HoverCardContent","HoverCardArrow","Label","ListboxRoot","ListboxContent","ListboxFilter","ListboxItem","ListboxItemIndicator","ListboxVirtualizer","ListboxGroup","ListboxGroupLabel","MenubarRoot","MenubarTrigger","MenubarPortal","MenubarContent","MenubarArrow","MenubarItem","MenubarGroup","MenubarSeparator","MenubarCheckboxItem","MenubarItemIndicator","MenubarLabel","MenubarRadioGroup","MenubarRadioItem","MenubarSub","MenubarSubContent","MenubarSubTrigger","MenubarMenu","NavigationMenuRoot","NavigationMenuContent","NavigationMenuIndicator","NavigationMenuItem","NavigationMenuLink","NavigationMenuList","NavigationMenuSub","NavigationMenuTrigger","NavigationMenuViewport","NumberFieldRoot","NumberFieldInput","NumberFieldIncrement","NumberFieldDecrement","PaginationRoot","PaginationEllipsis","PaginationFirst","PaginationLast","PaginationList","PaginationListItem","PaginationNext","PaginationPrev","PinInputRoot","PinInputInput","PopoverRoot","PopoverTrigger","PopoverPortal","PopoverContent","PopoverArrow","PopoverClose","PopoverAnchor","ProgressRoot","ProgressIndicator","RadioGroupRoot","RadioGroupItem","RadioGroupIndicator","RangeCalendarRoot","RangeCalendarHeader","RangeCalendarHeading","RangeCalendarGrid","RangeCalendarCell","RangeCalendarHeadCell","RangeCalendarNext","RangeCalendarPrev","RangeCalendarGridHead","RangeCalendarGridBody","RangeCalendarGridRow","RangeCalendarCellTrigger","ScrollAreaRoot","ScrollAreaViewport","ScrollAreaScrollbar","ScrollAreaThumb","ScrollAreaCorner","SelectRoot","SelectTrigger","SelectPortal","SelectContent","SelectArrow","SelectSeparator","SelectItemIndicator","SelectLabel","SelectGroup","SelectItem","SelectItemText","SelectViewport","SelectScrollUpButton","SelectScrollDownButton","SelectValue","SelectIcon","Separator","SliderRoot","SliderThumb","SliderTrack","SliderRange","SplitterGroup","SplitterPanel","SplitterResizeHandle","StepperRoot","StepperItem","StepperTrigger","StepperDescription","StepperTitle","StepperIndicator","StepperSeparator","SwitchRoot","SwitchThumb","TabsRoot","TabsList","TabsContent","TabsTrigger","TabsIndicator","TagsInputRoot","TagsInputInput","TagsInputItem","TagsInputItemText","TagsInputItemDelete","TagsInputClear","TimeFieldInput","TimeFieldRoot","ToastProvider","ToastRoot","ToastPortal","ToastAction","ToastClose","ToastViewport","ToastTitle","ToastDescription","Toggle","ToggleGroupRoot","ToggleGroupItem","ToolbarRoot","ToolbarButton","ToolbarLink","ToolbarToggleGroup","ToolbarToggleItem","ToolbarSeparator","TooltipRoot","TooltipTrigger","TooltipContent","TooltipArrow","TooltipPortal","TooltipProvider","TreeRoot","TreeItem","TreeVirtualizer","Viewport","ConfigProvider","FocusScope","RovingFocusGroup","RovingFocusItem","Presence","Primitive","Slot","VisuallyHidden","NuxtLinkLocale","SwitchLocalePathLink","ContentRenderer","MDC","MDCRenderer","MDCSlot","VitePwaManifest","NuxtPwaManifest","NuxtPwaAssets","PwaAppleImage","PwaAppleSplashScreenImage","PwaFaviconImage","PwaMaskableImage","PwaTransparentImage","ColorScheme","NuxtPage","NoScript","Link","Base","Title","Meta","Style","Head","Html","Body","NuxtIsland"]