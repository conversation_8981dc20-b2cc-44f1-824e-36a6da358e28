(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
	typeof define === 'function' && define.amd ? define(factory) :
	(global = global || self, global.countryFlagEmoji = factory());
}(this, (function () { 'use strict';

	function createCommonjsModule(fn, module) {
		return module = { exports: {} }, fn(module, module.exports), module.exports;
	}

	var _global = createCommonjsModule(function (module) {
	// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
	var global = module.exports = typeof window != 'undefined' && window.Math == Math
	  ? window : typeof self != 'undefined' && self.Math == Math ? self
	  // eslint-disable-next-line no-new-func
	  : Function('return this')();
	if (typeof __g == 'number') __g = global; // eslint-disable-line no-undef
	});

	var _core = createCommonjsModule(function (module) {
	var core = module.exports = { version: '2.6.0' };
	if (typeof __e == 'number') __e = core; // eslint-disable-line no-undef
	});
	var _core_1 = _core.version;

	var _isObject = function (it) {
	  return typeof it === 'object' ? it !== null : typeof it === 'function';
	};

	var _anObject = function (it) {
	  if (!_isObject(it)) throw TypeError(it + ' is not an object!');
	  return it;
	};

	var _fails = function (exec) {
	  try {
	    return !!exec();
	  } catch (e) {
	    return true;
	  }
	};

	// Thank's IE8 for his funny defineProperty
	var _descriptors = !_fails(function () {
	  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;
	});

	var document = _global.document;
	// typeof document.createElement is 'object' in old IE
	var is = _isObject(document) && _isObject(document.createElement);
	var _domCreate = function (it) {
	  return is ? document.createElement(it) : {};
	};

	var _ie8DomDefine = !_descriptors && !_fails(function () {
	  return Object.defineProperty(_domCreate('div'), 'a', { get: function () { return 7; } }).a != 7;
	});

	// 7.1.1 ToPrimitive(input [, PreferredType])

	// instead of the ES6 spec version, we didn't implement @@toPrimitive case
	// and the second argument - flag - preferred type is a string
	var _toPrimitive = function (it, S) {
	  if (!_isObject(it)) return it;
	  var fn, val;
	  if (S && typeof (fn = it.toString) == 'function' && !_isObject(val = fn.call(it))) return val;
	  if (typeof (fn = it.valueOf) == 'function' && !_isObject(val = fn.call(it))) return val;
	  if (!S && typeof (fn = it.toString) == 'function' && !_isObject(val = fn.call(it))) return val;
	  throw TypeError("Can't convert object to primitive value");
	};

	var dP = Object.defineProperty;

	var f = _descriptors ? Object.defineProperty : function defineProperty(O, P, Attributes) {
	  _anObject(O);
	  P = _toPrimitive(P, true);
	  _anObject(Attributes);
	  if (_ie8DomDefine) try {
	    return dP(O, P, Attributes);
	  } catch (e) { /* empty */ }
	  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported!');
	  if ('value' in Attributes) O[P] = Attributes.value;
	  return O;
	};

	var _objectDp = {
		f: f
	};

	var _propertyDesc = function (bitmap, value) {
	  return {
	    enumerable: !(bitmap & 1),
	    configurable: !(bitmap & 2),
	    writable: !(bitmap & 4),
	    value: value
	  };
	};

	var _hide = _descriptors ? function (object, key, value) {
	  return _objectDp.f(object, key, _propertyDesc(1, value));
	} : function (object, key, value) {
	  object[key] = value;
	  return object;
	};

	var hasOwnProperty = {}.hasOwnProperty;
	var _has = function (it, key) {
	  return hasOwnProperty.call(it, key);
	};

	var id = 0;
	var px = Math.random();
	var _uid = function (key) {
	  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + px).toString(36));
	};

	var _redefine = createCommonjsModule(function (module) {
	var SRC = _uid('src');
	var TO_STRING = 'toString';
	var $toString = Function[TO_STRING];
	var TPL = ('' + $toString).split(TO_STRING);

	_core.inspectSource = function (it) {
	  return $toString.call(it);
	};

	(module.exports = function (O, key, val, safe) {
	  var isFunction = typeof val == 'function';
	  if (isFunction) _has(val, 'name') || _hide(val, 'name', key);
	  if (O[key] === val) return;
	  if (isFunction) _has(val, SRC) || _hide(val, SRC, O[key] ? '' + O[key] : TPL.join(String(key)));
	  if (O === _global) {
	    O[key] = val;
	  } else if (!safe) {
	    delete O[key];
	    _hide(O, key, val);
	  } else if (O[key]) {
	    O[key] = val;
	  } else {
	    _hide(O, key, val);
	  }
	// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative
	})(Function.prototype, TO_STRING, function toString() {
	  return typeof this == 'function' && this[SRC] || $toString.call(this);
	});
	});

	var _aFunction = function (it) {
	  if (typeof it != 'function') throw TypeError(it + ' is not a function!');
	  return it;
	};

	// optional / simple context binding

	var _ctx = function (fn, that, length) {
	  _aFunction(fn);
	  if (that === undefined) return fn;
	  switch (length) {
	    case 1: return function (a) {
	      return fn.call(that, a);
	    };
	    case 2: return function (a, b) {
	      return fn.call(that, a, b);
	    };
	    case 3: return function (a, b, c) {
	      return fn.call(that, a, b, c);
	    };
	  }
	  return function (/* ...args */) {
	    return fn.apply(that, arguments);
	  };
	};

	var PROTOTYPE = 'prototype';

	var $export = function (type, name, source) {
	  var IS_FORCED = type & $export.F;
	  var IS_GLOBAL = type & $export.G;
	  var IS_STATIC = type & $export.S;
	  var IS_PROTO = type & $export.P;
	  var IS_BIND = type & $export.B;
	  var target = IS_GLOBAL ? _global : IS_STATIC ? _global[name] || (_global[name] = {}) : (_global[name] || {})[PROTOTYPE];
	  var exports = IS_GLOBAL ? _core : _core[name] || (_core[name] = {});
	  var expProto = exports[PROTOTYPE] || (exports[PROTOTYPE] = {});
	  var key, own, out, exp;
	  if (IS_GLOBAL) source = name;
	  for (key in source) {
	    // contains in native
	    own = !IS_FORCED && target && target[key] !== undefined;
	    // export native or passed
	    out = (own ? target : source)[key];
	    // bind timers to global for call from export context
	    exp = IS_BIND && own ? _ctx(out, _global) : IS_PROTO && typeof out == 'function' ? _ctx(Function.call, out) : out;
	    // extend global
	    if (target) _redefine(target, key, out, type & $export.U);
	    // export
	    if (exports[key] != out) _hide(exports, key, exp);
	    if (IS_PROTO && expProto[key] != out) expProto[key] = out;
	  }
	};
	_global.core = _core;
	// type bitmap
	$export.F = 1;   // forced
	$export.G = 2;   // global
	$export.S = 4;   // static
	$export.P = 8;   // proto
	$export.B = 16;  // bind
	$export.W = 32;  // wrap
	$export.U = 64;  // safe
	$export.R = 128; // real proto method for `library`
	var _export = $export;

	var toString = {}.toString;

	var _cof = function (it) {
	  return toString.call(it).slice(8, -1);
	};

	// fallback for non-array-like ES3 and non-enumerable old V8 strings

	// eslint-disable-next-line no-prototype-builtins
	var _iobject = Object('z').propertyIsEnumerable(0) ? Object : function (it) {
	  return _cof(it) == 'String' ? it.split('') : Object(it);
	};

	// 7.2.1 RequireObjectCoercible(argument)
	var _defined = function (it) {
	  if (it == undefined) throw TypeError("Can't call method on  " + it);
	  return it;
	};

	// to indexed object, toObject with fallback for non-array-like ES3 strings


	var _toIobject = function (it) {
	  return _iobject(_defined(it));
	};

	// 7.1.4 ToInteger
	var ceil = Math.ceil;
	var floor = Math.floor;
	var _toInteger = function (it) {
	  return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);
	};

	// 7.1.15 ToLength

	var min = Math.min;
	var _toLength = function (it) {
	  return it > 0 ? min(_toInteger(it), 0x1fffffffffffff) : 0; // pow(2, 53) - 1 == 9007199254740991
	};

	var max = Math.max;
	var min$1 = Math.min;
	var _toAbsoluteIndex = function (index, length) {
	  index = _toInteger(index);
	  return index < 0 ? max(index + length, 0) : min$1(index, length);
	};

	// false -> Array#indexOf
	// true  -> Array#includes



	var _arrayIncludes = function (IS_INCLUDES) {
	  return function ($this, el, fromIndex) {
	    var O = _toIobject($this);
	    var length = _toLength(O.length);
	    var index = _toAbsoluteIndex(fromIndex, length);
	    var value;
	    // Array#includes uses SameValueZero equality algorithm
	    // eslint-disable-next-line no-self-compare
	    if (IS_INCLUDES && el != el) while (length > index) {
	      value = O[index++];
	      // eslint-disable-next-line no-self-compare
	      if (value != value) return true;
	    // Array#indexOf ignores holes, Array#includes - not
	    } else for (;length > index; index++) if (IS_INCLUDES || index in O) {
	      if (O[index] === el) return IS_INCLUDES || index || 0;
	    } return !IS_INCLUDES && -1;
	  };
	};

	var _shared = createCommonjsModule(function (module) {
	var SHARED = '__core-js_shared__';
	var store = _global[SHARED] || (_global[SHARED] = {});

	(module.exports = function (key, value) {
	  return store[key] || (store[key] = value !== undefined ? value : {});
	})('versions', []).push({
	  version: _core.version,
	  mode:  'global',
	  copyright: '© 2018 Denis Pushkarev (zloirock.ru)'
	});
	});

	var shared = _shared('keys');

	var _sharedKey = function (key) {
	  return shared[key] || (shared[key] = _uid(key));
	};

	var arrayIndexOf = _arrayIncludes(false);
	var IE_PROTO = _sharedKey('IE_PROTO');

	var _objectKeysInternal = function (object, names) {
	  var O = _toIobject(object);
	  var i = 0;
	  var result = [];
	  var key;
	  for (key in O) if (key != IE_PROTO) _has(O, key) && result.push(key);
	  // Don't enum bug & hidden keys
	  while (names.length > i) if (_has(O, key = names[i++])) {
	    ~arrayIndexOf(result, key) || result.push(key);
	  }
	  return result;
	};

	// IE 8- don't enum bug keys
	var _enumBugKeys = (
	  'constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf'
	).split(',');

	// 19.1.2.14 / 15.2.3.14 Object.keys(O)



	var _objectKeys = Object.keys || function keys(O) {
	  return _objectKeysInternal(O, _enumBugKeys);
	};

	var f$1 = {}.propertyIsEnumerable;

	var _objectPie = {
		f: f$1
	};

	var isEnum = _objectPie.f;
	var _objectToArray = function (isEntries) {
	  return function (it) {
	    var O = _toIobject(it);
	    var keys = _objectKeys(O);
	    var length = keys.length;
	    var i = 0;
	    var result = [];
	    var key;
	    while (length > i) if (isEnum.call(O, key = keys[i++])) {
	      result.push(isEntries ? [key, O[key]] : O[key]);
	    } return result;
	  };
	};

	// https://github.com/tc39/proposal-object-values-entries

	var $values = _objectToArray(false);

	_export(_export.S, 'Object', {
	  values: function values(it) {
	    return $values(it);
	  }
	});

	var _wks = createCommonjsModule(function (module) {
	var store = _shared('wks');

	var Symbol = _global.Symbol;
	var USE_SYMBOL = typeof Symbol == 'function';

	var $exports = module.exports = function (name) {
	  return store[name] || (store[name] =
	    USE_SYMBOL && Symbol[name] || (USE_SYMBOL ? Symbol : _uid)('Symbol.' + name));
	};

	$exports.store = store;
	});

	// ********* Array.prototype[@@unscopables]
	var UNSCOPABLES = _wks('unscopables');
	var ArrayProto = Array.prototype;
	if (ArrayProto[UNSCOPABLES] == undefined) _hide(ArrayProto, UNSCOPABLES, {});
	var _addToUnscopables = function (key) {
	  ArrayProto[UNSCOPABLES][key] = true;
	};

	var _iterStep = function (done, value) {
	  return { value: value, done: !!done };
	};

	var _iterators = {};

	var _objectDps = _descriptors ? Object.defineProperties : function defineProperties(O, Properties) {
	  _anObject(O);
	  var keys = _objectKeys(Properties);
	  var length = keys.length;
	  var i = 0;
	  var P;
	  while (length > i) _objectDp.f(O, P = keys[i++], Properties[P]);
	  return O;
	};

	var document$1 = _global.document;
	var _html = document$1 && document$1.documentElement;

	// ******** / ******** Object.create(O [, Properties])



	var IE_PROTO$1 = _sharedKey('IE_PROTO');
	var Empty = function () { /* empty */ };
	var PROTOTYPE$1 = 'prototype';

	// Create object with fake `null` prototype: use iframe Object with cleared prototype
	var createDict = function () {
	  // Thrash, waste and sodomy: IE GC bug
	  var iframe = _domCreate('iframe');
	  var i = _enumBugKeys.length;
	  var lt = '<';
	  var gt = '>';
	  var iframeDocument;
	  iframe.style.display = 'none';
	  _html.appendChild(iframe);
	  iframe.src = 'javascript:'; // eslint-disable-line no-script-url
	  // createDict = iframe.contentWindow.Object;
	  // html.removeChild(iframe);
	  iframeDocument = iframe.contentWindow.document;
	  iframeDocument.open();
	  iframeDocument.write(lt + 'script' + gt + 'document.F=Object' + lt + '/script' + gt);
	  iframeDocument.close();
	  createDict = iframeDocument.F;
	  while (i--) delete createDict[PROTOTYPE$1][_enumBugKeys[i]];
	  return createDict();
	};

	var _objectCreate = Object.create || function create(O, Properties) {
	  var result;
	  if (O !== null) {
	    Empty[PROTOTYPE$1] = _anObject(O);
	    result = new Empty();
	    Empty[PROTOTYPE$1] = null;
	    // add "__proto__" for Object.getPrototypeOf polyfill
	    result[IE_PROTO$1] = O;
	  } else result = createDict();
	  return Properties === undefined ? result : _objectDps(result, Properties);
	};

	var def = _objectDp.f;

	var TAG = _wks('toStringTag');

	var _setToStringTag = function (it, tag, stat) {
	  if (it && !_has(it = stat ? it : it.prototype, TAG)) def(it, TAG, { configurable: true, value: tag });
	};

	var IteratorPrototype = {};

	// ********.1 %IteratorPrototype%[@@iterator]()
	_hide(IteratorPrototype, _wks('iterator'), function () { return this; });

	var _iterCreate = function (Constructor, NAME, next) {
	  Constructor.prototype = _objectCreate(IteratorPrototype, { next: _propertyDesc(1, next) });
	  _setToStringTag(Constructor, NAME + ' Iterator');
	};

	// 7.1.13 ToObject(argument)

	var _toObject = function (it) {
	  return Object(_defined(it));
	};

	// 19.1.2.9 / 15.2.3.2 Object.getPrototypeOf(O)


	var IE_PROTO$2 = _sharedKey('IE_PROTO');
	var ObjectProto = Object.prototype;

	var _objectGpo = Object.getPrototypeOf || function (O) {
	  O = _toObject(O);
	  if (_has(O, IE_PROTO$2)) return O[IE_PROTO$2];
	  if (typeof O.constructor == 'function' && O instanceof O.constructor) {
	    return O.constructor.prototype;
	  } return O instanceof Object ? ObjectProto : null;
	};

	var ITERATOR = _wks('iterator');
	var BUGGY = !([].keys && 'next' in [].keys()); // Safari has buggy iterators w/o `next`
	var FF_ITERATOR = '@@iterator';
	var KEYS = 'keys';
	var VALUES = 'values';

	var returnThis = function () { return this; };

	var _iterDefine = function (Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED) {
	  _iterCreate(Constructor, NAME, next);
	  var getMethod = function (kind) {
	    if (!BUGGY && kind in proto) return proto[kind];
	    switch (kind) {
	      case KEYS: return function keys() { return new Constructor(this, kind); };
	      case VALUES: return function values() { return new Constructor(this, kind); };
	    } return function entries() { return new Constructor(this, kind); };
	  };
	  var TAG = NAME + ' Iterator';
	  var DEF_VALUES = DEFAULT == VALUES;
	  var VALUES_BUG = false;
	  var proto = Base.prototype;
	  var $native = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT];
	  var $default = $native || getMethod(DEFAULT);
	  var $entries = DEFAULT ? !DEF_VALUES ? $default : getMethod('entries') : undefined;
	  var $anyNative = NAME == 'Array' ? proto.entries || $native : $native;
	  var methods, key, IteratorPrototype;
	  // Fix native
	  if ($anyNative) {
	    IteratorPrototype = _objectGpo($anyNative.call(new Base()));
	    if (IteratorPrototype !== Object.prototype && IteratorPrototype.next) {
	      // Set @@toStringTag to native iterators
	      _setToStringTag(IteratorPrototype, TAG, true);
	      // fix for some old engines
	      if ( typeof IteratorPrototype[ITERATOR] != 'function') _hide(IteratorPrototype, ITERATOR, returnThis);
	    }
	  }
	  // fix Array#{values, @@iterator}.name in V8 / FF
	  if (DEF_VALUES && $native && $native.name !== VALUES) {
	    VALUES_BUG = true;
	    $default = function values() { return $native.call(this); };
	  }
	  // Define iterator
	  if ( (BUGGY || VALUES_BUG || !proto[ITERATOR])) {
	    _hide(proto, ITERATOR, $default);
	  }
	  // Plug for library
	  _iterators[NAME] = $default;
	  _iterators[TAG] = returnThis;
	  if (DEFAULT) {
	    methods = {
	      values: DEF_VALUES ? $default : getMethod(VALUES),
	      keys: IS_SET ? $default : getMethod(KEYS),
	      entries: $entries
	    };
	    if (FORCED) for (key in methods) {
	      if (!(key in proto)) _redefine(proto, key, methods[key]);
	    } else _export(_export.P + _export.F * (BUGGY || VALUES_BUG), NAME, methods);
	  }
	  return methods;
	};

	// 22.1.3.4 Array.prototype.entries()
	// 22.1.3.13 Array.prototype.keys()
	// 22.1.3.29 Array.prototype.values()
	// 22.1.3.30 Array.prototype[@@iterator]()
	var es6_array_iterator = _iterDefine(Array, 'Array', function (iterated, kind) {
	  this._t = _toIobject(iterated); // target
	  this._i = 0;                   // next index
	  this._k = kind;                // kind
	// 22.1.5.2.1 %ArrayIteratorPrototype%.next()
	}, function () {
	  var O = this._t;
	  var kind = this._k;
	  var index = this._i++;
	  if (!O || index >= O.length) {
	    this._t = undefined;
	    return _iterStep(1);
	  }
	  if (kind == 'keys') return _iterStep(0, index);
	  if (kind == 'values') return _iterStep(0, O[index]);
	  return _iterStep(0, [index, O[index]]);
	}, 'values');

	// argumentsList[@@iterator] is %ArrayProto_values% (9.4.4.6, 9.4.4.7)
	_iterators.Arguments = _iterators.Array;

	_addToUnscopables('keys');
	_addToUnscopables('values');
	_addToUnscopables('entries');

	var ITERATOR$1 = _wks('iterator');
	var TO_STRING_TAG = _wks('toStringTag');
	var ArrayValues = _iterators.Array;

	var DOMIterables = {
	  CSSRuleList: true, // TODO: Not spec compliant, should be false.
	  CSSStyleDeclaration: false,
	  CSSValueList: false,
	  ClientRectList: false,
	  DOMRectList: false,
	  DOMStringList: false,
	  DOMTokenList: true,
	  DataTransferItemList: false,
	  FileList: false,
	  HTMLAllCollection: false,
	  HTMLCollection: false,
	  HTMLFormElement: false,
	  HTMLSelectElement: false,
	  MediaList: true, // TODO: Not spec compliant, should be false.
	  MimeTypeArray: false,
	  NamedNodeMap: false,
	  NodeList: true,
	  PaintRequestList: false,
	  Plugin: false,
	  PluginArray: false,
	  SVGLengthList: false,
	  SVGNumberList: false,
	  SVGPathSegList: false,
	  SVGPointList: false,
	  SVGStringList: false,
	  SVGTransformList: false,
	  SourceBufferList: false,
	  StyleSheetList: true, // TODO: Not spec compliant, should be false.
	  TextTrackCueList: false,
	  TextTrackList: false,
	  TouchList: false
	};

	for (var collections = _objectKeys(DOMIterables), i = 0; i < collections.length; i++) {
	  var NAME = collections[i];
	  var explicit = DOMIterables[NAME];
	  var Collection = _global[NAME];
	  var proto = Collection && Collection.prototype;
	  var key;
	  if (proto) {
	    if (!proto[ITERATOR$1]) _hide(proto, ITERATOR$1, ArrayValues);
	    if (!proto[TO_STRING_TAG]) _hide(proto, TO_STRING_TAG, NAME);
	    _iterators[NAME] = ArrayValues;
	    if (explicit) for (key in es6_array_iterator) if (!proto[key]) _redefine(proto, key, es6_array_iterator[key], true);
	  }
	}

	// most Object methods by ES6 should accept primitives



	var _objectSap = function (KEY, exec) {
	  var fn = (_core.Object || {})[KEY] || Object[KEY];
	  var exp = {};
	  exp[KEY] = exec(fn);
	  _export(_export.S + _export.F * _fails(function () { fn(1); }), 'Object', exp);
	};

	// 19.1.2.14 Object.keys(O)



	_objectSap('keys', function () {
	  return function keys(it) {
	    return _objectKeys(_toObject(it));
	  };
	});

	var data = {
	  AC: {
	    code: "AC",
	    unicode: "U+1F1E6 U+1F1E8",
	    name: "Ascension Island",
	    emoji: "🇦🇨"
	  },
	  AD: {
	    code: "AD",
	    unicode: "U+1F1E6 U+1F1E9",
	    name: "Andorra",
	    emoji: "🇦🇩"
	  },
	  AE: {
	    code: "AE",
	    unicode: "U+1F1E6 U+1F1EA",
	    name: "United Arab Emirates",
	    emoji: "🇦🇪"
	  },
	  AF: {
	    code: "AF",
	    unicode: "U+1F1E6 U+1F1EB",
	    name: "Afghanistan",
	    emoji: "🇦🇫"
	  },
	  AG: {
	    code: "AG",
	    unicode: "U+1F1E6 U+1F1EC",
	    name: "Antigua & Barbuda",
	    emoji: "🇦🇬"
	  },
	  AI: {
	    code: "AI",
	    unicode: "U+1F1E6 U+1F1EE",
	    name: "Anguilla",
	    emoji: "🇦🇮"
	  },
	  AL: {
	    code: "AL",
	    unicode: "U+1F1E6 U+1F1F1",
	    name: "Albania",
	    emoji: "🇦🇱"
	  },
	  AM: {
	    code: "AM",
	    unicode: "U+1F1E6 U+1F1F2",
	    name: "Armenia",
	    emoji: "🇦🇲"
	  },
	  AO: {
	    code: "AO",
	    unicode: "U+1F1E6 U+1F1F4",
	    name: "Angola",
	    emoji: "🇦🇴"
	  },
	  AQ: {
	    code: "AQ",
	    unicode: "U+1F1E6 U+1F1F6",
	    name: "Antarctica",
	    emoji: "🇦🇶"
	  },
	  AR: {
	    code: "AR",
	    unicode: "U+1F1E6 U+1F1F7",
	    name: "Argentina",
	    emoji: "🇦🇷"
	  },
	  AS: {
	    code: "AS",
	    unicode: "U+1F1E6 U+1F1F8",
	    name: "American Samoa",
	    emoji: "🇦🇸"
	  },
	  AT: {
	    code: "AT",
	    unicode: "U+1F1E6 U+1F1F9",
	    name: "Austria",
	    emoji: "🇦🇹"
	  },
	  AU: {
	    code: "AU",
	    unicode: "U+1F1E6 U+1F1FA",
	    name: "Australia",
	    emoji: "🇦🇺"
	  },
	  AW: {
	    code: "AW",
	    unicode: "U+1F1E6 U+1F1FC",
	    name: "Aruba",
	    emoji: "🇦🇼"
	  },
	  AX: {
	    code: "AX",
	    unicode: "U+1F1E6 U+1F1FD",
	    name: "Åland Islands",
	    emoji: "🇦🇽"
	  },
	  AZ: {
	    code: "AZ",
	    unicode: "U+1F1E6 U+1F1FF",
	    name: "Azerbaijan",
	    emoji: "🇦🇿"
	  },
	  BA: {
	    code: "BA",
	    unicode: "U+1F1E7 U+1F1E6",
	    name: "Bosnia & Herzegovina",
	    emoji: "🇧🇦"
	  },
	  BB: {
	    code: "BB",
	    unicode: "U+1F1E7 U+1F1E7",
	    name: "Barbados",
	    emoji: "🇧🇧"
	  },
	  BD: {
	    code: "BD",
	    unicode: "U+1F1E7 U+1F1E9",
	    name: "Bangladesh",
	    emoji: "🇧🇩"
	  },
	  BE: {
	    code: "BE",
	    unicode: "U+1F1E7 U+1F1EA",
	    name: "Belgium",
	    emoji: "🇧🇪"
	  },
	  BF: {
	    code: "BF",
	    unicode: "U+1F1E7 U+1F1EB",
	    name: "Burkina Faso",
	    emoji: "🇧🇫"
	  },
	  BG: {
	    code: "BG",
	    unicode: "U+1F1E7 U+1F1EC",
	    name: "Bulgaria",
	    emoji: "🇧🇬"
	  },
	  BH: {
	    code: "BH",
	    unicode: "U+1F1E7 U+1F1ED",
	    name: "Bahrain",
	    emoji: "🇧🇭"
	  },
	  BI: {
	    code: "BI",
	    unicode: "U+1F1E7 U+1F1EE",
	    name: "Burundi",
	    emoji: "🇧🇮"
	  },
	  BJ: {
	    code: "BJ",
	    unicode: "U+1F1E7 U+1F1EF",
	    name: "Benin",
	    emoji: "🇧🇯"
	  },
	  BL: {
	    code: "BL",
	    unicode: "U+1F1E7 U+1F1F1",
	    name: "St. Barthélemy",
	    emoji: "🇧🇱"
	  },
	  BM: {
	    code: "BM",
	    unicode: "U+1F1E7 U+1F1F2",
	    name: "Bermuda",
	    emoji: "🇧🇲"
	  },
	  BN: {
	    code: "BN",
	    unicode: "U+1F1E7 U+1F1F3",
	    name: "Brunei",
	    emoji: "🇧🇳"
	  },
	  BO: {
	    code: "BO",
	    unicode: "U+1F1E7 U+1F1F4",
	    name: "Bolivia",
	    emoji: "🇧🇴"
	  },
	  BQ: {
	    code: "BQ",
	    unicode: "U+1F1E7 U+1F1F6",
	    name: "Caribbean Netherlands",
	    emoji: "🇧🇶"
	  },
	  BR: {
	    code: "BR",
	    unicode: "U+1F1E7 U+1F1F7",
	    name: "Brazil",
	    emoji: "🇧🇷"
	  },
	  BS: {
	    code: "BS",
	    unicode: "U+1F1E7 U+1F1F8",
	    name: "Bahamas",
	    emoji: "🇧🇸"
	  },
	  BT: {
	    code: "BT",
	    unicode: "U+1F1E7 U+1F1F9",
	    name: "Bhutan",
	    emoji: "🇧🇹"
	  },
	  BV: {
	    code: "BV",
	    unicode: "U+1F1E7 U+1F1FB",
	    name: "Bouvet Island",
	    emoji: "🇧🇻"
	  },
	  BW: {
	    code: "BW",
	    unicode: "U+1F1E7 U+1F1FC",
	    name: "Botswana",
	    emoji: "🇧🇼"
	  },
	  BY: {
	    code: "BY",
	    unicode: "U+1F1E7 U+1F1FE",
	    name: "Belarus",
	    emoji: "🇧🇾"
	  },
	  BZ: {
	    code: "BZ",
	    unicode: "U+1F1E7 U+1F1FF",
	    name: "Belize",
	    emoji: "🇧🇿"
	  },
	  CA: {
	    code: "CA",
	    unicode: "U+1F1E8 U+1F1E6",
	    name: "Canada",
	    emoji: "🇨🇦"
	  },
	  CC: {
	    code: "CC",
	    unicode: "U+1F1E8 U+1F1E8",
	    name: "Cocos (Keeling) Islands",
	    emoji: "🇨🇨"
	  },
	  CD: {
	    code: "CD",
	    unicode: "U+1F1E8 U+1F1E9",
	    name: "Congo - Kinshasa",
	    emoji: "🇨🇩"
	  },
	  CF: {
	    code: "CF",
	    unicode: "U+1F1E8 U+1F1EB",
	    name: "Central African Republic",
	    emoji: "🇨🇫"
	  },
	  CG: {
	    code: "CG",
	    unicode: "U+1F1E8 U+1F1EC",
	    name: "Congo - Brazzaville",
	    emoji: "🇨🇬"
	  },
	  CH: {
	    code: "CH",
	    unicode: "U+1F1E8 U+1F1ED",
	    name: "Switzerland",
	    emoji: "🇨🇭"
	  },
	  CI: {
	    code: "CI",
	    unicode: "U+1F1E8 U+1F1EE",
	    name: "Côte d’Ivoire",
	    emoji: "🇨🇮"
	  },
	  CK: {
	    code: "CK",
	    unicode: "U+1F1E8 U+1F1F0",
	    name: "Cook Islands",
	    emoji: "🇨🇰"
	  },
	  CL: {
	    code: "CL",
	    unicode: "U+1F1E8 U+1F1F1",
	    name: "Chile",
	    emoji: "🇨🇱"
	  },
	  CM: {
	    code: "CM",
	    unicode: "U+1F1E8 U+1F1F2",
	    name: "Cameroon",
	    emoji: "🇨🇲"
	  },
	  CN: {
	    code: "CN",
	    unicode: "U+1F1E8 U+1F1F3",
	    name: "China",
	    emoji: "🇨🇳"
	  },
	  CO: {
	    code: "CO",
	    unicode: "U+1F1E8 U+1F1F4",
	    name: "Colombia",
	    emoji: "🇨🇴"
	  },
	  CP: {
	    code: "CP",
	    unicode: "U+1F1E8 U+1F1F5",
	    name: "Clipperton Island",
	    emoji: "🇨🇵"
	  },
	  CR: {
	    code: "CR",
	    unicode: "U+1F1E8 U+1F1F7",
	    name: "Costa Rica",
	    emoji: "🇨🇷"
	  },
	  CU: {
	    code: "CU",
	    unicode: "U+1F1E8 U+1F1FA",
	    name: "Cuba",
	    emoji: "🇨🇺"
	  },
	  CV: {
	    code: "CV",
	    unicode: "U+1F1E8 U+1F1FB",
	    name: "Cape Verde",
	    emoji: "🇨🇻"
	  },
	  CW: {
	    code: "CW",
	    unicode: "U+1F1E8 U+1F1FC",
	    name: "Curaçao",
	    emoji: "🇨🇼"
	  },
	  CX: {
	    code: "CX",
	    unicode: "U+1F1E8 U+1F1FD",
	    name: "Christmas Island",
	    emoji: "🇨🇽"
	  },
	  CY: {
	    code: "CY",
	    unicode: "U+1F1E8 U+1F1FE",
	    name: "Cyprus",
	    emoji: "🇨🇾"
	  },
	  CZ: {
	    code: "CZ",
	    unicode: "U+1F1E8 U+1F1FF",
	    name: "Czechia",
	    emoji: "🇨🇿"
	  },
	  DE: {
	    code: "DE",
	    unicode: "U+1F1E9 U+1F1EA",
	    name: "Germany",
	    emoji: "🇩🇪"
	  },
	  DG: {
	    code: "DG",
	    unicode: "U+1F1E9 U+1F1EC",
	    name: "Diego Garcia",
	    emoji: "🇩🇬"
	  },
	  DJ: {
	    code: "DJ",
	    unicode: "U+1F1E9 U+1F1EF",
	    name: "Djibouti",
	    emoji: "🇩🇯"
	  },
	  DK: {
	    code: "DK",
	    unicode: "U+1F1E9 U+1F1F0",
	    name: "Denmark",
	    emoji: "🇩🇰"
	  },
	  DM: {
	    code: "DM",
	    unicode: "U+1F1E9 U+1F1F2",
	    name: "Dominica",
	    emoji: "🇩🇲"
	  },
	  DO: {
	    code: "DO",
	    unicode: "U+1F1E9 U+1F1F4",
	    name: "Dominican Republic",
	    emoji: "🇩🇴"
	  },
	  DZ: {
	    code: "DZ",
	    unicode: "U+1F1E9 U+1F1FF",
	    name: "Algeria",
	    emoji: "🇩🇿"
	  },
	  EA: {
	    code: "EA",
	    unicode: "U+1F1EA U+1F1E6",
	    name: "Ceuta & Melilla",
	    emoji: "🇪🇦"
	  },
	  EC: {
	    code: "EC",
	    unicode: "U+1F1EA U+1F1E8",
	    name: "Ecuador",
	    emoji: "🇪🇨"
	  },
	  EE: {
	    code: "EE",
	    unicode: "U+1F1EA U+1F1EA",
	    name: "Estonia",
	    emoji: "🇪🇪"
	  },
	  EG: {
	    code: "EG",
	    unicode: "U+1F1EA U+1F1EC",
	    name: "Egypt",
	    emoji: "🇪🇬"
	  },
	  EH: {
	    code: "EH",
	    unicode: "U+1F1EA U+1F1ED",
	    name: "Western Sahara",
	    emoji: "🇪🇭"
	  },
	  ER: {
	    code: "ER",
	    unicode: "U+1F1EA U+1F1F7",
	    name: "Eritrea",
	    emoji: "🇪🇷"
	  },
	  ES: {
	    code: "ES",
	    unicode: "U+1F1EA U+1F1F8",
	    name: "Spain",
	    emoji: "🇪🇸"
	  },
	  ET: {
	    code: "ET",
	    unicode: "U+1F1EA U+1F1F9",
	    name: "Ethiopia",
	    emoji: "🇪🇹"
	  },
	  EU: {
	    code: "EU",
	    unicode: "U+1F1EA U+1F1FA",
	    name: "European Union",
	    emoji: "🇪🇺"
	  },
	  FI: {
	    code: "FI",
	    unicode: "U+1F1EB U+1F1EE",
	    name: "Finland",
	    emoji: "🇫🇮"
	  },
	  FJ: {
	    code: "FJ",
	    unicode: "U+1F1EB U+1F1EF",
	    name: "Fiji",
	    emoji: "🇫🇯"
	  },
	  FK: {
	    code: "FK",
	    unicode: "U+1F1EB U+1F1F0",
	    name: "Falkland Islands",
	    emoji: "🇫🇰"
	  },
	  FM: {
	    code: "FM",
	    unicode: "U+1F1EB U+1F1F2",
	    name: "Micronesia",
	    emoji: "🇫🇲"
	  },
	  FO: {
	    code: "FO",
	    unicode: "U+1F1EB U+1F1F4",
	    name: "Faroe Islands",
	    emoji: "🇫🇴"
	  },
	  FR: {
	    code: "FR",
	    unicode: "U+1F1EB U+1F1F7",
	    name: "France",
	    emoji: "🇫🇷"
	  },
	  GA: {
	    code: "GA",
	    unicode: "U+1F1EC U+1F1E6",
	    name: "Gabon",
	    emoji: "🇬🇦"
	  },
	  GB: {
	    code: "GB",
	    unicode: "U+1F1EC U+1F1E7",
	    name: "United Kingdom",
	    emoji: "🇬🇧"
	  },
	  GD: {
	    code: "GD",
	    unicode: "U+1F1EC U+1F1E9",
	    name: "Grenada",
	    emoji: "🇬🇩"
	  },
	  GE: {
	    code: "GE",
	    unicode: "U+1F1EC U+1F1EA",
	    name: "Georgia",
	    emoji: "🇬🇪"
	  },
	  GF: {
	    code: "GF",
	    unicode: "U+1F1EC U+1F1EB",
	    name: "French Guiana",
	    emoji: "🇬🇫"
	  },
	  GG: {
	    code: "GG",
	    unicode: "U+1F1EC U+1F1EC",
	    name: "Guernsey",
	    emoji: "🇬🇬"
	  },
	  GH: {
	    code: "GH",
	    unicode: "U+1F1EC U+1F1ED",
	    name: "Ghana",
	    emoji: "🇬🇭"
	  },
	  GI: {
	    code: "GI",
	    unicode: "U+1F1EC U+1F1EE",
	    name: "Gibraltar",
	    emoji: "🇬🇮"
	  },
	  GL: {
	    code: "GL",
	    unicode: "U+1F1EC U+1F1F1",
	    name: "Greenland",
	    emoji: "🇬🇱"
	  },
	  GM: {
	    code: "GM",
	    unicode: "U+1F1EC U+1F1F2",
	    name: "Gambia",
	    emoji: "🇬🇲"
	  },
	  GN: {
	    code: "GN",
	    unicode: "U+1F1EC U+1F1F3",
	    name: "Guinea",
	    emoji: "🇬🇳"
	  },
	  GP: {
	    code: "GP",
	    unicode: "U+1F1EC U+1F1F5",
	    name: "Guadeloupe",
	    emoji: "🇬🇵"
	  },
	  GQ: {
	    code: "GQ",
	    unicode: "U+1F1EC U+1F1F6",
	    name: "Equatorial Guinea",
	    emoji: "🇬🇶"
	  },
	  GR: {
	    code: "GR",
	    unicode: "U+1F1EC U+1F1F7",
	    name: "Greece",
	    emoji: "🇬🇷"
	  },
	  GS: {
	    code: "GS",
	    unicode: "U+1F1EC U+1F1F8",
	    name: "South Georgia & South Sandwich Islands",
	    emoji: "🇬🇸"
	  },
	  GT: {
	    code: "GT",
	    unicode: "U+1F1EC U+1F1F9",
	    name: "Guatemala",
	    emoji: "🇬🇹"
	  },
	  GU: {
	    code: "GU",
	    unicode: "U+1F1EC U+1F1FA",
	    name: "Guam",
	    emoji: "🇬🇺"
	  },
	  GW: {
	    code: "GW",
	    unicode: "U+1F1EC U+1F1FC",
	    name: "Guinea-Bissau",
	    emoji: "🇬🇼"
	  },
	  GY: {
	    code: "GY",
	    unicode: "U+1F1EC U+1F1FE",
	    name: "Guyana",
	    emoji: "🇬🇾"
	  },
	  HK: {
	    code: "HK",
	    unicode: "U+1F1ED U+1F1F0",
	    name: "Hong Kong SAR China",
	    emoji: "🇭🇰"
	  },
	  HM: {
	    code: "HM",
	    unicode: "U+1F1ED U+1F1F2",
	    name: "Heard & McDonald Islands",
	    emoji: "🇭🇲"
	  },
	  HN: {
	    code: "HN",
	    unicode: "U+1F1ED U+1F1F3",
	    name: "Honduras",
	    emoji: "🇭🇳"
	  },
	  HR: {
	    code: "HR",
	    unicode: "U+1F1ED U+1F1F7",
	    name: "Croatia",
	    emoji: "🇭🇷"
	  },
	  HT: {
	    code: "HT",
	    unicode: "U+1F1ED U+1F1F9",
	    name: "Haiti",
	    emoji: "🇭🇹"
	  },
	  HU: {
	    code: "HU",
	    unicode: "U+1F1ED U+1F1FA",
	    name: "Hungary",
	    emoji: "🇭🇺"
	  },
	  IC: {
	    code: "IC",
	    unicode: "U+1F1EE U+1F1E8",
	    name: "Canary Islands",
	    emoji: "🇮🇨"
	  },
	  ID: {
	    code: "ID",
	    unicode: "U+1F1EE U+1F1E9",
	    name: "Indonesia",
	    emoji: "🇮🇩"
	  },
	  IE: {
	    code: "IE",
	    unicode: "U+1F1EE U+1F1EA",
	    name: "Ireland",
	    emoji: "🇮🇪"
	  },
	  IL: {
	    code: "IL",
	    unicode: "U+1F1EE U+1F1F1",
	    name: "Israel",
	    emoji: "🇮🇱"
	  },
	  IM: {
	    code: "IM",
	    unicode: "U+1F1EE U+1F1F2",
	    name: "Isle of Man",
	    emoji: "🇮🇲"
	  },
	  IN: {
	    code: "IN",
	    unicode: "U+1F1EE U+1F1F3",
	    name: "India",
	    emoji: "🇮🇳"
	  },
	  IO: {
	    code: "IO",
	    unicode: "U+1F1EE U+1F1F4",
	    name: "British Indian Ocean Territory",
	    emoji: "🇮🇴"
	  },
	  IQ: {
	    code: "IQ",
	    unicode: "U+1F1EE U+1F1F6",
	    name: "Iraq",
	    emoji: "🇮🇶"
	  },
	  IR: {
	    code: "IR",
	    unicode: "U+1F1EE U+1F1F7",
	    name: "Iran",
	    emoji: "🇮🇷"
	  },
	  IS: {
	    code: "IS",
	    unicode: "U+1F1EE U+1F1F8",
	    name: "Iceland",
	    emoji: "🇮🇸"
	  },
	  IT: {
	    code: "IT",
	    unicode: "U+1F1EE U+1F1F9",
	    name: "Italy",
	    emoji: "🇮🇹"
	  },
	  JE: {
	    code: "JE",
	    unicode: "U+1F1EF U+1F1EA",
	    name: "Jersey",
	    emoji: "🇯🇪"
	  },
	  JM: {
	    code: "JM",
	    unicode: "U+1F1EF U+1F1F2",
	    name: "Jamaica",
	    emoji: "🇯🇲"
	  },
	  JO: {
	    code: "JO",
	    unicode: "U+1F1EF U+1F1F4",
	    name: "Jordan",
	    emoji: "🇯🇴"
	  },
	  JP: {
	    code: "JP",
	    unicode: "U+1F1EF U+1F1F5",
	    name: "Japan",
	    emoji: "🇯🇵"
	  },
	  KE: {
	    code: "KE",
	    unicode: "U+1F1F0 U+1F1EA",
	    name: "Kenya",
	    emoji: "🇰🇪"
	  },
	  KG: {
	    code: "KG",
	    unicode: "U+1F1F0 U+1F1EC",
	    name: "Kyrgyzstan",
	    emoji: "🇰🇬"
	  },
	  KH: {
	    code: "KH",
	    unicode: "U+1F1F0 U+1F1ED",
	    name: "Cambodia",
	    emoji: "🇰🇭"
	  },
	  KI: {
	    code: "KI",
	    unicode: "U+1F1F0 U+1F1EE",
	    name: "Kiribati",
	    emoji: "🇰🇮"
	  },
	  KM: {
	    code: "KM",
	    unicode: "U+1F1F0 U+1F1F2",
	    name: "Comoros",
	    emoji: "🇰🇲"
	  },
	  KN: {
	    code: "KN",
	    unicode: "U+1F1F0 U+1F1F3",
	    name: "St. Kitts & Nevis",
	    emoji: "🇰🇳"
	  },
	  KP: {
	    code: "KP",
	    unicode: "U+1F1F0 U+1F1F5",
	    name: "North Korea",
	    emoji: "🇰🇵"
	  },
	  KR: {
	    code: "KR",
	    unicode: "U+1F1F0 U+1F1F7",
	    name: "South Korea",
	    emoji: "🇰🇷"
	  },
	  KW: {
	    code: "KW",
	    unicode: "U+1F1F0 U+1F1FC",
	    name: "Kuwait",
	    emoji: "🇰🇼"
	  },
	  KY: {
	    code: "KY",
	    unicode: "U+1F1F0 U+1F1FE",
	    name: "Cayman Islands",
	    emoji: "🇰🇾"
	  },
	  KZ: {
	    code: "KZ",
	    unicode: "U+1F1F0 U+1F1FF",
	    name: "Kazakhstan",
	    emoji: "🇰🇿"
	  },
	  LA: {
	    code: "LA",
	    unicode: "U+1F1F1 U+1F1E6",
	    name: "Laos",
	    emoji: "🇱🇦"
	  },
	  LB: {
	    code: "LB",
	    unicode: "U+1F1F1 U+1F1E7",
	    name: "Lebanon",
	    emoji: "🇱🇧"
	  },
	  LC: {
	    code: "LC",
	    unicode: "U+1F1F1 U+1F1E8",
	    name: "St. Lucia",
	    emoji: "🇱🇨"
	  },
	  LI: {
	    code: "LI",
	    unicode: "U+1F1F1 U+1F1EE",
	    name: "Liechtenstein",
	    emoji: "🇱🇮"
	  },
	  LK: {
	    code: "LK",
	    unicode: "U+1F1F1 U+1F1F0",
	    name: "Sri Lanka",
	    emoji: "🇱🇰"
	  },
	  LR: {
	    code: "LR",
	    unicode: "U+1F1F1 U+1F1F7",
	    name: "Liberia",
	    emoji: "🇱🇷"
	  },
	  LS: {
	    code: "LS",
	    unicode: "U+1F1F1 U+1F1F8",
	    name: "Lesotho",
	    emoji: "🇱🇸"
	  },
	  LT: {
	    code: "LT",
	    unicode: "U+1F1F1 U+1F1F9",
	    name: "Lithuania",
	    emoji: "🇱🇹"
	  },
	  LU: {
	    code: "LU",
	    unicode: "U+1F1F1 U+1F1FA",
	    name: "Luxembourg",
	    emoji: "🇱🇺"
	  },
	  LV: {
	    code: "LV",
	    unicode: "U+1F1F1 U+1F1FB",
	    name: "Latvia",
	    emoji: "🇱🇻"
	  },
	  LY: {
	    code: "LY",
	    unicode: "U+1F1F1 U+1F1FE",
	    name: "Libya",
	    emoji: "🇱🇾"
	  },
	  MA: {
	    code: "MA",
	    unicode: "U+1F1F2 U+1F1E6",
	    name: "Morocco",
	    emoji: "🇲🇦"
	  },
	  MC: {
	    code: "MC",
	    unicode: "U+1F1F2 U+1F1E8",
	    name: "Monaco",
	    emoji: "🇲🇨"
	  },
	  MD: {
	    code: "MD",
	    unicode: "U+1F1F2 U+1F1E9",
	    name: "Moldova",
	    emoji: "🇲🇩"
	  },
	  ME: {
	    code: "ME",
	    unicode: "U+1F1F2 U+1F1EA",
	    name: "Montenegro",
	    emoji: "🇲🇪"
	  },
	  MF: {
	    code: "MF",
	    unicode: "U+1F1F2 U+1F1EB",
	    name: "St. Martin",
	    emoji: "🇲🇫"
	  },
	  MG: {
	    code: "MG",
	    unicode: "U+1F1F2 U+1F1EC",
	    name: "Madagascar",
	    emoji: "🇲🇬"
	  },
	  MH: {
	    code: "MH",
	    unicode: "U+1F1F2 U+1F1ED",
	    name: "Marshall Islands",
	    emoji: "🇲🇭"
	  },
	  MK: {
	    code: "MK",
	    unicode: "U+1F1F2 U+1F1F0",
	    name: "Macedonia",
	    emoji: "🇲🇰"
	  },
	  ML: {
	    code: "ML",
	    unicode: "U+1F1F2 U+1F1F1",
	    name: "Mali",
	    emoji: "🇲🇱"
	  },
	  MM: {
	    code: "MM",
	    unicode: "U+1F1F2 U+1F1F2",
	    name: "Myanmar (Burma)",
	    emoji: "🇲🇲"
	  },
	  MN: {
	    code: "MN",
	    unicode: "U+1F1F2 U+1F1F3",
	    name: "Mongolia",
	    emoji: "🇲🇳"
	  },
	  MO: {
	    code: "MO",
	    unicode: "U+1F1F2 U+1F1F4",
	    name: "Macau SAR China",
	    emoji: "🇲🇴"
	  },
	  MP: {
	    code: "MP",
	    unicode: "U+1F1F2 U+1F1F5",
	    name: "Northern Mariana Islands",
	    emoji: "🇲🇵"
	  },
	  MQ: {
	    code: "MQ",
	    unicode: "U+1F1F2 U+1F1F6",
	    name: "Martinique",
	    emoji: "🇲🇶"
	  },
	  MR: {
	    code: "MR",
	    unicode: "U+1F1F2 U+1F1F7",
	    name: "Mauritania",
	    emoji: "🇲🇷"
	  },
	  MS: {
	    code: "MS",
	    unicode: "U+1F1F2 U+1F1F8",
	    name: "Montserrat",
	    emoji: "🇲🇸"
	  },
	  MT: {
	    code: "MT",
	    unicode: "U+1F1F2 U+1F1F9",
	    name: "Malta",
	    emoji: "🇲🇹"
	  },
	  MU: {
	    code: "MU",
	    unicode: "U+1F1F2 U+1F1FA",
	    name: "Mauritius",
	    emoji: "🇲🇺"
	  },
	  MV: {
	    code: "MV",
	    unicode: "U+1F1F2 U+1F1FB",
	    name: "Maldives",
	    emoji: "🇲🇻"
	  },
	  MW: {
	    code: "MW",
	    unicode: "U+1F1F2 U+1F1FC",
	    name: "Malawi",
	    emoji: "🇲🇼"
	  },
	  MX: {
	    code: "MX",
	    unicode: "U+1F1F2 U+1F1FD",
	    name: "Mexico",
	    emoji: "🇲🇽"
	  },
	  MY: {
	    code: "MY",
	    unicode: "U+1F1F2 U+1F1FE",
	    name: "Malaysia",
	    emoji: "🇲🇾"
	  },
	  MZ: {
	    code: "MZ",
	    unicode: "U+1F1F2 U+1F1FF",
	    name: "Mozambique",
	    emoji: "🇲🇿"
	  },
	  NA: {
	    code: "NA",
	    unicode: "U+1F1F3 U+1F1E6",
	    name: "Namibia",
	    emoji: "🇳🇦"
	  },
	  NC: {
	    code: "NC",
	    unicode: "U+1F1F3 U+1F1E8",
	    name: "New Caledonia",
	    emoji: "🇳🇨"
	  },
	  NE: {
	    code: "NE",
	    unicode: "U+1F1F3 U+1F1EA",
	    name: "Niger",
	    emoji: "🇳🇪"
	  },
	  NF: {
	    code: "NF",
	    unicode: "U+1F1F3 U+1F1EB",
	    name: "Norfolk Island",
	    emoji: "🇳🇫"
	  },
	  NG: {
	    code: "NG",
	    unicode: "U+1F1F3 U+1F1EC",
	    name: "Nigeria",
	    emoji: "🇳🇬"
	  },
	  NI: {
	    code: "NI",
	    unicode: "U+1F1F3 U+1F1EE",
	    name: "Nicaragua",
	    emoji: "🇳🇮"
	  },
	  NL: {
	    code: "NL",
	    unicode: "U+1F1F3 U+1F1F1",
	    name: "Netherlands",
	    emoji: "🇳🇱"
	  },
	  NO: {
	    code: "NO",
	    unicode: "U+1F1F3 U+1F1F4",
	    name: "Norway",
	    emoji: "🇳🇴"
	  },
	  NP: {
	    code: "NP",
	    unicode: "U+1F1F3 U+1F1F5",
	    name: "Nepal",
	    emoji: "🇳🇵"
	  },
	  NR: {
	    code: "NR",
	    unicode: "U+1F1F3 U+1F1F7",
	    name: "Nauru",
	    emoji: "🇳🇷"
	  },
	  NU: {
	    code: "NU",
	    unicode: "U+1F1F3 U+1F1FA",
	    name: "Niue",
	    emoji: "🇳🇺"
	  },
	  NZ: {
	    code: "NZ",
	    unicode: "U+1F1F3 U+1F1FF",
	    name: "New Zealand",
	    emoji: "🇳🇿"
	  },
	  OM: {
	    code: "OM",
	    unicode: "U+1F1F4 U+1F1F2",
	    name: "Oman",
	    emoji: "🇴🇲"
	  },
	  PA: {
	    code: "PA",
	    unicode: "U+1F1F5 U+1F1E6",
	    name: "Panama",
	    emoji: "🇵🇦"
	  },
	  PE: {
	    code: "PE",
	    unicode: "U+1F1F5 U+1F1EA",
	    name: "Peru",
	    emoji: "🇵🇪"
	  },
	  PF: {
	    code: "PF",
	    unicode: "U+1F1F5 U+1F1EB",
	    name: "French Polynesia",
	    emoji: "🇵🇫"
	  },
	  PG: {
	    code: "PG",
	    unicode: "U+1F1F5 U+1F1EC",
	    name: "Papua New Guinea",
	    emoji: "🇵🇬"
	  },
	  PH: {
	    code: "PH",
	    unicode: "U+1F1F5 U+1F1ED",
	    name: "Philippines",
	    emoji: "🇵🇭"
	  },
	  PK: {
	    code: "PK",
	    unicode: "U+1F1F5 U+1F1F0",
	    name: "Pakistan",
	    emoji: "🇵🇰"
	  },
	  PL: {
	    code: "PL",
	    unicode: "U+1F1F5 U+1F1F1",
	    name: "Poland",
	    emoji: "🇵🇱"
	  },
	  PM: {
	    code: "PM",
	    unicode: "U+1F1F5 U+1F1F2",
	    name: "St. Pierre & Miquelon",
	    emoji: "🇵🇲"
	  },
	  PN: {
	    code: "PN",
	    unicode: "U+1F1F5 U+1F1F3",
	    name: "Pitcairn Islands",
	    emoji: "🇵🇳"
	  },
	  PR: {
	    code: "PR",
	    unicode: "U+1F1F5 U+1F1F7",
	    name: "Puerto Rico",
	    emoji: "🇵🇷"
	  },
	  PS: {
	    code: "PS",
	    unicode: "U+1F1F5 U+1F1F8",
	    name: "Palestinian Territories",
	    emoji: "🇵🇸"
	  },
	  PT: {
	    code: "PT",
	    unicode: "U+1F1F5 U+1F1F9",
	    name: "Portugal",
	    emoji: "🇵🇹"
	  },
	  PW: {
	    code: "PW",
	    unicode: "U+1F1F5 U+1F1FC",
	    name: "Palau",
	    emoji: "🇵🇼"
	  },
	  PY: {
	    code: "PY",
	    unicode: "U+1F1F5 U+1F1FE",
	    name: "Paraguay",
	    emoji: "🇵🇾"
	  },
	  QA: {
	    code: "QA",
	    unicode: "U+1F1F6 U+1F1E6",
	    name: "Qatar",
	    emoji: "🇶🇦"
	  },
	  RE: {
	    code: "RE",
	    unicode: "U+1F1F7 U+1F1EA",
	    name: "Réunion",
	    emoji: "🇷🇪"
	  },
	  RO: {
	    code: "RO",
	    unicode: "U+1F1F7 U+1F1F4",
	    name: "Romania",
	    emoji: "🇷🇴"
	  },
	  RS: {
	    code: "RS",
	    unicode: "U+1F1F7 U+1F1F8",
	    name: "Serbia",
	    emoji: "🇷🇸"
	  },
	  RU: {
	    code: "RU",
	    unicode: "U+1F1F7 U+1F1FA",
	    name: "Russia",
	    emoji: "🇷🇺"
	  },
	  RW: {
	    code: "RW",
	    unicode: "U+1F1F7 U+1F1FC",
	    name: "Rwanda",
	    emoji: "🇷🇼"
	  },
	  SA: {
	    code: "SA",
	    unicode: "U+1F1F8 U+1F1E6",
	    name: "Saudi Arabia",
	    emoji: "🇸🇦"
	  },
	  SB: {
	    code: "SB",
	    unicode: "U+1F1F8 U+1F1E7",
	    name: "Solomon Islands",
	    emoji: "🇸🇧"
	  },
	  SC: {
	    code: "SC",
	    unicode: "U+1F1F8 U+1F1E8",
	    name: "Seychelles",
	    emoji: "🇸🇨"
	  },
	  SD: {
	    code: "SD",
	    unicode: "U+1F1F8 U+1F1E9",
	    name: "Sudan",
	    emoji: "🇸🇩"
	  },
	  SE: {
	    code: "SE",
	    unicode: "U+1F1F8 U+1F1EA",
	    name: "Sweden",
	    emoji: "🇸🇪"
	  },
	  SG: {
	    code: "SG",
	    unicode: "U+1F1F8 U+1F1EC",
	    name: "Singapore",
	    emoji: "🇸🇬"
	  },
	  SH: {
	    code: "SH",
	    unicode: "U+1F1F8 U+1F1ED",
	    name: "St. Helena",
	    emoji: "🇸🇭"
	  },
	  SI: {
	    code: "SI",
	    unicode: "U+1F1F8 U+1F1EE",
	    name: "Slovenia",
	    emoji: "🇸🇮"
	  },
	  SJ: {
	    code: "SJ",
	    unicode: "U+1F1F8 U+1F1EF",
	    name: "Svalbard & Jan Mayen",
	    emoji: "🇸🇯"
	  },
	  SK: {
	    code: "SK",
	    unicode: "U+1F1F8 U+1F1F0",
	    name: "Slovakia",
	    emoji: "🇸🇰"
	  },
	  SL: {
	    code: "SL",
	    unicode: "U+1F1F8 U+1F1F1",
	    name: "Sierra Leone",
	    emoji: "🇸🇱"
	  },
	  SM: {
	    code: "SM",
	    unicode: "U+1F1F8 U+1F1F2",
	    name: "San Marino",
	    emoji: "🇸🇲"
	  },
	  SN: {
	    code: "SN",
	    unicode: "U+1F1F8 U+1F1F3",
	    name: "Senegal",
	    emoji: "🇸🇳"
	  },
	  SO: {
	    code: "SO",
	    unicode: "U+1F1F8 U+1F1F4",
	    name: "Somalia",
	    emoji: "🇸🇴"
	  },
	  SR: {
	    code: "SR",
	    unicode: "U+1F1F8 U+1F1F7",
	    name: "Suriname",
	    emoji: "🇸🇷"
	  },
	  SS: {
	    code: "SS",
	    unicode: "U+1F1F8 U+1F1F8",
	    name: "South Sudan",
	    emoji: "🇸🇸"
	  },
	  ST: {
	    code: "ST",
	    unicode: "U+1F1F8 U+1F1F9",
	    name: "São Tomé & Príncipe",
	    emoji: "🇸🇹"
	  },
	  SV: {
	    code: "SV",
	    unicode: "U+1F1F8 U+1F1FB",
	    name: "El Salvador",
	    emoji: "🇸🇻"
	  },
	  SX: {
	    code: "SX",
	    unicode: "U+1F1F8 U+1F1FD",
	    name: "Sint Maarten",
	    emoji: "🇸🇽"
	  },
	  SY: {
	    code: "SY",
	    unicode: "U+1F1F8 U+1F1FE",
	    name: "Syria",
	    emoji: "🇸🇾"
	  },
	  SZ: {
	    code: "SZ",
	    unicode: "U+1F1F8 U+1F1FF",
	    name: "Swaziland",
	    emoji: "🇸🇿"
	  },
	  TA: {
	    code: "TA",
	    unicode: "U+1F1F9 U+1F1E6",
	    name: "Tristan da Cunha",
	    emoji: "🇹🇦"
	  },
	  TC: {
	    code: "TC",
	    unicode: "U+1F1F9 U+1F1E8",
	    name: "Turks & Caicos Islands",
	    emoji: "🇹🇨"
	  },
	  TD: {
	    code: "TD",
	    unicode: "U+1F1F9 U+1F1E9",
	    name: "Chad",
	    emoji: "🇹🇩"
	  },
	  TF: {
	    code: "TF",
	    unicode: "U+1F1F9 U+1F1EB",
	    name: "French Southern Territories",
	    emoji: "🇹🇫"
	  },
	  TG: {
	    code: "TG",
	    unicode: "U+1F1F9 U+1F1EC",
	    name: "Togo",
	    emoji: "🇹🇬"
	  },
	  TH: {
	    code: "TH",
	    unicode: "U+1F1F9 U+1F1ED",
	    name: "Thailand",
	    emoji: "🇹🇭"
	  },
	  TJ: {
	    code: "TJ",
	    unicode: "U+1F1F9 U+1F1EF",
	    name: "Tajikistan",
	    emoji: "🇹🇯"
	  },
	  TK: {
	    code: "TK",
	    unicode: "U+1F1F9 U+1F1F0",
	    name: "Tokelau",
	    emoji: "🇹🇰"
	  },
	  TL: {
	    code: "TL",
	    unicode: "U+1F1F9 U+1F1F1",
	    name: "Timor-Leste",
	    emoji: "🇹🇱"
	  },
	  TM: {
	    code: "TM",
	    unicode: "U+1F1F9 U+1F1F2",
	    name: "Turkmenistan",
	    emoji: "🇹🇲"
	  },
	  TN: {
	    code: "TN",
	    unicode: "U+1F1F9 U+1F1F3",
	    name: "Tunisia",
	    emoji: "🇹🇳"
	  },
	  TO: {
	    code: "TO",
	    unicode: "U+1F1F9 U+1F1F4",
	    name: "Tonga",
	    emoji: "🇹🇴"
	  },
	  TR: {
	    code: "TR",
	    unicode: "U+1F1F9 U+1F1F7",
	    name: "Turkey",
	    emoji: "🇹🇷"
	  },
	  TT: {
	    code: "TT",
	    unicode: "U+1F1F9 U+1F1F9",
	    name: "Trinidad & Tobago",
	    emoji: "🇹🇹"
	  },
	  TV: {
	    code: "TV",
	    unicode: "U+1F1F9 U+1F1FB",
	    name: "Tuvalu",
	    emoji: "🇹🇻"
	  },
	  TW: {
	    code: "TW",
	    unicode: "U+1F1F9 U+1F1FC",
	    name: "Taiwan",
	    emoji: "🇹🇼"
	  },
	  TZ: {
	    code: "TZ",
	    unicode: "U+1F1F9 U+1F1FF",
	    name: "Tanzania",
	    emoji: "🇹🇿"
	  },
	  UA: {
	    code: "UA",
	    unicode: "U+1F1FA U+1F1E6",
	    name: "Ukraine",
	    emoji: "🇺🇦"
	  },
	  UG: {
	    code: "UG",
	    unicode: "U+1F1FA U+1F1EC",
	    name: "Uganda",
	    emoji: "🇺🇬"
	  },
	  UM: {
	    code: "UM",
	    unicode: "U+1F1FA U+1F1F2",
	    name: "U.S. Outlying Islands",
	    emoji: "🇺🇲"
	  },
	  UN: {
	    code: "UN",
	    unicode: "U+1F1FA U+1F1F3",
	    name: "United Nations",
	    emoji: "🇺🇳"
	  },
	  US: {
	    code: "US",
	    unicode: "U+1F1FA U+1F1F8",
	    name: "United States",
	    emoji: "🇺🇸"
	  },
	  UY: {
	    code: "UY",
	    unicode: "U+1F1FA U+1F1FE",
	    name: "Uruguay",
	    emoji: "🇺🇾"
	  },
	  UZ: {
	    code: "UZ",
	    unicode: "U+1F1FA U+1F1FF",
	    name: "Uzbekistan",
	    emoji: "🇺🇿"
	  },
	  VA: {
	    code: "VA",
	    unicode: "U+1F1FB U+1F1E6",
	    name: "Vatican City",
	    emoji: "🇻🇦"
	  },
	  VC: {
	    code: "VC",
	    unicode: "U+1F1FB U+1F1E8",
	    name: "St. Vincent & Grenadines",
	    emoji: "🇻🇨"
	  },
	  VE: {
	    code: "VE",
	    unicode: "U+1F1FB U+1F1EA",
	    name: "Venezuela",
	    emoji: "🇻🇪"
	  },
	  VG: {
	    code: "VG",
	    unicode: "U+1F1FB U+1F1EC",
	    name: "British Virgin Islands",
	    emoji: "🇻🇬"
	  },
	  VI: {
	    code: "VI",
	    unicode: "U+1F1FB U+1F1EE",
	    name: "U.S. Virgin Islands",
	    emoji: "🇻🇮"
	  },
	  VN: {
	    code: "VN",
	    unicode: "U+1F1FB U+1F1F3",
	    name: "Vietnam",
	    emoji: "🇻🇳"
	  },
	  VU: {
	    code: "VU",
	    unicode: "U+1F1FB U+1F1FA",
	    name: "Vanuatu",
	    emoji: "🇻🇺"
	  },
	  WF: {
	    code: "WF",
	    unicode: "U+1F1FC U+1F1EB",
	    name: "Wallis & Futuna",
	    emoji: "🇼🇫"
	  },
	  WS: {
	    code: "WS",
	    unicode: "U+1F1FC U+1F1F8",
	    name: "Samoa",
	    emoji: "🇼🇸"
	  },
	  XK: {
	    code: "XK",
	    unicode: "U+1F1FD U+1F1F0",
	    name: "Kosovo",
	    emoji: "🇽🇰"
	  },
	  YE: {
	    code: "YE",
	    unicode: "U+1F1FE U+1F1EA",
	    name: "Yemen",
	    emoji: "🇾🇪"
	  },
	  YT: {
	    code: "YT",
	    unicode: "U+1F1FE U+1F1F9",
	    name: "Mayotte",
	    emoji: "🇾🇹"
	  },
	  ZA: {
	    code: "ZA",
	    unicode: "U+1F1FF U+1F1E6",
	    name: "South Africa",
	    emoji: "🇿🇦"
	  },
	  ZM: {
	    code: "ZM",
	    unicode: "U+1F1FF U+1F1F2",
	    name: "Zambia",
	    emoji: "🇿🇲"
	  },
	  ZW: {
	    code: "ZW",
	    unicode: "U+1F1FF U+1F1FC",
	    name: "Zimbabwe",
	    emoji: "🇿🇼"
	  }
	};

	var countryCodes = Object.keys(data);
	var list = Object.values(data);
	/**
	 * Get country flag emoji.
	 *
	 * @param {String} countryCode
	 * @return {Object|Undefined}
	 */

	var get = function get(countryCode) {
	  if (countryCode === undefined) {
	    return list;
	  }

	  if (typeof countryCode !== "string") {
	    return undefined;
	  }

	  var code = countryCode.toUpperCase();
	  return Object.prototype.hasOwnProperty.call(data, code) ? data[code] : undefined;
	};

	var index = {
	  data: data,
	  countryCodes: countryCodes,
	  list: list,
	  get: get
	};

	return index;

})));
