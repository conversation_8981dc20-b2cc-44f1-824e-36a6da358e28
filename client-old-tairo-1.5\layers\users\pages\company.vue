<!-- client-old-tairo-1.5/layers/users/pages/company.vue -->

<template>
  <div class="p-6 dark:bg-muted-900">
    <TairoContentWrapper>
      <template #left>
        <BaseHeading tag="h1" size="xl" weight="light" class="text-muted-800 dark:text-white">
          Company Profile
        </BaseHeading>
        <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
          Manage your company information and settings
        </BaseText>
      </template>

      <div class="mt-6">
        <!-- Company Header with Cover Image and Logo -->
        <CompanyHeader
          :company="userStore.primaryCompany?.company"
          @logo-updated="handleLogoUpdated"
          @cover-updated="handleCoverUpdated"
        />

        <div class="mt-20">
          <BaseTabs
            v-model="activeTab"
            :tabs="[
              { label: 'Company Details', value: 'details' },
              { label: 'Team Members', value: 'team' },
              { label: 'Documents', value: 'documents' },
              { label: 'Settings', value: 'settings' },
            ]"
            color="primary"
          />

          <div class="mt-6">
            <!-- Company Details Tab -->
            <div
              v-show="activeTab === 'details'"
              class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm"
            >
              <CompanyDetailsTab
                :company="userStore.primaryCompany?.company"
                @company-updated="handleCompanyUpdated"
              />
            </div>

            <!-- Team Members Tab -->
            <div
              v-show="activeTab === 'team'"
              class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm"
            >
              <CompanyTeamTab :company-id="userStore.primaryCompany?.company?.id" />
            </div>

            <!-- Documents Tab -->
            <div
              v-show="activeTab === 'documents'"
              class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm"
            >
              <CompanyDocumentsTab :company-id="userStore.primaryCompany?.company?.id" />
            </div>

            <!-- Settings Tab -->
            <div
              v-show="activeTab === 'settings'"
              class="bg-white dark:bg-muted-800 rounded-lg p-6 shadow-sm"
            >
              <CompanySettingsTab
                :company="userStore.primaryCompany?.company"
                @settings-updated="handleSettingsUpdated"
              />
            </div>
          </div>
        </div>
      </div>
    </TairoContentWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../../../stores/useUserStore'
import { useToaster } from '../../../composables/toaster'

// Import components
import CompanyHeader from '../components/company/CompanyHeader.vue'
import CompanyDetailsTab from '../components/company/CompanyDetailsTab.vue'
import CompanyTeamTab from '../components/company/CompanyTeamTab.vue'
import CompanyDocumentsTab from '../components/company/CompanyDocumentsTab.vue'
import CompanySettingsTab from '../components/company/CompanySettingsTab.vue'

// Initialize user store and toaster
const userStore = useUserStore()
const toaster = useToaster()

// State
const activeTab = ref('details')

// Methods

const handleLogoUpdated = () => {
  toaster.show({
    title: 'Success',
    message: 'Company logo updated successfully',
    color: 'success',
    icon: 'ph:check-circle-duotone',
    closable: true,
  })
}

const handleCoverUpdated = () => {
  toaster.show({
    title: 'Success',
    message: 'Company cover image updated successfully',
    color: 'success',
    icon: 'ph:check-circle-duotone',
    closable: true,
  })
}

const handleCompanyUpdated = () => {
  toaster.show({
    title: 'Success',
    message: 'Company details updated successfully',
    color: 'success',
    icon: 'ph:check-circle-duotone',
    closable: true,
  })

  // Refresh user data to get updated company info
  userStore.fetchUser()
  console.log('Company updated', userStore.primaryCompany?.company)
}

const handleSettingsUpdated = () => {
  toaster.show({
    title: 'Success',
    message: 'Company settings updated successfully',
    color: 'success',
    icon: 'ph:check-circle-duotone',
    closable: true,
  })
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser()
  }

  // Redirect if user doesn't have a company
  if (!userStore.primaryCompany) {
    toaster.show({
      title: 'Error',
      message: 'You are not associated with any company',
      color: 'danger',
      icon: 'ph:x-circle-duotone',
      closable: true,
    })
  }
})
</script>
