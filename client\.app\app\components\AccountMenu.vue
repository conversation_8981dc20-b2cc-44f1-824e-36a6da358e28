<!-- client/app/components/AccountMenu.vue -->

<script setup lang="ts">
import {
  DropdownMenuRoot,
  DropdownMenuTrigger,
  DropdownMenuPortal,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "reka-ui";
import { useUserStore } from "../../stores/useUserStore";
import { useAuthStore } from "../../stores/useAuthStore";
import { useRouter } from "vue-router";
import { onMounted } from "vue";

const props = defineProps<{
  horizontal?: boolean;
}>();

const userStore = useUserStore();
const authStore = useAuthStore();
const router = useRouter();
const { t } = useI18n();

// Fetch user data if not already loaded
onMounted(async () => {
  if (authStore.isLoggedIn && !userStore.isAuthenticated) {
    try {
      // Use type assertion to access the method
      await (userStore as any).fetchUser();
    } catch (error) {
      console.error("Failed to load user:", error);
    }
  }
});

function logout() {
  // Clear user store (which also clears auth store)
  (userStore as any).logout();
  // Redirect to home page
  router.push("/");
}

function getInitials(name: string): string {
  if (!name) return "";
  const names = name
    .trim()
    .split(" ")
    .filter((n) => n.length > 0);
  if (names.length === 0) return "";
  if (names.length === 1) {
    return names[0]?.substring(0, 2).toUpperCase() || "";
  } else {
    const first = names[0]?.[0] || "";
    const last = names[names.length - 1]?.[0] || "";
    return (first + last).toUpperCase();
  }
}
</script>

<template>
  <div class="group inline-flex items-center justify-center text-right">
    <DropdownMenuRoot>
      <DropdownMenuTrigger
        class="group-hover:ring-primary-500 dark:ring-offset-muted-900 inline-flex size-10 items-center justify-center rounded-full ring-1 ring-transparent transition-all duration-300 group-hover:ring-offset-4 cursor-pointer mb-3"
      >
        <div
          class="relative inline-flex size-10 items-center justify-center rounded-full"
        >
          <img
            v-if="userStore.user?.avatar"
            :src="userStore.user.avatar"
            class="w-10 h-10 rounded-full object-cover shadow-sm dark:border-transparent"
            alt="User avatar"
          />
          <div
            v-else-if="userStore.user?.firstName"
            class="flex size-full items-center justify-center rounded-full bg-primary-500"
          >
            <span class="text-xl font-bold text-white">
              {{
                getInitials(
                  `${userStore.user?.firstName || ""} ${
                    userStore.user?.lastName || ""
                  }`
                )
              }}
            </span>
          </div>
          <img
            v-else
            src="/img/avatars/cute-astronout.svg"
            class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
            alt="Default avatar"
          />
        </div>
      </DropdownMenuTrigger>

      <DropdownMenuPortal disabled>
        <DropdownMenuContent
          class="border-muted-200 dark:border-muted-700 dark:bg-muted-900 w-60 origin-bottom-right rounded-md border bg-white text-left shadow-lg focus:outline-none ml-2"
          :class="props.horizontal ? 'mt-2' : 'mb-2'"
          :side="props.horizontal ? 'bottom' : 'top'"
          :align="'end'"
          :side-offset="8"
        >
          <div class="bg-muted-50 dark:bg-muted-800/40 p-6">
            <div class="flex items-center">
              <div
                class="relative inline-flex size-14 items-center justify-center rounded-full"
              >
                <img
                  v-if="userStore.user?.avatar"
                  :src="userStore.user.avatar"
                  class="w-14 h-14 rounded-full object-cover shadow-sm dark:border-transparent"
                  alt="User avatar"
                />
                <div
                  v-else-if="userStore.user?.firstName"
                  class="flex size-full items-center justify-center rounded-full bg-primary-500"
                >
                  <span class="text-2xl font-bold text-white">
                    {{
                      getInitials(
                        `${userStore.user?.firstName || ""} ${
                          userStore.user?.lastName || ""
                        }`
                      )
                    }}
                  </span>
                </div>
                <img
                  v-else
                  src="/img/avatars/cute-astronout.svg"
                  class="max-w-full rounded-full object-cover shadow-sm dark:border-transparent"
                  alt="Default avatar"
                />
              </div>
              <div class="ms-3">
                <h6
                  class="font-heading text-muted-800 text-sm font-medium dark:text-white"
                >
                  {{
                    `${userStore.user?.firstName || ""} ${
                      userStore.user?.lastName || ""
                    }`.trim() || t("account_menu.unknown_user")
                  }}
                </h6>
                <p class="text-muted-400 font-sans text-xs">
                  {{
                    userStore.user?.roles && userStore.user.roles.length > 0
                      ? userStore.user.roles[0]?.role
                      : t("account_menu.unknown_role")
                  }}
                </p>
              </div>
            </div>
          </div>
          <div class="p-2">
            <!-- User Profile -->
            <DropdownMenuItem as-child>
              <NuxtLink
                to="/users/profile"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300 text-muted-500 hover:bg-muted-100 dark:hover:bg-muted-800 hover:text-primary-500 focus:bg-muted-100 dark:focus:bg-muted-800 focus:text-primary-500 focus:outline-none"
              >
                <Icon name="solar:user-circle-linear" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    {{ t("account_menu.my_profile") }}
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">
                    {{ t("account_menu.my_profile_desc") }}
                  </p>
                </div>
              </NuxtLink>
            </DropdownMenuItem>

            <!-- Company Profile (if user has a company) -->
            <DropdownMenuItem
              v-if="
                userStore.user?.companies && userStore.user.companies.length > 0
              "
              as-child
            >
              <NuxtLink
                to="/users/company"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300 text-muted-500 hover:bg-muted-100 dark:hover:bg-muted-800 hover:text-primary-500 focus:bg-muted-100 dark:focus:bg-muted-800 focus:text-primary-500 focus:outline-none"
              >
                <Icon name="solar:buildings-linear" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    {{ t("account_menu.company_profile") }}
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">
                    {{ t("account_menu.company_profile_desc") }}
                  </p>
                </div>
              </NuxtLink>
            </DropdownMenuItem>

            <!-- Settings -->
            <DropdownMenuItem as-child>
              <NuxtLink
                to="/users/settings"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300 text-muted-500 hover:bg-muted-100 dark:hover:bg-muted-800 hover:text-primary-500 focus:bg-muted-100 dark:focus:bg-muted-800 focus:text-primary-500 focus:outline-none"
              >
                <Icon name="solar:settings-linear" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    {{ t("account_menu.settings") }}
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">
                    {{ t("account_menu.settings_desc") }}
                  </p>
                </div>
              </NuxtLink>
            </DropdownMenuItem>

            <!-- Subscriptions -->
            <DropdownMenuItem as-child>
              <NuxtLink
                to="/core/subscription"
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300 text-muted-500 hover:bg-muted-100 dark:hover:bg-muted-800 hover:text-primary-500 focus:bg-muted-100 dark:focus:bg-muted-800 focus:text-primary-500 focus:outline-none"
              >
                <Icon name="solar:dollar-minimalistic-linear" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white"
                  >
                    {{ t("account_menu.subscriptions") }}
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">
                    {{ t("account_menu.subscriptions_desc") }}
                  </p>
                </div>
              </NuxtLink>
            </DropdownMenuItem>
          </div>

          <DropdownMenuSeparator
            class="border-muted-200 dark:border-muted-700 border-b"
          />

          <div class="p-2">
            <!-- Logout Button -->
            <DropdownMenuItem as-child>
              <button
                class="group flex w-full items-center rounded-md p-3 text-sm transition-colors duration-300 text-muted-500 hover:bg-muted-100 dark:hover:bg-muted-700 hover:text-danger-500 focus:bg-muted-100 dark:focus:bg-muted-700 focus:text-danger-500 focus:outline-none"
                @click="logout"
              >
                <Icon name="solar:logout-linear" class="size-5" />
                <div class="ms-3">
                  <h6
                    class="font-heading text-muted-800 text-xs font-medium leading-none dark:text-white text-left"
                  >
                    {{ t("account_menu.logout") }}
                  </h6>
                  <p class="text-muted-400 font-sans text-xs">
                    {{ t("account_menu.logout_desc") }}
                  </p>
                </div>
              </button>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenuRoot>
  </div>
</template>
