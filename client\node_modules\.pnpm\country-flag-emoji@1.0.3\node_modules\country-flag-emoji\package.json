{"name": "country-flag-emoji", "version": "1.0.3", "description": "List of country codes and its flag emojis", "keywords": ["country", "country-code", "emoji", "flag", "iso-3166"], "homepage": "https://github.com/risan/country-flag-emoji#readme", "bugs": {"url": "https://github.com/risan/country-flag-emoji/issues"}, "license": "MIT", "author": {"name": "Risan Bagja Pradana", "email": "<EMAIL>", "url": "https://bagja.net"}, "main": "dist/country-flag-emoji.cjs.js", "browser": "dist/country-flag-emoji.umd.js", "repository": {"type": "git", "url": "https://github.com/risan/country-flag-emoji.git"}, "scripts": {"build": "NODE_ENV=build rollup -c", "lint": "eslint ./", "lint-fix": "eslint ./ --fix", "prepublishOnly": "npm run lint && npm run test && npm run build", "test": "NODE_ENV=test jest"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "eslint": "^6.0.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^5.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.0", "jest": "^23.6.0", "prettier": "^1.15.2", "rollup": "^1.0.0", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.0", "rollup-plugin-terser": "^5.0.0"}, "engines": {"node": ">=10.0.0"}}