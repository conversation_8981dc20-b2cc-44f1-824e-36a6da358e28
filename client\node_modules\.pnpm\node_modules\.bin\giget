#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules/giget/dist/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules/giget/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules/giget/dist/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules/giget/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/giget@2.0.0/node_modules:/mnt/c/Users/<USER>/comanager/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../giget/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../giget/dist/cli.mjs" "$@"
fi
