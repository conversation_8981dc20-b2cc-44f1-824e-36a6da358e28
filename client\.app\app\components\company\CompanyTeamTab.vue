<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <BaseHeading
        tag="h2"
        size="md"
        weight="medium"
        class="text-muted-800 dark:text-white"
      >
        Team Members
      </BaseHeading>

      <div class="flex items-center space-x-2">
        <BaseInput
          v-model="searchQuery"
          icon="solar:magnifer-linear"
          placeholder="Search members..."
          class="w-full sm:w-64"
        />
        <BaseButton variant="solid" color="primary" @click="openInviteModal">
          <Icon name="solar:user-plus-linear" class="h-4 w-4 mr-2" />
          Invite Member
        </BaseButton>
      </div>
    </div>

    <!-- Team Members List -->
    <TeamMembersList
      :members="filteredTeamMembers"
      :is-loading="isLoading"
      :error="error"
      :can-manage-team="canManageTeam"
      :current-user-id="userStore.user?.id"
      @refresh="fetchTeamMembers"
      @invite="openInviteModal"
      @edit="openEditRoleModal"
      @remove="confirmRemoveMember"
    />

    <!-- Pending Invitations Section -->
    <div class="mt-8">
      <InvitationsList
        :invitations="invitations"
        :is-loading="loadingInvitations"
        :error="invitationError"
        :resending-invitation="resendingInvitation"
        :canceling-invitation="cancelingInvitation"
        @refresh="fetchInvitations"
        @resend="resendInvitation"
        @cancel="cancelInvitation"
      />
    </div>

    <!-- Invite Member Modal -->
    <InviteModal
      :is-open="isInviteModalOpen"
      :is-submitting="isSendingInvite"
      :initial-data="inviteForm"
      @close="closeInviteModal"
      @submit="sendInvitation"
    />

    <!-- Edit Role Modal -->
    <EditRoleModal
      :is-open="isEditRoleModalOpen"
      :member="selectedMember"
      :is-submitting="isUpdatingRole"
      @close="closeEditRoleModal"
      @update="updateMemberRole"
    />

    <!-- Confirm Remove Modal -->
    <ConfirmRemoveModal
      :is-open="isConfirmRemoveModalOpen"
      :member="selectedMember"
      :is-submitting="isRemovingMember"
      @close="closeConfirmRemoveModal"
      @confirm="removeMember"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStore } from "../../../stores/useUserStore";
import { useApi } from "../../../app/composables/useApi";
import TeamMembersList from "./team/TeamMembersList.vue";
import InvitationsList from "./team/InvitationsList.vue";
import InviteModal from "./team/InviteModal.vue";
import EditRoleModal from "./team/EditRoleModal.vue";
import ConfirmRemoveModal from "./team/ConfirmRemoveModal.vue";

const api = useApi();
const toaster = useNuiToasts();
const props = defineProps<{
  companyId?: string;
}>();

// Types
interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

interface TeamMember {
  id: number;
  userId: number;
  companyId: string;
  role: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: User;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  companyId: string;
  status: string;
  createdAt: string;
  expiresAt: string;
}

// Stores
const userStore = useUserStore();

// State
const isLoading = ref(true);
const loadingInvitations = ref(false);
const teamMembers = ref<TeamMember[]>([]);
const invitations = ref<Invitation[]>([]);
const error = ref("");
const invitationError = ref("");
const searchQuery = ref("");

// Modal states
const isInviteModalOpen = ref(false);
const isEditRoleModalOpen = ref(false);
const isConfirmRemoveModalOpen = ref(false);
const isSendingInvite = ref(false);
const isUpdatingRole = ref(false);
const isRemovingMember = ref(false);
const resendingInvitation = ref("");
const cancelingInvitation = ref("");
const selectedMember = ref<TeamMember | undefined>(undefined);

// Form state
const inviteForm = ref({
  email: "",
  role: "",
  message: "",
});

// Computed
const filteredTeamMembers = computed(() => {
  if (!searchQuery.value) return teamMembers.value;

  const query = searchQuery.value.toLowerCase();
  return teamMembers.value.filter((member) => {
    const fullName = `${member.user?.firstName || ""} ${
      member.user?.lastName || ""
    }`.toLowerCase();
    const email = (member.user?.email || "").toLowerCase();
    const role = (member.role || "").toLowerCase();

    return (
      fullName.includes(query) || email.includes(query) || role.includes(query)
    );
  });
});

const canManageTeam = computed(() => {
  return (
    userStore.isSuperAdmin ||
    teamMembers.value.some(
      (member) =>
        member.userId === userStore.user?.id &&
        ["OWNER", "ADMIN"].includes(member.role)
    )
  );
});

// Form state is now handled in the modal components

// Methods
const fetchTeamMembers = async () => {
  if (!props.companyId) return;

  isLoading.value = true;
  error.value = "";

  try {
    const response = await api.get(`/companies/${props.companyId}/members`);
    teamMembers.value = response.data;
  } catch (err) {
    console.error("Error fetching team members:", err);
    error.value = "Failed to load team members. Please try again.";
    toaster.add({
      title: "Error",
      description: "Failed to load team members",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isLoading.value = false;
  }
};

const fetchInvitations = async () => {
  if (!props.companyId) return;

  loadingInvitations.value = true;
  invitationError.value = "";

  try {
    const response = await api.get(`/companies/${props.companyId}/invitations`);
    invitations.value = response.data;
  } catch (err) {
    console.error("Error fetching invitations:", err);
    invitationError.value = "Failed to load invitations";
  } finally {
    loadingInvitations.value = false;
  }
};

// Helper functions moved to component files

const openInviteModal = () => {
  inviteForm.value = {
    email: "",
    role: "",
    message: "",
  };
  isInviteModalOpen.value = true;
};

const closeInviteModal = () => {
  isInviteModalOpen.value = false;
};

// Validation is now handled in the InviteModal component

const sendInvitation = async (formData: {
  email: string;
  role: string | { value: string; label: string };
  message?: string;
}) => {
  console.log("CompanyTeamTab: sendInvitation called with data:", formData);
  console.log("CompanyTeamTab: companyId:", props.companyId);

  if (!props.companyId) {
    console.error("CompanyTeamTab: Missing companyId");
    return;
  }

  try {
    isSendingInvite.value = true;
    console.log(
      "CompanyTeamTab: Sending invitation request to:",
      `/companies/${props.companyId}/invitations`
    );

    console.log("CompanyTeamTab: Role before extraction:", formData.role);

    // Extract the role value if it's an object
    const roleValue =
      typeof formData.role === "object" && formData.role !== null
        ? formData.role.value
        : formData.role;

    console.log("CompanyTeamTab: Extracted role value:", roleValue);

    const response = await api.post(
      `/companies/${props.companyId}/invitations`,
      {
        email: formData.email,
        role: roleValue,
        message: formData.message,
      }
    );

    console.log("CompanyTeamTab: Invitation response:", response.data);

    if (response.data) {
      toaster.add({
        title: "Success",
        description: "Invitation sent successfully",
        icon: "solar:check-circle-linear",
        progress: true,
      });

      closeInviteModal();
      fetchTeamMembers();
      fetchInvitations();
    }
  } catch (err) {
    console.error("CompanyTeamTab: Error sending invitation:", err);
    toaster.add({
      title: "Error",
      description: err.response?.data?.message || "Failed to send invitation",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isSendingInvite.value = false;
  }
};

const openEditRoleModal = (member: TeamMember) => {
  selectedMember.value = member;
  isEditRoleModalOpen.value = true;
};

const closeEditRoleModal = () => {
  isEditRoleModalOpen.value = false;
  selectedMember.value = undefined;
};

// Validation is now handled in the EditRoleModal component

const updateMemberRole = async (
  newRole: string | { value: string; label: string }
) => {
  if (!props.companyId || !selectedMember.value) return;

  console.log("CompanyTeamTab: updateMemberRole called with role:", newRole);

  // Extract the role value if it's an object
  const roleValue =
    typeof newRole === "object" && newRole !== null && newRole.value
      ? newRole.value
      : newRole;

  console.log("CompanyTeamTab: Extracted role value:", roleValue);

  try {
    isUpdatingRole.value = true;

    const response = await api.put(
      `/companies/${props.companyId}/members/${selectedMember.value.id}`,
      {
        role: roleValue,
      }
    );

    if (response.data) {
      toaster.add({
        title: "Success",
        description: "Member role updated successfully",
        icon: "solar:check-circle-linear",
        progress: true,
      });

      closeEditRoleModal();
      fetchTeamMembers();
    }
  } catch (err) {
    console.error("Error updating member role:", err);
    toaster.add({
      title: "Error",
      description:
        err.response?.data?.message || "Failed to update member role",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isUpdatingRole.value = false;
  }
};

const confirmRemoveMember = (member: TeamMember) => {
  selectedMember.value = member;
  isConfirmRemoveModalOpen.value = true;
};

const closeConfirmRemoveModal = () => {
  isConfirmRemoveModalOpen.value = false;
  selectedMember.value = undefined;
};

const removeMember = async () => {
  if (!props.companyId || !selectedMember.value) return;

  try {
    isRemovingMember.value = true;

    await api.delete(
      `/companies/${props.companyId}/members/${selectedMember.value.id}`
    );

    toaster.add({
      title: "Success",
      description: "Member removed successfully",
      icon: "solar:check-circle-linear",
      progress: true,
    });

    closeConfirmRemoveModal();
    fetchTeamMembers();
  } catch (err) {
    console.error("Error removing member:", err);
    toaster.add({
      title: "Error",
      description: err.response?.data?.message || "Failed to remove member",
      icon: "solar:close-circle-linear",
      progress: true,
    });
  } finally {
    isRemovingMember.value = false;
  }
};

const resendInvitation = async (invitation: Invitation) => {
  if (!props.companyId) return;

  resendingInvitation.value = invitation.id;

  try {
    await api.post(
      `/companies/${props.companyId}/invitations/${invitation.id}/resend`
    );

    toaster.add({
      title: "Success",
      description: "Invitation resent successfully",
      icon: "ph:check-circle-duotone",
      progress: true,
      duration: 3000,
    });

    fetchInvitations();
  } catch (err) {
    console.error("Error resending invitation:", err);
    toaster.add({
      title: "Error",
      description: err.response?.data?.message || "Failed to resend invitation",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    resendingInvitation.value = "";
  }
};

const cancelInvitation = async (invitation: Invitation) => {
  if (!props.companyId) return;

  cancelingInvitation.value = invitation.id;

  try {
    await api.delete(
      `/companies/${props.companyId}/invitations/${invitation.id}`
    );

    toaster.add({
      title: "Success",
      description: "Invitation cancelled successfully",
      icon: "ph:check-circle-duotone",
      progress: true,
      duration: 3000,
    });

    fetchInvitations();
  } catch (err) {
    console.error("Error cancelling invitation:", err);
    toaster.add({
      title: "Error",
      description: err.response?.data?.message || "Failed to cancel invitation",
      icon: "ph:x-circle-duotone",
      progress: true,
      duration: 3000,
    });
  } finally {
    cancelingInvitation.value = "";
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchTeamMembers();
  fetchInvitations();
});
</script>
