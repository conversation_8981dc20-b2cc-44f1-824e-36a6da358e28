@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\prebuild-install@7.1.3\node_modules\prebuild-install\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\prebuild-install@7.1.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\prebuild-install@7.1.3\node_modules\prebuild-install\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\prebuild-install@7.1.3\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\prebuild-install\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\prebuild-install\bin.js" %*
)
