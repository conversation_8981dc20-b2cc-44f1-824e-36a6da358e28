<!-- client-old-tairo-1.5/components/Users/<USER>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useSidebar } from '../../composables/sidebar'
import { useAuthStore } from '../../stores/useAuthStore'
import { useUserStore } from '../../stores/useUserStore'
// Import the AI Assistant component
import AiAssistantButton from '../AiAssistantButton.vue'

const props = withDefaults(
  defineProps<{
    sidebar?: boolean
    horizontalScroll?: boolean
  }>(),
  {
    sidebar: true,
  }
)

const { hasSubsidebar } = useSidebar()
const authStore = useAuthStore()
const userStore = useUserStore()
// AI Assistant state is managed by the AiAssistantButton component

// Fetch user data if not already loaded
onMounted(async () => {
  if (authStore.isLoggedIn && !userStore.isAuthenticated) {
    await userStore.fetchUser()
  }
})

const showNavBurger = computed(() => {
  // Safely check if the sidebar property exists
  return props.sidebar && hasSubsidebar.value
})

// Logout functionality moved to AccountMenu component
</script>

<template>
  <div class="relative z-[1] mb-5 flex h-16 items-center bg-muted-50 dark:bg-muted-800 w-full">
    <UsersSidebarBurger v-if="showNavBurger" class="ml-2" />

    <div class="flex w-full justify-between px-6">
      <div class="flex flex-row items-center gap-4">
        <div class="flex flex-col items-center justify-center">
          <BaseTag rounded="md" variant="solid" color="primary" class="!px-2 !py-0">
            {{
              userStore.userRoles && userStore.userRoles.length > 0
                ? userStore.userRoles[0]
                : 'UNKNOWN'
            }}
          </BaseTag>
          <BaseSnack
            :label="userStore.fullName || 'Unknown'"
            color="default"
            size="xs"
            :image="userStore.user?.avatar || '/img/avatars/cute-astronout.svg'"
          />
        </div>
        <div v-if="userStore.primaryCompany" class="flex flex-col items-center justify-center">
          <BaseTag rounded="md" variant="solid" color="primary" class="!px-2 !py-0">
            Company
          </BaseTag>
          <BaseSnack
            :label="userStore.primaryCompany.company.name || 'Company'"
            color="default"
            size="xs"
            :image="userStore.primaryCompany.company.logo || ''"
            :icon="!userStore.primaryCompany.company.logo ? 'ph:building' : undefined"
          />
        </div>
      </div>

      <div class="flex items-center gap-2">
        <!-- AI Assistant Button -->
        <AiAssistantButton />

        <!-- Feedback Toast is now handled by the AiAssistantButton component -->

        <BaseButtonIcon rounded="full" variant="solid" to="/notifications">
          <div class="relative">
            <Icon name="ph:bell" class="size-6 text-muted-400 dark:text-muted-300" />
            <!-- Notification badge - can be dynamically shown based on notification count -->
            <div
              v-if="false"
              class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-danger-500 text-[10px] font-bold text-white"
            >
              3
            </div>
          </div>
        </BaseButtonIcon>

        <BaseThemeSwitch />

        <!-- Logout button removed as it's now in the AccountMenu -->
      </div>
    </div>
  </div>
</template>
