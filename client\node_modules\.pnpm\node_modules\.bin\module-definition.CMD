@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules\module-definition\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules\module-definition\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules\module-definition\bin\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules\module-definition\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\module-definition@6.0.1\node_modules;C:\Users\<USER>\comanager\client\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\module-definition\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\module-definition\bin\cli.js" %*
)
