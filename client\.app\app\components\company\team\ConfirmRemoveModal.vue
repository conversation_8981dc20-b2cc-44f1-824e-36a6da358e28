<template>
  <TairoModal :open="isOpen" size="xs" @close="$emit('close')">
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Remove Team Member
        </h3>
        <BaseButtonClose @click="$emit('close')" />
      </div>
    </template>
    <div class="p-4 md:p-6">
      <div v-if="member">
        <p class="text-muted-500 dark:text-muted-400 mb-2">
          Are you sure you want to remove
          <span class="font-semibold">
            {{ member.user?.firstName || 'Unknown' }}
            {{ member.user?.lastName || '' }}
          </span>
          from the team?
        </p>
        <p class="text-danger-500 text-sm">This action cannot be undone.</p>
      </div>

      <div class="flex justify-end space-x-2 mt-4">
        <BaseButton type="button" color="muted" @click="$emit('close')"> Cancel </BaseButton>
        <BaseButton type="button" color="danger" @click="$emit('confirm')" :loading="isSubmitting">
          Remove
        </BaseButton>
      </div>
    </div>
  </TairoModal>
</template>

<script setup lang="ts">
defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  member: {
    type: Object,
    default: undefined,
  },
  isSubmitting: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['close', 'confirm'])
</script>
