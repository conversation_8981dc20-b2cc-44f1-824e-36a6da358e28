// client/.app/app/composables/useApi.ts

import type { ApiError } from "~/utils/api/client";
import { ref } from "vue";
import { apiClient } from "~/utils/api/client";

export function useApi() {
  const loading = ref(false);
  const error = ref<ApiError | null>(null);

  const execute = async <R>(operation: () => Promise<R>): Promise<R | null> => {
    loading.value = true;
    error.value = null;

    try {
      console.log("🚀 useApi: Starting operation...");
      const result = await operation();
      console.log("✅ useApi: Operation successful, result:", result);
      return result;
    } catch (e) {
      console.error("❌ useApi: Operation failed:", e);
      console.error("❌ useApi: Error details:", e);
      error.value = e as ApiError;
      return null;
    } finally {
      loading.value = false;
      console.log("🏁 useApi: Operation completed, loading set to false");
    }
  };

  const get = <T>(url: string, params?: Record<string, any>) => {
    console.log(`🌐 useApi.get: Calling ${url} with params:`, params);
    return execute(() => apiClient.get<T>(url, params));
  };

  const post = <T>(
    url: string,
    data?: any,
    p0?: { headers: { "Content-Type": string } }
  ) => execute(() => apiClient.post<T>(url, data));

  const put = <T>(url: string, data?: any) =>
    execute(() => apiClient.put<T>(url, data));

  const remove = <T>(url: string) => execute(() => apiClient.delete<T>(url));

  const postFormData = <T>(url: string, formData: FormData) => {
    console.log(`🌐 useApi.postFormData: Calling ${url} with FormData`);
    return execute(() => apiClient.postFormData<T>(url, formData));
  };

  const postBinary = (url: string, data?: any) => {
    console.log(`🌐 useApi.postBinary: Calling ${url} with data:`, data);
    return execute(() => apiClient.postBinary(url, data));
  };

  return {
    loading,
    error,
    get,
    post,
    put,
    delete: remove,
    postFormData,
    postBinary,
  };
}
