<script setup lang="ts">
import { reactive, shallowRef, computed } from "vue";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();
const currentLocale = computed(() => locale.value);

// Revenue data
const revenueData = [
  10835, 40214, 36257, 51411, 45697, 61221, 65295, 91512, 75648, 82365, 97458,
  114523,
];

// Expense data (approximately 70-85% of revenue)
const expenseData = [
  8668, 32171, 30818, 41129, 38842, 49588, 55501, 73210, 64301, 65892, 77966,
  91618,
];

// Month labels
const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

// Calculate the spacing between points
const spacing = 800 / (months.length - 1);

// Generate points for both series
function generatePoints(data, maxValue) {
  return data.map((value, index) => {
    const x = index * spacing;
    const y = 200 - (value / maxValue) * 180; // Leave some margin at top
    return { x, y };
  });
}

// Calculate max value for scaling
const maxValue = Math.max(...revenueData, ...expenseData);

// Generate points
const revenuePoints = generatePoints(revenueData, maxValue);
const expensePoints = generatePoints(expenseData, maxValue);

// Create SVG paths using smooth curves
function createSmoothPath(points) {
  if (points.length < 2) return "";

  let path = `M ${points[0].x},${points[0].y}`;

  for (let i = 0; i < points.length - 1; i++) {
    const x_mid = (points[i].x + points[i + 1].x) / 2;
    const y_mid = (points[i].y + points[i + 1].y) / 2;
    const cp_x1 = (x_mid + points[i].x) / 2;
    const cp_x2 = (x_mid + points[i + 1].x) / 2;

    path += ` Q ${cp_x1},${points[i].y} ${x_mid},${y_mid}`;
    path += ` Q ${cp_x2},${points[i + 1].y} ${points[i + 1].x},${
      points[i + 1].y
    }`;
  }

  return path;
}

// Generate SVG paths
const revenuePath = createSmoothPath(revenuePoints);
const expensePath = createSmoothPath(expensePoints);

// Generate area path for revenue
const revenueAreaPath = `${revenuePath} L ${
  revenuePoints[revenuePoints.length - 1].x
},240 L 0,240 Z`;

// Legend labels
const revenueLabel = computed(() =>
  currentLocale.value === "et" ? "Tulu" : "Revenue"
);
const expenseLabel = computed(() =>
  currentLocale.value === "et" ? "Kulu" : "Expense"
);
</script>

<template>
  <div class="h-60 w-full">
    <svg
      class="h-full w-full"
      viewBox="0 0 800 240"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Background grid -->
      <line
        x1="0"
        y1="240"
        x2="800"
        y2="240"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />
      <line
        x1="0"
        y1="200"
        x2="800"
        y2="200"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />
      <line
        x1="0"
        y1="160"
        x2="800"
        y2="160"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />
      <line
        x1="0"
        y1="120"
        x2="800"
        y2="120"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />
      <line
        x1="0"
        y1="80"
        x2="800"
        y2="80"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />
      <line
        x1="0"
        y1="40"
        x2="800"
        y2="40"
        stroke="#e5e7eb"
        stroke-width="1"
        class="dark:stroke-muted-700"
      />

      <!-- Revenue area -->
      <path :d="revenueAreaPath" fill="url(#gradient)" fill-opacity="0.2" />

      <!-- Expense line -->
      <path
        :d="expensePath"
        stroke="var(--color-purple-500)"
        stroke-width="2.5"
        stroke-linecap="round"
        stroke-linejoin="round"
        fill="none"
      />

      <!-- Revenue line -->
      <path
        :d="revenuePath"
        stroke="var(--color-primary-500)"
        stroke-width="3"
        stroke-linecap="round"
        stroke-linejoin="round"
        fill="none"
      />

      <!-- X-axis labels -->
      <text
        x="0"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Jan
      </text>
      <text
        x="67"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Feb
      </text>
      <text
        x="133"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Mar
      </text>
      <text
        x="200"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Apr
      </text>
      <text
        x="267"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        May
      </text>
      <text
        x="333"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Jun
      </text>
      <text
        x="400"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Jul
      </text>
      <text
        x="467"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Aug
      </text>
      <text
        x="533"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Sep
      </text>
      <text
        x="600"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Oct
      </text>
      <text
        x="667"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Nov
      </text>
      <text
        x="733"
        y="260"
        fill="var(--color-muted-500)"
        font-size="10"
        text-anchor="middle"
      >
        Dec
      </text>

      <!-- Legend -->
      <g transform="translate(5, 5)">
        <!-- Revenue legend -->
        <line
          x1="0"
          y1="0"
          x2="15"
          y2="0"
          stroke="var(--color-primary-500)"
          stroke-width="3"
          stroke-linecap="round"
        />
        <text
          x="20"
          y="4"
          fill="var(--color-muted-800)"
          class="dark:fill-white"
          font-size="10"
        >
          {{ revenueLabel }}
        </text>

        <!-- Expense legend -->
        <line
          x1="0"
          y1="15"
          x2="15"
          y2="15"
          stroke="var(--color-purple-500)"
          stroke-width="2.5"
          stroke-linecap="round"
        />
        <text
          x="20"
          y="19"
          fill="var(--color-muted-800)"
          class="dark:fill-white"
          font-size="10"
        >
          {{ expenseLabel }}
        </text>
      </g>

      <!-- Gradient definition -->
      <defs>
        <linearGradient id="gradient" x1="0" y1="0" x2="0" y2="1">
          <stop
            offset="0%"
            stop-color="var(--color-primary-500)"
            stop-opacity="0.7"
          />
          <stop
            offset="100%"
            stop-color="var(--color-primary-500)"
            stop-opacity="0.05"
          />
        </linearGradient>
      </defs>
    </svg>
  </div>
</template>
