!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):(e=e||self).countryFlagEmoji=o()}(this,(function(){"use strict";function e(e,o){return e(o={exports:{}},o.exports),o.exports}var o=e((function(e){var o=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=o)})),n=e((function(e){var o=e.exports={version:"2.6.0"};"number"==typeof __e&&(__e=o)})),i=(n.version,function(e){return"object"==typeof e?null!==e:"function"==typeof e}),F=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e},a=function(e){try{return!!e()}catch(e){return!0}},c=!a((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),d=o.document,m=i(d)&&i(d.createElement),u=function(e){return m?d.createElement(e):{}},U=!c&&!a((function(){return 7!=Object.defineProperty(u("div"),"a",{get:function(){return 7}}).a})),t=Object.defineProperty,r={f:c?Object.defineProperty:function(e,o,n){if(F(e),o=function(e,o){if(!i(e))return e;var n,F;if(o&&"function"==typeof(n=e.toString)&&!i(F=n.call(e)))return F;if("function"==typeof(n=e.valueOf)&&!i(F=n.call(e)))return F;if(!o&&"function"==typeof(n=e.toString)&&!i(F=n.call(e)))return F;throw TypeError("Can't convert object to primitive value")}(o,!0),F(n),U)try{return t(e,o,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[o]=n.value),e}},E=function(e,o){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:o}},j=c?function(e,o,n){return r.f(e,o,E(1,n))}:function(e,o,n){return e[o]=n,e},s={}.hasOwnProperty,l=function(e,o){return s.call(e,o)},f=0,S=Math.random(),C=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++f+S).toString(36))},A=e((function(e){var i=C("src"),F=Function.toString,a=(""+F).split("toString");n.inspectSource=function(e){return F.call(e)},(e.exports=function(e,n,F,c){var d="function"==typeof F;d&&(l(F,"name")||j(F,"name",n)),e[n]!==F&&(d&&(l(F,i)||j(F,i,e[n]?""+e[n]:a.join(String(n)))),e===o?e[n]=F:c?e[n]?e[n]=F:j(e,n,F):(delete e[n],j(e,n,F)))})(Function.prototype,"toString",(function(){return"function"==typeof this&&this[i]||F.call(this)}))})),M=function(e,o,n){if(function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!")}(e),void 0===o)return e;switch(n){case 1:return function(n){return e.call(o,n)};case 2:return function(n,i){return e.call(o,n,i)};case 3:return function(n,i,F){return e.call(o,n,i,F)}}return function(){return e.apply(o,arguments)}},p=function(e,i,F){var a,c,d,m,u=e&p.F,U=e&p.G,t=e&p.S,r=e&p.P,E=e&p.B,s=U?o:t?o[i]||(o[i]={}):(o[i]||{}).prototype,l=U?n:n[i]||(n[i]={}),f=l.prototype||(l.prototype={});for(a in U&&(F=i),F)d=((c=!u&&s&&void 0!==s[a])?s:F)[a],m=E&&c?M(d,o):r&&"function"==typeof d?M(Function.call,d):d,s&&A(s,a,d,e&p.U),l[a]!=d&&j(l,a,m),r&&f[a]!=d&&(f[a]=d)};o.core=n,p.F=1,p.G=2,p.S=4,p.P=8,p.B=16,p.W=32,p.U=64,p.R=128;var y,B,h=p,T={}.toString,G=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==function(e){return T.call(e).slice(8,-1)}(e)?e.split(""):Object(e)},I=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},g=function(e){return G(I(e))},L=Math.ceil,v=Math.floor,P=function(e){return isNaN(e=+e)?0:(e>0?v:L)(e)},b=Math.min,N=Math.max,O=Math.min,D=e((function(e){var i=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(e.exports=function(e,o){return i[e]||(i[e]=void 0!==o?o:{})})("versions",[]).push({version:n.version,mode:"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})})),R=D("keys"),K=function(e){return R[e]||(R[e]=C(e))},H=(y=!1,function(e,o,n){var i,F,a=g(e),c=(i=a.length)>0?b(P(i),9007199254740991):0,d=function(e,o){return(e=P(e))<0?N(e+o,0):O(e,o)}(n,c);if(y&&o!=o){for(;c>d;)if((F=a[d++])!=F)return!0}else for(;c>d;d++)if((y||d in a)&&a[d]===o)return y||d||0;return!y&&-1}),V=K("IE_PROTO"),w="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),k=Object.keys||function(e){return function(e,o){var n,i=g(e),F=0,a=[];for(n in i)n!=V&&l(i,n)&&a.push(n);for(;o.length>F;)l(i,n=o[F++])&&(~H(a,n)||a.push(n));return a}(e,w)},Z={f:{}.propertyIsEnumerable}.f,_=(B=!1,function(e){for(var o,n=g(e),i=k(n),F=i.length,a=0,c=[];F>a;)Z.call(n,o=i[a++])&&c.push(B?[o,n[o]]:n[o]);return c});h(h.S,"Object",{values:function(e){return _(e)}});var W=e((function(e){var n=D("wks"),i=o.Symbol,F="function"==typeof i;(e.exports=function(e){return n[e]||(n[e]=F&&i[e]||(F?i:C)("Symbol."+e))}).store=n})),J=W("unscopables"),Y=Array.prototype;null==Y[J]&&j(Y,J,{});var x=function(e){Y[J][e]=!0},z=function(e,o){return{value:o,done:!!e}},Q={},X=c?Object.defineProperties:function(e,o){F(e);for(var n,i=k(o),a=i.length,c=0;a>c;)r.f(e,n=i[c++],o[n]);return e},q=o.document,$=q&&q.documentElement,ee=K("IE_PROTO"),oe=function(){},ne=function(){var e,o=u("iframe"),n=w.length;for(o.style.display="none",$.appendChild(o),o.src="javascript:",(e=o.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),ne=e.F;n--;)delete ne.prototype[w[n]];return ne()},ie=Object.create||function(e,o){var n;return null!==e?(oe.prototype=F(e),n=new oe,oe.prototype=null,n[ee]=e):n=ne(),void 0===o?n:X(n,o)},Fe=r.f,ae=W("toStringTag"),ce=function(e,o,n){e&&!l(e=n?e:e.prototype,ae)&&Fe(e,ae,{configurable:!0,value:o})},de={};j(de,W("iterator"),(function(){return this}));var me=function(e,o,n){e.prototype=ie(de,{next:E(1,n)}),ce(e,o+" Iterator")},ue=function(e){return Object(I(e))},Ue=K("IE_PROTO"),te=Object.prototype,re=Object.getPrototypeOf||function(e){return e=ue(e),l(e,Ue)?e[Ue]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?te:null},Ee=W("iterator"),je=!([].keys&&"next"in[].keys()),se=function(){return this},le=function(e,o,n,i,F,a,c){me(n,o,i);var d,m,u,U=function(e){if(!je&&e in s)return s[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},t=o+" Iterator",r="values"==F,E=!1,s=e.prototype,l=s[Ee]||s["@@iterator"]||F&&s[F],f=l||U(F),S=F?r?U("entries"):f:void 0,C="Array"==o&&s.entries||l;if(C&&(u=re(C.call(new e)))!==Object.prototype&&u.next&&(ce(u,t,!0),"function"!=typeof u[Ee]&&j(u,Ee,se)),r&&l&&"values"!==l.name&&(E=!0,f=function(){return l.call(this)}),(je||E||!s[Ee])&&j(s,Ee,f),Q[o]=f,Q[t]=se,F)if(d={values:r?f:U("values"),keys:a?f:U("keys"),entries:S},c)for(m in d)m in s||A(s,m,d[m]);else h(h.P+h.F*(je||E),o,d);return d}(Array,"Array",(function(e,o){this._t=g(e),this._i=0,this._k=o}),(function(){var e=this._t,o=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,z(1)):z(0,"keys"==o?n:"values"==o?e[n]:[n,e[n]])}),"values");Q.Arguments=Q.Array,x("keys"),x("values"),x("entries");for(var fe=W("iterator"),Se=W("toStringTag"),Ce=Q.Array,Ae={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},Me=k(Ae),pe=0;pe<Me.length;pe++){var ye,Be=Me[pe],he=Ae[Be],Te=o[Be],Ge=Te&&Te.prototype;if(Ge&&(Ge[fe]||j(Ge,fe,Ce),Ge[Se]||j(Ge,Se,Be),Q[Be]=Ce,he))for(ye in le)Ge[ye]||A(Ge,ye,le[ye],!0)}var Ie,ge,Le,ve;Ie="keys",ge=function(){return function(e){return k(ue(e))}},Le=(n.Object||{})[Ie]||Object[Ie],(ve={})[Ie]=ge(Le),h(h.S+h.F*a((function(){Le(1)})),"Object",ve);var Pe={AC:{code:"AC",unicode:"U+1F1E6 U+1F1E8",name:"Ascension Island",emoji:"🇦🇨"},AD:{code:"AD",unicode:"U+1F1E6 U+1F1E9",name:"Andorra",emoji:"🇦🇩"},AE:{code:"AE",unicode:"U+1F1E6 U+1F1EA",name:"United Arab Emirates",emoji:"🇦🇪"},AF:{code:"AF",unicode:"U+1F1E6 U+1F1EB",name:"Afghanistan",emoji:"🇦🇫"},AG:{code:"AG",unicode:"U+1F1E6 U+1F1EC",name:"Antigua & Barbuda",emoji:"🇦🇬"},AI:{code:"AI",unicode:"U+1F1E6 U+1F1EE",name:"Anguilla",emoji:"🇦🇮"},AL:{code:"AL",unicode:"U+1F1E6 U+1F1F1",name:"Albania",emoji:"🇦🇱"},AM:{code:"AM",unicode:"U+1F1E6 U+1F1F2",name:"Armenia",emoji:"🇦🇲"},AO:{code:"AO",unicode:"U+1F1E6 U+1F1F4",name:"Angola",emoji:"🇦🇴"},AQ:{code:"AQ",unicode:"U+1F1E6 U+1F1F6",name:"Antarctica",emoji:"🇦🇶"},AR:{code:"AR",unicode:"U+1F1E6 U+1F1F7",name:"Argentina",emoji:"🇦🇷"},AS:{code:"AS",unicode:"U+1F1E6 U+1F1F8",name:"American Samoa",emoji:"🇦🇸"},AT:{code:"AT",unicode:"U+1F1E6 U+1F1F9",name:"Austria",emoji:"🇦🇹"},AU:{code:"AU",unicode:"U+1F1E6 U+1F1FA",name:"Australia",emoji:"🇦🇺"},AW:{code:"AW",unicode:"U+1F1E6 U+1F1FC",name:"Aruba",emoji:"🇦🇼"},AX:{code:"AX",unicode:"U+1F1E6 U+1F1FD",name:"Åland Islands",emoji:"🇦🇽"},AZ:{code:"AZ",unicode:"U+1F1E6 U+1F1FF",name:"Azerbaijan",emoji:"🇦🇿"},BA:{code:"BA",unicode:"U+1F1E7 U+1F1E6",name:"Bosnia & Herzegovina",emoji:"🇧🇦"},BB:{code:"BB",unicode:"U+1F1E7 U+1F1E7",name:"Barbados",emoji:"🇧🇧"},BD:{code:"BD",unicode:"U+1F1E7 U+1F1E9",name:"Bangladesh",emoji:"🇧🇩"},BE:{code:"BE",unicode:"U+1F1E7 U+1F1EA",name:"Belgium",emoji:"🇧🇪"},BF:{code:"BF",unicode:"U+1F1E7 U+1F1EB",name:"Burkina Faso",emoji:"🇧🇫"},BG:{code:"BG",unicode:"U+1F1E7 U+1F1EC",name:"Bulgaria",emoji:"🇧🇬"},BH:{code:"BH",unicode:"U+1F1E7 U+1F1ED",name:"Bahrain",emoji:"🇧🇭"},BI:{code:"BI",unicode:"U+1F1E7 U+1F1EE",name:"Burundi",emoji:"🇧🇮"},BJ:{code:"BJ",unicode:"U+1F1E7 U+1F1EF",name:"Benin",emoji:"🇧🇯"},BL:{code:"BL",unicode:"U+1F1E7 U+1F1F1",name:"St. Barthélemy",emoji:"🇧🇱"},BM:{code:"BM",unicode:"U+1F1E7 U+1F1F2",name:"Bermuda",emoji:"🇧🇲"},BN:{code:"BN",unicode:"U+1F1E7 U+1F1F3",name:"Brunei",emoji:"🇧🇳"},BO:{code:"BO",unicode:"U+1F1E7 U+1F1F4",name:"Bolivia",emoji:"🇧🇴"},BQ:{code:"BQ",unicode:"U+1F1E7 U+1F1F6",name:"Caribbean Netherlands",emoji:"🇧🇶"},BR:{code:"BR",unicode:"U+1F1E7 U+1F1F7",name:"Brazil",emoji:"🇧🇷"},BS:{code:"BS",unicode:"U+1F1E7 U+1F1F8",name:"Bahamas",emoji:"🇧🇸"},BT:{code:"BT",unicode:"U+1F1E7 U+1F1F9",name:"Bhutan",emoji:"🇧🇹"},BV:{code:"BV",unicode:"U+1F1E7 U+1F1FB",name:"Bouvet Island",emoji:"🇧🇻"},BW:{code:"BW",unicode:"U+1F1E7 U+1F1FC",name:"Botswana",emoji:"🇧🇼"},BY:{code:"BY",unicode:"U+1F1E7 U+1F1FE",name:"Belarus",emoji:"🇧🇾"},BZ:{code:"BZ",unicode:"U+1F1E7 U+1F1FF",name:"Belize",emoji:"🇧🇿"},CA:{code:"CA",unicode:"U+1F1E8 U+1F1E6",name:"Canada",emoji:"🇨🇦"},CC:{code:"CC",unicode:"U+1F1E8 U+1F1E8",name:"Cocos (Keeling) Islands",emoji:"🇨🇨"},CD:{code:"CD",unicode:"U+1F1E8 U+1F1E9",name:"Congo - Kinshasa",emoji:"🇨🇩"},CF:{code:"CF",unicode:"U+1F1E8 U+1F1EB",name:"Central African Republic",emoji:"🇨🇫"},CG:{code:"CG",unicode:"U+1F1E8 U+1F1EC",name:"Congo - Brazzaville",emoji:"🇨🇬"},CH:{code:"CH",unicode:"U+1F1E8 U+1F1ED",name:"Switzerland",emoji:"🇨🇭"},CI:{code:"CI",unicode:"U+1F1E8 U+1F1EE",name:"Côte d’Ivoire",emoji:"🇨🇮"},CK:{code:"CK",unicode:"U+1F1E8 U+1F1F0",name:"Cook Islands",emoji:"🇨🇰"},CL:{code:"CL",unicode:"U+1F1E8 U+1F1F1",name:"Chile",emoji:"🇨🇱"},CM:{code:"CM",unicode:"U+1F1E8 U+1F1F2",name:"Cameroon",emoji:"🇨🇲"},CN:{code:"CN",unicode:"U+1F1E8 U+1F1F3",name:"China",emoji:"🇨🇳"},CO:{code:"CO",unicode:"U+1F1E8 U+1F1F4",name:"Colombia",emoji:"🇨🇴"},CP:{code:"CP",unicode:"U+1F1E8 U+1F1F5",name:"Clipperton Island",emoji:"🇨🇵"},CR:{code:"CR",unicode:"U+1F1E8 U+1F1F7",name:"Costa Rica",emoji:"🇨🇷"},CU:{code:"CU",unicode:"U+1F1E8 U+1F1FA",name:"Cuba",emoji:"🇨🇺"},CV:{code:"CV",unicode:"U+1F1E8 U+1F1FB",name:"Cape Verde",emoji:"🇨🇻"},CW:{code:"CW",unicode:"U+1F1E8 U+1F1FC",name:"Curaçao",emoji:"🇨🇼"},CX:{code:"CX",unicode:"U+1F1E8 U+1F1FD",name:"Christmas Island",emoji:"🇨🇽"},CY:{code:"CY",unicode:"U+1F1E8 U+1F1FE",name:"Cyprus",emoji:"🇨🇾"},CZ:{code:"CZ",unicode:"U+1F1E8 U+1F1FF",name:"Czechia",emoji:"🇨🇿"},DE:{code:"DE",unicode:"U+1F1E9 U+1F1EA",name:"Germany",emoji:"🇩🇪"},DG:{code:"DG",unicode:"U+1F1E9 U+1F1EC",name:"Diego Garcia",emoji:"🇩🇬"},DJ:{code:"DJ",unicode:"U+1F1E9 U+1F1EF",name:"Djibouti",emoji:"🇩🇯"},DK:{code:"DK",unicode:"U+1F1E9 U+1F1F0",name:"Denmark",emoji:"🇩🇰"},DM:{code:"DM",unicode:"U+1F1E9 U+1F1F2",name:"Dominica",emoji:"🇩🇲"},DO:{code:"DO",unicode:"U+1F1E9 U+1F1F4",name:"Dominican Republic",emoji:"🇩🇴"},DZ:{code:"DZ",unicode:"U+1F1E9 U+1F1FF",name:"Algeria",emoji:"🇩🇿"},EA:{code:"EA",unicode:"U+1F1EA U+1F1E6",name:"Ceuta & Melilla",emoji:"🇪🇦"},EC:{code:"EC",unicode:"U+1F1EA U+1F1E8",name:"Ecuador",emoji:"🇪🇨"},EE:{code:"EE",unicode:"U+1F1EA U+1F1EA",name:"Estonia",emoji:"🇪🇪"},EG:{code:"EG",unicode:"U+1F1EA U+1F1EC",name:"Egypt",emoji:"🇪🇬"},EH:{code:"EH",unicode:"U+1F1EA U+1F1ED",name:"Western Sahara",emoji:"🇪🇭"},ER:{code:"ER",unicode:"U+1F1EA U+1F1F7",name:"Eritrea",emoji:"🇪🇷"},ES:{code:"ES",unicode:"U+1F1EA U+1F1F8",name:"Spain",emoji:"🇪🇸"},ET:{code:"ET",unicode:"U+1F1EA U+1F1F9",name:"Ethiopia",emoji:"🇪🇹"},EU:{code:"EU",unicode:"U+1F1EA U+1F1FA",name:"European Union",emoji:"🇪🇺"},FI:{code:"FI",unicode:"U+1F1EB U+1F1EE",name:"Finland",emoji:"🇫🇮"},FJ:{code:"FJ",unicode:"U+1F1EB U+1F1EF",name:"Fiji",emoji:"🇫🇯"},FK:{code:"FK",unicode:"U+1F1EB U+1F1F0",name:"Falkland Islands",emoji:"🇫🇰"},FM:{code:"FM",unicode:"U+1F1EB U+1F1F2",name:"Micronesia",emoji:"🇫🇲"},FO:{code:"FO",unicode:"U+1F1EB U+1F1F4",name:"Faroe Islands",emoji:"🇫🇴"},FR:{code:"FR",unicode:"U+1F1EB U+1F1F7",name:"France",emoji:"🇫🇷"},GA:{code:"GA",unicode:"U+1F1EC U+1F1E6",name:"Gabon",emoji:"🇬🇦"},GB:{code:"GB",unicode:"U+1F1EC U+1F1E7",name:"United Kingdom",emoji:"🇬🇧"},GD:{code:"GD",unicode:"U+1F1EC U+1F1E9",name:"Grenada",emoji:"🇬🇩"},GE:{code:"GE",unicode:"U+1F1EC U+1F1EA",name:"Georgia",emoji:"🇬🇪"},GF:{code:"GF",unicode:"U+1F1EC U+1F1EB",name:"French Guiana",emoji:"🇬🇫"},GG:{code:"GG",unicode:"U+1F1EC U+1F1EC",name:"Guernsey",emoji:"🇬🇬"},GH:{code:"GH",unicode:"U+1F1EC U+1F1ED",name:"Ghana",emoji:"🇬🇭"},GI:{code:"GI",unicode:"U+1F1EC U+1F1EE",name:"Gibraltar",emoji:"🇬🇮"},GL:{code:"GL",unicode:"U+1F1EC U+1F1F1",name:"Greenland",emoji:"🇬🇱"},GM:{code:"GM",unicode:"U+1F1EC U+1F1F2",name:"Gambia",emoji:"🇬🇲"},GN:{code:"GN",unicode:"U+1F1EC U+1F1F3",name:"Guinea",emoji:"🇬🇳"},GP:{code:"GP",unicode:"U+1F1EC U+1F1F5",name:"Guadeloupe",emoji:"🇬🇵"},GQ:{code:"GQ",unicode:"U+1F1EC U+1F1F6",name:"Equatorial Guinea",emoji:"🇬🇶"},GR:{code:"GR",unicode:"U+1F1EC U+1F1F7",name:"Greece",emoji:"🇬🇷"},GS:{code:"GS",unicode:"U+1F1EC U+1F1F8",name:"South Georgia & South Sandwich Islands",emoji:"🇬🇸"},GT:{code:"GT",unicode:"U+1F1EC U+1F1F9",name:"Guatemala",emoji:"🇬🇹"},GU:{code:"GU",unicode:"U+1F1EC U+1F1FA",name:"Guam",emoji:"🇬🇺"},GW:{code:"GW",unicode:"U+1F1EC U+1F1FC",name:"Guinea-Bissau",emoji:"🇬🇼"},GY:{code:"GY",unicode:"U+1F1EC U+1F1FE",name:"Guyana",emoji:"🇬🇾"},HK:{code:"HK",unicode:"U+1F1ED U+1F1F0",name:"Hong Kong SAR China",emoji:"🇭🇰"},HM:{code:"HM",unicode:"U+1F1ED U+1F1F2",name:"Heard & McDonald Islands",emoji:"🇭🇲"},HN:{code:"HN",unicode:"U+1F1ED U+1F1F3",name:"Honduras",emoji:"🇭🇳"},HR:{code:"HR",unicode:"U+1F1ED U+1F1F7",name:"Croatia",emoji:"🇭🇷"},HT:{code:"HT",unicode:"U+1F1ED U+1F1F9",name:"Haiti",emoji:"🇭🇹"},HU:{code:"HU",unicode:"U+1F1ED U+1F1FA",name:"Hungary",emoji:"🇭🇺"},IC:{code:"IC",unicode:"U+1F1EE U+1F1E8",name:"Canary Islands",emoji:"🇮🇨"},ID:{code:"ID",unicode:"U+1F1EE U+1F1E9",name:"Indonesia",emoji:"🇮🇩"},IE:{code:"IE",unicode:"U+1F1EE U+1F1EA",name:"Ireland",emoji:"🇮🇪"},IL:{code:"IL",unicode:"U+1F1EE U+1F1F1",name:"Israel",emoji:"🇮🇱"},IM:{code:"IM",unicode:"U+1F1EE U+1F1F2",name:"Isle of Man",emoji:"🇮🇲"},IN:{code:"IN",unicode:"U+1F1EE U+1F1F3",name:"India",emoji:"🇮🇳"},IO:{code:"IO",unicode:"U+1F1EE U+1F1F4",name:"British Indian Ocean Territory",emoji:"🇮🇴"},IQ:{code:"IQ",unicode:"U+1F1EE U+1F1F6",name:"Iraq",emoji:"🇮🇶"},IR:{code:"IR",unicode:"U+1F1EE U+1F1F7",name:"Iran",emoji:"🇮🇷"},IS:{code:"IS",unicode:"U+1F1EE U+1F1F8",name:"Iceland",emoji:"🇮🇸"},IT:{code:"IT",unicode:"U+1F1EE U+1F1F9",name:"Italy",emoji:"🇮🇹"},JE:{code:"JE",unicode:"U+1F1EF U+1F1EA",name:"Jersey",emoji:"🇯🇪"},JM:{code:"JM",unicode:"U+1F1EF U+1F1F2",name:"Jamaica",emoji:"🇯🇲"},JO:{code:"JO",unicode:"U+1F1EF U+1F1F4",name:"Jordan",emoji:"🇯🇴"},JP:{code:"JP",unicode:"U+1F1EF U+1F1F5",name:"Japan",emoji:"🇯🇵"},KE:{code:"KE",unicode:"U+1F1F0 U+1F1EA",name:"Kenya",emoji:"🇰🇪"},KG:{code:"KG",unicode:"U+1F1F0 U+1F1EC",name:"Kyrgyzstan",emoji:"🇰🇬"},KH:{code:"KH",unicode:"U+1F1F0 U+1F1ED",name:"Cambodia",emoji:"🇰🇭"},KI:{code:"KI",unicode:"U+1F1F0 U+1F1EE",name:"Kiribati",emoji:"🇰🇮"},KM:{code:"KM",unicode:"U+1F1F0 U+1F1F2",name:"Comoros",emoji:"🇰🇲"},KN:{code:"KN",unicode:"U+1F1F0 U+1F1F3",name:"St. Kitts & Nevis",emoji:"🇰🇳"},KP:{code:"KP",unicode:"U+1F1F0 U+1F1F5",name:"North Korea",emoji:"🇰🇵"},KR:{code:"KR",unicode:"U+1F1F0 U+1F1F7",name:"South Korea",emoji:"🇰🇷"},KW:{code:"KW",unicode:"U+1F1F0 U+1F1FC",name:"Kuwait",emoji:"🇰🇼"},KY:{code:"KY",unicode:"U+1F1F0 U+1F1FE",name:"Cayman Islands",emoji:"🇰🇾"},KZ:{code:"KZ",unicode:"U+1F1F0 U+1F1FF",name:"Kazakhstan",emoji:"🇰🇿"},LA:{code:"LA",unicode:"U+1F1F1 U+1F1E6",name:"Laos",emoji:"🇱🇦"},LB:{code:"LB",unicode:"U+1F1F1 U+1F1E7",name:"Lebanon",emoji:"🇱🇧"},LC:{code:"LC",unicode:"U+1F1F1 U+1F1E8",name:"St. Lucia",emoji:"🇱🇨"},LI:{code:"LI",unicode:"U+1F1F1 U+1F1EE",name:"Liechtenstein",emoji:"🇱🇮"},LK:{code:"LK",unicode:"U+1F1F1 U+1F1F0",name:"Sri Lanka",emoji:"🇱🇰"},LR:{code:"LR",unicode:"U+1F1F1 U+1F1F7",name:"Liberia",emoji:"🇱🇷"},LS:{code:"LS",unicode:"U+1F1F1 U+1F1F8",name:"Lesotho",emoji:"🇱🇸"},LT:{code:"LT",unicode:"U+1F1F1 U+1F1F9",name:"Lithuania",emoji:"🇱🇹"},LU:{code:"LU",unicode:"U+1F1F1 U+1F1FA",name:"Luxembourg",emoji:"🇱🇺"},LV:{code:"LV",unicode:"U+1F1F1 U+1F1FB",name:"Latvia",emoji:"🇱🇻"},LY:{code:"LY",unicode:"U+1F1F1 U+1F1FE",name:"Libya",emoji:"🇱🇾"},MA:{code:"MA",unicode:"U+1F1F2 U+1F1E6",name:"Morocco",emoji:"🇲🇦"},MC:{code:"MC",unicode:"U+1F1F2 U+1F1E8",name:"Monaco",emoji:"🇲🇨"},MD:{code:"MD",unicode:"U+1F1F2 U+1F1E9",name:"Moldova",emoji:"🇲🇩"},ME:{code:"ME",unicode:"U+1F1F2 U+1F1EA",name:"Montenegro",emoji:"🇲🇪"},MF:{code:"MF",unicode:"U+1F1F2 U+1F1EB",name:"St. Martin",emoji:"🇲🇫"},MG:{code:"MG",unicode:"U+1F1F2 U+1F1EC",name:"Madagascar",emoji:"🇲🇬"},MH:{code:"MH",unicode:"U+1F1F2 U+1F1ED",name:"Marshall Islands",emoji:"🇲🇭"},MK:{code:"MK",unicode:"U+1F1F2 U+1F1F0",name:"Macedonia",emoji:"🇲🇰"},ML:{code:"ML",unicode:"U+1F1F2 U+1F1F1",name:"Mali",emoji:"🇲🇱"},MM:{code:"MM",unicode:"U+1F1F2 U+1F1F2",name:"Myanmar (Burma)",emoji:"🇲🇲"},MN:{code:"MN",unicode:"U+1F1F2 U+1F1F3",name:"Mongolia",emoji:"🇲🇳"},MO:{code:"MO",unicode:"U+1F1F2 U+1F1F4",name:"Macau SAR China",emoji:"🇲🇴"},MP:{code:"MP",unicode:"U+1F1F2 U+1F1F5",name:"Northern Mariana Islands",emoji:"🇲🇵"},MQ:{code:"MQ",unicode:"U+1F1F2 U+1F1F6",name:"Martinique",emoji:"🇲🇶"},MR:{code:"MR",unicode:"U+1F1F2 U+1F1F7",name:"Mauritania",emoji:"🇲🇷"},MS:{code:"MS",unicode:"U+1F1F2 U+1F1F8",name:"Montserrat",emoji:"🇲🇸"},MT:{code:"MT",unicode:"U+1F1F2 U+1F1F9",name:"Malta",emoji:"🇲🇹"},MU:{code:"MU",unicode:"U+1F1F2 U+1F1FA",name:"Mauritius",emoji:"🇲🇺"},MV:{code:"MV",unicode:"U+1F1F2 U+1F1FB",name:"Maldives",emoji:"🇲🇻"},MW:{code:"MW",unicode:"U+1F1F2 U+1F1FC",name:"Malawi",emoji:"🇲🇼"},MX:{code:"MX",unicode:"U+1F1F2 U+1F1FD",name:"Mexico",emoji:"🇲🇽"},MY:{code:"MY",unicode:"U+1F1F2 U+1F1FE",name:"Malaysia",emoji:"🇲🇾"},MZ:{code:"MZ",unicode:"U+1F1F2 U+1F1FF",name:"Mozambique",emoji:"🇲🇿"},NA:{code:"NA",unicode:"U+1F1F3 U+1F1E6",name:"Namibia",emoji:"🇳🇦"},NC:{code:"NC",unicode:"U+1F1F3 U+1F1E8",name:"New Caledonia",emoji:"🇳🇨"},NE:{code:"NE",unicode:"U+1F1F3 U+1F1EA",name:"Niger",emoji:"🇳🇪"},NF:{code:"NF",unicode:"U+1F1F3 U+1F1EB",name:"Norfolk Island",emoji:"🇳🇫"},NG:{code:"NG",unicode:"U+1F1F3 U+1F1EC",name:"Nigeria",emoji:"🇳🇬"},NI:{code:"NI",unicode:"U+1F1F3 U+1F1EE",name:"Nicaragua",emoji:"🇳🇮"},NL:{code:"NL",unicode:"U+1F1F3 U+1F1F1",name:"Netherlands",emoji:"🇳🇱"},NO:{code:"NO",unicode:"U+1F1F3 U+1F1F4",name:"Norway",emoji:"🇳🇴"},NP:{code:"NP",unicode:"U+1F1F3 U+1F1F5",name:"Nepal",emoji:"🇳🇵"},NR:{code:"NR",unicode:"U+1F1F3 U+1F1F7",name:"Nauru",emoji:"🇳🇷"},NU:{code:"NU",unicode:"U+1F1F3 U+1F1FA",name:"Niue",emoji:"🇳🇺"},NZ:{code:"NZ",unicode:"U+1F1F3 U+1F1FF",name:"New Zealand",emoji:"🇳🇿"},OM:{code:"OM",unicode:"U+1F1F4 U+1F1F2",name:"Oman",emoji:"🇴🇲"},PA:{code:"PA",unicode:"U+1F1F5 U+1F1E6",name:"Panama",emoji:"🇵🇦"},PE:{code:"PE",unicode:"U+1F1F5 U+1F1EA",name:"Peru",emoji:"🇵🇪"},PF:{code:"PF",unicode:"U+1F1F5 U+1F1EB",name:"French Polynesia",emoji:"🇵🇫"},PG:{code:"PG",unicode:"U+1F1F5 U+1F1EC",name:"Papua New Guinea",emoji:"🇵🇬"},PH:{code:"PH",unicode:"U+1F1F5 U+1F1ED",name:"Philippines",emoji:"🇵🇭"},PK:{code:"PK",unicode:"U+1F1F5 U+1F1F0",name:"Pakistan",emoji:"🇵🇰"},PL:{code:"PL",unicode:"U+1F1F5 U+1F1F1",name:"Poland",emoji:"🇵🇱"},PM:{code:"PM",unicode:"U+1F1F5 U+1F1F2",name:"St. Pierre & Miquelon",emoji:"🇵🇲"},PN:{code:"PN",unicode:"U+1F1F5 U+1F1F3",name:"Pitcairn Islands",emoji:"🇵🇳"},PR:{code:"PR",unicode:"U+1F1F5 U+1F1F7",name:"Puerto Rico",emoji:"🇵🇷"},PS:{code:"PS",unicode:"U+1F1F5 U+1F1F8",name:"Palestinian Territories",emoji:"🇵🇸"},PT:{code:"PT",unicode:"U+1F1F5 U+1F1F9",name:"Portugal",emoji:"🇵🇹"},PW:{code:"PW",unicode:"U+1F1F5 U+1F1FC",name:"Palau",emoji:"🇵🇼"},PY:{code:"PY",unicode:"U+1F1F5 U+1F1FE",name:"Paraguay",emoji:"🇵🇾"},QA:{code:"QA",unicode:"U+1F1F6 U+1F1E6",name:"Qatar",emoji:"🇶🇦"},RE:{code:"RE",unicode:"U+1F1F7 U+1F1EA",name:"Réunion",emoji:"🇷🇪"},RO:{code:"RO",unicode:"U+1F1F7 U+1F1F4",name:"Romania",emoji:"🇷🇴"},RS:{code:"RS",unicode:"U+1F1F7 U+1F1F8",name:"Serbia",emoji:"🇷🇸"},RU:{code:"RU",unicode:"U+1F1F7 U+1F1FA",name:"Russia",emoji:"🇷🇺"},RW:{code:"RW",unicode:"U+1F1F7 U+1F1FC",name:"Rwanda",emoji:"🇷🇼"},SA:{code:"SA",unicode:"U+1F1F8 U+1F1E6",name:"Saudi Arabia",emoji:"🇸🇦"},SB:{code:"SB",unicode:"U+1F1F8 U+1F1E7",name:"Solomon Islands",emoji:"🇸🇧"},SC:{code:"SC",unicode:"U+1F1F8 U+1F1E8",name:"Seychelles",emoji:"🇸🇨"},SD:{code:"SD",unicode:"U+1F1F8 U+1F1E9",name:"Sudan",emoji:"🇸🇩"},SE:{code:"SE",unicode:"U+1F1F8 U+1F1EA",name:"Sweden",emoji:"🇸🇪"},SG:{code:"SG",unicode:"U+1F1F8 U+1F1EC",name:"Singapore",emoji:"🇸🇬"},SH:{code:"SH",unicode:"U+1F1F8 U+1F1ED",name:"St. Helena",emoji:"🇸🇭"},SI:{code:"SI",unicode:"U+1F1F8 U+1F1EE",name:"Slovenia",emoji:"🇸🇮"},SJ:{code:"SJ",unicode:"U+1F1F8 U+1F1EF",name:"Svalbard & Jan Mayen",emoji:"🇸🇯"},SK:{code:"SK",unicode:"U+1F1F8 U+1F1F0",name:"Slovakia",emoji:"🇸🇰"},SL:{code:"SL",unicode:"U+1F1F8 U+1F1F1",name:"Sierra Leone",emoji:"🇸🇱"},SM:{code:"SM",unicode:"U+1F1F8 U+1F1F2",name:"San Marino",emoji:"🇸🇲"},SN:{code:"SN",unicode:"U+1F1F8 U+1F1F3",name:"Senegal",emoji:"🇸🇳"},SO:{code:"SO",unicode:"U+1F1F8 U+1F1F4",name:"Somalia",emoji:"🇸🇴"},SR:{code:"SR",unicode:"U+1F1F8 U+1F1F7",name:"Suriname",emoji:"🇸🇷"},SS:{code:"SS",unicode:"U+1F1F8 U+1F1F8",name:"South Sudan",emoji:"🇸🇸"},ST:{code:"ST",unicode:"U+1F1F8 U+1F1F9",name:"São Tomé & Príncipe",emoji:"🇸🇹"},SV:{code:"SV",unicode:"U+1F1F8 U+1F1FB",name:"El Salvador",emoji:"🇸🇻"},SX:{code:"SX",unicode:"U+1F1F8 U+1F1FD",name:"Sint Maarten",emoji:"🇸🇽"},SY:{code:"SY",unicode:"U+1F1F8 U+1F1FE",name:"Syria",emoji:"🇸🇾"},SZ:{code:"SZ",unicode:"U+1F1F8 U+1F1FF",name:"Swaziland",emoji:"🇸🇿"},TA:{code:"TA",unicode:"U+1F1F9 U+1F1E6",name:"Tristan da Cunha",emoji:"🇹🇦"},TC:{code:"TC",unicode:"U+1F1F9 U+1F1E8",name:"Turks & Caicos Islands",emoji:"🇹🇨"},TD:{code:"TD",unicode:"U+1F1F9 U+1F1E9",name:"Chad",emoji:"🇹🇩"},TF:{code:"TF",unicode:"U+1F1F9 U+1F1EB",name:"French Southern Territories",emoji:"🇹🇫"},TG:{code:"TG",unicode:"U+1F1F9 U+1F1EC",name:"Togo",emoji:"🇹🇬"},TH:{code:"TH",unicode:"U+1F1F9 U+1F1ED",name:"Thailand",emoji:"🇹🇭"},TJ:{code:"TJ",unicode:"U+1F1F9 U+1F1EF",name:"Tajikistan",emoji:"🇹🇯"},TK:{code:"TK",unicode:"U+1F1F9 U+1F1F0",name:"Tokelau",emoji:"🇹🇰"},TL:{code:"TL",unicode:"U+1F1F9 U+1F1F1",name:"Timor-Leste",emoji:"🇹🇱"},TM:{code:"TM",unicode:"U+1F1F9 U+1F1F2",name:"Turkmenistan",emoji:"🇹🇲"},TN:{code:"TN",unicode:"U+1F1F9 U+1F1F3",name:"Tunisia",emoji:"🇹🇳"},TO:{code:"TO",unicode:"U+1F1F9 U+1F1F4",name:"Tonga",emoji:"🇹🇴"},TR:{code:"TR",unicode:"U+1F1F9 U+1F1F7",name:"Turkey",emoji:"🇹🇷"},TT:{code:"TT",unicode:"U+1F1F9 U+1F1F9",name:"Trinidad & Tobago",emoji:"🇹🇹"},TV:{code:"TV",unicode:"U+1F1F9 U+1F1FB",name:"Tuvalu",emoji:"🇹🇻"},TW:{code:"TW",unicode:"U+1F1F9 U+1F1FC",name:"Taiwan",emoji:"🇹🇼"},TZ:{code:"TZ",unicode:"U+1F1F9 U+1F1FF",name:"Tanzania",emoji:"🇹🇿"},UA:{code:"UA",unicode:"U+1F1FA U+1F1E6",name:"Ukraine",emoji:"🇺🇦"},UG:{code:"UG",unicode:"U+1F1FA U+1F1EC",name:"Uganda",emoji:"🇺🇬"},UM:{code:"UM",unicode:"U+1F1FA U+1F1F2",name:"U.S. Outlying Islands",emoji:"🇺🇲"},UN:{code:"UN",unicode:"U+1F1FA U+1F1F3",name:"United Nations",emoji:"🇺🇳"},US:{code:"US",unicode:"U+1F1FA U+1F1F8",name:"United States",emoji:"🇺🇸"},UY:{code:"UY",unicode:"U+1F1FA U+1F1FE",name:"Uruguay",emoji:"🇺🇾"},UZ:{code:"UZ",unicode:"U+1F1FA U+1F1FF",name:"Uzbekistan",emoji:"🇺🇿"},VA:{code:"VA",unicode:"U+1F1FB U+1F1E6",name:"Vatican City",emoji:"🇻🇦"},VC:{code:"VC",unicode:"U+1F1FB U+1F1E8",name:"St. Vincent & Grenadines",emoji:"🇻🇨"},VE:{code:"VE",unicode:"U+1F1FB U+1F1EA",name:"Venezuela",emoji:"🇻🇪"},VG:{code:"VG",unicode:"U+1F1FB U+1F1EC",name:"British Virgin Islands",emoji:"🇻🇬"},VI:{code:"VI",unicode:"U+1F1FB U+1F1EE",name:"U.S. Virgin Islands",emoji:"🇻🇮"},VN:{code:"VN",unicode:"U+1F1FB U+1F1F3",name:"Vietnam",emoji:"🇻🇳"},VU:{code:"VU",unicode:"U+1F1FB U+1F1FA",name:"Vanuatu",emoji:"🇻🇺"},WF:{code:"WF",unicode:"U+1F1FC U+1F1EB",name:"Wallis & Futuna",emoji:"🇼🇫"},WS:{code:"WS",unicode:"U+1F1FC U+1F1F8",name:"Samoa",emoji:"🇼🇸"},XK:{code:"XK",unicode:"U+1F1FD U+1F1F0",name:"Kosovo",emoji:"🇽🇰"},YE:{code:"YE",unicode:"U+1F1FE U+1F1EA",name:"Yemen",emoji:"🇾🇪"},YT:{code:"YT",unicode:"U+1F1FE U+1F1F9",name:"Mayotte",emoji:"🇾🇹"},ZA:{code:"ZA",unicode:"U+1F1FF U+1F1E6",name:"South Africa",emoji:"🇿🇦"},ZM:{code:"ZM",unicode:"U+1F1FF U+1F1F2",name:"Zambia",emoji:"🇿🇲"},ZW:{code:"ZW",unicode:"U+1F1FF U+1F1FC",name:"Zimbabwe",emoji:"🇿🇼"}},be=Object.keys(Pe),Ne=Object.values(Pe);return{data:Pe,countryCodes:be,list:Ne,get:function(e){if(void 0===e)return Ne;if("string"==typeof e){var o=e.toUpperCase();return Object.prototype.hasOwnProperty.call(Pe,o)?Pe[o]:void 0}}}}));
