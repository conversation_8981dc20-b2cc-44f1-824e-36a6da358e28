<!-- client/.app/app/pages/users/settings.vue -->

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20 dark:bg-muted-900 min-h-screen">
    <TairoContentWrapper>
      <template #left>
        <BaseHeading
          tag="h1"
          size="xl"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          User Settings
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
          Manage your application preferences and notification settings
        </BaseParagraph>
      </template>

      <div class="mt-6">
        <BaseTabs
          v-model="activeTab"
          :tabs="[
            {
              label: 'Appearance',
              value: 'appearance',
              icon: 'solar:palette-linear',
            },
            {
              label: 'Notifications',
              value: 'notifications',
              icon: 'solar:bell-linear',
            },
            {
              label: 'Privacy',
              value: 'privacy',
              icon: 'solar:shield-check-linear',
            },
          ]"
          color="primary"
        />

        <div class="mt-6">
          <!-- Appearance Tab -->
          <BaseCard
            v-show="activeTab === 'appearance'"
            rounded="lg"
            class="p-6"
          >
            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="mb-6 text-muted-800 dark:text-white"
            >
              Appearance Settings
            </BaseHeading>

            <div class="mb-6">
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-2 text-muted-800 dark:text-white"
              >
                Theme
              </BaseHeading>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div
                  class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="{
                    'border-primary-500 bg-primary-500/10':
                      preferences.theme === 'light',
                    'border-muted-200 dark:border-muted-700':
                      preferences.theme !== 'light',
                  }"
                  @click="updateTheme('light')"
                >
                  <div class="flex items-center justify-between mb-2">
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Light Mode
                    </BaseHeading>
                    <div
                      v-if="preferences.theme === 'light'"
                      class="h-5 w-5 rounded-full bg-primary-500 flex items-center justify-center"
                    >
                      <Icon
                        name="solar:check-circle-linear"
                        class="h-3 w-3 text-white"
                      />
                    </div>
                  </div>
                  <div
                    class="h-20 bg-white border border-muted-200 rounded-md"
                  ></div>
                </div>

                <div
                  class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="{
                    'border-primary-500 bg-primary-500/10':
                      preferences.theme === 'dark',
                    'border-muted-200 dark:border-muted-700':
                      preferences.theme !== 'dark',
                  }"
                  @click="updateTheme('dark')"
                >
                  <div class="flex items-center justify-between mb-2">
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Dark Mode
                    </BaseHeading>
                    <div
                      v-if="preferences.theme === 'dark'"
                      class="h-5 w-5 rounded-full bg-primary-500 flex items-center justify-center"
                    >
                      <Icon
                        name="solar:check-circle-linear"
                        class="h-3 w-3 text-white"
                      />
                    </div>
                  </div>
                  <div
                    class="h-20 bg-muted-900 border border-muted-700 rounded-md"
                  ></div>
                </div>

                <div
                  class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="{
                    'border-primary-500 bg-primary-500/10':
                      preferences.theme === 'system',
                    'border-muted-200 dark:border-muted-700':
                      preferences.theme !== 'system',
                  }"
                  @click="updateTheme('system')"
                >
                  <div class="flex items-center justify-between mb-2">
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      System Default
                    </BaseHeading>
                    <div
                      v-if="preferences.theme === 'system'"
                      class="h-5 w-5 rounded-full bg-primary-500 flex items-center justify-center"
                    >
                      <Icon
                        name="solar:check-circle-linear"
                        class="h-3 w-3 text-white"
                      />
                    </div>
                  </div>
                  <div
                    class="h-20 bg-gradient-to-r from-white to-muted-900 border border-muted-200 rounded-md"
                  ></div>
                </div>
              </div>
            </div>

            <div class="mb-6">
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-2 text-muted-800 dark:text-white"
              >
                Language
              </BaseHeading>
              <BaseSelect
                v-model="selectedLanguage"
                :options="languageOptions"
                placeholder="Select a language"
                @update:modelValue="updateLanguage"
              />
            </div>
          </BaseCard>

          <!-- Notifications Tab -->
          <BaseCard
            v-show="activeTab === 'notifications'"
            rounded="lg"
            class="p-6"
          >
            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="mb-6 text-muted-800 dark:text-white"
            >
              Notification Settings
            </BaseHeading>

            <div class="mb-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Enable Notifications
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about important updates and activities
                  </BaseParagraph>
                </div>
                <BaseSwitch
                  v-model="notificationSettings.enabled"
                  @update:modelValue="updateNotificationSettings('enabled')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Auto-dismiss Low Priority
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Automatically dismiss low priority notifications
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="notificationSettings.flushLowPriority"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="
                    updateNotificationSettings('flushLowPriority')
                  "
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Marketing Notifications
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive updates about new features and promotions
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="notificationSettings.marketing"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateNotificationSettings('marketing')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Partner Notifications
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive updates from our partners
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="notificationSettings.partners"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateNotificationSettings('partners')"
                />
              </div>
            </div>

            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="mb-4 mt-8 text-muted-800 dark:text-white"
            >
              Security Notifications
            </BaseHeading>

            <div class="mb-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Session Notifications
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about new login sessions
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="securitySettings.session"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateSecuritySettings('session')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Password Change Notifications
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications when your password is changed
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="securitySettings.password"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateSecuritySettings('password')"
                />
              </div>
            </div>

            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="mb-4 mt-8 text-muted-800 dark:text-white"
            >
              Activity Notifications
            </BaseHeading>

            <div>
              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Incoming Payments
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about incoming payments
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="activitySettings.incoming"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateActivitySettings('incoming')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Outgoing Payments
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about outgoing payments
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="activitySettings.outgoing"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateActivitySettings('outgoing')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Failed Payments
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about failed payments
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="activitySettings.failed"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateActivitySettings('failed')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Uncashed Payments
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive notifications about uncashed payments
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="activitySettings.uncashed"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateActivitySettings('uncashed')"
                />
              </div>

              <div class="flex items-center justify-between mb-4">
                <div>
                  <BaseHeading
                    tag="h3"
                    size="sm"
                    weight="medium"
                    class="text-muted-800 dark:text-white"
                  >
                    Payment Reminders
                  </BaseHeading>
                  <BaseText
                    size="xs"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    Receive reminders about upcoming payments
                  </BaseText>
                </div>
                <BaseSwitch
                  v-model="activitySettings.payments"
                  :disabled="!notificationSettings.enabled"
                  @update:modelValue="updateActivitySettings('payments')"
                />
              </div>
            </div>
          </BaseCard>

          <!-- Privacy Tab -->
          <BaseCard v-show="activeTab === 'privacy'" rounded="lg" class="p-6">
            <BaseHeading
              tag="h2"
              size="md"
              weight="medium"
              class="mb-6 text-muted-800 dark:text-white"
            >
              Privacy Settings
            </BaseHeading>

            <div class="mb-8">
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-4 text-muted-800 dark:text-white"
              >
                Data Usage
              </BaseHeading>

              <div class="space-y-4">
                <div class="flex items-start">
                  <BaseCheckbox
                    v-model="privacySettings.allowDataCollection"
                    class="mt-0.5 mr-3"
                    @update:modelValue="
                      updatePrivacySettings('allowDataCollection')
                    "
                  />
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Allow Data Collection
                    </BaseHeading>
                    <BaseParagraph
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Allow us to collect anonymous usage data to improve our
                      services
                    </BaseParagraph>
                  </div>
                </div>

                <div class="flex items-start">
                  <BaseCheckbox
                    v-model="privacySettings.allowCookies"
                    class="mt-0.5 mr-3"
                    @update:modelValue="updatePrivacySettings('allowCookies')"
                  />
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Allow Cookies
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Allow us to use cookies to enhance your browsing
                      experience
                    </BaseText>
                  </div>
                </div>

                <div class="flex items-start">
                  <BaseCheckbox
                    v-model="privacySettings.allowThirdParty"
                    class="mt-0.5 mr-3"
                    @update:modelValue="
                      updatePrivacySettings('allowThirdParty')
                    "
                  />
                  <div>
                    <BaseHeading
                      tag="h4"
                      size="xs"
                      weight="medium"
                      class="text-muted-800 dark:text-white"
                    >
                      Allow Third-Party Sharing
                    </BaseHeading>
                    <BaseText
                      size="xs"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      Allow us to share your data with trusted third-party
                      services
                    </BaseText>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-8">
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="mb-4 text-muted-800 dark:text-white"
              >
                Account Data
              </BaseHeading>

              <div class="space-y-4">
                <BaseButton variant="outline" color="info">
                  <Icon name="solar:download-linear" class="h-4 w-4 mr-2" />
                  Download My Data
                </BaseButton>

                <BaseButton variant="outline" color="danger">
                  <Icon
                    name="solar:trash-bin-minimalistic-linear"
                    class="h-4 w-4 mr-2"
                  />
                  Delete My Account
                </BaseButton>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>
    </TairoContentWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  useUserStore,
  type UserPreferences,
} from "../../../stores/useUserStore";

// Initialize user store
const userStore = useUserStore();

// State
const activeTab = ref("appearance");
const selectedLanguage = ref("");

// Computed properties
const preferences = computed<UserPreferences>(() => userStore.preferences);

// Form state for notifications
const notificationSettings = ref({
  enabled: true,
  flushLowPriority: false,
  marketing: false,
  partners: false,
});

const securitySettings = ref({
  session: true,
  password: true,
});

const activitySettings = ref({
  incoming: true,
  outgoing: false,
  failed: false,
  uncashed: false,
  payments: true,
});

// Privacy settings
const privacySettings = ref({
  allowDataCollection: true,
  allowCookies: true,
  allowThirdParty: false,
});

// Language options
const languageOptions = [
  { label: "English", value: "en" },
  { label: "Estonian", value: "et" },
  { label: "Spanish", value: "es" },
  { label: "French", value: "fr" },
  { label: "German", value: "de" },
];

// Methods

const updateTheme = async (theme: "light" | "dark" | "system") => {
  try {
    await userStore.updatePreferences({
      theme,
    });
  } catch (error) {
    console.error("Error updating theme:", error);
  }
};

const updateLanguage = async () => {
  try {
    await userStore.updatePreferences({
      language: selectedLanguage.value,
    });
  } catch (error) {
    console.error("Error updating language:", error);
  }
};

const updateNotificationSettings = async (key: string) => {
  try {
    await userStore.updatePreferences({
      notifications: {
        ...preferences.value.notifications,
        [key]:
          notificationSettings.value[
            key as keyof typeof notificationSettings.value
          ],
      },
    });
  } catch (error) {
    console.error(`Error updating notification setting (${key}):`, error);
  }
};

const updateSecuritySettings = async (key: string) => {
  try {
    await userStore.updatePreferences({
      notifications: {
        ...preferences.value.notifications,
        security: {
          ...preferences.value.notifications.security,
          [key]:
            securitySettings.value[key as keyof typeof securitySettings.value],
        },
      },
    });
  } catch (error) {
    console.error(`Error updating security setting (${key}):`, error);
  }
};

const updateActivitySettings = async (key: string) => {
  try {
    await userStore.updatePreferences({
      notifications: {
        ...preferences.value.notifications,
        activity: {
          ...preferences.value.notifications.activity,
          [key]:
            activitySettings.value[key as keyof typeof activitySettings.value],
        },
      },
    });
  } catch (error) {
    console.error(`Error updating activity setting (${key}):`, error);
  }
};

const updatePrivacySettings = async (key: string) => {
  // This would typically update privacy settings on the server
  console.log(
    `Privacy setting updated: ${key} = ${
      privacySettings.value[key as keyof typeof privacySettings.value]
    }`
  );
};

// Initialize settings from user preferences
const initSettings = () => {
  selectedLanguage.value = preferences.value.language;

  notificationSettings.value = {
    enabled: preferences.value.notifications.enabled,
    flushLowPriority: preferences.value.notifications.flushLowPriority,
    marketing: preferences.value.notifications.marketing,
    partners: preferences.value.notifications.partners,
  };

  securitySettings.value = {
    session: preferences.value.notifications.security.session,
    password: preferences.value.notifications.security.password,
  };

  activitySettings.value = {
    incoming: preferences.value.notifications.activity.incoming,
    outgoing: preferences.value.notifications.activity.outgoing,
    failed: preferences.value.notifications.activity.failed,
    uncashed: preferences.value.notifications.activity.uncashed,
    payments: preferences.value.notifications.activity.payments,
  };
};

// Lifecycle hooks
onMounted(async () => {
  // Fetch user data if not already loaded
  if (!userStore.isAuthenticated) {
    await userStore.fetchUser();
  }

  // Initialize settings
  initSettings();
});
</script>
