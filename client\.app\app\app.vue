<script setup lang="ts">
const { locale } = useI18n();
const head = useLocaleHead();
const route = useRoute();

/**
 * Global head configuration
 */
useHead({
  title: () => route?.meta?.title ?? "",
  titleTemplate: (titleChunk) => {
    return titleChunk ? `${titleChunk} - CoManager` : `CoManager`;
  },
  meta: [
    {
      name: "viewport",
      content:
        "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, viewport-fit=cover",
    },
    { name: "description", content: "Company Management System" },

    {
      name: "apple-mobile-web-app-status-bar-style",
      content: "default",
    },
    { name: "mobile-web-app-capable", content: "yes" },
    { name: "apple-mobile-web-app-title", content: "CoManager" },
  ],
  htmlAttrs: {
    lang: () => head.value.htmlAttrs!.lang,
    dir: () => head.value.htmlAttrs!.dir as any,
    style: "scroll-behavior: smooth;",
  },
  link: [
    {
      rel: "icon",
      type: "image/svg+xml",
      href: "/favicon.svg",
    },
    {
      rel: "apple-touch-icon",
      href: "/pwa-192x192.png",
    },
    {
      rel: "mask-icon",
      href: "/favicon.svg",
      color: "#000000",
    },
  ],
});
</script>

<template>
  <BaseProviders
    :config="{ dir: head.htmlAttrs!.dir as any, locale }"
    :toast="{ position: 'top-center' }"
  >
    <NuxtLayout>
      <NuxtLoadingIndicator color="var(--color-primary-500)" />
      <NuxtPage />
    </NuxtLayout>

    <TairoPanels />
    <NuxtPwaManifest />
    <PwaThemeColor />

    <!-- Universal AI Assistant Window - rendered at the highest level -->
    <AiAssistantWindow />
  </BaseProviders>
</template>
