// server/prisma/seed/map-data.ts
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Tallinn coordinates bounds
const TALLINN_BOUNDS = {
  north: 59.4778,
  south: 59.3778,
  east: 24.8278,
  west: 24.6278,
};

// Generate random coordinates within Tallinn
function generateTallinnCoordinates() {
  const lat =
    TALLINN_BOUNDS.south +
    Math.random() * (TALLINN_BOUNDS.north - TALLINN_BOUNDS.south);
  const lng =
    TALLINN_BOUNDS.west +
    Math.random() * (TALLINN_BOUNDS.east - TALLINN_BOUNDS.west);
  return { latitude: lat, longitude: lng };
}

// Estonian project data
const projectsData = [
  {
    name: "Tallinna Kesklinnas Büroohoone Ehitus",
    description: "Kaasaegse büroohoone ehitamine Tallinna kesklinnas",
    status: "IN_PROGRESS" as const,
    startDate: new Date("2024-01-15"),
    endDate: new Date("2024-12-31"),
    budget: 2500000,
  },
  {
    name: "Tartu Ülikooli Uus Teadushoone",
    description: "Teadushoone ehitamine Tartu Ülikooli kampuses",
    status: "PLANNING" as const,
    startDate: new Date("2024-03-01"),
    endDate: new Date("2025-06-30"),
    budget: 4200000,
  },
  {
    name: "Pärnu Rannahotelli Renoveerimine",
    description: "Ajaloolise rannahotelli täielik renoveerimine",
    status: "IN_PROGRESS" as const,
    startDate: new Date("2024-02-01"),
    endDate: new Date("2024-10-15"),
    budget: 1800000,
  },
  {
    name: "Narva Kultuurikeskuse Ehitus",
    description: "Uue kultuurikeskuse ehitamine Narva linnas",
    status: "PLANNING" as const,
    startDate: new Date("2024-05-01"),
    endDate: new Date("2025-08-31"),
    budget: 3100000,
  },
];

// Estonian asset data
const assetsData = [
  {
    name: "Ekskavaator CAT 320",
    description: "Rasketehnika ehitustöödeks",
    type: "MACHINERY" as const,
    status: "ACTIVE" as const,
    serialNumber: "CAT320-EST-001",
    model: "320 GC",
    manufacturer: "Caterpillar",
    purchasePrice: 180000,
    currentValue: 150000,
  },
  {
    name: "Betoonisegur Volvo",
    description: "Betoonisegur ehitusplatsil kasutamiseks",
    type: "VEHICLE" as const,
    status: "ACTIVE" as const,
    serialNumber: "VOLVO-MIX-002",
    model: "FMX 500",
    manufacturer: "Volvo",
    purchasePrice: 220000,
    currentValue: 180000,
  },
  {
    name: "Tornkraan Liebherr",
    description: "Kõrge tornkraan suurte ehitusprojektide jaoks",
    type: "MACHINERY" as const,
    status: "ACTIVE" as const,
    serialNumber: "LH-TOWER-003",
    model: "280 EC-H",
    manufacturer: "Liebherr",
    purchasePrice: 450000,
    currentValue: 380000,
  },
  {
    name: "Kallur Mercedes-Benz",
    description: "Raskeveok materjalide transportimiseks",
    type: "VEHICLE" as const,
    status: "ACTIVE" as const,
    serialNumber: "MB-TRUCK-004",
    model: "Actros 2545",
    manufacturer: "Mercedes-Benz",
    purchasePrice: 120000,
    currentValue: 95000,
  },
  {
    name: "Generaator Caterpillar",
    description: "Elektrigeneraator ehitusplatsi varustamiseks",
    type: "EQUIPMENT" as const,
    status: "MAINTENANCE" as const,
    serialNumber: "CAT-GEN-005",
    model: "C18 ACERT",
    manufacturer: "Caterpillar",
    purchasePrice: 85000,
    currentValue: 70000,
  },
];

// Estonian work locations
const workLocationsData = [
  {
    name: "Tallinna Kesklinnas Ehitusplats",
    description: "Peamine ehitusplats Tallinna kesklinnas",
    address: "Viru väljak 4, Tallinn",
    radius: 100,
    isDefault: true,
  },
  {
    name: "Tartu Ülikooli Kampus",
    description: "Ehitustööde ala Tartu Ülikooli territooriumil",
    address: "Ülikooli 18, Tartu",
    radius: 150,
    isDefault: false,
  },
  {
    name: "Pärnu Rannapiirkond",
    description: "Hotelli renoveerimise ala Pärnu rannas",
    address: "Ranna puiestee 1, Pärnu",
    radius: 80,
    isDefault: false,
  },
  {
    name: "Narva Kultuurikvartali Ala",
    description: "Kultuurikeskuse ehitusala Narvas",
    address: "Puškin 13, Narva",
    radius: 120,
    isDefault: false,
  },
  {
    name: "Tallinna Sadama Laoala",
    description: "Materjalide ladustamise ala sadamas",
    address: "Sadama 25, Tallinn",
    radius: 200,
    isDefault: false,
  },
];

export async function seedMapData() {
  console.log("🌱 Seeding map data...");

  try {
    // Get the first company for relations
    const company = await prisma.company.findFirst();
    if (!company) {
      throw new Error("No company found. Please seed companies first.");
    }

    // Get first worker for project relations
    let firstWorker = await prisma.worker.findFirst();
    if (!firstWorker) {
      // Create a worker if none exists
      const firstUser = await prisma.user.findFirst();
      if (!firstUser) {
        throw new Error("No user found. Please seed users first.");
      }

      firstWorker = await prisma.worker.create({
        data: {
          firstName: firstUser.firstName,
          lastName: firstUser.lastName,
          email: firstUser.email,
          phone: firstUser.phone,
          address: firstUser.address,
          status: "AVAILABLE",
          currentCompanyId: company.id,
        },
      });
      console.log(
        `✅ Created worker: ${firstWorker.firstName} ${firstWorker.lastName}`
      );
    }

    // Seed Projects with coordinates
    console.log("📋 Seeding projects...");
    const projects = [];
    for (let i = 0; i < projectsData.length; i++) {
      const projectData = projectsData[i];
      const coordinates = generateTallinnCoordinates();
      const project = await prisma.project.create({
        data: {
          ...projectData,
          companyId: company.id,
          clientId: company.id, // Using same company as client for simplicity
          projectLeadId: firstWorker.id,
          barcode: `PROJECT${Date.now()}${i}`,
          qrCode: `QR_PROJECT${Date.now()}${i}`,
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          address: `${projectData.name} asukoht`,
        },
      });

      // Create a project location entry (we'll need to add this to the schema)
      // For now, we'll store coordinates in a separate way or extend the project model
      projects.push({ ...project, coordinates });
      console.log(
        `✅ Created project: ${
          project.name
        } at [${coordinates.longitude.toFixed(
          4
        )}, ${coordinates.latitude.toFixed(4)}]`
      );
    }

    // Seed Work Locations
    console.log("📍 Seeding work locations...");
    const workLocations = [];
    for (const locationData of workLocationsData) {
      const coordinates = generateTallinnCoordinates();
      const workLocation = await prisma.workLocation.create({
        data: {
          ...locationData,
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          companyId: company.id,
        },
      });
      workLocations.push(workLocation);
      console.log(`✅ Created work location: ${workLocation.name}`);
    }

    // Seed Trackable Assets
    console.log("🚛 Seeding trackable assets...");
    const assets = [];
    for (const assetData of assetsData) {
      const coordinates = generateTallinnCoordinates();

      // Create trackable asset
      const asset = await prisma.trackableAsset.create({
        data: {
          ...assetData,
          companyId: company.id,
        },
      });

      // Create current location for the asset
      await prisma.trackableAssetLocation.create({
        data: {
          assetId: asset.id,
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          address: `${assetData.name} asukoht`,
          lastUpdate: new Date(),
          isInGeofence: true,
          geofenceName: "Tallinna tööala",
        },
      });

      assets.push(asset);
      console.log(`✅ Created asset: ${asset.name}`);
    }

    // Seed Worker Locations for existing users
    console.log("👷 Seeding worker locations...");
    const users = await prisma.user.findMany({
      where: {
        companies: {
          some: {
            companyId: company.id,
          },
        },
      },
      take: 10, // Limit to first 10 users
    });

    for (const user of users) {
      const coordinates = generateTallinnCoordinates();
      const randomProject =
        projects[Math.floor(Math.random() * projects.length)];
      const randomWorkLocation =
        workLocations[Math.floor(Math.random() * workLocations.length)];

      await prisma.workerLocation.create({
        data: {
          workerId: user.id,
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          accuracy: Math.random() * 10 + 2, // 2-12 meters accuracy
          timestamp: new Date(),
          projectId: randomProject.id,
          workLocationId: randomWorkLocation.id,
          isCompliant: Math.random() > 0.2, // 80% compliant
        },
      });
      console.log(
        `✅ Created worker location for: ${user.firstName} ${user.lastName}`
      );
    }

    console.log("🎉 Map data seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding map data:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedMapData()
    .then(() => {
      console.log("✅ Map data seeding completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Map data seeding failed:", error);
      process.exit(1);
    });
}
