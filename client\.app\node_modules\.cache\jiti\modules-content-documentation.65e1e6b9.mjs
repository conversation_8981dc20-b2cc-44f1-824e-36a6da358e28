"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _nodeFs = await jitiImport("node:fs");
var _promises = await jitiImport("node:fs/promises");
var _nodeUrl = await jitiImport("node:url");
var _kit = await jitiImport("@nuxt/kit");
var _magicString = _interopRequireDefault(await jitiImport("magic-string"));
var _pathe = await jitiImport("pathe");
var _package = await jitiImport("../../package.json");function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };}

// This is a regular expression used to extract the example source code from the markdown content.
const docExampleRe = /^<!-- demo: #examples\/([\w-]+)\/([\w-]+)(:?.vue)? -->$/gm;
const tairoVersionRe = /__TAIRO_VERSION__/g;

// Custom nuxt module to make the examples available in the markdown content.
// It also enable the nuxt-component-meta module to inject the component metadata.
var _default = exports.default = (0, _kit.defineNuxtModule)({
  meta: {
    name: 'content-documentation'
  },
  setup(options, nuxt) {
    const logger = (0, _kit.useLogger)('content-documentation');

    const examplesFolder = (0, _nodeUrl.fileURLToPath)(new URL('../examples', "file:///C:/Users/<USER>/comanager/client/.app/modules/content-documentation.ts"));

    (0, _kit.addComponentsDir)({
      path: examplesFolder,
      prefix: 'examples',
      pathPrefix: true,
      isAsync: true
    });

    /**
     * This hook is used to inject the tairo version
     * into the markdown documentation content.
     */
    nuxt.hook('content:file:beforeParse', async ({ file }) => {
      if (file.extension !== '.md') {
        return;
      }

      const s = new _magicString.default(file.body);

      s.replace(tairoVersionRe, _package.version);

      file.body = s.toString();
    });

    /**
     * This hook is used to inject the example source code
     * into the markdown documentation content.
     */
    nuxt.hook('content:file:beforeParse', async ({ file }) => {
      if (file.extension !== '.md') {
        return;
      }

      if (!docExampleRe.test(file.body)) {
        return;
      }

      const reads = [];
      const replacements =


      [];

      // Ensure the regex is reset before using it
      docExampleRe.lastIndex = 0;
      const matches = [...file.body.matchAll(docExampleRe)];

      for (const [search, folder, name] of matches) {
        const path = (0, _pathe.join)(examplesFolder, `/${folder}/${name}.vue`);

        if (!(0, _nodeFs.existsSync)(path)) {
          logger.warn(`Example file not found in "${file.id}": ${path}`);
          continue;
        }

        reads.push(
          (0, _promises.readFile)(path, 'utf-8').
          then((source) => {
            if (!source) {
              logger.warn(`Example file is empty in "${file.id}": ${path}`);
              return;
            }

            const replace = [
            '::doc-component-demo',
            '',
            '#demo',
            `:examples-${folder}-${name}`,
            '',
            '#source',
            ':::code-group',
            `\`\`\`vue [#examples/${folder}/${name}.vue]`,
            source.trim(),
            '```',
            ':::',
            '::'].
            join('\n');

            replacements.push({ search, replace });
          }).
          catch((error) => {
            logger.warn(`Error reading example file in "${file.id}": ${path}`);
            logger.error(error);
          })
        );
      }

      await Promise.all(reads);

      const s = new _magicString.default(file.body);
      for (const { search, replace } of replacements) {
        s.replace(search, replace);
      }

      file.body = s.toString();
    });

    // if (nuxt.options.dev && !import.meta.env.ENABLE_DOCUMENTATION) {
    //   logger.info('Documentation component meta disabled during dev, set ENABLE_DOCUMENTATION=true to enable it')

    (0, _kit.installModule)('nuxt-component-meta', {
      metaSources: ['@cssninja/tairo-component-meta'],
      exclude: [
      () => true]

    });

    //   return
    // }

    // logger.info('Documentation component meta enabled, make sure to set NODE_OPTIONS=--max-old-space-size=8192')
    // installModule('nuxt-component-meta', {
    //   exclude: [
    //     (component: any) => {
    //       const hasBasePrefix = component?.pascalName?.startsWith('Base')
    //       const hasTairoPrefix = component?.pascalName?.startsWith('Tairo')
    //       const hasAddonPrefix = component?.pascalName?.startsWith('Addon')
    //       const isBlacklisted = hasBasePrefix || ['TairoWelcome', 'TairoLogo', 'TairoLogoText'].includes(component?.pascalName)

    //       const isExcluded = !(hasTairoPrefix || hasAddonPrefix)

    //       return isBlacklisted || isExcluded
    //     },
    //   ],
    // })
  }
}); /* v9-fb9a1116e6a8fe2f */
